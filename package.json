{"name": "vue-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:release": "vue-cli-service build --mode release", "lint": "vue-cli-service lint", "copy": "ncp ./node_modules/@arcgis/core/assets ./public/assets"}, "dependencies": {"@arcgis/core": "^4.21.2", "axios": "^0.19.2", "core-js": "^2.6.5", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "echarts": "^4.8.0", "echarts-liquidfill": "^2.0.6", "element-ui": "^2.13.2", "hls.js": "^1.5.17", "js-cookie": "^2.2.0", "jsencrypt": "^3.3.2", "leaflet": "^1.9.4", "loadsh": "0.0.4", "swiper": "^5.4.5", "vue": "^2.6.10", "vue-awesome-swiper": "^4.1.1", "vue-router": "^3.1.6", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.1.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.12.0", "@vue/cli-plugin-eslint": "^3.12.0", "@vue/cli-service": "^3.12.0", "babel-eslint": "^10.0.1", "compression-webpack-plugin": "^3.1.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.26.8", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}, "postcss-px-to-viewport": {"viewportWidth": 1920, "viewportHeight": 1080, "unitPrecision": 5, "viewportUnit": "vw", "selectorBlackList": [], "minPixelValue": 1, "mediaQuery": false}}}, "browserslist": ["> 1%", "last 2 versions"]}