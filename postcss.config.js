module.exports = {
  plugins: {
    autoprefixer: {},
    "postcss-px-to-viewport": {
      viewportWidth: 1920, // 设计稿的视口宽度
      viewportHeight: 1080, // 设计稿的视口高度
      unitPrecision: 5, // 单位转换后保留的精度
      viewportUnit: "vw", // 希望使用的视口单位
      selectorBlackList: [], // 不转换为视口单位的CSS选择器
      minPixelValue: 1, // 小于或等于1px不转换为视口单位
      mediaQuery: false, // 是否在媒体查询中转换px
      // exclude: [/node_modules/] // 忽略某些文件夹下的文件
    },
  },
};
