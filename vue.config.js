/*
 * @Author: your name
 * @Date: 2020-10-12 16:58:13
 * @LastEditTime: 2021-02-22 10:08:06
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \vue-demo\vue-demo\vue.config.js
 */
const path = require('path');
const resolve = (dir) => path.join(__dirname, dir);
const CompressionWebpackPlugin = require('compression-webpack-plugin');
const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i;
const Timestamp = new Date().getTime();

module.exports = {
    publicPath: '/',
    outputDir: 'dist',
    assetsDir: 'assets',
    lintOnSave: false,
    productionSourceMap: false,
    // configureWebpack: config => {
    //     config.output.filename = `[name].${Timestamp}.js`;
    //     config.output.chunkFilename = `[name].${Timestamp}.js`;
    //     const plugins = [];
    //     plugins.push(
    //         new CompressionWebpackPlugin({
    //             filename: `[path].${Timestamp}.gz[query]`,
    //             algorithm: 'gzip',
    //             test: productionGzipExtensions,
    //             threshold: 10240, // 大于10kb的会压缩
    //             minRatio: 0.8
    //         })
    //     );
    //     config.plugins = [
    //         ...config.plugins,
    //         ...plugins
    //     ];
    //     if (process.env.NODE_ENV === 'production') {} else {}
    // },
    chainWebpack: config => {
        // 添加别名
        config.resolve.alias
            .set('@', resolve('src'))
            .set('api', resolve('src/api'))
            .set('assets', resolve('src/assets'))
            .set('components', resolve('src/components'))
            .set('layout', resolve('src/layout'))
            .set('utils', resolve('src/utils'))
            .set('views', resolve('src/views'));
    },
    //跨域代理配置
    devServer: {
        port: 9528, // 端口号
        // open: 'Chrome',
        proxy: {
            '/dev-api': {
                // target: "http://*************:8211/",  // 开发环境
                // target: "http://192.168.100.8:9502",  // 内网服务器开发环境
                target: 'http://223.75.177.151:9093/api',
                // target: 'http://223.75.177.151:49502',
                secure: true,
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                  '^/dev-api': ''
                }
            },
            '/revenue-api': {
              // target: "http://192.168.100.49:8080",
              target: 'http://223.75.177.151:19506/',
              secure: true,
              changeOrigin: true,
              pathRewrite: {
                '^/revenue-api': ''
              }
            },
            '/prod-api': {
              // target: "http://192.168.100.49:8080",
              target: 'http://223.75.177.151:9093/',
              secure: true,
              changeOrigin: true,
              // pathRewrite: {
              //   '^/prod-api': ''
              // }
            },
            '/geo': {
              // target: "http://192.168.100.49:8080",
              target: 'http://223.75.177.151:28080/', // 兴山服务器 geoserver
              secure: true,
              changeOrigin: true,
              pathRewrite: {
                '^/geo': ''
              }
            },
            '/dev-weather': {
              target: "https://widget-page.qweather.net",
              pathRewrite: {
                '^/dev-weather': ''
              }
            }
        }
    }
};