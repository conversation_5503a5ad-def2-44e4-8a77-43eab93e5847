rtmp {
    server {
        listen 1935;
        chunk_size 4096;
        
        application live {
            live on;
            record off;
            # hls
            hls on;
            hls_path /usr/local/nginx/hls;
            hls_fragment 3s;
            hls_playlist_length 60s;
        }
    }
}


http {

    
    server {
        listen 8080;

        location /hls {
            types {
                application/vnd.apple.mpegurl m3u8;
                video/mp2t ts;
            }
            root /usr/local/nginx/hls;  # 存放 HLS 文件的目录
            add_header Cache-Control no-cache;
            add_header Access-Control-Allow-Origin *;
        }
    }
}
