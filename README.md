<!--
 * @Author: your name
 * @Date: 2020-10-13 17:42:18
 * @LastEditTime: 2020-10-14 09:41:58
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \vue-demo\README.md
-->
# vue-demo

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Project catalog

public: 存放公共资源文件

src：源文件

​         assets:  项目资源文件

​                      css： 公共css样式文件（至少包含common，rewriteElementUI，variable三个文件）

​                                 common.scss：公共css样式的提取

​                                 rewriteElementUI.scss：针对本项目的ui风格，对elementUi的样式重写

​                                 variable：基础样式变量文件（针对后面对ui可能的一些整体的修改）

​                      font：阿里云图标库文件

​                      img：图片资源文件（按模块再细分文件夹）

​         components：项目封装的组件库

​                     （例） table：按组件名创建文件夹，封装组件

​                                 basicTable.vue: 组件命名必须是多单词，防止和现有组件冲突，必须添加注释

​                        index.js：组件库入口文件，所有组件需要在这个文件注册

​         layout：项目布局文件（多布局创建不同的布局文件）

​                       （例)  mainLayout.vue： 项目主体布局文件，如头部、左侧菜单、底部的一些处理）

​        pages：项目主体页面文件（按模块拆分文件夹）

​                       （例）login： 登录模块

​                                        components： 登录模块的公共组件，相对登录模块复用性高

​                                        register.vue: 注册功能的主体代码文件

​                                        login:  登录功能的文件

​                                                  login.vue：登录功能的主体代码文件

​                                                  thirdparty： 第三方登录的文件

​                                         ....以此类推

​         router：项目的路由管理文件（按模块拆分文件夹，防止多人开发提交文件冲突）

​                      （例）loginReg： 注册登录模块

​                         index.js：路由的入口文件，所有路由都在这个文件里注册

​         store：vue的状态管理文件

​                      modules：不同store的管理文件

​                      getters.js:  暴露在外部的一些方法

​                      index.js：状态的入口文件，所有state都在这个文件里注册

​         utils：公共方法文件

​                    filters：项目的公共过滤器文件（如：时间过滤）

​                    https：项目的axios的二次封装文件（处理一些公共的拦截问题）

​                    service：不同模块的api的接口统一管理文件

​                    tools：公共方法文件（如加减乘除）

​         App.vue:  根组件

​         main.js：项目的入口文件

.env.master：项目配置文件（测试打包时不用频繁修改文件，只需要执行打包命令）

package.json：项目的公共依赖文件

vue.config.js：项目的打包配置文件

