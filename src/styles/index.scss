@font-face {
  font-family: PangMenZhengDao;
  src: url('../assets/font/PangMenZhengDao.TTF');
}
@font-face {
  font-family: DIN;
  src: url('../assets/font/DIN-BOLD.TTF');
}
@font-face {
  font-family: LED;
  src: url('../assets/font/UNIDREAMLED.TTF');
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.app-container {
  .tab {
    color: #ffffff;    
    display: flex;
    div {
      width: 136px;
      height: 56px;
      opacity: 0.6;
      text-align: center;
      padding-top: 26px;
      cursor: pointer;
      // background-color: (180deg, #FFFFFF 0%, #67C6FF 100%);
    }
    .active {
      opacity: 1;
    }
    .first {
      background-image: url('~@/assets/img/wq/first-tab.png');
      background-size: 100%;      
    }
    .other {
      background-image: url('~@/assets/img/wq/other-tab.png');
      background-size: 100%;      
    }
  }
  .screen-common {
    background-color: rgba(4, 15, 45, 0.3);
    .title {
      height: 35px;
      width: 430px;
      font-size: 22px;
      font-family: PangMenZhengDao;
      padding-left: 44px;
      background-image: url("~@/assets/img/title.png");
      background-repeat: no-repeat;
      background-size: 430px 35px;
      line-height: 35px;
    }
    .flex-between {
      padding-right: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .common-card {
      // background-color: rgba(4, 15, 45, 0.3);
    }
  }
}
