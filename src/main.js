import Vue from 'vue';
import App from './App.vue';
import router from "./router";
import store from './store'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import _ from 'lodash';
import api from './api/index'
import * as filters from './utils/filters/index';
import hawkUI from './components/index';
import * as echarts from 'echarts'
// import '@arcgis/core/assets/esri/themes/light/main.css';

import './premission'
import './styles/index.scss'
import './styles/qweather-icons/qweather-icons.css'

Vue.prototype.$echarts = echarts
// eachrts的宽度自适应配置
Vue.prototype.$echartsResize = function(ref) {
  window.addEventListener('resize', function() {
    ref.resize()
  })
}

Vue.config.productionTip = false;

Vue.prototype._ = _;

Vue.use(ElementUI);

// 挂载到全局，在代码里就可以直接用了
Vue.use(hawkUI); 

//api挂载到vue原型，可以使用this.$api直接调用api
Vue.prototype.$api = api;

// 注册全局过滤器
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
});

new Vue({
  store,
  router,
  render: h => h(App),
}).$mount('#app')

// 生产环境取消控制台打印
if (process.env.NODE_ENV == 'production') {
  console.log = function() {}
  console.error = function() {}
}