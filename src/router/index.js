import Vue from 'vue';
import Router from 'vue-router';
import MainLayout from '@/layout/MainLayout'
import IndexLayout from '@/views/home/<USER>'
import WqScreen from '@/views/wq/screen'

Vue.use(Router);

const routerPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(error => error)
};

export const routes = [
  {
    path: '/',
    redirect: 'main'
  },
  {
    path: '/v2',
    redirect: '/v2/home'
  },
  {
    path: '/v2/home',
    name: 'V2Home',
    component: () => import('@/views/v2/Home.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login'),
  },
  {
    path: '/project',
    name: 'Project',
    component: () => import('@/views/project')
  },
  {
    path: '/demo',
    name: 'Demo',
    component: () => import('@/views/demo'),
  },
  // 首页
  {
    path: '/main',
    component: MainLayout,
    redirect: '/main/home/<USER>',
    children: [
      // {
      //   path: 'index',
      //   name: 'Home',
      //   component: () => import('@/views/home'),
      //   meta: {
      //     title: '欢迎'
      //   }
      // },
      {
        path: 'uav',
        name: 'UAV',
        component: () => import('@/views/home/<USER>/uav'),
      },
      {
        path: 'home',
        name: 'Home',
        component: IndexLayout,
        // 首页三级路由
        children: [
          {
            path: 'infoMap',
            name: 'InfoMap',
            component: () => import('@/views/home/<USER>'),
          },
          // {
          //   path: 'uav',
          //   name: 'UAV',
          //   component: () => import('@/views/home/<USER>/uav'),
          // },
          {
            path: 'layerCenter',
            name: 'LayerCenter',
            component: () => import('@/views/home/<USER>'),
          },
          // 一张图路由集中
          {
            path: 'dispatchMap',
            name: 'DispatchMap',
            component: () => import('@/views/dispatch/screen'),
          },
          {
            path: 'leakageLossMap',
            name: 'LeakageLossMap',
            component: () => import('@/views/leakageLoss/screen')
          },
          {
            path: 'pipNetworkMap',
            name: 'PipNetworkMap',
            component: () => import('@/views/pipeNetwork/index')
          },
          {
            path: 'wqMap',
            name: 'WqMap',
            component: WqScreen
          },
          {
            path: 'revenueMap',
            name: 'RevenueMap',
            component: () => import('@/views/revenue/screen')
          },
          {
            path: 'monitorPage',
            name: 'MonitorPage',
            component: () => import('@/views/home/<USER>/videoWall')
          }
        ]
      },
      {
        path: 'videoWall',
        name: 'VideoWall',
        component: () => import('@/views/home/<USER>/videoWall'),
      },
      // 子页面
      {
        path: 'waterFactory',
        name: 'HomeWaterFactory',
        component: () => import('@/views/home/<USER>/waterFactory'),
      },
      {
        path: 'uav',
        name: 'HomeUav',
        component: () => import('@/views/home/<USER>/uav'),
      }
    ]
  },
  // 水质监测
  {
    path: '/wqMonitor',
    component: MainLayout,
    children: [
      // 水质首页
      {
        path: 'screen',
        name: 'WqScreen',
        // component: () => import('@/views/wq/screen')
        component: WqScreen
      },
      // 实时数据
      {
        path: 'index',
        name: 'WqMonitor',
        component: () => import('@/views/wq'),
      },
      // 数据分析
      {
        path: 'wqAnalyze',
        name: 'WqAnalyze',
        component: () => import('@/views/wq/analyze'),
      },
      // 应急方案
      {
        path: 'wqProgramme',
        name: 'WqProgramme',
        component: () => import('@/views/wq/programme/index'),
      },
      // 水质预警
      {
        path: 'wqWarn',
        name: 'WqWarn',
        component: () => import('@/views/wq/wqWarn'),
      },
      // 水质报告
      {
        path: 'wqReport',
        name: 'WqReport',
        component: () => import('@/views/wq/wqReport'),
      }
    ]
  },
  // 管网分析
  {
    path: '/pipeNetwork',
    component: MainLayout,
    children: [
      {
        path: 'index',
        name: 'PipeNetworkIndex',
        component: () => import('@/views/pipeNetwork/index')
      },
      {
        path: 'business',
        name: 'Business',
        component: () => import('@/views/pipeNetwork/business')
      },
      {
        path: 'screen',
        name: 'PipeNetworkScreen',
        component: () => import('@/views/pipeNetwork/screen')
      }
    ]
  },
  // 供水调度
  {
    path: '/wsDispatch',
    component: MainLayout,
    children: [
      // 首页
      {
        path: 'wsScreen',
        name: 'WsScreen',
        component: () => import('@/views/dispatch/screen')
      },
      // 实时监测
      {
        path: 'realTimeMonitor',
        name: 'RealTimeMonitor',
        component: () => import('@/views/dispatch/realTimeMonitor/screen')
      },
      {
        path: 'realTimeMap',
        name: 'RealTimeMap',
        component: () => import('@/views/dispatch/realTimeMonitor/otherMap')
      },
      {
        path: 'data',
        name: 'Data',
        component: () => import('@/views/dispatch/realTimeMonitor')
      },
      // 预警预报
      {
        path: 'warn',
        name: 'Warn',
        component: () => import('@/views/dispatch/warn')
      },
      // 风险预测
      {
        path: 'riskProfile',
        name: 'RiskProfile',
        component: () => import('@/views/dispatch/riskProfile')
      },
      // 调蓄调度
      {
        path: 'stroeRegulate',
        name: 'StroeRegulate',
        component: () => import('@/views/dispatch/store')
      },
      {
        path: 'riskControl',
        name: 'RiskControl',
        component: () => import('@/views/dispatch/riskControl')
      },
      {
        path: 'overviewPage',
        name: 'OverviewPage',
        component: () => import('@/views/dispatch/overview')
      },
      // 月度报告
      {
        path: 'monthReport',
        name: 'MonthReport',
        component: () => import('@/views/dispatch/monthReport')
      },
      //风险管控
      {
        path: 'riskManage',
        name: 'RiskManage',
        component: () => import('@/views/dispatch/riskManage')
      }
    ]
  },

  // 漏损中心
  {
    path: '/wlStatistic',
    component: MainLayout,
    children: [
      {
        path: 'wlManage',
        name: 'WlManage',
        component: () => import('@/views/leakageLoss/layout'),
        children: [
          // 分区管理
          {
            path: 'subzone',
            name: 'Subzone',
            component: () => import('@/views/leakageLoss/subzone/indexNew.vue'),
            meta: { name: '分区管理' }
          },
          // 大用户管理
          {
            path: 'bigUser',
            name: 'BigUser',
            component: () => import('@/views/leakageLoss/subzone/bigUser'),
            meta: { name: '分区管理' }
          },
          // 压力地图
          {
            path: 'pressureMap',
            name: 'PressureMap',
            component: () => import('@/views/leakageLoss/pressureMap'),
            meta: { name: '压力地图' }
          },
          // 漏损分析
          {
            path: 'wlAnalysis',
            name: 'WlAnalysis',
            component: () => import('@/views/leakageLoss/wlAnalysis/indexNew.vue'),
            meta: { name: '漏损分析' }
          },
          // 智能诊断
          {
            path: 'diagnosis',
            name: 'Diagnosis',
            component: () => import('@/views/leakageLoss/diagnosis'),
            meta: { name: '智能诊断' }
          },
          // 工单中心
          // {
          //   path: 'orderCenter',
          //   name: 'OrderCenter',
          //   component: () => import('@/views/leakageLoss/orderCenter'),
          //   meta: { name: '工单中心' }
          // },
          // 调度管理
          {
            path: 'dispatch',
            name: 'Dispatch',
            component: () => import('@/views/leakageLoss/dispatch'),
            meta: { name: '调度管理' }
          },
          // 绩效管理
          {
            path: 'performance',
            name: 'Performance',
            component: () => import('@/views/leakageLoss/performance'),
            meta: { name: '绩效管理' }
          },
          // 考核标准
          {
            path: 'standard',
            name: 'Standard',
            component: () => import('@/views/leakageLoss/performance/standard'),
            meta: { name: '标准管理' }
          },
          {
            path: 'addStandard',
            name: 'AddStandard',
            component: () => import('@/views/leakageLoss/performance/standard/addStandard'),
            meta: { name: '新增标准' }
          },
          // 考核评分记录
          {
            path: 'score',
            name: 'Score',
            component: () => import('@/views/leakageLoss/performance/score'),
            meta: { name: '评分管理' }
          },
          {
            path: 'addScore',
            name: 'AddScore',
            component: () => import('@/views/leakageLoss/performance/score/addScore'),
            meta: { name: '新建评分' }
          },
          {
            path: 'editScore',
            name: 'EditScore',
            component: () => import('@/views/leakageLoss/performance/score/addScore'),
            meta: { name: '编辑评分' }
          },
          {
            path: 'warning',
            name: 'Warning',
            component: () => import('@/views/leakageLoss/warning'),
            meta: { name: '漏损报警' }
          },
        ]
      },
      // 首页
      {
        path: 'screen',
        name: 'WlScreen',
        component: () => import('@/views/leakageLoss/screen')
      },
      // 修改为二级菜单
      {
        path: 'subarea',
        name: 'Subarea',
        component: () => import('@/views/leakageLoss/subzone/indexNew.vue'),
        meta: { name: '分区概览' }
      },
      {
        path: 'special',
        name: 'Special',
        component: () => import('@/views/leakageLoss/new/special'),
        meta: { name: '专题分析' }
      },
      {
        path: 'realTime',
        name: 'RealTime',
        component: () => import('@/views/leakageLoss/new/realTime'),
        meta: { name: '实时数据' }
      },
      {
        path: 'nightMinFlow',
        name: 'NightMinFlow',
        component: () => import('@/views/leakageLoss/new/nightMinFlow'),
        meta: { name: '夜间做小流量' }
      },
      {
        path: 'pressureArea',
        name: 'PressureArea',
        component: () => import('@/views/leakageLoss/pressureMap/map'),
        meta: { name: '压力分布' }
      },
      {
        path: 'balance',
        name: 'Balance',
        component: () => import('@/views/leakageLoss/new/balance'),
        meta: { name: '水平衡分析' }
      },
      {
        path: 'bigUserMeter',
        name: 'BigUserMeter',
        component: () => import('@/views/leakageLoss/new/bigUserMeter'),
        meta: { name: '大用户表' }
      },
      {
        path: 'smart',
        name: 'Smart',
        component: () => import('@/views/leakageLoss/diagnosis'),
        meta: { name: '智能诊断' }
      },
      {
        path: 'dispatchPage',
        name: 'DispatchPage',
        component: () => import('@/views/leakageLoss/new/dispatchPage'),
        meta: { name: '调度管理' }
      },
      {
        path: 'performanceScore',
        name: 'PerformanceScore',
        component: () => import('@/views/leakageLoss/new/performancePage'),
        meta: { name: '绩效管理' }
      },
      {
        path: 'platform',
        name: 'Platform',
        component: () => import('@/views/leakageLoss/new/platform'),
        meta: { name: '平台管理' }
      },
      // 压力分析
      // {
      //   path: 'pressure',
      //   name: 'Pressure',
      //   component: () => import('@/views/leakageLoss/pressure')
      // },
      // 漏损分析
      // {
      //   path: 'wlAnalysis',
      //   name: 'WlAnalysis',
      //   component: () => import('@/views/leakageLoss/wlAnalysis')
      // },
      // // 漏损排行
      // {
      //   path: 'wlRank',
      //   name: 'WlRank',
      //   component: () => import('@/views/leakageLoss/rank')
      // },
      // // 大用户
      // {
      //   path: 'bigUser',
      //   name: 'BigUser',
      //   component: () => import('@/views/leakageLoss/bigUser')
      // },
    ]
  },

  // 营收管理
  {
    path: '/revenue',
    component: MainLayout,
    children: [
      {
        path: 'index',
        name: 'Revenue',
        component: () => import('@/views/revenue/screen')
      },
      {
        path: 'subSystem',
        name: 'SubSystem',
        component: () => import('@/views/revenue/subSystem')
      }
    ]
  }
];

const createRouter = () => new Router({
  mode: 'history',
  routes: routes
});

const router = createRouter();

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
}

export default router;