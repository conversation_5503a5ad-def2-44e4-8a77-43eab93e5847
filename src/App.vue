<!--
 * @Author: your name
 * @Date: 2020-10-12 16:56:51
 * @LastEditTime: 2020-10-12 17:55:37
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \vue-demo\src\App.vue
-->
<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: "app",
  mounted() {
    function bodyScale() {
      // 首先获取当前文档的可视区域宽度,
      // 然后将其除以设计稿的尺寸宽度1920，得到一个缩放比例scale。
      // 最后，将该缩放比例应用于document.body.style.zoom属性，以实现页面的缩放效果。
      var devicewidth = document.documentElement.clientWidth;
      var scale = devicewidth / 1920; // 分母——设计稿的尺寸宽度
      document.body.style.zoom = scale;
    }
    // 页面加载和窗口大小改变时会重新设置比例
    window.onload = window.onresize = function () {
      // bodyScale();
    };
  },
};
</script>

<style>
@import "./assets/css/rewriteElementUI.scss";
@import "./assets/css/common.scss";

#app {
  width: 100%;
  height: 100%;
  min-width: 1920px;
}
</style>
