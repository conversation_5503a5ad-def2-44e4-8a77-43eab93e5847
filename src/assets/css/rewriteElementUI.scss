@import "./variable.scss";

/**
 * 提示信息框 默认重写
**/
.el-tooltip__popper {
  padding: 6px 12px !important;
  max-width: 200px;
}
/**
 * 表单 默认重写
**/
.el-form {
  .el-form-item__label{
    font-size: 16px;
    line-height: 36px;
    padding: 0;
    color: #fff;
  }
  .el-form-item__content{
    font-size: 16px;
    line-height: 36px;
  }
}
/**
 * 输入框 默认重写
**/
.el-input.is-disabled .el-input__inner, .el-textarea__inner {
  background-color: #003970 !important;
  border: solid 1px #1382E6 !important;

}
.el-input__inner {
  border: solid 1px #1382E6 !important;
  border-radius: 4px;
  background: #031E49 !important;
  color: #fff !important;
  font-size: 16px;
}
/**
 * 表格 默认重写
 // 表格悬浮边框高亮，需要配合设置tr td 的上边框和下边框
**/
.el-table {
  background-color: transparent !important;
}

.el-table tr:hover > td,
.el-table__body tr.current-row > td,
.el-table__body tr.hover-row > td,
.el-table__body tr.checked > td {
  background-color: #053A7F !important;
  border-bottom: 1px solid #52D0DF!important; 
  border-top: 1px solid #52D0DF !important; 
}

.el-table__body tr:first-child{
  &:hover > td, &.current-row > td, .hover-row > td, tr.checked > td {
    border-top: 1px solid #52D0DF!important; 
  }
}

.el-table tr:hover > td:first-child,
.el-table__body tr.current-row > td:first-child,
.el-table__body tr.hover-row > td:first-child,
.el-table__body tr.checked > td:first-child {
  border-left: 1px solid #52D0DF!important; 
}

.el-table tr:hover > td:last-child,
.el-table__body tr.current-row > td:last-child,
.el-table__body tr.hover-row > td:last-child,
.el-table__body tr.checked > td:last-child {
  border-right: 1px solid #52D0DF!important; 
}
.el-table th, .el-table td {
  text-align: center !important;
}
.el-table__empty-block {
  background: #021F3D;
  .el-table__empty-text {
    color: #fff !important

  }
}
.el-table tr > td {
  color: white !important;

}
.el-table {
  font-size: 16px !important;
  .el-table__fixed {
    height: auto !important; 
    bottom: 0; 
  }

  &.tb-Scroll {
    .el-table__fixed {
      bottom: 17px;
    }
  }
}

.el-table::before {
  display: none !important;
}

.el-table__fixed-right::before, .el-table__fixed::before {
  display: none !important;
}

.el-table__header-wrapper {
  background: linear-gradient(180deg, #2A5DD2 0%, #2A5DD2 25%, #023280 100%) !important;
}

.el-table th {
  color: #fff;
  border: none !important;
  background: #053A7F !important;
}

.el-table--border,
.el-table td {
  border: none !important;
}

.el-table--border::after, .el-table--group::after, .el-table::before {
  background-color: #fff !important;
}

.el-table tr > td {
  color: #60697d;
}

.el-table__body tr > td {
  border-bottom: 1px solid #023280!important;
  border-top: 1px solid transparent!important;
}

/* 表格奇数行背景色 */
.el-table tr:nth-child(odd){
  background: #031E49;
}

/* 表格偶数行背景色 */
.el-table__body > tbody > tr:nth-child(even) {
  background: #031E49;
}



/**
 * 链接 默认重写
**/
.el-link.el-link--primary {
  color: $base-color !important;
}

.el-link.el-link--danger {
  color: #f05150 !important;
}

.el-link.is-disabled {
  color: #ccc !important;
}

/**
 * 分页 默认重写
**/
.el-pagination button,
.el-pager li {
  height: 32px !important;
  line-height: 32px !important;
  min-width: 32px !important;
  margin: 0 4px !important;
}

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  background-color: transparent !important;
  border: solid 1px #1382E6;
  border-radius: 6px !important;
}

.el-pagination.is-background .btn-next:not(:disabled):hover,
.el-pagination.is-background .btn-prev:not(:disabled):hover,
.el-pagination.is-background .el-pager li:hover {
  border-color: $base-color;
  color: $base-color;
}

.el-pagination {
  .el-pagination__total {
    color: #ffffff;
  }
  .el-pagination__jump {
    color: #ffffff;
  }
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  border-color: $base-color;
  background-color: #1382E6 !important;
  color: $light-color !important;
}

.el-pagination__total {
  height: 32px !important;
  line-height: 32px !important;
}

/**
 * 按钮 默认重写
**/
.el-button--small, .el-button--small.is-round {
  padding: 9px 17px !important;
}

.el-button--default {
  color: $btn-default-font-color;
  background-color: $light-color !important;
  border-color: $btn-default-color !important;
  &:focus,
  &:not(:disabled):hover {
    color: $btn-hover-color !important;
    background-color: $light-color !important;
    border-color: $btn-hover-color !important;
  }
  &:not(:disabled):active {
    color: $btn-primary-color !important;
    background-color: $light-color !important;
    border-color: $btn-primary-color !important;
  }
}

.el-button--primary,
.el-button--default.el-button--primary {
  color: $light-color !important;
  background: linear-gradient(180deg, #1382E6 0%, #0E4C9A 68.59%, #043474 99.23%) !important;
  border-color: $btn-primary-color !important;
  &:not(:disabled):hover {
    color: $light-color !important;
    background-color: $btn-hover-color !important;
    border-color: $btn-hover-color !important;
  }
  &:not(:disabled):active {
    color: $light-color !important;
    background-color: $btn-primary-color !important;
    border-color: $btn-primary-color !important;
  }
  &:disabled {
    background-color: $btn-disable-color !important;
    border-color: $btn-disable-color !important;
    color: $btn-disable-font-color !important;
  }
}

/**
 * 输入框 默认重写
**/
.el-input__inner {
  border: solid 1px #d9dce2 ;
  padding-left: 10px ;
  &:focus {
    border-color: $base-color !important;
  }
  &::-webkit-input-placeholder {
    color: $placeholder-color !important;
  }
  &:-moz-placeholder {
    color: $placeholder-color !important;
  }
  &::-moz-placeholder {
    color: $placeholder-color !important;
  }
  &:-ms-input-placeholder {
    color: $placeholder-color !important;
  }
}

.el-select {
  width: 100%;
}


/* 文本域 */
.el-textarea {
  .el-textarea__inner {
    padding: 8px 10px !important;
    font-family: 微软雅黑;
  }
  .el-input__count {
    height: 14px !important;
    line-height: 1;
    background: transparent !important;
  }
}

.el-select,
.el-cascader {
  .el-input__inner {
    display: block !important;
  }
}

.el-input--small {
  &:not(.el-textarea) {
    height: 32px;
  }
  .el-input__icon {
    height: 31px !important;
  }
}

/* 控件鼠标悬停样式 */
.el-input:not(.is-disabled):hover,
.el-select:not(.is-disabled):hover,
.el-cascader:not(.is-disabled):hover {
  .el-input__inner {
    border-color: $base-color !important;
  }
}

/* 级联选择和下拉选择框，距离上部距离 */
.el-popper {
  margin-top: 5px !important;
}

.el-cascader-menu {
  min-width: 120px !important;
}

.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
  font-family: '宋体';
}

/* 下拉 选项被选中效果 */
.el-select-dropdown__item.selected,
.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  color: $light-color !important;
  background-color: $base-color !important;
}

/* 输入框右侧图标改写：验证通过和失败图标、清除图标 */
.el-input__suffix {
  .el-input__validateIcon.el-icon-circle-check {
    &:before {
      content: "\e79c";
    }
  }
  .el-input__validateIcon.el-icon-circle-close,
  .el-input__clear.el-icon-circle-close {
    &:before {
      content: "\e79d";
    }
  }
  .el-input__clear {
    color: #959aa8 !important;
  }
  &:hover .el-input__clear {
    color: #656d81 !important;
  }
}

.el-dialog {
  background: #002140 !important;
  .el-dialog__header {
    height: 50px;
    padding: 16px 30px;
    background: linear-gradient(90deg, rgba(15, 104, 184, 1) 0%, rgba(0, 57, 112, 1) 100%);
    display: flex;
    align-items: center;
    .el-dialog__title {
      font-size: 18px;
      font-weight: 700;
      color: #fff;
    }
    .el-dialog__headerbtn {
      top: 12px;
      right: 25px;
      font-size: 24px;
      .el-dialog__close {
        color: #fff;
      }
    }
  }
  .el-dialog__body {
    padding: 30px 30px 0;
    .el-form-item {
      margin-bottom: 30px;
    }
  }
  .el-dialog__footer {
    text-align: center;
    padding: 0 0 30px;
    border-top: none;
    .btn+.btn{
      margin-left: 20px;
    }
    .btn {
      height: 36px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      color: #fff;
      line-height: 34px;
      img {
        height: 18px;
        margin-right: 8px;
      }
    }
  
    .primary_btn {
      width: 90px;
      background: #1382E6;
      border: 1px solid #1382E6;
    }
  
    .plain_btn {
      width: 90px;
      background: #003970;
      border: 1px solid #1382E6;   
    }
  }
  
}