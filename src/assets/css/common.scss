* {
    box-sizing: border-box;
  }
  
  html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: auto;
    font-family: 'Avenir', Helvetica, Arial, sans-serif, 'PingFangHK-Regular';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: $base-font-color;
    font-size: $font-14;
  }
  
  ul, dl, dd, dt {
    margin: 0;
    padding: 0;
    list-style: none
  }
  
  audio, video {
    outline: none;
  }
  
  .text_left {
    text-align: left !important;;
  }
  
  .text_center {
    text-align: center !important;
  }
  
  .text_right {
    text-align: right !important;;
  }
  
  .pull_left {
    float: left;
  }
  
  .pull_right {
    float: right;
  }
  
  .clear_fix:after {
    content: '';
    clear: both;
    display: block;
  }