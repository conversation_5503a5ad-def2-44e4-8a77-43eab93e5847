<template>
  <div class="main-layout" :class="{ bgSet: routerIsHome }">
    <div class="top-bar flex">
      <!-- 原有的五个主要导航按钮已移除，保留中央标题和右侧设置 -->
      <div class="title"></div>

      <div
        class="menu flex menu6"
        :class="{ ractive: activetab === '6' }"
        @click="tabchange('6')"
        @mouseenter="handleMouseEnter('showRevenueMenu')"
        @mouseleave="handleMouseLeave('showRevenueMenu')"
      >
        营收管理
        <div class="menuList" v-show="showRevenueMenu">
          <div class="menuBody">
            <div
              v-for="m in revenueRouteList"
              :key="m.id"
              @click.stop="subMenuJump(m.name, '6')"
              :class="{ menuActive: m.name === currentRouteName }">
              {{ m.label }}
            </div>
          </div>
        </div>
      </div>
      <div class="setting">
        <!-- <i class="el-icon-setting icon" @click="toAdmin"></i> -->
        <el-dropdown @command="handleCommand">
          <i class="el-icon-setting icon"></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="logout">退出</el-dropdown-item>
            <el-dropdown-item command="backstage">后台</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="main-bar">
      
      <!-- <div class="side-bar"></div>
      <div class="main-body">
        <div class="work-area">
          <router-view />
        </div>
      </div> -->
      <router-view />
    </div>
  </div>
</template>

<script>
import { getToken, removeToken } from '@/utils/auth'
import PipeMap from '@/views/pipeNetwork/screen/map'

import { mapMutations, mapGetters } from 'vuex'

export default {
  name: "MainLayout",
  components: { PipeMap },
  data() {
    return {
      activetab: "1",
      showWqMenu: false,
      showPipeNetworkMenu: false,
      // activePipeNetWorkMenu: Number(localStorage.getItem('activePipeNetWorkMenu')) || -1,
      activePipeNetWorkMenu: this.$store.state.app.pipeNetworkMenuActive,
      showWsMenu: false,
      showLsMenu: false,
      showRevenueMenu: false,
      // 水质监测二级菜单
      wqRouteList: [
        {
          id: 5,
          name: 'WqMonitor',
          label: '实时数据'
        },
        {
          id: 1,
          name: 'WqAnalyze',
          label: '数据分析'
        },
        {
          id: 2,
          name: 'WqWarn',
          label: '水质预警'
        },
        {
          id: 3,
          name: 'WqReport',
          label: '水质报告'
        },
        {
          id: 4,
          name: 'WqProgramme',
          label: '应急方案'
        }
      ],
      // 管网分析二级菜单
      pipenetwork: [
        {
          id: 1,
          value: 1,
          label: '查询统计'
        },
        {
          id: 2,
          value: 2,
          label: '管网分析'
        },
        {
          id: 3,
          value: 3,
          label: '专题展示'
        },
        {
          id: 4,
          value: 4,
          label: '管网编辑'
        }
      ],
      // 供水调度二级菜单
      wsRouteList: [
        {
          id: 5,
          name: 'OverviewPage',
          label: '调度总览'
        },
        {
          id: 1,
          name: 'RealTimeMonitor',
          label: '运行一张图'
        },
        // {
        //   id: 8,
        //   name: 'RealTimeMap',
        //   label: '实时监测'
        // },
        {
          id: 2,
          name: 'Warn',
          label: '风险预警'
        },
        {
          id: 4,
          name: 'StroeRegulate',
          label: '错峰调度'
        },
        // {
        //   id: 6,
        //   name: 'MonthReport',
        //   label: '月度报告',
        // },
        {
          id: 7,
          name: 'RiskManage',
          label:'风险管控'
        },
        {
          id: 3,
          name: 'RiskProfile',
          label: '风险预测'
        },
      ],
      // 漏损统计二级菜单
      lsRouteList: [
        // {
        //   id: 1,
        //   name: 'Subzone',
        //   label: '漏损中心'
        // },
        { id: 2, name: 'Subarea', label: 'DMA概览' },
        { id: 3, name: 'Special', label: '专题分析' },
        { id: 4, name: 'RealTime', label: '实时数据' },
        { id: 5, name: 'NightMinFlow', label: '夜间最小流量' },
        { id: 6, name: 'PressureArea', label: '压力分布' },
        // { id: 7, name: 'Balance', label: '水平衡分析' },
        // { id: 8, name: 'BigUserMeter', label: '大用户表' },
        // { id: 9, name: 'Smart', label: '智能诊断' },
        { id: 10, name: 'DispatchPage', label: '调度管理' },
        { id: 11, name: 'PerformanceScore', label: '绩效管理' },
        { id: 12, name: 'Platform', label: '平台管理' },
        // {
        //   id: 1,
        //   name: 'Subzone',
        //   label: '分区管理'
        // },
        // {
        //   id: 2,
        //   name: 'Pressure',
        //   label: '压力分析'
        // },
        // {
        //   id: 5,
        //   name: 'BigUser',
        //   label: '大用户'
        // },
        // {
        //   id: 3,
        //   name: '',
        //   label: '漏损管理'
        // },
        // {
        //   id: 4,
        //   name: 'WlAnalysis',
        //   label: '漏损分析'
        // },
        // {
        //   id: 5,
        //   name: 'WlRank',
        //   label: '漏损排行'
        // }
      ],
      // 营收管理二级菜单
      revenueRouteList: [
        {
          id: 1,
          name: 'SubSystem',
          label: '营收系统'
        },
        // { id: 2, name: 'Balance', label: '水平衡分析' },
      ]
    };
  },
  watch: {
    '$route.query.type': {
      handler(newVal, oldVal) {
        this.activePipeNetWorkMenu = newVal
      },
      immediate: true
    },
  },
  methods: {
    ...mapMutations('app', ['SET_pipeNetworkMenuActive']),
    // 一级菜单点击
    tabchange(idx) {
      if(idx != '3') {
        this.activePipeNetWorkMenu = -1
        // localStorage.setItem('activePipeNetWorkMenu', -1)
        this.SET_pipeNetworkMenuActive(-1)
      }
      if(idx === '1') {
        this.activetab = idx;
        this.$router.push({
          name: 'InfoMap'
        })
      }
      if(idx === '1') {
        this.$router.push({
          name: 'InfoMap'
        })
      } else if(idx === '2') {
        this.$router.push({
          name: 'WqScreen'
        })
      } else if(idx === '3') {
        this.$router.push({
          name: 'PipeNetworkIndex'
        })
        this.activePipeNetWorkMenu = -1
      } else if(idx === '4') {
        this.$router.push({
          name: 'WsScreen'
        })
      } else if(idx === '5') {
        this.$router.push({
          name: 'WlScreen'
        })
      } else if(idx === '6') {
        this.$router.push({
          name: 'Revenue'
        })
      }
    },
    // 处理一级菜单active样式
    menuActive(path) {
      return (this.$route.path).includes(path)
    },
    // 管网分析二级菜单，单独处理，不进行路由跳转
    clickMenu(m) {
      this.activePipeNetWorkMenu = m.value
      this.SET_pipeNetworkMenuActive(m.value)
      // localStorage.setItem('activePipeNetWorkMenu', m.value)
      // this.$router.push({ name: 'PipeNetworkScreen' })
      this.$router.push({
        name: 'Business',
        query: { type: m.value }
      })
    },
    // 二级路由跳转
    subMenuJump(routeName, menuIndex) {
      this.activetab = menuIndex
      if(routeName) {
        console.log(routeName)
        this.$router.push({
          name: routeName
        })
      }
    },
    handleMouseEnter(prop) {
      this[prop] = true
    },
    handleMouseLeave(prop) {
      this[prop] = false
    },
    toAdmin() {
      window.open(process.env.VUE_APP_BACK_URL)
    },
    handleCommand(command) {
      if(command === 'logout') {
        this.$router.push('/project')
        // removeToken()
        // this.$router.push(`/login?redirect=${this.$route.fullPath}`)
        // this.$router.push('/login')
      }
      if(command === 'backstage') {
        console.log('后台' + process.env.VUE_APP_BACK_URL)
        window.open(process.env.VUE_APP_BACK_URL)
      }
    }
  },
  computed: {
    ...mapGetters({
      getHomeAction: 'app/getHomeAction'
    }),
    routerIsHome() {
      return this.$route.name === 'Home' && this.getHomeAction === 1 || this.$route.name === 'RiskControl'
    },
    currentRouteName() {
      return this.$route.name
    },
    currentPath() {
      return this.$route.path
    }
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
@import "../assets/css/variable";
.main-layout {
  position: relative;
  height: 100%;
  overflow: hidden;
  background-color:#021F3D;
}
.bgSet {
  background: url("../assets/img/bg.png") center no-repeat;
} 

.top-bar {
  display: flex;
  align-items: center;
  height: 84px;
  font-family: Microsoft YaHei;
  background: url("../assets/img/top.png") center no-repeat;
  background-size: 100%;
  .flex {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .home {
    margin-left: 88px;
  }
  .title {
    display: block;
    width: 456px;
    height: 42px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    color: #ffffff;
    font-size: 30px;
    font-weight: bold;
    text-align: center;
    background: url("../assets/img/main_title.png") center no-repeat;
  }
  .menu {
    cursor: pointer;
    font-size: 22px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    font-style: italic;
    color: #ffffff;
    line-height: 24px;
    width: 140px;
    height: 42px;
    position: relative;
    .menuList {
      padding: 20px 0;
      position: absolute;
      top: 40px;
      font-style: normal;
      font-size: 16px;
      z-index: 9999;
      width: 140px;
      &::before {
        content: '';
        display: block;
        width: 100%;
        height: 20px;
        position: absolute;
        top: 0;
        background-image: url("../assets/img/menu_top.png");
        background-repeat: no-repeat;
        background-size: 100%;
      }
      &::after {
        content: '';
        display: block;
        width: 100%;
        height: 20px;
        position: absolute;
        bottom: 0;
        background-image: url("../assets/img/menu_bottom.png");
        background-repeat: no-repeat;
        background-size: 100%;
      }
      .menuBody {
        background-image: url("../assets/img/menu_body.png");
        background-repeat: no-repeat;
        background-size: 140px 100%;
      }
      .menuBody > div {
        height: 34px;
        line-height: 32px;
        margin-bottom: 5px;
        text-align: center;
        border: 1px solid transparent;
        &:hover {
          background: linear-gradient(90deg, #012866 0%, #1382E6 50%, #002869 100%);
          border: 1px solid #1382E6;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
      .menuActive {
        background: linear-gradient(90deg, #012866 0%, #1382E6 50%, #002869 100%);
        border: 1px solid #1382E6;
      }
      
    }
  }
  .menu4 {
    position: absolute;
    right: 375px;
  }
  .menu5 {
    position: absolute;
    right: 235px;
  }
  .menu6 {
    position: absolute;
    right: 96px;
  }
  .setting {
    position: absolute;
    right: 40px;
    .icon {
      color: #ffffff;
      font-size: 20px;
      cursor: pointer;
    }
  }
}
.active {
  background: url("../assets/img/active.png") center no-repeat;
  background-size: 100%;
}
.ractive {
  background: url("../assets/img/ractive.png") center no-repeat;
  background-size: 100%;
}
.main-bar {
  // display: flex;
  // display: -webkit-flex;
  // height: 100%;
}
</style>