import router from './router'
import { getToken } from '@/utils/auth'

const whiteList = ['/login']

router.beforeEach(async(to, from, next) => {

  const hasToken = getToken()

  if(hasToken) {
    // has token
    if(to.path === '/login') {
      next({ path: '/' })
    } else {
      next()
    }
  } else {
    // has no token
    if(whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
    }
  }
})
