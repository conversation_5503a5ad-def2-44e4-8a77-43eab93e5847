import request from '@/utils/request'

/* 营收管理首页 */

// 本月营收额与售水量
// export function getCurrentMonthInfo() {
//   return request({
//     url: '/revenue/currMonthSell',
//     method: 'get'
//   })
// }
export function getCurrentMonthInfo() {
  return request({
    url: '/revenue/monthSell',
    method: 'post'
  })
}

// 年营收额与售水量
export function getYearInfo() {
  return request({
    url: '/revenue/yearSell',
    method: 'get'
  })
}

// 上方统计数据
export function getTopInfo() {
  return request({
    url: '/revenue/top',
    method: 'get'
  })
}

// 营收数据日历
export function getYearDetail(params) {
  return request({
    url: '/revenue/yearAmount',
    method: 'get',
    params
  })
}

// 水表情况
export function getMeterTypeAndCount() {
  return request({
    url: '/revenue/meterType',
    method: 'get'
  })
}

// 排名
export function getSellRank() {
  return request({
    url: '/revenue/monthSellRank',
    method: 'post'
  })
}
