import request from '@/utils/request'

const REVENUE_API = '/revenue-api'

// 年度月度营收费用统计
export function getYearAndMonthRevenueData() {
  return request({
    baseURL: REVENUE_API,
    url: '/statistics/year-revenue-data',
    method: 'get'
  })
}

// 当月营收数据
export function getCurrentMonthRevenueData(params) {
  return request({
    baseURL: REVENUE_API,
    timeout: 20000,
    url: '/statistics/revenue-data',
    method: 'get',
    params
  })
}

// 2组排名数据
export function getTwoRankData(month) {
  return request({
    timeout: 20000,
    url: '/revenue/monthSellRank',
    method: 'get',
    params: { time: month }
  })
}