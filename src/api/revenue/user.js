import request from '@/utils/request'

const REVENUE_API = '/revenue-api'

// 统计展示
export function getTotalInfo(params) {
  return request({
    baseURL: REVENUE_API,
    timeout: 10000,
    url: '/bush-user/statistics',
    method: 'get',
    params
  })
}

// 用户中心账单分页
export function getUserBill(data) {
  return request({
    baseURL: REVENUE_API,
    timeout: 20000,
    url: '/bush-user/page',
    method: 'post',
    data
  })
}

// 用户中心详情
export function getUserDetail(params) {
  return request({
    baseURL: REVENUE_API,
    timeout: 10000,
    url: '/bush-user/user-detail',
    method: 'get',
    params
  })
}

// 历史账单
export function getUserHistoryBill(data) {
  return request({
    baseURL: REVENUE_API,
    url: '/bush-user/bill-history',
    method: 'post',
    data
  })
}

// 用水分析
export function getUserDay(data) {
  return request({
    baseURL: REVENUE_API,
    timeout: 10000,
    url: '/bush-user/day-use',
    method: 'post',
    data
  })
}

// 营业区域-镇
export function getBusinessArea() {
  return request({
    baseURL: REVENUE_API,
    timeout: 10000,
    url: '/bush-user/businessArea',
    method: 'get'
  })
}
// 小区-村
export function getResidentialQuartersById(id) {
  return request({
    baseURL: REVENUE_API,
    timeout: 10000,
    url: '/bush-user/bushResidentialQuarters?id=' + id,
    method: 'get'
  })
}