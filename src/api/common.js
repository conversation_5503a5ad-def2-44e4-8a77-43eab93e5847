import request from '@/utils/request'

// 枚举
export const getEnum = () => {
  return request({
    url: '/common/enums',
    method: 'get'
  })
}

// 获取站点列表
// name参数不建议使用
export function getStationList(type = 'ALL') {
  return request({
    url: `/common/stationMenu?type=${type}`,
    method: 'get'
  })
}

// 获取镇村关系
export function getTownList() {
  return request({
    url: '/town/tree',
    method: 'get'
  })
}

// 获取 镇->村->设施类型->设施
export function getFacilityTree() {
  return request({
    url: '/town/townAndVillage',
    method: 'get'
  })
}

// 获取天气
export function getWeatherInfo() {
  return request({
    url: '/weather-info/qRealTime',
    method: 'get'
  })
}

// 通用上传接口
export function uploadFile(data) {
  return request({
    url: '/common/uploadAnnex.htm',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 0,
    data
  })
}

// 通用下载接口
export function commonFileDownload(url) {
  return request({
    url: '/common/downLoad.htm',
    method: 'get',
    params: { url },
    responseType: 'blob'
  })
}
