import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/userManager/accountInfo/login',
    method: 'post',
    data
  })
}

export function getXKey() {
  return request({
    url: '/userManager/accountInfo/public-key',
    method: 'get'
  })
}

export function loginNew(data) {
  return request({
    url: '/userManager/accountInfo/ruoyiLogin',
    method: 'post',
    data
  })
}

export function getInfo(token) {
  return request({
    url: '/userManager/accountInfo/getAccountInfoByToken',
    method: 'get',
    params: { token }
  })
}

export function logout() {
  return request({
    url: '/userManager/accountInfo/loginOut',
    method: 'post'
  })
}

export function logoutNew() {
  return request({
    url: '/userManager/accountInfo/ruoyiLogout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/api/code.htm',
    method: 'post'
  })
}

export function sendMailCode(account,type,codeId,code) {
  return request({
    url: `/api/code/${account}/${type}/${codeId}/${code}`,
    method: 'post'
  })
}

export function forgotPassword(data) {
  return request({
    url: '/userManager/accountInfo/forgotPassword',
    method: 'post',
    data
  })
}

/*
  首页
*/

// 查询
export function getHomepageInfo() {
  return request({
    url: '/firstPage/query',
    method: 'post'
  })
}
