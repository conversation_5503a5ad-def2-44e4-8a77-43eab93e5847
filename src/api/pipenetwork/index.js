import request from '@/utils/request'

/* 管网分析 */

// 管网数据分页
export function getPipeNetworkPage(data) {
  return request({
    url: '/pipe-network/page',
    method: 'post',
    data
  })
}
// 新建管网
export function addPipeNetwork(data) {
  return request({
    url: '/pipe-network/',
    method: 'post',
    data
  })
}
// 横断面分析（交点）
export function getPipeByHengDuanMian(data) {
  return request({
    url: '/pipe-network/intersectPipe',
    method: 'post',
    data
  })
}
// 纵断面分析
export function getLngSection(data) {
  return request({
    url: '/pipe-network/lngSection',
    method: 'post',
    data
  })
}
// 管网分类统计
export function getPipeNetworkCategoryLength(townId) {
  return request({
    url: `/home-page/typeLenth?townId=${townId}`,
    method: 'get'
  })
}
// 管网查询
export function queryPipeNetwork(data) {
  return request({
    url: '/pipe-network/pageNew',
    method: 'post',
    data
  })
}
// 管径枚举
export function getPipeNetworkDiameterRangeEnum() {
  return request({
    url: '/pipe-network/diameterRangeEnum',
    method: 'get'
  })
}
// 管径枚举
export function getPipeNetworkPaveTimeRangeEnum() {
  return request({
    url: '/pipe-network/paveTimeRangeEnum',
    method: 'get'
  })
}
