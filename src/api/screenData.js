import request from '@/utils/request'

// 首页数据概览
export function getHomepageOverviewData() {
  return request({
    url: '/home-page/data-overview',
    method: 'get'
  })
}

// 超滤设备
export function getUFEquipment(data) {
  return request({
    url: '/litree-equipment/page',
    method: 'post',
    data
  })
}
// 超滤设备最新一条数据
export function getUFEquipmentLatestData(id) {
  return request({
    url: '/litree-data/page',
    method: 'post',
    data: {
      id,
      pageNo: 1,
      pageSize: 1,
      orderBy: 'dataTime'
    }
  })
}
export function getUFEquipmentLatestDataNew(id) {
  return request({
    url: `/water-plant-telemetry/factory/${id}/latest`,
    method: 'get'
  })
}

// 根据水厂id获取实时出口流量信息
export function getFactoryOutInfo(id) {
  return request({
    url: `/water-flow/factory/${id}`,
    method: 'get'
  })
}
// 水池最新数据
export function getFactoryLatestData(facilityId) {
  return request({
    url: '/water-level/getByFacilityId?facilityId=' + facilityId,
    method: 'get'
  })
}

// 供水数据
export function factorySupplyData() {
  return request({
    url: '/home-page/factorySupplyStatistic',
    method: 'get'
  })
}
export function factorySupplyDataNew() {
  return request({
    url: '/home-page/meter-data-statistic',
    method: 'get'
  })
}
// 供水数据按时间 1234 时天月年
export function factorySupplyDataWithTime(type) {
  return request({
    url: '/home-page/supply-statistic/' + type,
    method: 'get'
  })
}
export function factorySupplyDataWithTimeNew(type) {
  return request({
    url: '/home-page/meter-statistic/' + type,
    method: 'get'
  })
}
// 获取今天和昨日每小时供水量
export function hourSupplyData() {
  return request({
    url: '/home-page/hourSupply',
    method: 'get'
  })
}

// 站点数量统计
export function getStationTypeCount() {
  return request({
    url: '/home-page/stationTypeCount',
    method: 'get'
  })
}