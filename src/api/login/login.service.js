/*
 * @Author: your name
 * @Date: 2020-10-13 13:26:54
 * @LastEditTime: 2020-10-13 13:29:01
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \vue-demo\src\utils\service\login\login.service.js
 */
import request from '@/utils/https/request'

const LOGIN_SERVICE = {
    // 登录
    login(data) {
        return request({
            method: 'post',
            url: '/ius/user/login',
            data
        })
    },
    // 登出
    logout() {
        return request({
            method: 'post',
            url: '/ius/user/logout',
            showErrorTip: false
        })
    },
}

export default LOGIN_SERVICE;