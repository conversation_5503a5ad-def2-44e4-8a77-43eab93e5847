import request from '@/utils/request'

/* 水质报告 */
// 分页查询
export function getReportPageList(data) {
  return request({
    url: '/report/page',
    method: 'post',
    data
  })
}

// 删除
export function deleteReportById(id) {
  return request({
    url: `/report/${id}`,
    method: 'delete'
  })
}

// 手动生成报告
export function generateReport(data) {
  return request({
    url: '/report/generateReport',
    method: 'post',
    data
  })
}

// 获取报告模板类型
export function getReportTemplateType() {
  return request({
    url: '/report/getTemplateType',
    method: 'post'
  })
}

// 人工上传报告
export function uploadReport(data) {
  return request({
    url: '/report/uploadReport',
    method: 'post',
    data
  })
}

// 预览报告
export function previewReport(id) {
  return request({
    url: `/report/preview/${id}`,
    method: 'get',
    responseType: 'blob',
    timeout: 0,
  })
}
