import request from '@/utils/request'
/* 水质历史数据 */

// 分页
export function getWqProgramme(data) {
  return request({
    url: '/emergency-plan/page',
    method: 'post',
    data
  })
}

// 删除
export function deleteWqProgramme(id) {
  return request({
    url: `/emergency-plan/${id}`,
    method: 'delete'
  })
}

// 插入
export function insertWqProgramme(data) {
  return request({
    url: '/emergency-plan/',
    method: 'post',
    data
  })
}

// 更新
export function updateWqProgramme(data) {
  return request({
    url: '/emergency-plan/',
    method: 'put',
    data
  })
}

// 上传单个附件
export function uploadFile(data) {
  return request({
    url: '/common/uploadAnnex.htm',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 0,
    data
  })
}


// 预览
export function previewFile(id) {
  return request({
    url: `/emergency-plan/preview?id=${id}`,
    method: 'get',
    responseType: 'blob',
    timeout: 0,
  })
}
