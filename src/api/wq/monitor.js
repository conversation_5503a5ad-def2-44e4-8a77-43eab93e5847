import request from '@/utils/request'

/* 水质数据 */

// 历史数据分页
export function getWqHistoryDataPage(data) {
  return request({
    url: '/water-quality-history/page',
    method: 'post',
    data
  })
}

// 历史数据删除
export function deleteWqDataById(id) {
  return request({
    url: `/water-quality-history/${id}`,
    method: 'delete'
  })
}

// 历史数据插入
export function insertWqData(data) {
  return request({
    url: '/water-quality-history',
    method: 'post',
    data
  })
}

// 历史数据更新
export function updateWqData(data) {
  return request({
    url: '/water-quality-history/',
    method: 'put',
    data
  })
}

// 粗差
export function setWqDataError(data) {
  return request({
    url: '/water-quality-history/',
    method: 'put',
    data
  })
}

// 实时数据分页
export function getWqRealtimeDataPage(data) {
  return request({
    url: '/water-quality-history/singlePage',
    method: 'post',
    data
  })
}

// 下载水质数据导入模板
export function getWqImportTemplate() {
  return request({
    url: '/water-quality-history/waterQualityTemplateDownload',
    method: 'post',
    responseType: 'blob'
  })
}

// 导入水质数据
export function exportWqData(data) {
  return request({
    url: '/water-quality-history/batchAddWaterQualityHistory',
    method: 'post',
    data
  })
}
