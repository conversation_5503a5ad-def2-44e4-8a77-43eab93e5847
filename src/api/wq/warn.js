import request from '@/utils/request'
/* 水质预警 */

// 按状态统计
export function getWqWarnCountWithStatus(params) {
  return request({
    url: '/warning-record/status-count',
    method: 'get',
    params
  })
}

// 按来源统计
export function getWqWarnCountWithSource(params) {
  return request({
    url: '/warning-record/station-type-count',
    method: 'get',
    params
  })
}

export function getWqWarnCountWithType(params) {
  return request({
    url: '/warning-record/count',
    method: 'get',
    params
  })
}

// 分页
export function getWqWarn(data) {
  return request({
    url: '/warning-record/page',
    method: 'post',
    data
  })
}

// 删除
export function deleteWqWarn(id) {
  return request({
    url: `/warning-record/${id}`,
    method: 'delete'
  })
}

// 插入
export function insertWqWarn(data) {
  return request({
    url: '/warning-record/',
    method: 'post',
    data
  })
}

// 更新
export function updateWqWarn(data) {
  return request({
    url: '/warning-record/',
    method: 'put',
    data
  })
}

/* 水质阈值 */
// 分页
export function getWqConfig(data) {
  return request({
    url: '/water-quality-config/page',
    method: 'post',
    data
  })
}
// 插入
export function insertWqConfig(data) {
  return request({
    url: '/water-quality-config/',
    method: 'post',
    data
  })
}
// 详情
export function getWqConfigById(id) {
  return request({
    url: `/water-quality-config/${id}`,
    method: 'get',
  })
}
// 更新
export function updateWqConfig(data) {
  return request({
    url: '/water-quality-config/',
    method: 'put',
    data
  })
}
// 删除
export function deleteWqConfig(id) {
  return request({
    url: `/water-quality-config/${id}`,
    method: 'delete',
  })
}
// 修改配置状态
export function setWqConfigStatus(id, status) {
  return request({
    url: '/water-quality-config/status',
    method: 'get',
    params: {
      id,
      status
    }
  })
}

// 查询趋势
export function warningTrend(data) {
  return request({
    url: '/warning-record/trend',
    method: 'post',
    data
  })
}
