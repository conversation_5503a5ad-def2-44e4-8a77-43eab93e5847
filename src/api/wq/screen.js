import request from '@/utils/request'
/* 水质监测首页 */

// 实时水质
export function getWQLatest() {
  return request({
    url: '/water-quality/latest',
    method: 'get'
  })
}

// 在线设备统计
export function getOnlineInsm() {
  return request({
    url: '/home-page/onlineInsm',
    method: 'get'
  })
}

// 监测分布
export function getWqCount() {
  return request({
    url: '/waterQualityHead/waterQualityInsmStatistics',
    method: 'get'
  })
}

// 水质达标情况
export function getWqPassData() {
  return request({
    url: '/waterQualityHead/stationPassMonth',
    method: 'get'
  })
}

// 今日健康达标率
export function getWqHealthData() {
  return request({
    url: '/waterQualityHead/stationPassDay',
    method: 'get'
  })
}

// 水质达标情况
export function getWqHealthDataNew(params) {
  return request({
    url: '/waterQualityHead/stationPassDayNew',
    method: 'get',
    params
  })
}