import request from '@/utils/request'

// 蓄水池最新数据
export function getPondLatestData() {
  return request({
    url: '/water-level/latest',
    method: 'get'
  })
}
// 原水主题 
export function getHydrologyLatestData() {
  return request({
    url: '/hydrographic-station-data/lastData',
    method: 'get'
  })
}
export function getPumpLatestData() {
  return request({
    url: '/pump-station-data/lastData',
    method: 'get'
  })
}

// 水质主题合格率
export function getWqTopicRate() {
  return request({
    url: '/today-water-quality/qualified/all/rate',
    method: 'get'
  })
}

// 水厂主题出场水合格率
export function getWaterFactoryRate() {
  return request({
    url: '/today-water-quality/qualified/factory/rate',
    method: 'get'
  })
}
// 水厂主题制水量统计
export function getWaterFactoryStatistics(date) {
  return request({
    url: `/water-plant-telemetry/statistic/${date}`,
    method: 'get'
  })
}
