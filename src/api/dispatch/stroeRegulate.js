import request from '@/utils/request'

// 分页
export function getPageList(data) {
  return request({
    url: '/facility-rest-run-day/page',
    method: 'post',
    data
  })
}


/* 调度方案 */
// 分页
export function getDispatchPlanPageList(data) {
  return request({
    url: '/dispatch-plan/page',
    method: 'post',
    data
  })
}

export function addDispatchPlan(data) {
  return request({
    url: '/dispatch-plan/',
    method: 'post',
    data
  })
}

export function updateDispatchPlan(data) {
  return request({
    url: '/dispatch-plan/',
    method: 'put',
    data
  })
}

export function deleteDispatchPlanById(id) {
  return request({
    url: `/dispatch-plan/${id}`,
    method: 'delete'
  })
}

export function getDispatchPlanDetail(id) {
  return request({
    url: `/dispatch-plan/${id}`,
    method: 'post'
  })
}
// 方案确认
export function caseConfirm(id) {
  return request({
    url: `/dispatch-plan/confirm?id=${id}`,
    method: 'post'
  })
}
// 方案转经验 planld
export function caseToExperience(data) {
  return request({
    url: '/dispatch-experience/planToExperience',
    method: 'post',
    data
  })
}
// 获取tag
export function getTagList() {
  return request({
    url: '/dispatch-experience/tags',
    method: 'get'
  })
}

/* 调度经验 */
// 分页
export function getDispatchExperiencePageList(data) {
  return request({
    url: '/dispatch-experience/page',
    method: 'post',
    data
  })
}
// 新增
export function addDispatchExperience(data) {
  return request({
    url: '/dispatch-experience/',
    method: 'post',
    data
  })
}
// 更新
export function updateDispatchExperience(data) {
  return request({
    url: '/dispatch-experience/',
    method: 'put',
    data
  })
}
// 更新
export function deleteDispatchExperienceById(id) {
  return request({
    url: `/dispatch-experience/${id}`,
    method: 'delete'
  })
}
// 详情
export function getDispatchExperienceDetail(id) {
  return request({
    url: `/dispatch-experience/${id}`,
    method: 'post'
  })
}
// 经验转方案
export function dispatchExperienceToCase(data) {
  return request({
    url: '/dispatch-experience/experienceToPlan',
    method: 'post',
    data
  })
}
