import request from '@/utils/request'

export function getMonthReport(data) {
  return request({
    url: '/safe-report/page',
    method: 'post',
    data
  })
}

// 安全报告（风险类型数量统计）
export function getRiskTypeCount(data) {
  return request({
    url: '/risk/statusStatistics',
    method: 'post',
    data
  })
}

// 报警统计排名
export function getWarnRank(params) {
  return request({
    url: '/risk/facilityRiskStatistics',
    method: 'get',
    params
  })
}

// 报警分析
export function getWarnAnalyse(data) {
  return request({
    url: '/risk/riskAnalyse',
    method: 'post',
    data
  })
}
