import request from '@/utils/request'

// 获取列表
export function getLineList(data) {
  return request({
    url: '/water-supply-area/list',
    method: 'post',
    data
  })
}

// 获取站点 水位 流量 压力
export function getStationAllData(data) {
  return request({
    url: '/allMonitorData/getStationAllData',
    method: 'post',
    data
  })
}

// 获取水厂线所有站点监测数据 pageNo -1 查最新 1 过去10分钟 2 过去20分钟...
export function getStationLastData(data) {
  return request({
    url: '/allMonitorData/getLastDataOrderByStation',
    method: 'post',
    data
  })
}

// 获取水厂瞬时流量（来自蓄水池）
export function getPointSpeedForMap() {
  return request({
    url: '/allMonitorData/factoryMonitor',
    method: 'post'
  })
}

// 获取蓄水池
export function getPondPointDataForMap() {
  return request({
    url: '/allMonitorData/getAllMainStationData',
    method: 'post'
  })
}

// 根据线路获取监测点位数据
export function getLastDataOrderByStation(supplyAreaId) {
  return request({
    url: '/allMonitorData/getLastDataOrderByStation/' + supplyAreaId,
    method: 'post'
  })
}

// 水位数据分页
export function getWaterLevelPageList(data) {
  return request({
    url: '/water-level/page',
    method: 'post',
    data
  })
}

// 获取水位数据绘制图表
export function getWaterLevelForChart(data) {
  return request({
    url: '/water-level/getWaterLevelPictureResp',
    method: 'post',
    data
  })
}

// 流量数据分页
export function getFlowRatePageList(data) {
  return request({
    url: '/water-flow/page',
    method: 'post',
    data
  })
}

// 获取流量数据绘制图表
export function getFlowRateForChart(data) {
  return request({
    url: '/water-flow/picture',
    method: 'post',
    data
  })
}

// 根据镇子获取下面的村子
export function getVillagesByTown(townName) {
  return request({
    url: `/town/${townName}/villages`,
    method: 'get'
  })
}

// 泵站数据分页
export function getPumpDataPageList(data) {
  return request({
    url: '/pump-station-data/page',
    method: 'post',
    data
  })
}
