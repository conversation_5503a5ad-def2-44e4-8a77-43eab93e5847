import request from '@/utils/request'

/* 风险预测 */

/* 水质预测 */
// 分页
export function getWqPredictionPageList(data) {
  return request({
    url: '/water-quality-predictive/page',
    method: 'post',
    data
  })
}
// 水质预测曲线(实际值+预测值)
export function getWqPredictionPic(data) {
  return request({
    url: '/water-quality-predictive/pic',
    method: 'post',
    data
  })
}

/* 水量预测 */
// 分页
export function getFlowPredictionPageList(data) {
  return request({
    url: '/water-level-predictive/page',
    method: 'post',
    data
  })
}
// 水量预测曲线(实际值+预测值)
export function getFlowPredictionPic(data) {
  return request({
    url: '/water-level-predictive/pic',
    method: 'post',
    data
  })
}
