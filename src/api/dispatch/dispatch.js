import request from '@/utils/request'

// 调度指令分页
export function getDispatchCmdPageList(data) {
  return request({
    url: '/dispatch-instruction/page',
    method: 'post',
    data
  })
}
// 调度指令新增
export function addDispatchCmd(data) {
  return request({
    url: '/dispatch-instruction/',
    method: 'post',
    data
  })
}
// 调度指令详情
export function getDispatchCmdDetail(id) {
  return request({
    url: '/dispatch-instruction/' + id,
    method: 'post'
  })
}
// 调度指令修改
export function updateDispatchCmd(data) {
  return request({
    url: '/dispatch-instruction/',
    method: 'put',
    data
  })
}
// 调度指令删除
export function deleteDispatchCmdById(id) {
  return request({
    url: `/dispatch-instruction/${id}`,
    method: 'delete'
  })
}

// 我的待办
export function getDispatchCmdTodoPageList(data) {
  return request({
    url: '/dispatch-instruction/todo/page',
    method: 'post',
    data
  })
}
// 处理我的待办
export function handleDispatchCmdTodo(data) {
  return request({
    url: '/dispatch-instruction/handle/todo',
    method: 'post',
    data
  })
}
// 我的已办
export function getDispatchCmdDonePageList(data) {
  return request({
    url: '/dispatch-instruction/done/page',
    method: 'post',
    data
  })
}

// 调度预案分页
export function getDispatchPlanPageList(data) {
  return request({
    url: '/dispatch-pre-plan/page',
    method: 'post',
    data
  })
}
// 调度预案新增
export function addDispatchPlan(data) {
  return request({
    url: '/dispatch-pre-plan/',
    method: 'post',
    data
  })
}
// 调度预案详情
export function getDispatchPlanDetail(id) {
  return request({
    url: '/dispatch-pre-plan/' + id,
    method: 'post'
  })
}
// 修改预案
export function updateDispatchPlan(data) {
  return request({
    url: '/dispatch-pre-plan/',
    method: 'put',
    data
  })
}
// 删除预案
export function deleteDispatchPlanById(id) {
  return request({
    url: `/dispatch-pre-plan/${id}`,
    method: 'delete'
  })
}