import request from '@/utils/request'

/* 预警
  - 1 水质预警
  - 2 水位预警
  - 3 流量预警
  - 4 设备预警
*/
export function getWarnListByType(data) {
  return request({
    url: '/warning-record/page',
    method: 'post',
    data
  })
}

/* 天气预报 */
export function getWeatherDataPageList(data) {
  return request(
    {
      url: '/weather-info/page',
      method: 'post',
      data
    }
  )
}

export function getWeatherDataBy(data) {
  return request(
    {
      url: '/weather-info/pic',
      method: 'post',
      data
    }
  )
}

/* 风险预测 */

// 分页
export function getRiskProfilePageList(data){
  return request(
    {
      url:'/risk/page',
      method:'post',
      data
    }
  )
}

//详情
export function getRiskProfileDetail(id) {
  return request({
    url: `/risk/${id}`,
    method: 'post'
  })
}

// 新增
export function createRiskProfile(data) {
  return request({
    url: '/risk/',
    method: 'post',
    data
  })
}

//更新
export function updateRiskProfile(data) {
  return request({
    url: '/risk/',
    method: 'put',
    data
  })
}

// 启动应急预案
export function startRiskPlansById(id, plans = []) {
  return request({
    url: `/risk/${id}/plans`,
    method: 'put',
    data: plans
  })
}

//获取风险类型
export function getRiskProfileType(data){
  return request({
    url:'/risk/types',
    method:'get',
    data
  })
}

//获取状态
export function getRiskProfilesStatus(data){
  return request({
    url:'/risk/status',
    method:'get',
    data
  })
}

//删除
export function delRiskProfilesStatus(id){
  return request({
    url:`/risk/${id}`,
    method:'delete',
  })
}

// 用户用水报警
export function getUserMeterWarnPageList(data) {
  return request({
    url: '/water-meter-alarm/page',
    method: 'post',
    data
  })
}
// 删除用户用水报警
export function deleteUserMeterWarn(id) {
  return request({
    url: `/water-meter-alarm/${id}`,
    method: 'delete',
  })
}

// 获取站点列表
export function getStationList(type = 'ALL') {
  return request({
    url: `/common/stationMenu?type=${type}`,
    method: 'get'
  })
}

// 通用上传口
export function uploadFile(data) {
  return request({
    url: '/common/uploadAnnex.htm',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 0,
    data
  })
}
