import request from '@/utils/request'


// 工程列表，创建图层
export function getProjectList() {
  return request({
    url: '/project/layer',
    method: 'get'
  })
}

export function getHomeWQ() {
  return request({
    url: '/home-page/supplyAreaQuality',
    method: 'get'
  })
}

/* 首页图层中心使用 */
// 获取 flow pressure level 数据
export function getAllMonitorDatadetail(params,id,sourceType) {
  return request({
    url: '/allMonitorData/detail/' + id + "/" + sourceType,
    method: 'get',
    params
  })
}
// 右侧菜单
export function layermenu() {
  return request({
    url: '/layer/menu',
    method: 'get',
  })
}
// 获取 水表 meter 最新一条数据
export function getMeterLatestDataById(id) {
  return request({
    url: '/meter-origin-data/latestData',
    method: 'get',
    params: { id }
  })
}
export function getMeterLatestDataByIdNew(id) {
  return request({
    url: '/water-meter-data/latest/' + id,
    method: 'get'
  })
}
// 获取一体化水文站最新一条数据
export function getHydroStationLatestDataById(id) {
  return request({
    url: '/hydrographic-station-data/latest/' + id,
    method: 'get'
  })
}
// 获取水质弹窗数据
export function getPointWqData(id, sourceType) {
  return request({
    url: `/allMonitorData/quality-detail/${id}/${sourceType}`,
    method: 'get'
  })
}
// 水质站点历史数据图表数据
export function getPointWqHistoryData(data) {
  return request({
    url: '/water-quality-history/chart',
    method: 'post',
    data
  })
}
// 设施详情
export function getProjectDetail(type, id) {
  return request({
    url: `/${type}/${id}`,
    method: 'get'
  })
}

// 工程详情
export function getProjectDetailForOther(id) {
  return request({
    url: `/project/${id}`,
    method: 'post'
  })
}

// 获取点位监测信息 数据比较多
export function getAllMonitorData(params) {
  return request({
    url: '/allMonitorData/getAllMonitorData',
    method: 'post',
    params
  })
}
// 获取点位基本信息
export function getPointBaseInfo(params) {
  return request({
    url: '/allMonitorData/getBasicInfo',
    method: 'post',
    params
  })
}
// 获取点位监测信息 facilityId stationType 单纯只有监测信息
export function getPointMonitorData(params) {
  return request({
    url: '/allMonitorData/monitor',
    method: 'post',
    params
  })
}
export function getwaterfactory(id) {
  return request({
    url: '/water-factory/' + id,
    method: 'get',
  })
}
export function getstoragepond(id) {
  return request({
    url: '/water-storage-pond/' + id,
    method: 'get',
  })
}
export function getinsmTypes() {
  return request({
    url: '/insm/insmTypes',
    method: 'get',
  })
}

export function getpoint(data) {
  return request({
    url: '/insm/point',
    method: 'post',
    data
  })
}

// 查询设施监测历史数据
export function getHistoryData(data) {
  return request({
    url: '/water-level/allDataPicture',
    method: 'post',
    data
  })
}

// 图层中心-历史数据报表
export function getReport(data) {
  return request({
    url: '/allMonitorData/statement',
    method: 'post',
    data
  })
}

// 获取视频点位列表（暂时用于首页查看更多）
export function getMonitorVideoList(data) {
  return request({
    url: '/video-monitor/page',
    method: 'post',
    data
  })
}

// 视频点位树状列表
export function getMonitorVideoTree() {
  return request({
    url: '/video-monitor/tree',
    method: 'get'
  })
}

// 供水调度类型统计
export function getSupplyTypeCount(params) {
  return request({
    url: '/dispatch-instruction/count',
    method: 'get',
    params
  })
}
// 水质合格率
export function getWaterQualityRate() {
  return request({
    url: '/today-water-quality/qualified/rate',
    method: 'get'
  })
}
