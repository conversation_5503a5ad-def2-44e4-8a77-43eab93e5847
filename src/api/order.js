import request from '@/utils/request'

/**
 * 根据预警记录 id 生成工单
 * @param {Object} data
 * @param {number} data.recordId 预警记录id
 * @param {string} data.name 工单名称
 * @returns {Promise<AxiosPromise>}
 */
export function createOrderByWarnId(data) {
  return request({
    url: '/work-ticket/autoCreate',
    method: 'post',
    data
  })
}

/**
 * 根据调度方案 id 生成工单
 * @param {Object} data
 * @param {number} data.recordId 调度方案id
 * @param {string} data.name 工单名称
 * @returns {Promise<AxiosPromise>}
 */
export function createOrderByCaseId(data) {
  return request({
    url: '/work-ticket/createDispatchTicket',
    method: 'post',
    data
  })
}

// 获取工单列表
export function getOrderPageList(data) {
  return request({
    url: '/work-ticket/page',
    method: 'post',
    data
  })
}
