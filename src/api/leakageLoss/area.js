import request from '@/utils/request'

/* 分区相关接口 */


// 考核表关联-绑定
export function bindFlowInsm(data) {
  return request({
    url: '/area-insm/bind',
    method: 'put',
    data
  })
}
// 考核表关联-解绑
export function unBindFlowInsm(data) {
  return request({
    url: '/area-insm/unbind',
    method: 'put',
    data
  })
}
// 设备列表-已绑定
export function getAreaInsmBindList(areaId) {
  return request({
    url: `/area-info/${areaId}/flowmeters`,
    method: 'get'
  })
}
// 设备列表-未绑定
export function getInsmPage(data) {
  return request({
    url: '/insm/page',
    method: 'post',
    data
  })
}
// 水表绑定
export function bindWaterMeter(data) {
  return request({
    url: '/area-user-water/bind',
    method: 'put',
    data
  })
}
// 水表解绑
export function unBindWaterMeter(data) {
  return request({
    url: '/area-user-water/unbind',
    method: 'put',
    data
  })
}

// 分区分页
export function getAreaPageList(data) {
  return request({
    url: '/area-info/page',
    method: 'post',
    data
  })
}

// 获取分区详情
export function getAreaDetailById(id) {
  return request({
    url: '/area-info/' + id,
    method: 'get'
  })
}

// 新增分区
export function addArea(data) {
  return request({
    url: '/area-info/',
    method: 'post',
    data
  })
}

// 编辑分区
export function editArea(data) {
  return request({
    url: '/area-info/',
    method: 'put',
    data
  })
}

// 获取某级区域列表
export function getAreaListByGrade(grade) {
  return request({
    url: '/area-info/optional',
    method: 'get',
    params: {
      needGrade: grade
    }
  })
}

// 获取区域水表
export function getAreaWaterMeters(data) {
  return request({
    url: '/water-meter/page',
    method: 'post',
    data
  })
}

// 关联水表
export function linkMeters(data) {
  return request({
    url: '/water-meter',
    method: 'put',
    data
  })
}

// 夜间最小流量设置 报警
export function getMinFlowConfigByAreaId(areaId) {
  return request({
    url: '/min-night-flow-config/getByAreaId',
    method: 'post',
    params: {
      areaId
    }
  })
}

export function addMinFlowConfig(data) {
  return request({
    url: '/min-night-flow-config/',
    method: 'post',
    data
  })
}

export function updateMinFlowConfig(data) {
  return request({
    url: '/min-night-flow-config/',
    method: 'put',
    data
  })
}

// 新增区域漏损参数设置
export function addAreaConfig(data) {
  return request({
    url: '/area-lost-config/',
    method: 'post',
    data
  })
}

// 修改区域漏损参数设置 
export function updateAreaConfig(data) {
  return request({
    url: '/area-lost-config/',
    method: 'put',
    data
  })
}

// 获取区域漏损参数设置
export function getAreaConfig(areaId) {
  return request({
    url: '/area-lost-config/getByAreaId',
    method: 'get',
    params: { areaId }
  })
}