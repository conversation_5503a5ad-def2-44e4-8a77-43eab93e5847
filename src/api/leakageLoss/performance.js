import request from '@/utils/request'

/* 绩效考核 */

/* 考核标准 */
// 考核标准列表
export function getStandardList(data) {
  return request({
    url: '/assessment-standard/list',
    method: 'post',
    data
  })
}
// 考核标准添加
export function addStandard(data) {
  return request({
    url: '/assessment-standard/batchInsert',
    method: 'post',
    data
  })
}
// 上传考核标准
export function uploadStandard(params, data) {
  return request({
    url: '/assessment-standard/upload',
    method: 'post',
    params,
    data
  })
}
// 考核标准更新
export function updateStandard(data) {
  return request({
    url: '/assessment-standard/batchUpdate',
    method: 'post',
    data
  })
}
// 删除考核标准
export function deleteStandardByName(tableName) {
  return request({
    url: '/assessment-standard/deleteAssessmentTable/' + tableName,
    method: 'delete'
  })
}
// 获取考核标准详情
export function getStandardByName(tableName) {
  return request({
    url: '/assessment-standard/tableDetail/' + tableName,
    method: 'post'
  })
}

/* 考核评分记录 */
// 获取评分记录列表
export function getScoreList(data) {
  return request({
    url: '/assessment-total-score/page',
    method: 'post',
    data
  })
}
// 新增
export function addScore(data) {
  return request({
    url: '/assessment-total-score/',
    method: 'post',
    data
  })
}
// 更新
export function updateScore(data) {
  return request({
    url: '/assessment-total-score/',
    method: 'put',
    data
  })
}
// 详情
export function getScoreDetailById(id) {
  return request({
    url: '/assessment-total-score/' + id,
    method: 'post'
  })
}
// 删除
export function deleteScoreDetailById(id) {
  return request({
    url: '/assessment-total-score/' + id,
    method: 'delete'
  })
}
