import request from '@/utils/request'

// 区域最新漏损值
export function getAreaLastData() {
  return request({
    url: '/area-flow-info/lastAreaFlowData',
    method: 'post'
  })
}

// 年产销差排名
export function getYearNRW() {
  return request({
    url: '/leak-analysis-report/year-nrw',
    method: 'get'
  })
}

// 月产销差变化
export function getMonthNRW() {
  return request({
    url: '/leak-analysis-report/month-nrw',
    method: 'get'
  })
}

// 按数值统计报警条数
export function getLeakageLossWarnCount() {
  return request({
    url: '/leak-analysis-report/warn-count-value',
    method: 'get'
  })
}

// 按漏损预警级别统计条数
export function getLeakageLossWarnCountForLevel() {
  return request({
    url: '/leak-analysis-report/warn-count-level',
    method: 'get'
  })
}