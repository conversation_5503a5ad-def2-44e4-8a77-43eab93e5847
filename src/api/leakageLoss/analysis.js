import request from '@/utils/request'

// 全局漏损统计
export function getGlobalLost(data) {
  return request({
    url: '/area-lost-record/total-lost',
    method: 'post',
    data
  })
}

/* 专题分析 */
// 峰谷分析
export function getFlowMorePage(data) {
  return request({
    url: '/area-flow-daily-feature/page',
    method: 'post',
    data
  })
}
export function getLostPage(data) {
  return request({
    url: '/area-lost-record/page',
    method: 'post',
    data
  })
}
// 夜间最小流量
export function getFlowMinPage(data) {
  return request({
    url: '/area-flow-detail/page',
    method: 'post',
    data
  })
}
// 夜间最小流量曲线
export function getFlowMinChart(data) {
  return request({
    url: '/area-flow-detail/chart',
    method: 'post',
    data
  })
}

/* 漏损分析 */

// 区域菜单
export function getAreaTree() {
  return request({
    url: '/area-info/menu',
    method: 'get'
  })
}
// 区域信息
export function getAreaInfo(id) {
  return request({
    url: '/area-loss-basic-info/' + id,
    method: 'get'
  })
}
// 漏损报告记录
export function getReportPage(data) {
  return request({
    url: '/leak-analysis-report/page',
    method: 'post',
    data
  })
}
// 时供水量
export function getAreaHourWaterSupply(data) {
  return request({
    url: '/area-flow-info/chart/dayWaterSupply',
    method: 'post',
    data
  })
}
// 日供水量
export function getAreaDayWaterSupply(data) {
  return request({
    url: '/area-flow-info/chart/monthWaterSupply',
    method: 'post',
    data
  })
}
// 水平衡记录
export function getBalancePage(data) {
  return request({
    url: '/water-balance-statistics/page',
    method: 'post',
    data
  })
}

// 区域夜间最小流量分页
export function getAreaNightMinFlowPageList(data) {
  return request({
    url: '/min-night-flow/page',
    method: 'post',
    data
  })
}
// 区域夜间最小流量 折线图
export function getAreaNightMinFlowForChart(data) {
  return request({
    url: '/min-night-flow/chart',
    method: 'post',
    data
  })
}
// 获取区域设备
export function getAreaInsm(areaId) {
  return request({
    url: `/area-info/queryPipeNetwork?areaId=${areaId}`,
    method: 'get'
  })
}
// 获取区域流量+压力
export function getAreaInsmValue(data) {
  return request({
    url: '/area-info/chart',
    method: 'post',
    data
  })
}

// 获取区域压力数据，按设备分组
export function getAreaPressureList(data) {
  return request({
    url: '/water-pressure/picture',
    method: 'post',
    data
  })
}
// 获取区域净流量和最小流量
export function getAreaFlowList(data) {
  return request({
    url: '/area-flow-info/chat/nf-mnf',
    method: 'post',
    data
  })
}

/* 分区管理DMA管理 */
// 供水量
export function getWaterSupply(data) {
  return request({
    url: '/area-flow-info/chart/waterSupplyTrendMonth',
    method: 'post',
    data
  })
}
// 售水量
export function getWaterSell(data) {
  return request({
    url: '/area-flow-info/chart/waterSellTrendMonth',
    method: 'post',
    data
  })
}
// 每日供水趋势
export function getWaterSupplyDay(data) {
  return request({
    url: '/area-flow-info/chart/waterSupplyTrendDay',
    method: 'post',
    data
  })
}
