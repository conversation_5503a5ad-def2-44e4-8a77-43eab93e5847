<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '100%'
        },
        pieData: {
            type: Array,
            default: () => []
        },
        xAxisData: {
            type: Array,
            default: () => []
        },
        allLength:{
            type:Number,
            default: 0,
        },
        legendData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            chart: null,
        }
    },
    watch: {
        pieData: {
            deep: true,
            handler(val) {
                this.initChart()
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$el)
            this.chart.setOption({
                tooltip: {
                    trigger: 'item'
                },
                title: {
                    text: (this.allLength / 1000).toFixed(2),
                    top: "39%",
                    subtext: "总数(km)",
                    x: "25%",
                    y: "50%",
                    textStyle: {
                        fontSize: 16,
                        color: "#FFF"
                    },
                    subtextStyle: {
                        color: "#D8F0FF",
                        fontSize: 14,
                    },
                    textAlign:'center'
                },
                grid: {
                    top: 5,
                    left: 40,
                    right: 30,
                    bottom: 20
                },
                legend: {
                    orient:'vertical',
                    left:180,
                    top:15,
                    textStyle:{
                        padding:[8, 0, 0, 0],
                        color:"#fff"
                    },
                },
                series: [
                    //主要显示层
                    {
                        color: ['#05CFF7', '#177BEE', '#BEE5FB','#00F872','#EDE497'],
                        type: 'pie',
                        label:{
                            show:false
                        },
                        radius: ['55%', '69%'],
                        center: ['25%', '50%'],
                        data: this.pieData
                    },
                    //边框设置
                    {
                        radius: ["41%", "41.3%"],
                        center: ["25%", "50%"],
                        type: "pie",
                        itemStyle: {
                            normal:{color:'#295bce'},
                            emphasis: { color: '#295bce' },
                        },
                        label:{
                            show:false
                        },
                        data: [
                            { value: 0.1 }
                        ]
                    }
                ]


            })
        }
    }
}

</script>