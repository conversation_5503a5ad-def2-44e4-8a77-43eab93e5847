<template>
  <div class="businessLayout">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="pipenetwork" />
    <div class="content">
      <!-- 地图 -->
      <LineMap class="map" ref="map" :checkedList="checkedList" :currentBusiness="currentBusiness" @drawPolygonEnd="drawPolygonEnd" @xEnd="xEnd" />
      <!-- top -->
      <div class="top">
        <div class="cursorPointer" style="width: 161px">
          <img
            src="@/assets/img/home/<USER>"
            alt=""
            style="height: 38px"
            @click="handleBack"
          />
          <!-- <span style="margin-left: 10px;">返回管网分析首页</span> -->
        </div>
        <div class="bList">
          <div
            class="item cursorPointer"
            :class="{ active: currentBusiness === item.id }"
            v-for="item in bList"
            :key="item.id"
            @click="handleChangeBusiness(item.id)"
          >
            <img :src="item.icon" alt="" style="width: 16px" />
            <span style="margin-left: 5px">{{ item.label }}</span>
          </div>
        </div>
        <div class="toolList">
          <div
            class="item cursorPointer"
            :class="{ active: currentBusiness === item.id }"
            v-for="item in toolList"
            :key="item.id"
            @click="handleChooseTool(item.id)"
          >
            <img :src="item.icon" alt="" style="width: 16px" />
            <span style="margin-left: 5px">{{ item.label }}</span>
          </div>
        </div>
      </div>

      <!-- 查询统计 -->
      <template v-if="currentBusiness === 1">
        <div class="commonPosition treeBox">
          <el-input
            placeholder="请输入镇名称"
            suffix-icon="el-icon-search"
            v-model="filterText"
            size="medium"
          >
          </el-input>
          <el-scrollbar class="treeContainer">
            <!-- 滚动条要包裹的tree内容 -->
            <el-tree
              :data="tree"
              node-key="id"
              :props="elTreeProps"
              @node-click="nodeClick"
              :filter-node-method="filterNode"
              :expand-on-click-node="false"
              ref="areaTree"
            ></el-tree>
          </el-scrollbar>
        </div>
        <div class="query">
          <el-select
            v-model="queryObj.type"
            placeholder="选择管网类型"
            class="select"
            size="medium"
          >
            <el-option v-for="item in supplyList" :key="item.id" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-select
            v-model="queryObj.diameter"
            placeholder="选择管网管径"
            class="select"
            size="medium"
          >
            <el-option v-for="item in diameterList" :key="item.code" :label="item.name" :value="item.code"></el-option>
          </el-select>
          <el-select
            v-model="queryObj.material"
            placeholder="选择管网材质"
            class="select"
            size="medium"
          >
            <el-option v-for="item in materialList" :key="item.id" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-select
            v-model="queryObj.paveTime"
            placeholder="选择管网管龄"
            class="select"
            size="medium"
          >
            <el-option v-for="item in paveTimeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
          </el-select>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="medium"
            @click="handleQuery"
            >查询</el-button
          >
          <div class="queyAreaList">
            <div
              class="item cursorPointer"
              v-for="item in queyAreaList"
              :key="item.id"
              :class="{ active: currentSelectArea === item.id }"
              @click="handleChooseArea(item.id)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
        <!-- 右侧图表 -->
        <div class="commonRightPosition queryRight" v-show="showQueryRight">
          <div class="chart">
            <div class="title">管线类型统计</div>
            <div class="chartBox">
              <PieChart :allLength="this.allLength"  :pieData="pieData.supply"/>
            </div>
          </div>
          <div class="chart">
            <div class="title">管线管径统计</div>
            <div class="chartBox">
              <PieChart :allLength="this.allLength"  :pieData="pieData.diameter"/>
            </div>
          </div>
          <div class="chart">
            <div class="title">管线材质统计</div>
            <div class="chartBox">
              <PieChart :allLength="this.allLength"  :pieData="pieData.material"/>
            </div>
          </div>
          <div class="chart">
            <div class="title">管线管龄统计</div>
            <div class="chartBox">
              <PieChart :allLength="this.allLength"  :pieData="pieData.pave"/>
            </div>
          </div>
        </div>
        <!-- 管网列表 -->
        <div
          class="control cursorPointer"
          :class="{ isClose: !showListResult }"
          @click="showListResult = !showListResult"
        ></div>
        <div class="listResult" v-show="showListResult">
          <el-table
            :data="pipeNetworkList"
            height="430px"
            size="mini"
            @row-click="rowClick"
          >
            <el-table-column label="名称" prop="name"></el-table-column>
            <el-table-column label="编号" prop="code"></el-table-column>
            <el-table-column
              label="所在地"
              prop="villageName"
            ></el-table-column>
            <el-table-column label="管径(米)" prop="diameter"></el-table-column>
            <el-table-column label="材质" prop="material"></el-table-column>
            <el-table-column label="管龄" prop="pipeAge"></el-table-column>
            <el-table-column label="长度(米)" prop="length"></el-table-column>
            <el-table-column
              label="埋设深度(米)"
              prop="deep"
              width="150px"
            ></el-table-column>
            <el-table-column
              label="设计使用年限(年)"
              prop="designYear"
              width="150px"
            ></el-table-column>
            <el-table-column
              label="铺设日期"
              prop="paveTime"
              width="200px"
            ></el-table-column>
          </el-table>

          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageInfo.pageNo"
            :page-sizes="[10, 20, 30, 40]"
            :page-size.sync="pageInfo.pageSize"
            layout="prev, pager, next"
            style="margin-bottom: 10px"
            :total="pageInfo.total"
          >
          </el-pagination>
        </div>
      </template>

      <!-- 右侧内容显示控制 -->
      <div
        v-show="currentBusiness === 1 || currentBusiness === 2"
        class="siderAction cursorPointer"
        :class="{ isClose: isClose, right: currentBusiness === 1 }"
        @click="handleShowRight"
      ></div>

      <!-- 管网分析 -->
      <template v-if="currentBusiness === 2">
        <!-- 左侧菜单 -->
        <div class="commonPosition menu">
          <div
            class="item cursorPointer"
            :class="{ active: activeMenu === item.id }"
            v-for="item in menus"
            :key="item.id"
            @click="activeMenu = item.id"
          >
            <img :src="item.icon" alt="" style="width: 16px" />
            <span style="margin-left: 18px">{{ item.label }}</span>
          </div>
        </div>
        <div class="commonRightPosition analyzeRight" v-show="showAnalyzeRight">
          <!-- 爆管分析 -->
          <div v-show="activeMenu === 1">

            <div class="innerTab">
              <div
                class="innerTabItem fontDefault"
                :class="{ innerTabActive: tabInner1 === 1 }"
                @click="handleClickInnerTab('tabInner1', 1)"
              >爆管分析</div>
              <div
                class="innerTabItem fontDefault"
                :class="{ innerTabActive: tabInner1 === 2 }"
                @click="handleClickInnerTab('tabInner1', 2)"
              >爆管历史</div>
            </div>

            <section>
              <!-- 爆管分析 -->
              <div style="padding-top: 10px;" v-show="tabInner1 === 1">
                <el-form :model="analyzeForm" label-width="130px" size="medium">
                  <el-form-item label="爆管点位置：">
                    <el-input
                      v-model="analyzeForm.value1"
                      placeholder="请输入内容"
                      style="width: 240px"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="爆管点经纬度：">
                    <el-input
                      v-model="analyzeForm.value2"
                      placeholder="请输入内容"
                      style="width: 240px"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="选择爆管点：">
                    <el-input
                      v-model="analyzeForm.value3"
                      placeholder="请输入内容"
                      style="width: 240px"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary">关阀分析</el-button>
                    <el-button type="primary">重置</el-button>
                  </el-form-item>
                </el-form>

                <!-- 方案切换 -->
                <div class="innerTab">
                  <div
                    v-for="item in caseList"
                    :key="item.id"
                    class="innerTabItem fontDefault"
                    :class="{ innerTabActive: currentCase === item.id }"
                    @click="changeCase(item.id)">{{ item.name }}</div>
                </div>

                <!-- 方案详情 -->
                <div style="padding-top: 10px;">
                  <div class="listHeader">
                    <span style="width: 90px">阀门名称</span>
                    <span style="width: 40px">编号</span>
                    <span style="width: 100px">位置</span>
                    <span style="width: 60px">影响用户</span>
                    <span style="width: 130px">停水通知</span>
                  </div>
                  <div class="listItem" v-for="(item, index) in caseInfo" :key="index">
                    <div style="width: 90px">{{ item.name }}</div>
                    <div style="width: 40px">{{ item.code }}</div>
                    <div style="width: 100px">{{ item.position }}</div>
                    <div style="width: 60px">{{ item.user }}</div>
                    <div style="width: 130px">{{ item.msg }}</div>
                  </div>
                </div>
              </div>

              <!-- 爆管历史 -->
            </section>

          </div>

          <!-- 横断面分析 -->
          <div v-if="activeMenu === 2">
            <div style="padding-left: 70px">
              <el-button type="primary" size="medium" @click="handleStartX"
                >绘制横断面</el-button
              >
            </div>
            <CommonLine
              :seriesData="seriesDataX"
              yAxisName="埋深(m)"
              width="100%"
              height="200px"
            />
          </div>

          <!-- 纵断面分析 -->
          <div v-if="activeMenu === 3">
            <div style="padding-left: 70px">
              <el-button type="primary" size="medium" @click="handleStart"
                >开始选取</el-button
              >
              <el-button type="primary" size="medium" @click="handleEnd"
                >确定</el-button
              >
            </div>
            <CommonLine
              :seriesData="seriesDataY"
              yAxisName="埋深(m)"
              width="100%"
              height="200px"
            />
          </div>
        </div>
      </template>

      <!-- 专题展示 -->
      <div class="legend" v-if="currentBusiness === 3">
        <el-tree
          :data="treeData"
          show-checkbox
          node-key="id"
          class="legendtree"
          ref="treeDataType"
          default-expand-all
          @check="handleNodeClick"
        >
          <span class="custom-tree-node" slot-scope="{ node }">
            <!-- v-if="node.level === 2" -->
            <img
              v-if="node.level === 2 && node.data.type"
              :src="
                require('../../../assets/img/pipe_network/' +
                  node.data.type +
                  '.png')
              "
              style="width: 20px;height: 24px;margin-right: 5px;"
              alt=""
            />
            <span class="labeltext">{{ node.label }}</span>
          </span>
        </el-tree>
      </div>

      <!-- 管网编辑 -->
    </div>
  </div>
</template>

<script>
import LineMap from "../screen/innerlayer.vue";
import CommonLine from "./chart/commonLine.vue";
import PieChart from "./chart/pieChart.vue";

import { getAreaTree } from "@/api/leakageLoss/analysis";
import { getTownList } from "@/api/common";
import { getPipeNetworkCategoryLength, queryPipeNetwork, getLngSection, getPipeNetworkDiameterRangeEnum, getPipeNetworkPaveTimeRangeEnum } from "@/api/pipenetwork";

import { mapMutations } from 'vuex'

import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: "BusinessLayout",
  components: {
    LineMap,
    CommonLine,
    PieChart,
    FloatingSideMenu,
  },
  data() {
    return {
      currentBusiness: 1,
      allLength: 0,
      bList: [
        {
          id: 1,
          label: "查询统计",
          icon: require("@/assets/img/pipe_network/icon/b1.png"),
        },
        {
          id: 2,
          label: "管网分析",
          icon: require("@/assets/img/pipe_network/icon/b2.png"),
        },
        {
          id: 3,
          label: "专题展示",
          icon: require("@/assets/img/pipe_network/icon/b3.png"),
        },
        {
          id: 4,
          label: "管网编辑",
          icon: require("@/assets/img/pipe_network/icon/b4.png"),
        },
      ],
      toolList: [
        {
          id: 1,
          label: "测量",
          icon: require("@/assets/img/pipe_network/icon/t1.png"),
        },
        {
          id: 2,
          label: "上传",
          icon: require("@/assets/img/pipe_network/icon/t2.png"),
        },
        {
          id: 3,
          label: "打印",
          icon: require("@/assets/img/pipe_network/icon/t3.png"),
        },
      ],
      treeData: [
        {
          id: 1,
          label: "设备仪器",
          children: [
            {
              id: 2,
              label: "阀门",
              type: "pipe1",
            },
            {
              id: 3,
              label: "水表",
              type: "pipe2",
            },
            {
              id: 4,
              label: "流量计",
              type: "pipe3",
            },
            {
              id: 5,
              label: "压力计",
              type: "pipe4",
            },
            {
              id: 6,
              label: "水泵",
              type: "pipe5",
            },
            {
              id: 7,
              label: "液位计",
              type: "pipe6",
            },
          ],
        },
        {
          id: 8,
          label: "管网",
          children: [
            {
              id: 9,
              label: "输水管线",
              type: "pipe7",
            },
            {
              id: 10,
              label: "引水管线",
              type: "pipe8",
            },
          ],
        },
        {
          id: 11,
          label: "厂站",
          children: [
            {
              id: 12,
              label: "水池",
              type: "pipe9",
            },
            {
              id: 13,
              label: "水厂",
              type: "pipe10",
            },
          ],
        },
      ],
      checkedList: [],
      // 查询统计区域
      tree: [],
      elTreeProps: {
        children: "childTown",
        label: "name",
      },
      townId: "",
      filterText: "",
      queryObj: {
        type: null,
        diameter: null,
        material: null,
        paveTime: null,
      },
      supplyList: [
        {
          id: 0,
          label: '引水管',
          value: 0
        },
        {
          id: 1,
          label: '配水管',
          value: 1
        },
        {
          id: 2,
          label: '提水管',
          value: 2
        }
      ],
      diameterList: [],
      materialList: [
        {
          id: 1,
          label: 'PE',
          value: 'PE'
        },
        {
          id: 2,
          label: '钢丝网骨架复合管',
          value: '钢丝网骨架复合管'
        },
        {
          id: 3,
          label: '钢管',
          value: '钢管'
        }
      ],
      paveTimeList: [],
      queyAreaList: [
        {
          id: 1,
          label: "全部",
        },
        {
          id: 2,
          label: "点选",
        },
        {
          id: 3,
          label: "多边形",
        },
      ],
      showListResult: false,
      currentSelectArea: 1,
      showQueryRight: true,
      pipeNetworkList: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      pieData: {
        diameter: [],
        material: [],
        pave: [],
        supply: []
      },
      // 管网分析
      menus: [
        {
          id: 1,
          label: "爆管分析",
          icon: require("@/assets/img/pipe_network/icon/m1.png"),
        },
        {
          id: 2,
          label: "横断面分析",
          icon: require("@/assets/img/pipe_network/icon/m2.png"),
        },
        {
          id: 3,
          label: "纵断面分析",
          icon: require("@/assets/img/pipe_network/icon/m3.png"),
        },
      ],
      activeMenu: 1,
      showAnalyzeRight: true,
      // 爆管分析
      tabInner1: 1,
      analyzeForm: {},
      caseList: [
        {
          name: '方案一',
          id: 1
        },
        {
          name: '方案二',
          id: 2
        }
      ],
      currentCase: 1,
      caseInfo: [
        {
          name: "阀门一",
          code: "FM001",
          position: "古夫镇合心3村",
          user: 18,
          msg: "发送停水通知",
        },
        {
          name: "阀门二",
          code: "FM002",
          position: "古夫镇合心3村",
          user: 18,
          msg: "发送停水通知",
        },
        {
          name: "阀门三",
          code: "FM003",
          position: "古夫镇合心3村",
          user: 18,
          msg: "发送停水通知",
        },
        {
          name: "阀门四",
          code: "FM004",
          position: "古夫镇合心3村",
          user: 18,
          msg: "发送停水通知",
        },
        {
          name: "阀门五",
          code: "FM005",
          position: "古夫镇合心3村",
          user: 18,
          msg: "发送停水通知",
        },
      ],
      // 横断面绘图数据
      seriesDataX: [
        {
          type: 'line',
          symbol: 'none',
          data: []
        },
        {
          type: 'scatter',
          symbolSize: 20,
          data: []
        }
      ],
      // 纵断面绘图数据
      seriesDataY: [
        {
          type: "line",
          data: [],
        },
      ],
    };
  },
  computed: {
    isClose() {
      if (this.currentBusiness === 1) {
        return !this.showQueryRight;
      }
      if (this.currentBusiness === 2) {
        return !this.showAnalyzeRight;
      }
    },
  },
  watch: {
    '$route.query.type': {
      handler(newVal, oldVal) {
        this.currentBusiness = Number(newVal)
      },
      immediate: true
    },
    filterText(val) {
      this.$refs.areaTree.filter(val);
    },
  },
  mounted() {
    this.initData();
    if (this.currentBusiness == 3) {
      // console.log(this.$refs.treeDataType);
      this.$refs.treeDataType.setCheckedKeys([9, 10]);
    }
    this.getNetworkInfo();
  },
  methods: {
    ...mapMutations('app', ['SET_pipeNetworkMenuActive']),
    initData() {
      // getAreaTree().then(res => {
      //   this.tree = res.data
      // })
      getTownList().then((res) => {
        this.tree = res.data.childTown.map(item => ({
          code: item.code,
          name: item.name,
          childTown: []
        }));
      });
      // 获取枚举参数
      getPipeNetworkDiameterRangeEnum().then(res => {
        this.diameterList = res.data
      })
      getPipeNetworkPaveTimeRangeEnum().then(res => {
        this.paveTimeList = res.data
      })
    },
    handleBack() {
      // this.$router.push({ name: "PipeNetworkIndex" });
      this.$router.push({ name: 'PipNetworkMap', query: { id: 4 } })
    },
    // 顶部组件切换
    handleChangeBusiness(id) {
      this.currentBusiness = id;
      this.$router.push({
        name: 'Business',
        query: { type: id }
      })
      this.SET_pipeNetworkMenuActive(id)
      if (id == 3) {
        this.$nextTick(() => {
          this.$refs.treeDataType.setCheckedKeys([9, 10]);
        });
      }
    },
    // 顶部右侧工具
    handleChooseTool(id) {
      console.log(id);
    },
    /* 查询统计相关 */
    // tree节点点击
    nodeClick(item, node, self) {
      console.log(item, node);
      this.townId = item.code;
      if(node.level === 1) {
        // 点击镇查询分类统计
        this.getNetworkInfo();
        if(this.showListResult) {
          this.handleQueryNetworkPage()
        }
      }
    },
    // tree过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 查询按钮
    handleQuery() {
      this.handleQueryNetworkPage();
      if (!this.showListResult) {
        this.showListResult = true;
      }
    },
    // 管网分页查询
    handleQueryNetworkPage() {
      const payload = {
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        type: this.queryObj.type,
        material: this.queryObj.material,
        diameterRange: this.queryObj.diameter,
        paveTimeRange: this.queryObj.paveTime,
        village: this.townId // 镇id 目前官网是绑定到镇
      }
      queryPipeNetwork(payload).then((res) => {
        const { status, count, data } = res;
        if (status === 200) {
          this.pipeNetworkList = data;
          this.pageInfo.total = count;
        }
      });
    },
    // 获取管线分类统计
    getNetworkInfo() {
      getPipeNetworkCategoryLength(this.townId).then((res) => {
        this.allLength = res.data.allLength;
        this.pieData.supply = res.data.supply.map(item => {
          return {
            name: item[0],
            value: item[1]
          }
        })
        this.pieData.diameter = res.data.diameter.map(item => {
          return {
            name: item[0],
            value: item[1]
          }
        })
        this.pieData.material = res.data.material.map(item => {
          return {
            name: item[0],
            value: item[1]
          }
        })
        this.pieData.pave = res.data.pave.map(item => {
          return {
            name: item[0],
            value: item[1]
          }
        })

      });
    },
    rowClick() {},
    handleSizeChange() {
      this.pageInfo.pageNo=1,
      this.handleQueryNetworkPage()
    },
    handleCurrentChange() {
      this.handleQueryNetworkPage();
    },
    // 查询区域右侧点击
    handleChooseArea(id) {
      this.currentSelectArea = id
      if(id === 3) {
        this.showListResult = false
        // 调用地图绘制多边形
        this.$refs.map.drawPolygon()
      }
    },
    // 获取多边形point
    drawPolygonEnd(list) {
      console.log(list)
      // this.currentSelectArea = id;
      let data = []
      list.forEach(item => {
        let point = {
          "x": item._lng,
          "y": item._lat,
        }
        data.push(point);
      })
      let param = {
        coordinateList: data
      }
      queryPipeNetwork(param).then((res) => {
        console.log(res)
        const { status, count, data } = res;
        if (status === 200) {
          this.pipeNetworkList = data;
          this.pageInfo.total = count;
          if (!this.showListResult) {
            this.showListResult = true;
          }
        }
      });
    },
    handleShowRight() {
      if (this.currentBusiness === 1) {
        this.showQueryRight = !this.showQueryRight;
      }
      if (this.currentBusiness === 2) {
        this.showAnalyzeRight = !this.showAnalyzeRight;
      }
    },
    // 图例
    handleNodeClick(data, checkedList) {
      const arr = [];
      if (checkedList.checkedNodes) {
        checkedList.checkedNodes.forEach((element) => {
          if (element.type) {
            arr.push(element.type);
          }
        });
      }
      this.checkedList = arr;
    },

    // 爆管分析
    handleClickInnerTab(key, id) {
      this[key] = id
    },
    changeCase(value) {
      this.currentCase = value
    },
    // 纵断面分析
    handleStartX() {
      this.$refs.map.qiehengduanmian();
    },
    xEnd(list1, list2) {
      this.seriesDataX[0].data = list1
      this.seriesDataX[1].data = list2
    },
    // 纵断面分析
    handleStart() {
      this.$refs.map.zongFlag = true;
      this.$refs.map.zongList = [];
    },
    handleEnd() {
      const list = this.$refs.map.zongList;
      if (list.length > 0) {
        console.log(list);
        getLngSection(list).then(res => {
          console.log('纵断面分析', res)
          this.seriesDataY[0].data = res.data.map(line => {
            return [line.length, line.deep]
          })
        })
      } else {
        this.$message.warning("请在地图上选取管线");
      }
    },
  },
};
</script>

<style lang="scss">
.businessLayout {
  .treeContainer {
    .el-scrollbar__wrap {
      overflow-x: auto;
    }
    // 覆盖el-tree样式
    .el-tree {
      background: transparent;
      color: #fff;
      .el-tree-node:focus > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #113b7a;
      }
      .el-tree-node__content:hover {
        background-color: #113b7a;
      }
    }
  }
  .el-pagination.is-background {
    .el-pager li {
      color: #fff;
    }
    .btn-prev {
      color: #fff;
    }
    .btn-next {
      color: #fff;
    }
  }
}
</style>

<style lang="scss" scoped>
.businessLayout {
  height: calc(100vh - 84px);
  padding: 18px 30px 30px;
  color: #fff;
  .content {
    width: 100%;
    height: 100%;
    border: 1px solid #2a5dd2;
    position: relative;
    .map {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
    .cursorPointer {
      cursor: pointer;
    }
    .top {
      position: absolute;
      top: 0;
      width: 100%;
      height: 40px;
      background-color: rgba(3, 29, 90, 0.6);
      padding: 0 19px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .bList {
        display: flex;
        .item {
          width: 120px;
          height: 40px;
          font-size: 18px;
          line-height: 40px;
          text-align: center;
          border-bottom: 2px solid transparent;
        }
        .active {
          background: linear-gradient(
            180deg,
            rgba(15, 35, 42, 0) 0%,
            #1e62b8 100%
          );
          border-bottom-color: #11e6ff;
        }
      }
      .toolList {
        display: flex;
        .item {
          font-size: 18px;
          margin-left: 20px;
        }
      }
    }
    .commonPosition {
      width: 240px;
      background: rgba(42, 93, 210, 0.1);
      border: 1px solid rgba(42, 93, 210, 0.5);
      position: absolute;
      left: 20px;
      top: 60px;
    }
    .treeBox {
      padding: 10px;
      bottom: 20px;
      .treeContainer {
        padding-top: 10px;
        height: calc(100% - 36px);
      }
    }
    .query {
      position: absolute;
      top: 60px;
      left: 280px;
      display: flex;
      .select {
        width: 180px;
        margin-right: 20px;
      }
      .queyAreaList {
        background-color: rgba(2, 50, 128, 0.8);
        display: flex;
        margin-left: 53px;
        .item {
          width: 84px;
          height: 34px;
          border: 1px solid #2758c8;
          text-align: center;
          line-height: 32px;
          color: #acc6ea;
        }
        .active {
          background-image: url("~@/assets/img/pipe_network/area_active.png");
          background-size: 100%;
        }
      }
    }
    .listResult {
      position: absolute;
      width: 1194px;
      top: 150px;
      left: 280px;
      background: rgba(42, 93, 210, 0.1);
      border: 1px solid rgba(42, 93, 210, 0.5);
    }
    .control {
      position: absolute;
      left: 50%;
      top: 100px;
      width: 20px;
      height: 52px;
      background-image: url("~@/assets/img/pipe_network/closeSider.png");
      background-size: 100%;
      transform: rotate(-90deg);
    }
    .siderAction {
      position: absolute;
      right: 460px;
      top: 50%;
      width: 20px;
      height: 52px;
      background-image: url("~@/assets/img/pipe_network/closeSider.png");
      background-size: 100%;
    }
    .right {
      right: 360px;
    }
    .isClose {
      right: 20px;
      background-image: url("~@/assets/img/pipe_network/openSider.png");
    }
    .commonRightPosition {
      width: 440px;
      position: absolute;
      right: 20px;
      top: 60px;
      bottom: 20px;
      border: 1px solid #2a5dd2;
      background-color: rgba(4, 15, 45, 0.5);
    }
    .queryRight {
      padding-top: 20px;
      width: 340px;
      .chart {
        height: 25%;
        .title {
          font-size: 18px;
          color: #d8f0ff;
          padding-left: 20px;
          &::before {
            display: inline-block;
            width: 18px;
            height: 18px;
            content: "";
            position: relative;
            background-image: url("~@/assets/img/pipe_network/icon.png");
            background-size: 100%;
            margin-right: 10px;
          }
        }
        .chartBox{
          width: 100%;
          height: calc(100% - 24px);
        }
      }
    }
    .menu {
      padding-bottom: 10px;
      .item {
        height: 50px;
        background-color: rgba(46, 94, 164, 0.2);
        margin-top: 10px;
        line-height: 50px;
        padding-left: 40px;
      }
      .active {
        background: linear-gradient(90deg, #2458c2 0%, #003970 100%);
      }
    }
    .analyzeRight {
      padding: 10px;
      font-size: 14px;
      .pointFormItem {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        span {
          flex: none;
        }
      }
    }
    .legend {
      position: absolute;
      top: 60px;
      right: 20px;
      width: 220px;
      height: 455px;
      background: rgba(4, 15, 45, 0.5);
      border-radius: 4px;
      padding: 20px;
    }
    .legendtree {
      background: transparent;
      .labeltext {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
      }
      ::v-deep.el-tree-node__content:hover {
        background: transparent;
      }
      ::v-deep.el-tree-node:focus > .el-tree-node__content {
        background-color: transparent;
      }
      ::v-deep.el-tree-node__label {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
      }
      ::v-deep.el-checkbox__inner {
        width: 16px;
        height: 16px;
        background: rgba(0, 57, 112, 0.5);
        border: 1px solid #0285ff;
      }
    }
  }
  .fontDefault {
    color: #ccecfc;
    font-size: 18px;
    text-align: center;
    line-height: 40px;
  }
  .innerTab {
    display: flex;
    border-bottom: 1px solid #2a5dd2;
    .innerTabItem {
      width: 120px;
      height: 40px;
      cursor: pointer;
      border-bottom: 5px solid transparent;
    }
    .innerTabActive {
      color: #ffffff;
      font-weight: bold;
      border-bottom-color: #2a5dd2;
    }
  }
  .listHeader {
    font-size: 14px;
    text-align: center;
    background-image: url("../../../assets/img/table.png");
    background-size: 100%;
    height: 34px;
    display: flex;
    align-items: center;
  }
  .listItem {
    font-size: 12px;
    display: flex;
    align-items: center;
    height: 30px;
    background-color: rgba(2, 50, 128, 0.23);
    border: 1px solid #023280;
    margin-top: 5px;
    div {
      text-align: center;
    }
  }
}
</style>