<template>
  <div class="pipeNetworkMap">
    <div id="mars3dContainer" class="mars3d-container"></div>

    <!-- 面板 -->
    <!-- <div class="infoview">
      <table class="mars-table">
        <tr class="undergroundAttr"></tr>
        <tr class="kaiwaAttr"></tr>
      </table>
    </div> -->
  </div>
</template>
  
<script>
export default {
  data() {
    return {
      deepHeight: 100,
      map: null,
      tmd: 0.5,
      opean: true,
      mapOptions: {
        scene: {
          //默认视角参数
          center: {
            lat: 31.220435,
            lng: 110.728503,
            alt: 5000,
            heading: 0,
            pitch: -90,
          },
          //   orderIndependentTranslucency: false,
          //   contextOptions: { webgl: { alpha: true } }, // 允许透明，只能Map初始化传入 [关键代码]
          //   showMoon: false,
          //   showSkyBox: false,
          //   showSkyAtmosphere: false,
          //   fog: false,
          //   globe: {
          //       depthTestAgainstTerrain: true // 开启深度检测
          //   }
        },
        control: {
          baseLayerPicker: true, // basemaps底图切换按钮
          homeButton: true, // 视角复位按钮
          // sceneModePicker: true, // 二三维切换按钮
          // navigationHelpButton: true, // 帮助按钮
          fullscreenButton: true, // 全屏按钮
          // contextmenu: { hasDefault: true } // 右键菜单
          terrainProviderViewModels: [],
          contextmenu: {
          hasDefault:false
          }
        },
        // terrain: {
        // url: 'http://data.mars3d.cn/terrain',
        // show: true
        // },
        basemaps: [
          // { "id": 10, "name": "地图底图", "type": "group" },
          //   {
          //     "id": 2021,
          //     "pid": 10,
          //     "name": "谷歌影像",
          //     "icon": "/img/basemaps/google_img.png",
          //     "type": "google",
          //     "layer": "img_d",
          //     "show": true
          //   },
          {
            "pid": 10,
            "name": "天地图影像",
            "icon": require("../../../assets/img/basemaps/tdt_img.png"),
            "type": "group",
            "layers": [
              { "name": "底图", "type": "tdt", "layer": "img_d" },
              { "name": "注记", "type": "tdt", "layer": "img_z" }
            ],
          },
          {
            "pid": 10,
            "name": "天地图电子",
            "icon": require("../../../assets/img/basemaps/tdt_vec.png"),
            "type": "group",
            "layers": [
              { "name": "底图", "type": "tdt", "layer": "vec_d" },
              { "name": "注记", "type": "tdt", "layer": "vec_z" }
            ]
          },
          {
            "pid": 10,
            "name": "高德影像",
            "type": "group",
            "icon": require("../../../assets/img/basemaps/gaode_img.png"),
            "layers": [
              { "name": "底图", "type": "gaode", "layer": "img_d" },
              { "name": "注记", "type": "gaode", "layer": "img_z" }
            ]
          },
          // {
          //   "pid": 10,
          //   "name": "蓝色底图",
          //   "icon": require("../../../assets/img/basemaps/bd-c-midnight.png"),
          //   "type": "xyz",
          //   // "url": "http://map.geoq.cn/arcgis/rest/services/ChinaOnlineStreetPurplishBlue/MapServer/tile/{z}/{y}/{x}",
          //   "url": "https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}",
          //   "chinaCRS": "GCJ02",
          //   "enablePickFeatures": false,
          //   "show": true
          // },
          {
            brightness: 0.6,
            chinaCRS: "GCJ02",
            contrast: 1.8,
            filterColor: "#4e70a6",
            gamma: 0.3,
            hue: 1,
            icon: require("../../../assets/img/basemaps/bd-c-midnight.png"),
            id: 2017,
            invertColor: true,
            layer: "vec",
            name: "蓝色底图",
            pid: 10,
            saturation: 0,
            type: "gaode",
            "show": true
          }
        ],
      },
      param1: 1,
      underground: null,
      terrainPlanClip: null,
      terrainClip: null,
      eventTarget: new mars3d.BaseClass(),
      queryMapserver: null,
      geourl: '',
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.initMap();
  },
  beforeDestroy() {
    if(this.map) {
      this.map.destroy()
    }
  },
  methods: {
    initMap() {
      this.map = new mars3d.Map("mars3dContainer", this.mapOptions);
      // this.map.container.style.backgroundColor = "#546a53"; // 背景色
      this.map.on('load', () => {
        console.log('mapload')
        // this.addLineLayer()
        // this.addWMS()
        this.addSupply();
        this.addIntake();
      })
      // this.showGeoJsonLayer();
      // this.addWMS();
    },
    // 供水管线
    addSupply() {
      const supplyLayer = new mars3d.layer.WmsLayer({
        url: this.geourl + "/geoserver/xingshan/wms",
        layers: "xingshan:supply_network",
        parameters: {
          service: 'WMS',
          version: '1.1.0',
          request: 'GetMap',
          srs: 'EPSG:4326',
          transparent: true,
          format: 'image/png'
          // format: 'application/openlayers'
        },
        hasToGraphic: true,
        featureToGraphic: (feature, event) => {
          const data = feature.data
          console.log(data)
          return {
            // type: "polyline",
            symbol: {
              type: "polylineP",
              merge: true,
              styleOptions: {
                label: {
                  font_size: 15,
                  text: "{name},{length}m",
                  color: "#ffffff",
                  outline: true,
                  outlineColor: "#ffffff",
                  distanceDisplayCondition: true,
                  distanceDisplayCondition_far: 50000,
                  distanceDisplayCondition_near: 0,
                  setHeight: 10000,
                },  
              },
            },
          }
        },
        popup: [
          { field: "name", name: "名称" },
          { field: "length", name: "长度" },
          { field: "code", name: "编码" },
          { field: "material", name: "材质" },
          { field: "diameter", name: "管径" },
          { field: "deep", name: "深度" },
        ],
        // popup: "all",
        zIndex: 20,
        flyTo: true,
      })
      this.map.addLayer(supplyLayer)
      // console.log(supplyLayer)
    },
    // 引水管线
    addIntake() {
      const intakeLayer = new mars3d.layer.WmsLayer({
        url: this.geourl + "/geoserver/xingshan/wms",
        layers: "xingshan:intake_network",
        parameters: {
          service: 'WMS',
          version: '1.1.0',
          request: 'GetMap',
          srs: 'EPSG:4326',
          transparent: true,
          format: 'image/png'
          // format: 'application/openlayers'
        },
        hasToGraphic: true,
        featureToGraphic: (feature, event) => {
          const data = feature.data
          // console.log(data)
          return {
            // type: "polyline",
            symbol: {
              type: "polylineP",
              merge: true,
              styleOptions: {
                label: {
                  font_size: 15,
                  text: "{name},{length}m",
                  color: "#ffffff",
                  outline: true,
                  outlineColor: "#ffffff",
                  distanceDisplayCondition: true,
                  distanceDisplayCondition_far: 50000,
                  distanceDisplayCondition_near: 0,
                  setHeight: 10000,
                },  
              },
            },
          }
        },
        popup: [
          { field: "name", name: "名称" },
          { field: "length", name: "长度" },
          { field: "code", name: "编码" },
          { field: "material", name: "材质" },
          { field: "diameter", name: "管径" },
          { field: "deep", name: "深度" },
        ],
        // popup: "all",
        zIndex: 20,
        flyTo: true,
      })
      this.map.addLayer(intakeLayer)
      // console.log(intakeLayer)
    },
    addLineLayer() {
      const layer = new mars3d.layer.WfsLayer({
        name: '管线',
        url: this.geourl + '/geoserver/xingshan/wfs',
        // layer: 'xingshan:total_pipe_network',
        layer: 'xingshan:town',
        id: 'lineLayer',
        // geometryName: 'geom',
        // getCapabilities: false,
        // rectangle: {
        //   xmin: 110.44901132586449,
        //   ymin: 31.09728826808128,
        //   xmax: 111.11212850036625,
        //   ymax: 31.56692602972464
        // },
        symbol: {
          // type: 'polylineP'
          // styleOptions: {
          //   type: 'polyline',
          // }
        }
      })
      this.map.addLayer(layer)
    },
    addWMS() {
      const tileLayer = new mars3d.layer.WmsLayer({
        url: this.geourl + "/geoserver/xingshan/wms",
        layers: "xingshan:total_pipe_network",
        parameters: {
          service: 'WMS',
          version: '1.1.0',
          request: 'GetMap',
          srs: 'EPSG:4326',
          transparent: true,
          format: 'image/png'
          // format: 'application/openlayers'
        },
        bbox: [110.44901132586449,31.09728826808128,111.11212850036625,31.56692602972464],
        tileWidth: null,
        tileHeight: null,
        hasToGraphic: true,
        featureToGraphic: (feature, event) => {
          const data = feature.data
          // console.log(data)
          return {
            // type: "polyline",
            symbol: {
              type: "polylineP",
              merge: true,
              styleOptions: {
                label: {
                  font_size: 15,
                  text: "{name},{length}m",
                  color: "#ffffff",
                  outline: true,
                  outlineColor: "#ffffff",
                  distanceDisplayCondition: true,
                  distanceDisplayCondition_far: 50000,
                  distanceDisplayCondition_near: 0,
                  setHeight: 10000,
                },  
              },
            },
          }
        },
        popup: [
          { field: "name", name: "名称" },
          { field: "length", name: "长度" },
          { field: "code", name: "编码" },
          { field: "material", name: "材质" },
          { field: "diameter", name: "管径" },
          { field: "deep", name: "深度" },
        ],
        // popup: "all",
        zIndex: 20,
        // flyTo: true,
      })
      this.map.addLayer(tileLayer)
      // console.log(tileLayer)
    },
    showGeoJsonLayer() {
      const graphicLayerGeo = new mars3d.layer.GeoJsonLayer({
        name: "兴山管网",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Atotal_pipe_network&maxFeatures=3000&outputFormat=application%2Fjson",
        format: this.simplifyGeoJSON, // 用于自定义处理geojson
        symbol: {
          type: "polylineP",
          styleOptions: {
            clampToGround: true,
            width: 2,
            materialType: mars3d.MaterialType.LineFlow,
            materialOptions: {
              color: "#00ffff",
              // image: "img/textures/fence-line.png",
              speed: 100,
              repeat_x: 10,
            },
            // distanceDisplayCondition: true,
            // distanceDisplayCondition_far: 12000000,
            // distanceDisplayCondition_near: 100000,
            label: {
              font_size: 15,
              text: "{name},{length}m",
              color: "#ffffff",
              outline: true,
              outlineColor: "#ffffff",
              // scaleByDistance: true,
              // scaleByDistance_far: 60000000,
              // scaleByDistance_farValue: 0.2,
              // scaleByDistance_near: 1000000,
              // scaleByDistance_nearValue: 1,
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 5000,
              distanceDisplayCondition_near: 0,
              setHeight: 10000,
            },
          },
        },
        popup: [
          { field: "name", name: "名称" },
          { field: "length", name: "长度" },
          { field: "code", name: "编码" },
          { field: "material", name: "材质" },
          { field: "diameter", name: "管径" },
          { field: "deep", name: "深度" },
        ],
        flyTo: true,
      });
      // graphicLayerGeo.bindPopup(function(event) {
      //   const attr = event.graphic.attr || {}
      //   attr["名称"] = attr["name"]
      //   attr["长度"] = attr["length"] + "m"
      // return mars3d.Util.getTemplateHtml({ title: "矢量图层", template: `
      //         <div class="grapopup">{name} {length} m</div>
      //        `, attr })
      // })
      this.map.addLayer(graphicLayerGeo);
      // console.log(graphicLayerGeo)
      // 绑定事件
      // graphicLayerGeo.on(mars3d.EventType.load, function (event) {
      //     console.log("数据加载完成", event)
      // })
    },
    simplifyGeoJSON(geojson) {
      try {
        geojson = turf.simplify(geojson, {
          tolerance: 0.0001,
          highQuality: true,
          mutate: true,
        });
      } catch (e) {
        //
      }
      return geojson;
    },
  },
};
</script>

<style lang="scss">
.pipeNetworkMap {
  .mars3d-popup-background {
    background: rgba(2, 50, 128, 0.5);
    border: 1px solid #1377d0;
  }
}
#mars3dContainer {
  .cesium-viewer-toolbar {
    // left: 465px;
  }
  .cesium-baseLayerPicker-dropDown-visible {
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    left: 48px
}
}
.grapopup {
  background-image: url('../../../assets/img/leakageLoss/lstc.png');
  background-repeat: repeat;
  background-position: top left;
  background-size: 100% 100%;
  width: 500px;
  height: 300px;
  background-color: #5a50dd;
  padding: 10px; 
}
</style>

<style scoped lang="scss">
/**infoview浮动面板*/
.infoview {
  position: absolute;
  top: 5px;
  left: 480px;
  padding: 10px 15px;
  border-radius: 4px;
  border: 1px solid rgba(128, 128, 128, 0.5);
  color: #ffffff;
  background: rgba(0, 0, 0, 0.4);
  box-shadow: 0 3px 14px rgba(128, 128, 128, 0.5);
  z-index: 19870101;
}
.mars-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>
    