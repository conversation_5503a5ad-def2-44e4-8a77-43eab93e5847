<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
import resize from "@/utils/chartResize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    allLength: {
      type: Number,
      default: 0,
    },
    title: {
      type: String,
      default: "",
    },
    seriesData: {
      type: Array,
      default: () => [],
    },
    xAxisData: {
      type: Array,
      default: () => [],
    },
    legendData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart();
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el);
      this.chart.setOption({
        // title: {
        //   text: this.title,
        //   left: "center",
        //   top: "40%",
        //   left: '37%',
        //   textStyle: {
        //     color: "#fff",
        //     fontSize: 14, // 添加合适的字体大小
        //   },
        // },
        legend: {
          icon: "circle",
          orient: 'vertical',
          right: 10,
          // bottom: 10,
          top: 'center',
          textStyle: {
            color: "#fff",
            fontSize: 12, // 添加合适的字体大小
          },
          formatter: (name) => {
            const target = this.seriesData.find((item) => item.name === name);
            return `${name}: ${target.length}km`
          },
          itemWidth: 10, // 图例标记的图形宽度
          itemHeight: 10, // 图例标记的图形高度
        },
        tooltip: {
          trigger: "item",
          formatter: (params) => {
            return `${params.name}: ${params.data.length}km (${params.percent}%)`;
          },
        },
        series: [
          {
            color: [
              "#FB7B1E",
              "#FFC056",
              "#00D360",
              "#03A4FF",
              "#00E4FF",
              "#C6ECDF",
            ],
            type: "pie",
            roseType: 'area',
            radius: ["45%", "60%"],
            center: ["40%", "45%"],
            avoidLabelOverlap: true, // 避免标签重叠
            data: this.seriesData,
            label: {
              formatter: '{d}%'
            }
          },
        ],
      });
    },
  },
};
</script>
