<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    yAxisName: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption(
        {
        color: ['#3392FF', '#FF8D1A', '#1CFF27', '#FF5733'],
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            const data = params[2]
            return `${data.name}：${data.value}km`
          }
        },
        legend: {
          textStyle: {
            color: '#fff'
          },
          data: this.legendData
        },
        grid: {
          top: '6%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          // boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#ACC6EA',
              opacity: 0.5
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          data: this.xAxisData
        },
        yAxis: [
          {
            name: this.yAxisName,
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#ACC6EA',
                opacity: 0.5
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#2D3C5C',
                opacity: 0.8
              }
            }
          }
        ],
        series: [
          {
            name: '',
            type: 'pictorialBar',
            symbolSize: [22, 10],
            symbolOffset: [0, -5],
            symbolPosition: 'end',
            color: '#1BDFFC',
            data: this.seriesData
          },
          {
            name: '',
            type: 'pictorialBar',
            symbolSize: [22, 10],
            symbolOffset: [0, 5],
            color: '#1251D2',
            data: this.seriesData
          },
          {
            type: 'bar',
            showBackground: true,
            barWidth: 22,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [
                { offset: 0, color: '#1BDFFC' },
                { offset: 1, color: '#1251D2' }
              ])
            },
            data: this.seriesData
          }
        ]
        }
      )
    }
  }
}
</script>
