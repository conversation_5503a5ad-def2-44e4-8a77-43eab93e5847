<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'
import { Decimal } from 'decimal.js'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    allLength: {
      type: Number,
      default: 0
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            color: ['#FB7B1E', '#FFC056 ', '#00D360', '#03A4FF', '#00E4FF', '#C6ECDF'],
            type: 'pie',
            radius: ['55%', '69%'],
            center: ['50%', '50%'],
            labelLine: {
              length: 10,
              length2: 60,
              lineStyle: {
                color: '#ACC6EA'
              }
            },
            label: {
              // '{top | {b}} \n {c}万m³({d}%)'
              // formatter: '{top|{b}} \n {bottom|{c}}({d}%)',
              formatter: (params) => {
                let decimal = new Decimal(params.value)
                let d = decimal.div(this.allLength).mul(100).toDecimalPlaces(2).toNumber()
                return '{top|' + params.name + '}' + '\n' + '{bottom|' + params.data.length + 'km' + '(' + d +'%)' + '}'
              },
              padding: -50,
              rich: {
                top: {
                  padding: 5
                },
                bottom: {
                  padding: 5
                }
              }
            },
            data: this.seriesData
          }
        ]
      })
    }
  }
}
</script>
