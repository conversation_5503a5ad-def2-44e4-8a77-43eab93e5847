<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    yAxisName: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    getData() {
      const colors = ['#FB7B1E', '#FFC056', '#00D360', '#03A4FF', '#00E4FF']
      return this.seriesData.map((item, index) => {
        return {
          showBackground: true,
          name: this.xAxisData[index].name,
          type: 'bar',
          roundCap: true,
          coordinateSystem: "polar",
          itemStyle: {
            color: colors[index % 5]
          },
          data: [item]
          // value: item,
          // itemStyle: {
          //   color: colors[index % 5]
          // }
        }
      })
    },
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption(
        {
          // colors: ['green', '#FFC056', '#00D360', '#03A4FF', '#00E4FF'],
          tooltip: {
            trigger: 'item',
            formatter: function(params) {
              return params.marker + params.seriesName + '<br />' + params.data.value + '%' + '(' + params.data.length + 'km)'
            }
          },
          legend: {
            orient: 'vertical',
            right: '10%',
            top: 'middle',
            icon: 'circle',
            textStyle: {
              color: '#fff'
            },
            data: this.xAxisData,
            formatter: function(value) {
              return value
            }
          },
          polar: {
            radius: ["5%", "100%"],
            center: ["30%", "50%"],
          },
          // 角度轴
          angleAxis: {
            type: 'value',
            startAngle: 90,
            axisLine: {
              // show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
          },
          // 径向轴
          radiusAxis: {
            type: "category",
            // data: this.xAxisData,
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLine: {
              show: false,
            },
          },
          series: this.getData()      
        }
      )
    }
  }
}
</script>
