<template>
  <div>
    <div id="mars3dContainer" class="mars3d-container"></div>

    <!-- 面板 -->
    <!-- <div class="infoview">
        <table class="mars-table">
          <tr class="undergroundAttr"></tr>
          <tr class="kaiwaAttr"></tr>
        </table>
      </div> -->
  </div>
</template>
    
<script>
export default {
  data() {
    return {
      deepHeight: 100,
      // map: null,
      tmd: 0.5,
      opean: true,
      mapOptions: {
        scene: {
          //默认视角参数
          // center: {
          //   lat: 31.310435,
          //   lng: 110.768503,
          //   alt: 130000,
          //   heading: 0,
          //   pitch: -90,
          // },
          extent: {
            xmin: 110.5,
            xmax: 111,
            ymin: 30,
            ymax: 32.5,
          },
          cameraController: {
            // 滚轮缩小最大高度
            maximumZoomDistance: 400000,
          },
          backgroundColor: "#013453",
          sceneMode: 2,
          globe: {
            baseColor: " #363635", // 地球地面背景色
            // showGroundAtmosphere: false,
            // enableLighting: false
          },
        },
        control: {
          homeButton: true, // 视角复位按钮
        },
        basemaps: [
          { id: 10, name: "地图底图", type: "group" },
          // {
          //   "id": 2023,
          //   // "pid": 10,
          //   "name": "无底图",
          //   "icon": "/img/basemaps/null.png",
          //   "type": "grid",
          //   "color": rgba(1,52,83,1),
          //   "backgroundColor": rgba(1,52,83,1),
          //   "alpha": 0.03,
          //   "cells": 2,
          //   "show": true
          // },
          {
            pid: 10,
            name: "单张图片 (本地离线)",
            icon: "/img/basemaps/offline.png",
            type: "image",
            url: require("../../../assets/img/pipe_network/bjpic.png"),
            show: true,
          },
          // {
          //   // wms也可以换一种xyz的直接写法
          //   id: 22,
          //   name: "光污染图层(XYZ方式)",
          //   icon: "img/basemaps/blackMarble.png",
          //   type: "xyz",
          //   url: "http://**************:8080/geoserver/img/wms?service=WMS&version=1.1.0&request=GetMap&layers=img%3Apng2&bbox=110.39483293%2C30.9630324254%2C111.15191207080001%2C31.591080100800003&width=768&height=637&srs=EPSG%3A4326&styles=&format=application/openlayers",
          //   alpha: 0.6, // 透明度
          //   // proxy: "//server.mars3d.cn/proxy/", // 代理服务，解决跨域问题
          //     show: true
          // },
        ],
      },
      param1: 1,
      underground: null,
      terrainPlanClip: null,
      terrainClip: null,
      eventTarget: new mars3d.BaseClass(),
      queryMapserver: null,
      geourl: "",
      waterFactoryLayer: null,
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL;
    this.initMap();
  },
  methods: {
    initMap() {
      this.map = new mars3d.Map("mars3dContainer", this.mapOptions);
      this.map.container.style.backgroundColor = "#546a53"; // 背景色
      this.addWMS();
      // this.addPic();
      // this.showGeoJsonLayer();
      this.showWaterFactory();
    },
    showWaterFactory() {
      this.waterFactoryLayer = new mars3d.layer.GeoJsonLayer({
        name: "水厂",
        url:
          this.geourl +
          "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Awater_factory&maxFeatures=50&outputFormat=application%2Fjson",
        show: true,
        zIndex: 100,
        symbol: {
          styleOptions: {
            // clampToGround: true,
            image: require("../../../assets/img/pipe_network/pipe9.png"),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
        },
      });

      this.waterFactoryLayer.bindPopup(
        function (event) {
          const attr = event.graphic.attr || {};
          // attr["名称"] = attr["name"]
          return mars3d.Util.getTemplateHtml({
            title: "矢量图层",
            template: `
                <div>
                  <div class="demo">{name}</div>
                </div>`,
            attr,
          });
        },
        {
          autoClose: false,
          closeOnClick: false,
          closeButton: false,
        }
      );
      this.map.addLayer(this.waterFactoryLayer);
      this.waterFactoryLayer.on(mars3d.EventType.load, (event) => {
        console.log("数据加载完成", event);
        const list = document.getElementsByClassName("demo");

        for (let index = 0; index < event.graphics.length; index++) {
          const element = event.graphics[index];
          element.openPopup();

          list[index].addEventListener("click", () => {
            console.log(element);
            this.handlePointClick(element.name);
          });
        }
      });
    },
    //   addPic() {
    //     const tileLayer = new mars3d.layer.ImageLayer({
    //       name: "中科大-西区",
    //       url: require("../../../assets/img/pipe_network/xsblue.png"),
    //       rectangle: { xmin: 110.611, xmax: 111.083, ymin: 31.033, ymax: 31.533 },
    //       zIndex: 20,
    //       opacity: 0.3
    //     })
    //     this.map.addLayer(tileLayer)
    //   },
    handlePointClick(name) {
      this.$router.push({
        name: "HomeWaterFactory",
        query: {
          id: name,
        },
      });
    },
    addWMS() {
      const tileLayer = new mars3d.layer.WmsLayer({
        url: this.geourl + "/geoserver/img/wms",
        // url: "http://**************:8080/geoserver/img/wms?service=WMS&version=1.1.0&request=GetMap&layers=img%3Apng2&bbox=110.39483%2C30.9630324%2C111.15191207%2C31.59108010&width=768&height=637&srs=EPSG%3A4326&styles=&format=application/openlayers",
        // /?srs=EPSG%3A4326&format=application/openlayers
        layers: "img:png2",
        // rectangle: {
        //   xmin: 110.5,
        //       xmax: 111,
        //       ymin: 30,
        //       ymax: 32.5,
        // },
        parameters: {
          service: "WMS",
          version: "1.1.0",
          request: "GetMap",
          // bbox: '110.39483293%2C30.9630324254%2C111.15191207080001%2C31.591080100800003',
          // width: 768,
          // height: 637,
          srs: "EPSG:4326",
          transparent: true,
          format: "image/png",
          // format: 'application/openlayers'
        },
        getFeatureInfoParameters: {
          feature_count: 10,
          INFO_FORMAT: "text/plain",
        },
        zIndex: 20,
        flyTo: true,
      });
      this.map.addLayer(tileLayer);
    },
    showGeoJsonLayer() {
      const graphicLayerGeo = new mars3d.layer.GeoJsonLayer({
        name: "兴山管网",
        url:
          this.geourl +
          "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Atotal_pipe_network&maxFeatures=3000&outputFormat=application%2Fjson",
        format: this.simplifyGeoJSON, // 用于自定义处理geojson
        symbol: {
          type: "polylineP",
          styleOptions: {
            clampToGround: true,
            width: 2,
            materialType: mars3d.MaterialType.LineFlow,
            materialOptions: {
              color: "#00ffff",
              // image: "img/textures/fence-line.png",
              speed: 100,
              repeat_x: 10,
            },
            label: {
              font_size: 30,
              //   text: "{deep}",
              color: "#00ffff",
              outline: true,
              outlineColor: "#000000",
              scaleByDistance: true,
              scaleByDistance_far: 60000000,
              scaleByDistance_farValue: 0.2,
              scaleByDistance_near: 1000000,
              scaleByDistance_nearValue: 1,
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 10000000,
              distanceDisplayCondition_near: 100000,
              setHeight: 10000,
            },
          },
        },
        popup: [
          { field: "name", name: "name" },
          { field: "length", name: "length" },
          { field: "code", name: "code" },
          { field: "material", name: "material" },
          { field: "diameter", name: "diameter" },
          { field: "deep", name: "deep" },
        ],
        // flyTo: true,
      });
      graphicLayerGeo.bindPopup(function (event) {
        const attr = event.graphic.attr || {};
        attr["名称"] = attr["name"];
        attr["长度"] = attr["length"] + "m";
        return mars3d.Util.getTemplateHtml({
          title: "矢量图层",
          template: `
                <div class="grapopup">{name} {length} m</div>
               `,
          attr,
        });
      });
      this.map.addLayer(graphicLayerGeo);
    },
    simplifyGeoJSON(geojson) {
      try {
        geojson = turf.simplify(geojson, {
          tolerance: 0.0001,
          highQuality: true,
          mutate: true,
        });
      } catch (e) {
        //
      }
      return geojson;
    },
  },
};
</script>
  
<style lang="scss">
#mars3dContainer {
  background-color: #22df74;
  .cesium-viewer-toolbar {
    left: 465px;
  }
  .cesium-baseLayerPicker-dropDown-visible {
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    left: 48px;
  }
}
.grapopup {
  // background-image: url('../../../assets/img/leakageLoss/lstc.png');
  background-repeat: repeat;
  background-position: top left;
  background-size: 100% 100%;
  width: 500px;
  height: 300px;
  background-color: #5a50dd;
  padding: 10px;
}
</style>
  
<style scoped lang="scss">
/**infoview浮动面板*/
.infoview {
  position: absolute;
  top: 5px;
  left: 480px;
  padding: 10px 15px;
  border-radius: 4px;
  border: 1px solid rgba(128, 128, 128, 0.5);
  color: #ffffff;
  background: rgba(0, 0, 0, 0.4);
  box-shadow: 0 3px 14px rgba(128, 128, 128, 0.5);
  z-index: 19870101;
}
.mars-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>
      