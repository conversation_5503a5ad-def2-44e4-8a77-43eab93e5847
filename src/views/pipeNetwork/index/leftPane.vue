<template>
  <div class="leftPane app-container">
    <div class="pipeNetworkInfo screen-common card">
      <div class="title"><span>管线资产统计</span></div>
      <div class="pipeNetworkBox common-card">
        <div class="textInfo">
          <div style="width: 88px;height: 88px;position: relative;">
            <div class="innerIcon"></div>
            <SmallBar :seriesData="pipeLengthInfo" />            
          </div>

          <div>
            <div class="textInfoTop">
              <span>管网总长</span>
              <div class="cellBox">
                <div class="numberCell" v-for="cell, index in cellNumber" :key="index">{{ cell }}</div>
                <div class="unit">km</div>
              </div>
            </div>
            <div class="textInfoBottom">
              <section>
                <span class="label" style="margin-right: 6px;">配水管线</span>
                <span class="value">{{ textInfo.length1 }}km</span>
              </section>
              <section>
                <span class="label" style="margin-right: 6px;">引水管线</span>
                <span class="value">{{ textInfo.length2 }}km</span>
              </section>
            </div>
          </div>
        </div>

        <div class="lineChart">
          <div class="lineTitle iconTitle">近1年管线变化(新增)</div>
          <PipeLengthLine height="200px" :xAxisData="xAxisData" :seriesData="lineData" yAxisName="km" />
        </div>
      </div>
    </div>

    <div class="bottom screen-common mt-10 card">
      <div class="title">
        <span>管线长度占比统计</span>
      </div>
      <div class="common-card chartBox">
        <div class="iconTitle" style="padding-top: 5px;padding-left: 10px;">管线材质统计</div>
        <div class="calcChart">
          <BigPie :seriesData="pieData1" :allLength="allLength" />
        </div>
        <div class="iconTitle" style="padding-left: 10px;">管径统计</div>
        <div class="calcChart" style="padding-top: 5px;">
          <!-- <TypeBar :seriesData="typeBarData" :xAxisData="typeList" /> -->
          <TypeBar :seriesData="typeBarData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SmallBar from './chart/smallBar'
import PipeLengthLine from './chart/pipeLengthLine'
import BigPie from './chart/bigPie'
// import TypeBar from './chart/pipeTypeBar'
import TypeBar from './chart/typePie'

import { getPipeNetworkTypeLength } from '@/api/pipenetwork/screen'

import { Decimal } from 'decimal.js'

export default {
  components: { SmallBar, PipeLengthLine, BigPie, TypeBar },
  data() {
    return {
      textInfo: {
        totalLength: 0,
        length1: 0,
        length2: 0
      },
      allLength: 0,
      pipeLengthInfo: [
        {
          name: '配水管线',
          value: 0
        },
        {
          name: '引水管线',
          value: 0
        }
      ],
      xAxisData: ['22-12', '23-1', '23-2', '23-3', '23-4', '23-5', '23-6', '23-7', '23-8', '23-9','23-10', '23-11'],
      lineData: [40, 38, 32, 35, 41, 39, 41, 42, 39, 28, 30, 22],
      pieData1: [],
      typeList: [],
      typeBarData: []
    }
  },
  computed: {
    cellNumber() {
      let temp = []
      for(let i = 0; i < this.textInfo.totalLength.toString().length; i++) {
        temp.push(this.textInfo.totalLength.toString()[i])
      }
      return temp
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getPipeNetworkTypeLength().then(res => {
        // console.log(res)
        const { status, data } = res
        if(status === 200) {
          console.log(data)
          this.allLength = data.allLength
          const allLength = data.allLength
          // 材质统计
          this.pieData1 = data.material.map(item => {
            return {
              name: item[0],
              value: item[1],
              length: Decimal(item[1]).div(1000).toFixed(1)
            }
          })
          // 口径统计
          this.typeList = data.diameter.map(item => {
            return {
              name: '口径' + item[0],
              length: Decimal(allLength).mul(item[1]).div(1000).floor().toNumber()
            }
          })
          this.typeBarData = data.diameter.map(item => {
            return {
              name: item[0],
              value: Decimal(item[1]).mul(100).toNumber(),
              length: Decimal(allLength).mul(item[1]).div(1000).floor().toNumber()
            }
          })

          // 管线资产统计
          this.textInfo.totalLength = Decimal(data.allLength).div(1000).toFixed(0)
          let type1 = Decimal(data.supply[1][1]).div(1000).toFixed(0) // 配水管线
          let type0 = Decimal(data.supply[0][1]).div(1000).toFixed(0) // 引水管线
          this.textInfo.length1 = type1
          this.textInfo.length2 = type0
          this.pipeLengthInfo[0].value = type1
          this.pipeLengthInfo[1].value = type0
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.leftPane {
  position: absolute;
  width: 430px;
  height: 100%;
  left: 30px;
  color: #ffffff;
  .iconTitle::before {
    display: inline-block;
    content: '';
    width: 16px;
    height: 16px;
    margin-right: 5px;
    background-image: url('../../../assets/img/icon.png');
  }
  .pipeNetworkInfo {
    height: 45.5%;
    .pipeNetworkBox {
      padding: 20px;
      .textInfo {
        background-color: rgba(0, 27, 70, 0.5);
        display: flex;
        justify-content: space-between;
        .innerIcon {
          position: absolute;
          background-image: url('../../../assets/img/pipe_network/innerIcon.png');
          background-repeat: no-repeat;
          background-position: center;
          top: 0;
          width: 100%;
          height: 100%;
        }
        .textInfoTop {
          display: flex;
          align-items: center;
          .cellBox {
            display: flex;
          }
        }
        .numberCell {
          width: 34px;
          height: 50px;
          color: #09FDFB;
          font-size: 30px;
          font-weight: bold;
          line-height: 52px;
          text-align: center;
          margin-right: 5px;
          background-image: url('../../../assets/img/number_cell.png');
          background-size: 100%;
        }
        .label {
          color: #CCECFC
        }
        .unit {
          font-size: 18px;
          line-height: 52px;
          color: #09FDFB;
        }
        .textInfoBottom {
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
          .value {
            font-size: 16px;
            font-weight: bold;
            color: #09FDFB;
          }
        }
      }
      .lineChart {        
        .lineTitle {
          font-size: 16px;
          line-height: 1;
          padding: 10px;
          display: flex;
          align-items: center;
        }
      }
    }
  }
  .mt-10 {
    margin-top: 10px;
  }
  .bottom {
    // height: calc(100% - 409px);
    height: 54.5%;
    .chartBox {
      height: calc(100% - 35px);
      .calcChart {
        height: calc((100% - 52px) / 2);
      }
    }
  }
  .card {
    backdrop-filter: blur(6px);
  }
}
</style>
