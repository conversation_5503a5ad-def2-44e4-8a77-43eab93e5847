<template>
  <div class="pipeNetwork">
    <div id="map"></div>

    <div class="baseBox" @click="changeBase" :class="{ mapModeFull: mapMode }">
      <img src="@/assets/img/home/<USER>/earth.png" alt="">
    </div>

    <div class="customPopup" ref="customPopupRef">
      <div id="popup_template">
        <div>{{ popupInfo.name }}</div>
      </div>
    </div>

  </div>
</template>

<script>
import Map from '@arcgis/core/Map'
import MapView from '@arcgis/core/views/MapView'
import WebTileLayer from '@arcgis/core/layers/WebTileLayer'
import WFSLayer from '@arcgis/core/layers/WFSLayer'
import Point from '@arcgis/core/geometry/Point'
import Popup from '@arcgis/core/widgets/Popup'

import Vue from 'vue'
import SimplePopup from './popup'
const lineComponentClass = Vue.extend(SimplePopup)

export default {
  props: {
    mapMode: {
      type: Boolean
    }
  },
  data() {
    return {
      map: null,
      view: null,
      JL1ImageLayer: null,
      tdtImageNoteLayer: null,
      lineLayer: null,
      geoUrl: '',
      popupInfo: {
        name: '',
        list: []
      },
      isSatellite: false,
    }
  },
  mounted() {
    this.geoUrl = process.env.VUE_APP_GEO_URL
    this.initMap()
  },
  methods: {
    initMap() {
      // 吉林一号影像瓦片
      this.JL1ImageLayer = new WebTileLayer({
        id: 'JL1ImageLayer',
        urlTemplate: 'https://api.jl1mall.com/getMap/{z}/{x}/{y}?mk=73ad26c4aa6957eef051ecc5a15308b4&tk=24277043ba78c68ff6ed3b50ce486283&pro=ca3a3754837f49d7ac3068edce0e65f7&sch=wmts',
      })
      // 天地图影像标注
      this.tdtImageNoteLayer = new WebTileLayer({
        id: 'tdtImageNoteLayer',
        title: 'tdtImageNoteLayer',
        urlTemplate: 'http://{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
        subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
        visible: false
      })
      // 管网
      // const ComponentClass = Vue.extend(SimplePopup)
      this.lineLayer = new WFSLayer({
        url: this.geoUrl + '/geoserver/xingshan/ows',
        name: 'xingshan:total_pipe_network',
        id: 'lineLayer',
        outFields: ['*'],
        // popupTemplate: {
        //   content: (feature) => {
        //     console.log(feature, '111')
        //     this.$nextTick(() => {
        //       this.popupInfo.name = feature.graphic.attributes.station_name

        //     })
        //     return document.getElementById('popup_template').cloneNode(true)
        //     // const instance = new ComponentClass({
        //     //   propsData: {
        //     //     name: feature.graphic.attributes.station_name
        //     //   }
        //     // })
        //     // return instance.$mount().$el
        //     // this.popupInfo.name = feature.graphic.attributes.station_name
        //     // return this.$refs.customPopupRef
        //   }
        // },
        renderer: {
          // 值渲染器
          type: 'unique-value',
          field: 'type',
          defaultSymbol: { type: 'simple-line' },
          uniqueValueInfos: [
            {
              value: 0,
              symbol: {
                type: 'simple-line',
                color: 'red',
                width: 2
              }
            },
            {
              value: 1,
              symbol: {
                type: 'simple-line',
                color: 'orange',
                width: 2
              }
            },
            {
              value: 2,
              symbol: {
                type: 'simple-line',
                color: 'green',
                width: 2
              }
            }
          ]
        }
      })
      this.map = new Map({
        basemap: {
          baseLayers: [
            this.JL1ImageLayer,
            this.tdtImageNoteLayer
          ]
        }
      })
      this.view = new MapView({
        container: 'map',
        map: this.map,
        // 约束
        constraints: {
          minZoom: 11,
          maxZoom: 18
        },
        // popup: new Popup({}),
        // zoom: 10,
        // scale: 144447.638572,
        ui: {
          components: []
        }
      })
      this.view.popup.autoOpenEnabled = false

      const targetPoint = new Point({
        latitude: 31.31583431501697,
        longitude: 110.79744352250614
      })
      this.view.goTo({
        target: targetPoint,
        zoom: 12
      })

      this.view.when(() => {
        this.view.map.add(this.lineLayer)
      })

      this.view.on('click', (event) => {
        // console.log(this.view.center)
        // console.log(this.view)

        this.view.hitTest(event).then(response => {
          const results = response.results
          if(results.length > 0) {
            const ret = results[0]
            console.log(ret)
            this.showPopup(ret)
          }
        })
        
      })
    },
    showPopup(ret) {
      console.log(ret)
      const attributes = ret.graphic.attributes
      let p = new lineComponentClass({
        propsData: {
          name: attributes.station_name,
          list: [
            '长度：' + (attributes.length || '-'),
            '编码：' + (attributes.code || '-'),
            '材质：' + (attributes.material || '-'),
            '管径：' + (attributes.diameter || '-'),
            '深度：' + (attributes.deep || '-'),
          ]
        }
      })
      p.$mount()

      this.view.popup.open({
        location: ret.mapPoint,
        content: p.$el
      })
    },
    // 切换底图
    changeBase() {
      this.isSatellite = !this.isSatellite
      if(this.isSatellite) {
        this.JL1ImageLayer.visible = true
        this.tdtImageNoteLayer.visible = false
      } else {
        this.JL1ImageLayer.visible = false
        this.tdtImageNoteLayer.visible = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pipeNetwork {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  #map {
    width: 100%;
    height: 100%;
  }
  .baseBox {
    position: absolute;
    top: 20px;
    right: 480px;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .mapModeFull {
    right: 30px;
  }
  .customPopup {
    // position: absolute;
    display: none;
  }
}
</style>