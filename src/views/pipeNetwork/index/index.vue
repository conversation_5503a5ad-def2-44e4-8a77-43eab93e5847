<template>
  <div class="PipeNetworkIndex">
    <PipeMap :mapMode="isFull" />

    <!-- 面板控制 -->
    <div class="mapMode" @click="changeMapMode" :class="{ mapModeFull: isFull }" >
      <img v-if="isFull" src="@/assets/img/home/<USER>/full-active.png" alt="">
      <img v-else src="@/assets/img/home/<USER>/full.png" alt="">
    </div>

    <LeftPane v-if="!isFull" />
    <RightPane v-if="!isFull" />
    <div class="jumpImg" @click="handleJump"></div>
    
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="pipenetwork" />
  </div>
</template>

<script>
// import PipeMap from './layer'
import LeftPane from './leftPane'
import RightPane from './rightPane'

import PipeMap from './mapNew'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'PipeNetworkIndex',
  components: { PipeMap, LeftPane, RightPane, FloatingSideMenu },
  data() {
    return {
      isFull: false
    }
  },
  watch: {
    isFull(value) {
      const toolbar = document.querySelector('.pipeNetworkMap .cesium-viewer .cesium-viewer-toolbar')
      if(toolbar) {
        if(value) {
          toolbar.style.right = '5px'
        } else {
          toolbar.style.right = '465px'
        }
      }
    }
  },
  methods: {
    changeMapMode() {
      this.isFull = !this.isFull
    },
    handleJump() {
      this.$router.push({ name: 'Business' })
    }
  }
}
</script>

<style lang="scss" scoped>
.PipeNetworkIndex {
  height: calc(100vh - 84px);
  position: relative;
  .map {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
  .mapMode {
    position: absolute;
    top: 20px;
    left: 490px;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .mapModeFull {
    left: 30px;
  }
  .jumpImg {
    width: 100px;
    height: 96px;
    background-image: url('~@/assets/img/pipe_network/jumpImg.png');
    background-size: 100%;
    position: absolute;
    right: 490px;
    bottom: 20px;
    cursor: pointer;
  }
}
</style>
