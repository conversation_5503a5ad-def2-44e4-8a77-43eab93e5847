<template>
  <div class="rightPane app-container">
    <div class="pipeNetworkInfo screen-common card">
      <div class="title"><span>巡检任务</span></div>
      <div class="pipeNetworkBox common-card" style="padding-bottom: 0px;">
        <div class="textInfo">
          <div class="textInfoItem" v-for="item, index in textInfo" :key="index">
            <div class="value">
              <span>{{ item.value }}</span>
              <span style="font-size: 12px;">
                <i class="el-icon-bottom" style="color: #D33803;font-weight: bold;" v-if="!item.up"></i>
                <i class="el-icon-top" style="color: #69FF00;font-weight: bold;" v-else></i>
                <span>{{ item.rate }}%</span>
              </span>
            </div>
            <div style="font-size: 14px;">{{ item.label }}</div>
          </div>
        </div>

        <div class="lineChart">
          <div class="lineTitle iconTitle">近1年巡检任务统计</div>
          <PipeLengthLine height="160px" :xAxisData="xAxisData" :seriesData="lineData" />
        </div>
      </div>
    </div>

    <div class="bottom screen-common mt-10 card">
      <div class="title">
        <span>管线年龄统计</span>
      </div>
      <div class="common-card chartBox">
        <div style="height: 40%;">
          <PipeLengthBar :xAxisData="xAxisData1" :seriesData="lineData1" />
        </div>        
        <div style="padding: 0 20px;height: 40%;">
          <div class="iconTitle">高龄管网</div>
          <div class="listTable">
            <div class="listHeader" style="text-align: center;">
              <span>名称</span>
              <span>所在位置</span>
              <span>管龄</span>
              <span>长度</span>
            </div>
            <div class="listItem" v-for="item in pipeAgeList" :key="item.id">
              <div>{{ item.name }}</div>
              <div>{{ item.villageName }}</div>
              <div>{{ item.age }}</div>
              <div>{{ item.length1 }}km</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PipeLengthLine from './chart/pipeLengthLine'
import PipeLengthBar from './chart/pipeLengthBar'

import { getPipeNetworkTypeLength } from '@/api/pipenetwork/screen'
import { getPipeNetworkPage } from '@/api/pipenetwork'
import { Decimal } from 'decimal.js'

export default {
  components: { PipeLengthLine, PipeLengthBar },
  data() {
    return {
      textInfo: [
        {
          label: '总任务',
          value: 128,
          rate: 0.3,
          up: false
        },
        {
          label: '已完成',
          value: 94,
          rate: 1.24,
          up: true
        },
        {
          label: '未完成',
          value: 34,
          rate: 0.5,
          up: false
        }
      ],
      xAxisData: ['22-11', '22-12', '23-1', '23-2', '23-3', '23-4', '23-5', '23-6', '23-7', '23-8', '23-9','23-10'],
      lineData: [100, 200, 300, 200, 400, 600, 300, 455, 300, 450, 360, 500],
      // 管线年龄统计
      xAxisData1: [],
      lineData1: [],
      pipeAgeList: [ ]
    }
  },
  mounted() {
    this.getData()
    this.getPipeAgeList()
  },
  methods: {
    getData() {
      getPipeNetworkTypeLength().then(res => {
        // console.log(res)
        const { status, data } = res
        if(status === 200) {
          const allLength = data.allLength
          // 管线年龄统计
          this.xAxisData1 = data.pave.map(item => {
            return item[0] + '年'
          })
          this.lineData1 = data.pave.map(item => {
            return Decimal(allLength).mul(item[1]).div(1000).floor().toNumber()
          })
        }
      })
    },
    getPipeAgeList() {
      const payload = {
        pageNo: 1,
        pageSize: 5,
        orderBy: 'paveTime',
        asc: true
      }
      getPipeNetworkPage(payload).then(res => {
        // console.log(res)
        if(res.status == 200) {
          this.pipeAgeList = res.data.map(item => {
            return {
              ...item,
              // 计算管龄
              age: Math.floor(item.pipeAge / 365) + '年' + (item.pipeAge % 365) + '天',
              length1: Decimal(Number(item.length)).div(1000)
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.rightPane {
  position: absolute;
  width: 430px;
  height: 100%;
  right: 30px;
  color: #ffffff;
  .pipeNetworkInfo {
    height: 38%;
    .pipeNetworkBox {
      // height: 300px;
      padding: 20px;
      .textInfo {
        display: flex;
        justify-content: space-between;
        .textInfoItem {
          width: 120px;
          // text-align: center;
          background-color: rgba(17, 62, 102, 0.5);
          border-radius: 5px;
          padding: 10px;
          .value {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 20px;
            font-weight: bold;
          }
        }
      }
      .lineChart {        
        .lineTitle {
          font-size: 14px;
          line-height: 1;
          padding: 10px;
        }
      }
    }
  }
  .mt-10 {
    margin-top: 10px;
  }

  .iconTitle::before {
    display: inline-block;
    content: '';
    width: 16px;
    height: 16px;
    margin-right: 5px;
    background-image: url('../../../assets/img/icon.png');
  }

  .bottom {
    // height: calc(100% - 355px);
    height: 62%;
    .chartBox {
      height: calc(100% - 35px);
      .listHeader {
        font-size: 14px;
        background-image: url("../../../assets/img/table.png");
        background-size: 100%;
        height: 34px;
        display: flex;
        align-items: center;
        span {
          flex: 1;
        }
      }
      .listItem {
        font-size: 12px;
        display: flex;
        align-items: center;
        height: 30px;
        background-color: rgba(2,50,128,0.23);
        border: 1px solid #023280;
        margin-top: 5px;
        div {
          text-align: center;
          flex: 1;
        }
      }
      .sectionBox {
        height: 50%;
      }
    }
  }
  .card {
    backdrop-filter: blur(6px);
  }
}
</style>
