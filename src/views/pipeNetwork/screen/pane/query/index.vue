<template>
  <div class="query">
    <div class="top">
      <span>名称：</span>
      <el-input v-model="name" size="mini" style="width: 200px;" class="mr-10"></el-input>
      <el-button type="primary" size="mini" @click="getData()">查询</el-button>
    </div>
    <el-table
      :data="pipeNetworkList"
      height="405px"
      size="mini"
      @row-click="rowClick"
    >
      <el-table-column label="名称" prop="name"></el-table-column>
      <el-table-column label="编号" prop="code"></el-table-column>
      <el-table-column label="所在地" prop="villageName"></el-table-column>
      <el-table-column label="管径(米)" prop="diameter"></el-table-column>
      <el-table-column label="材质" prop="material"></el-table-column>
      <el-table-column label="管龄" prop="pipeAge"></el-table-column>
      <el-table-column label="长度(米)" prop="length"></el-table-column>
      <el-table-column label="埋设深度(米)" prop="deep"></el-table-column>
      <el-table-column label="设计使用年限(年)" prop="designYear"></el-table-column>
      <el-table-column label="铺设日期" prop="paveTime" width="200px"></el-table-column>
    </el-table>

    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="pageInfo.pageNo"
      :page-sizes="[10, 20, 30, 40]"
      :page-size.sync="pageInfo.pageSize"
      layout="prev, pager, next"
      style="margin-top: 10px;"
      :total="pageInfo.total">
    </el-pagination>
  </div>
</template>

<script>
import { getPipeNetworkPage } from '@/api/pipenetwork'

export default {
  inheritAttrs: false,
  data() {
    return {
      name: '',
      pipeNetworkList: [],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getPipeNetworkPage({
        name: this.name,
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize
      }).then(res => {
        const { status, count, data } = res
        if(status === 200) {
          this.pipeNetworkList = data
          this.pageInfo.total = count
        }
      })
    },
    rowClick(row) {
      this.$emit('emitPoint', row)
    },
    handleSizeChange() {
      this.pageInfo.pageNo=1,
      this.getData()
    },
    handleCurrentChange() {
      this.getData()
    }
  },
  computed: {
    show: {
      get: function() {
        return this.$store.state.app.pipeNetworkMenuActive === 1
      },
      set: function() {
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.query {
  color: #fff;
  padding: 20px;
  position: absolute;
  top: 50px;
  left: 10%;
  width: 80%;
  background-color: rgba(0, 0, 0, 0.5);
  .top {
    margin-bottom: 20px;
  }
  .mr-10 {
    margin-right: 10px;
  }
}
</style>
