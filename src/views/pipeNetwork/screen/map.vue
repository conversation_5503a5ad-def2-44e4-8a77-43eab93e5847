<template>
  <div id="pipemap"></div>
</template>

<script>
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import Ground from "@arcgis/core/Ground";
import WMSLayer from "@arcgis/core/layers/WMSLayer";
import Graphic from "@arcgis/core/Graphic";
import WFSLayer from "@arcgis/core/layers/WFSLayer";
import Vue from "vue";
import TileInfo from "@arcgis/core/layers/support/TileInfo";
import WebTileLayer from "@arcgis/core/layers/WebTileLayer";
import GroupLayer from "@arcgis/core/layers/GroupLayer";
import Polygon from '@arcgis/core/geometry/Polygon';
import Point from '@arcgis/core/geometry/Point';
import Draw from '@arcgis/core/views/draw/Draw';
export default {
  data() {
    return {
      map: null,
      view: null,
      imglayer: null,
      pressure: null,
      geourl: "",
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.createmap();
  },
  methods: {
    createmap() {
      this.fenxian = new WMSLayer({
        url: this.geourl + "/geoserver/xschina/wms",
        id: "blue",
      });
      this.shuixi = new WMSLayer({
        url: this.geourl + "/geoserver/xslabel/wms",
        id: "blue",
      });
      this.cun = new WMSLayer({
        url: this.geourl + "/geoserver/cun/wms",
        id: "blue",
      });
      this.water1 = new WMSLayer({
        url: this.geourl + "/geoserver/water1/wms",
        id: "blue",
      });
      this.water2 = new WMSLayer({
        url: this.geourl + "/geoserver/wate2/wms",
        id: "blue",
      });
      let tileInfo = new TileInfo({
        dpi: 90.71428571427429, // 切片方案的每英寸点数（即像素）。
        size: 256, // 设置每个瓷砖的高度和宽度为[256, 256]像素。
        origin: {
          // 切片方案的原点。
          type: "point",
          x: -180,
          y: 90,
          spatialReference: { wkid: 4326 },
        },
        spatialReference: { wkid: 4326 },
        lods: [
          // 定义平铺方案的详细级别数组。
          {
            level: 2,
            levelValue: 2,
            resolution: 0.3515625,
            scale: 147748796.52937502,
          },
          {
            level: 3,
            levelValue: 3,
            resolution: 0.17578125,
            scale: 73874398.264687508,
          },
          {
            level: 4,
            levelValue: 4,
            resolution: 0.087890625,
            scale: 36937199.132343754,
          },
          {
            level: 5,
            levelValue: 5,
            resolution: 0.0439453125,
            scale: 18468599.566171877,
          },
          {
            level: 6,
            levelValue: 6,
            resolution: 0.02197265625,
            scale: 9234299.7830859385,
          },
          {
            level: 7,
            levelValue: 7,
            resolution: 0.010986328125,
            scale: 4617149.8915429693,
          },
          {
            level: 8,
            levelValue: 8,
            resolution: 0.0054931640625,
            scale: 2308574.9457714846,
          },
          {
            level: 9,
            levelValue: 9,
            resolution: 0.00274658203125,
            scale: 1154287.4728857423,
          },
          {
            level: 10,
            levelValue: 10,
            resolution: 0.001373291015625,
            scale: 577143.73644287116,
          },
          {
            level: 11,
            levelValue: 11,
            resolution: 0.0006866455078125,
            scale: 288571.86822143558,
          },
          {
            level: 12,
            levelValue: 12,
            resolution: 0.00034332275390625,
            scale: 144285.93411071779,
          },
          {
            level: 13,
            levelValue: 13,
            resolution: 0.000171661376953125,
            scale: 72142.967055358895,
          },
          {
            level: 14,
            levelValue: 14,
            resolution: 8.58306884765625e-5,
            scale: 36071.483527679447,
          },
          {
            level: 15,
            levelValue: 15,
            resolution: 4.291534423828125e-5,
            scale: 18035.741763839724,
          },
          {
            level: 16,
            levelValue: 16,
            resolution: 2.1457672119140625e-5,
            scale: 9017.8708819198619,
          },
          {
            level: 17,
            levelValue: 17,
            resolution: 1.0728836059570313e-5,
            scale: 4508.9354409599309,
          },
          {
            level: 18,
            levelValue: 18,
            resolution: 5.3644180297851563e-6,
            scale: 2254.4677204799655,
          },
          {
            level: 19,
            levelValue: 19,
            resolution: 2.68220901489257815e-6,
            scale: 1127.23386023998275,
          },
          {
            level: 20,
            levelValue: 2,
            resolution: 1.341104507446289075e-6,
            scale: 563.616930119991375,
          },
        ],
      });
      // 天地图影像底图
      let tiledLayer_img = new WebTileLayer({
        urlTemplate:
          "http://{subDomain}.tianditu.gov.cn/img_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=aaa61cb0d504a1ed32a4846a3f12009c",
        subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
        tileInfo: tileInfo,
        spatialReference: { wkid: 4326 },
        id: "image",
        visible: true,
      });
      // 天地图影像标注
      let tiledLayer_img_poi = new WebTileLayer({
        urlTemplate:
          "http://{subDomain}.tianditu.gov.cn/cia_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=aaa61cb0d504a1ed32a4846a3f12009c",
        subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
        tileInfo: tileInfo,
        spatialReference: { wkid: 4326 },
        id: "image",
        visible: true,
      });
      // let grouplayer = new GroupLayer({
      //   title: 'Group Layer',
      //   layers: [tiledLayer_img, tiledLayer_img_poi],
      //   visible: false
      // })
      this.imglayer = new WFSLayer({
        url: this.geourl + "/geoserver/pipe_line/wms",
        outFields:"*",
        renderer: {
          type: "simple",
          symbol: {
            type: "simple-line",
            color: '#00ED21',
            width: "6px",
          },
        },
      });
      this.map = new Map({
        ground: new Ground({
          layers: [],
          surfaceColor: "transparent",
          opacity: 0,
        }),
        layers: [],
      });
      this.view = new MapView({
        container: "pipemap",
        map: this.map,
        center: [110.76091, 31.230124],
        zoom: 14,
        background: {
          type: "color",
          color: [255, 252, 244, 0],
        },
        extent: {
          xmax: 110.78310314022976,
          xmin: 110.71962062546056,
          ymax: 31.261356703995727,
          ymin: 31.233123690848377,
        },
        spatialReference: {
          wkid: 4326,
        },
      });
      this.view.ui.remove("attribution");
      this.view.ui.empty("top-left");

      // this.view.map.add(this.fenxian);
      // this.view.map.add(this.shuixi);
      // this.view.map.add(this.cun);
      // this.view.map.add(this.water1);
      // this.view.map.add(this.water2);
      this.map.add(tiledLayer_img)
      // this.map.add(tiledLayer_img_poi)
      this.view.map.add(this.imglayer);
      this.view.on("click", async (e) => {
        console.log(e);
        this.view.hitTest(e).then((res) => {
          console.log(res);
        });
      });
      // const polyline = {
      //   type: "polyline", // autocasts as new Polyline()
      //   paths: [
      //     [110.5961660641468, 31.25792129580506],
      //     [110.71763150871472, 31.241282193809454],
      //   ],
      // };
      // const polyline2 = {
      //   type: "polyline", // autocasts as new Polyline()
      //   paths: [
      //     [110.72927888011164, 31.4201525402622],
      //     [110.8191300308879, 31.423480360661323],
      //   ],
      // };
      // const polyline3 = {
      //   type: "polyline", // autocasts as new Polyline()
      //   paths: [
      //     [110.88818230416966, 31.29535927529517],
      //     [110.87487102257317, 31.222147226514508],
      //   ],
      // };
      // const polyline4 = {
      //   type: "polyline", // autocasts as new Polyline()
      //   paths: [
      //     [110.65310507117579, 31.250899594762906],
      //     [110.67140808337095, 31.279186068155433],
      //   ],
      // };
      // const polyline5 = {
      //   type: "polyline", // autocasts as new Polyline()
      //   paths: [
      //     [110.6189949120848, 31.254227415162024],
      //     [110.62814641818238, 31.22843680706884],
      //   ],
      // };
      // const lineSymbol = {
      //   type: "simple-line", // autocasts as SimpleLineSymbol()
      //   color: [226, 119, 40],
      //   width: 4,
      // };
      // const lineAtt = {
      //   编号: "1-2-G-Y-1",
      //   管材: "PE",
      //   直径: "40",
      //   长度: "60",
      //   流量: "0.42",
      // };
      // const lineAtt2 = {
      //   编号: "1-2-G-Y-2",
      //   管材: "PE",
      //   直径: "40",
      //   长度: "60",
      //   流量: "0.8",
      // };
      // const lineAtt3 = {
      //   编号: "1-2-G-Y-3",
      //   管材: "PE",
      //   直径: "40",
      //   长度: "60",
      //   流量: "1.1",
      // };
      // const lineAtt4 = {
      //   编号: "1-2-G-Y-4",
      //   管材: "PE",
      //   直径: "40",
      //   长度: "60",
      //   流量: "0.2",
      // };
      // const lineAtt5 = {
      //   编号: "1-2-G-Y-5",
      //   管材: "PE",
      //   直径: "40",
      //   长度: "60",
      //   流量: "0.25",
      // };
      // const polylineGraphic = new Graphic({
      //   geometry: polyline,
      //   symbol: lineSymbol,
      //   attributes: lineAtt,
      //   popupTemplate: {
      //     title: "{编号}",
      //     content: [
      //       {
      //         type: "fields",
      //         fieldInfos: [
      //           {
      //             fieldName: "编号",
      //           },
      //           {
      //             fieldName: "管材",
      //           },
      //           {
      //             fieldName: "直径",
      //           },
      //           {
      //             fieldName: "长度",
      //           },
      //           {
      //             fieldName: "流量",
      //           },
      //         ],
      //       },
      //     ],
      //   },
      // });
      // const polylineGraphic2 = new Graphic({
      //   geometry: polyline2,
      //   symbol: lineSymbol,
      //   attributes: lineAtt2,
      //   popupTemplate: {
      //     title: "{编号}",
      //     content: [
      //       {
      //         type: "fields",
      //         fieldInfos: [
      //           {
      //             fieldName: "编号",
      //           },
      //           {
      //             fieldName: "管材",
      //           },
      //           {
      //             fieldName: "直径",
      //           },
      //           {
      //             fieldName: "长度",
      //           },
      //           {
      //             fieldName: "流量",
      //           },
      //         ],
      //       },
      //     ],
      //   },
      // });
      // const polylineGraphic3 = new Graphic({
      //   geometry: polyline3,
      //   symbol: lineSymbol,
      //   attributes: lineAtt3,
      //   popupTemplate: {
      //     title: "{编号}",
      //     content: [
      //       {
      //         type: "fields",
      //         fieldInfos: [
      //           {
      //             fieldName: "编号",
      //           },
      //           {
      //             fieldName: "管材",
      //           },
      //           {
      //             fieldName: "直径",
      //           },
      //           {
      //             fieldName: "长度",
      //           },
      //           {
      //             fieldName: "流量",
      //           },
      //         ],
      //       },
      //     ],
      //   },
      // });
      // const polylineGraphic4 = new Graphic({
      //   geometry: polyline4,
      //   symbol: lineSymbol,
      //   attributes: lineAtt4,
      //   popupTemplate: {
      //     title: "{编号}",
      //     content: [
      //       {
      //         type: "fields",
      //         fieldInfos: [
      //           {
      //             fieldName: "编号",
      //           },
      //           {
      //             fieldName: "管材",
      //           },
      //           {
      //             fieldName: "直径",
      //           },
      //           {
      //             fieldName: "长度",
      //           },
      //           {
      //             fieldName: "流量",
      //           },
      //         ],
      //       },
      //     ],
      //   },
      // });
      // const polylineGraphic5 = new Graphic({
      //   geometry: polyline5,
      //   symbol: lineSymbol,
      //   attributes: lineAtt5,
      //   popupTemplate: {
      //     title: "{编号}",
      //     content: [
      //       {
      //         type: "fields",
      //         fieldInfos: [
      //           {
      //             fieldName: "编号",
      //           },
      //           {
      //             fieldName: "管材",
      //           },
      //           {
      //             fieldName: "直径",
      //           },
      //           {
      //             fieldName: "长度",
      //           },
      //           {
      //             fieldName: "流量",
      //           },
      //         ],
      //       },
      //     ],
      //   },
      // });
      // this.view.graphics.add(polylineGraphic);
      // this.view.graphics.add(polylineGraphic2);
      // this.view.graphics.add(polylineGraphic3);
      // this.view.graphics.add(polylineGraphic4);
      // this.view.graphics.add(polylineGraphic5);
    },
  },
};
</script>
<style lang="scss">
.esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0.3);
}
.esri-view-width-medium .esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0.3);
}
.esri-popup__header {
  background-color: rgba(4, 15, 45, 0.3) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  background-color: rgba(4, 15, 45, 0) !important;
}
.esri-popup__icon,
.esri-icon-close {
  color: #fff !important;
}
.esri-popup__content {
  margin: 0;
}
.esri-feature {
  background-color: rgba(4, 15, 45, 0.3);
  padding: 0 15px 12px;
}
.esri-widget__table {
  color: #fff;
}
.esri-popup__header-title {
  background-color: rgba(4, 15, 45, 0) !important;
  color: #fff;
}
.esri-popup__pointer-direction {
  background-color: rgba(4, 15, 45, 0.3);
}

.esri-popup__footer {
  display: none;
}
.esri-popup__feature-menu {
  display: none;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
.esri-view-surface {
  outline: none !important;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: auto 0px Highlight !important;
  outline: auto 0px -webkit-focus-ring-color !important;
}
[class*="esri-popup--is-docked-top-"] .esri-popup__footer,
[class*="esri-popup--aligned-bottom-"] .esri-popup__footer {
  border-bottom: solid 1px #6e6e6e4d;
}
.esri-popup__header {
  border-top-left-radius: 5px !important;
  border-top-right-radius: 5px;
  background-color: #fff;
  color: #fff;
}
.esri-popup--shadow {
  box-shadow: 0 0 0 0 rgb(155, 155, 155) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  outline: none !important;
}
.esri-ui .esri-popup {
  border-radius: 5px !important;
}
.esri-popup {
  position: absolute !important;
}
.esri-popup__button {
  background-color: transparent !important;
  outline: none;
}
</style>
<style scoped lang="scss">
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
#pipemap {

}
</style>