<template>
  <div class="businessMap">
    <div id="mars3dContainer" class="mars3d-container"> 
    <div class="infoview" v-if="currentBusinessItem === 3 || currentBusinessItem === 4">
      <table class="mars-table">
        <tr class="undergroundAttr" v-if="currentBusinessItem === 3">
          <td>
            <!-- <el-checkbox @change="changeUndergroundStatue">开启地下模式</el-checkbox> -->
            <!-- <el-slider v-model="undergroundRate" :format-tooltip="fixUndergroundRate" :disabled="undergroundDisabled" ></el-slider> -->
          </td>
        </tr>
        <tr class="kaiwaAttr" v-if="currentBusinessItem === 3">
          <td>
            <el-checkbox @change="changekaiwaStatue">是否挖坑</el-checkbox>
            深度：<input id="txtHeight" type="number" value="30" min="-500" step="1" max="999" class="form-control" style="width: 100px" v-model="kaiwaRate" />（米）
            <el-button type="primary" @click="kaiwaDraw">多边形范围挖坑</el-button>
            <el-button type="primary" @click="clearWK">清除挖坑</el-button>
          </td>
        </tr>
        <tr class="addPipe">
          <el-button type="primary" icon="el-icon-search" size="medium" @click="drawPipe" v-if="currentBusinessItem === 4">新增管网</el-button>
        </tr>
      </table>
      <el-dialog
        title="管网新增"
        :visible.sync="addPipePopupFlag"
        width="20%">
        <span>管网名称:<input v-model="pipeName" /></span><br><br>
        <span>管网code:<input v-model="pipeCode" /></span><br><br>
        <span>管网深度:<input v-model="pipeDeep" /></span><br><br>
        <span slot="footer" class="dialog-footer">
          <el-button @click="addPipePopupFlag = false">取 消</el-button>
          <el-button type="primary" @click="submitPipe">提 交</el-button>
        </span>
      </el-dialog>
    </div>
    </div>
    
  </div>
</template>
  
<script>
import { mapGetters } from 'vuex'
import { getPipeByHengDuanMian, addPipeNetwork } from '@/api/pipenetwork/index'
export default {
  props: {
    point: {
      type: Object
    },
    checkedList: {
      type: Array
    },
    currentBusiness: {
      type: Number
    },
  },
  data() {
    return {
      // 高亮
      graphicOne: null,
      highlightLayer: null,
      currentBusinessItem: 3,
      deepHeight: 100,
      // map: null,
      tmd: 0.5,
      opean: true,
      mapOptions: {
        scene: {
          //默认视角参数
          center: {
            lat: 31.24575,
            lng: 110.7086,
            alt: 55000,
            heading: 0,
            pitch: -90,
          },
          cameraController: {
            // 滚轮缩小最大高度
            maximumZoomDistance: 200000,
            minimumZoomDistance: 1000
          },
          orderIndependentTranslucency: false,
          contextOptions: { webgl: { alpha: true } }, // 允许透明，只能Map初始化传入 [关键代码]
          showMoon: false,
          showSkyBox: false,
          showSkyAtmosphere: false,
          fog: false,
          globe: {
              depthTestAgainstTerrain: true // 开启深度检测
          }
        },
        control: {
          // baseLayerPicker: true, // basemaps底图切换按钮
          homeButton: true, // 视角复位按钮
          sceneModePicker: true, // 二三维切换按钮
          // navigationHelpButton: true, // 帮助按钮
          fullscreenButton: true, // 全屏按钮
          // contextmenu: { hasDefault: true } // 右键菜单
          terrainProviderViewModels: []
        },
        terrain: {
        url: 'http://data.mars3d.cn/terrain',
        show: true
        },
        basemaps: [
          { "id": 10, "name": "地图底图", "type": "group" },
          {
            "pid": 10,
            "name": "天地图影像",
            "icon": require("../../../assets/img/basemaps/tdt_img.png"),
            "type": "group",
            "layers": [
              { "name": "底图", "type": "tdt", "layer": "img_d" },
              { "name": "注记", "type": "tdt", "layer": "img_z" }
            ],
            "show": true
          },
          {
            "pid": 10,
            "name": "天地图电子",
            "icon": require("../../../assets/img/basemaps/tdt_vec.png"),
            "type": "group",
            "layers": [
              { "name": "底图", "type": "tdt", "layer": "vec_d" },
              { "name": "注记", "type": "tdt", "layer": "vec_z" }
            ]
          },
          {
            "pid": 10,
            "name": "高德影像",
            "type": "group",
            "icon": require("../../../assets/img/basemaps/gaode_img.png"),
            "layers": [
              { "name": "底图", "type": "gaode", "layer": "img_d" },
              { "name": "注记", "type": "gaode", "layer": "img_z" }
            ]
          },
          {
            "id": 2017,
            "pid": 10,
            "name": "暗色底图",
            "type": "gaode",
            "icon": require("../../../assets/img/basemaps/blackMarble.png"),
            "layer": "vec",
            "invertColor": true,
            "filterColor": "#4e70a6",
            "brightness": 0.6,
            "contrast": 1.8,
            "gamma": 0.3,
            "hue": 1,
            "saturation": 0
          }
        ],
      },
      param1: 1,
      underground: null,
      terrainPlanClip: null,
      terrainClip: null,
      eventTarget: new mars3d.BaseClass(),
      // queryMapserver: null,
      // valveLayer: null,
      // waterMeterLayer: null,
      // insmFlowLayer: null,
      // insmPressureLayer: null,
      // pumpLayer: null,
      // insmLevelLayer: null,
      // gongshiuTileLayer: null,
      // yinshuiTileLayer: null,
      // waterFactoryLayer: null,
      // storagePondLayer: null,
      underground: null,
      undergroundRate: 60,
      undergroundDisabled: true,
      terrainPlanClip: null,
      kaiwaRate: 100,
      geourl : '',
      addPipePopupFlag: false,
      pipeName: '',
      pipeCode: '',
      pipeDeep: 0,
      pipeCoordinate:[],
      zongFlag : false,
      zongList: [],
      queryArea: [],
      hengduanmiandixing: [],
      hengduanmianjiaodian: [],
    };
  },
  watch: {
    point: {
      handler(val) {
        if (val && val.coordinateList) {
          this.viewflay(val.coordinateList[0].x, val.coordinateList[0].y)
        }
      }
    },
    currentBusiness: {
      handler(val) {
        console.log(val)
        // this.currentBusinessItem = val
        if (val == 1 || val == 2 || val == 4) {
          if (this.valveLayer) {
            this.valveLayer.show = false
          }
          if (this.waterMeterLayer) {
            this.waterMeterLayer.show = false
          }
          if (this.insmFlowLayer) {
            this.insmFlowLayer.show = false
          }
          if (this.insmPressureLayer) {
            this.insmPressureLayer.show = false
          }
          if (this.pumpLayer) {
            this.pumpLayer.show = false
          }
          if (this.insmLevelLayer) {
            this.insmLevelLayer.show = false
          }
          if (this.gongshiuTileLayer) {
            this.gongshiuTileLayer.show = true
          }
          if (this.yinshuiTileLayer) {
            this.yinshuiTileLayer.show = true
          }
          if (this.storagePondLayer) {
            this.storagePondLayer.show = false
          }
          if (this.waterFactoryLayer) {
            this.waterFactoryLayer.show = false
          }
        }
      }
    },
    '$route.query.type': {
      handler(val) {
        console.log(val)
        this.currentBusinessItem = Number(val)
      },
      immediate: true
    },
    // 图层显示状态
    checkedList: {
      handler(list) {
        this.closeLayer()
        list.forEach(item => {
          if (item == 'pipe1' && this.valveLayer) {
            this.valveLayer.show = true
          }
          if (item == 'pipe2' && this.waterMeterLayer) {
            this.waterMeterLayer.show = true
          }
          if (item == 'pipe3' && this.insmFlowLayer) {
            this.insmFlowLayer.show = true
          }
          if (item == 'pipe4' && this.insmPressureLayer) {
            this.insmPressureLayer.show = true
          }
          if (item == 'pipe5' && this.pumpLayer) {
            this.pumpLayer.show = true
          }
          if (item == 'pipe6' && this.insmLevelLayer) {
            this.insmLevelLayer.show = true
          }
          if (item == 'pipe7' && this.gongshiuTileLayer) {
            this.gongshiuTileLayer.show = true
          }
          if (item == 'pipe8' && this.yinshuiTileLayer) {
            this.yinshuiTileLayer.show = true
          }
          if (item == 'pipe9' && this.storagePondLayer) {
            this.storagePondLayer.show = true
          }
          if (item == 'pipe10' && this.waterFactoryLayer) {
            this.waterFactoryLayer.show = true
          }
        });
      }
    }
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.initMap();

    // 监听webgl
    // let canvas=document.getElementById("mars3dContainer");
    // canvas = WebGLDebuqUtils.makeLostContextSimulatingCanvas(canvas);
    // canvas.loseContextInNCalls(5);
    // canvas.addEventListener("webglcontextlost", handleContextLost, false);
    // canvas.addEventListener("webqlcontextrestored",handleContextRestored, false);
    // let gl= canvas.getContext("webgl")
  },
  beforeDestroy() {
    if(this.map) {
      this.map.destroy()
    }
  },
  methods: {
    qiehengduanmian() {
      this.map.graphicLayer.startDraw({
        type: "polyline",
        style: {
          color: "#55ff33",
          width: 3
        },
        success: (graphic) => {
          // 地形
          this.hengduanmiandixing = []
          this.hengduanmianjiaodian = []
          const positions = graphic.positionsShow
          const arrLine = mars3d.PolyUtil.interPolyline({
            scene: this.map.scene,
            positions,
            splitNum: 300
          })
          const b = mars3d.PointTrans.cartesian2lonlat(positions[0], false)
          const bp = new mars3d.LngLatPoint(b[0], b[1])
          arrLine.forEach(item => {
            const a = mars3d.PointTrans.cartesian2lonlat(item, false)
            const ap = new mars3d.LngLatPoint(a[0], a[1])
            const h = this.map.getHeight(ap)
            // const d = Math.round(Cesium.Cartesian3.distance(ap, bp))
            const list = [ap, bp]
            const d = mars3d.MeasureUtil.getDistance(list, false)
            const d_h = []
            d_h.push(d)
            d_h.push(h)
            this.hengduanmiandixing.push(d_h)
          })
          // console.log("横断面地形", this.hengduanmiandixing)

          // 交点
          const data =  [
            {x: graphic.coordinates[0][0], y:graphic.coordinates[0][1]},
            {x: graphic.coordinates[1][0], y:graphic.coordinates[1][1]}
          ]
          getPipeByHengDuanMian(data).then(res => {
            if (res.status == 200) {
              res.data.forEach(item => {
                const d_h_c = []
                const p = item.point
                const d = item.distance
                const c = item.code
                const h = this.map.getHeight(new mars3d.LngLatPoint(p.x, p.y))
                d_h_c.push(d)
                d_h_c.push(h)
                d_h_c.push(c)
                this.hengduanmianjiaodian.push(d_h_c)
              })
              // console.log("横断面交点", this.hengduanmianjiaodian)
              this.$emit('xEnd', this.hengduanmiandixing, this.hengduanmianjiaodian)
            }
          })
          // graphic.remove(true)
        }
      })
    },
    initMap() {
      this.highlightLayer = new mars3d.layer.GraphicLayer()

      this.map = new mars3d.Map("mars3dContainer", this.mapOptions);
      this.map.container.style.backgroundColor = "#546a53"; // 背景色
      // 地下模式
      this.underground = new mars3d.thing.Underground({
          alpha: 0.9,
          enabled: true
      })
      this.map.addThing(this.underground)
      // 挖坑模式
      this.terrainPlanClip = new mars3d.thing.TerrainPlanClip({
          diffHeight: 100,
          image: require("../../../assets/img/pipe_network/poly-stone.jpg"),
          imageBottom: require("../../../assets/img/pipe_network/poly-soil.jpg"),
          splitNum: 50
      })
      this.map.addThing(this.terrainPlanClip)

      this.map.on('load', () => {
        console.log('map load')
        this.showyinshuiLayer();
        this.showGongshuiLayer();
        this.map.addLayer(this.highlightLayer)
      })

      // this.showyinshuiLayer();
      this.showWaterMeter();
      this.showFlowInsm();
      this.showPressureInsm();
      this.showLevelInsm();
      // this.showGongshuiLayer();
      this.showValve();
      this.showWaterStoragePond();
      this.showWaterFactory();
      this.showPump();

    
      this.map.on(mars3d.EventType.wheel,  (event) => {
        let cameraView = this.map.getCameraView({simplify : true})
        // if (cameraView.alt < 75205) {
        //   this.waterFactoryLayer.show = true
        // }
        // if (cameraView.alt > 100000) {
        //   this.waterFactoryLayer.show = false
        // }
      })
    },
    submitPipe() {
      // 请求url 管网提交
      if (this.pipeCoordinate.length < 2) {
        this.clearPipeSubmitPopup()
        return
      }
      // this.pipeCoordinate.forEach(item => {
      //   const i = {
      //     x: item[0],
      //     y: item[1],
      //     z: item[2],
      //   }
      //   coordinate.push(i)
      // });
      let coordinate = this.pipeCoordinate.map(item => {
        return {
          x: item[0],
          y: item[1],
          z: 0,
        }

      }) 
      const data = {
        name: this.pipeName,
        code: this.pipeCode,
        deep: this.pipeDeep,
        coordinateList: coordinate
      }
      addPipeNetwork(data).then(res => {
        if (res.status == 200) {
          this.$message.success('新建管网成功')
        } else {
          this.$message.warning('新建管网失败')
        }
      })
      console.log(data, "完成提交")
      this.clearPipeSubmitPopup()
    },
    clearPipeSubmitPopup() {
      this.pipeName= '',
      this.pipeCode= '',
      this.pipeDeep= 0,
      this.addPipePopupFlag = false
      this.pipeCoordinate = []
    },
    showWaterFactory() {
      this.waterFactoryLayer = new mars3d.layer.GeoJsonLayer({
        name: "水厂",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Awater_factory&maxFeatures=50&outputFormat=application%2Fjson",
        show: false,
        zIndex: 100,
        symbol: {
          styleOptions: {
            clampToGround: true,
            image: require("../../../assets/img/pipe_network/pipe10.png"),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            label: {
              font_size: 15,
              text: "{name}",
              color: "#09FDFB",
              outline: true,
              outlineColor: "#000000",
              scaleByDistance: true,
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 10000000,
              distanceDisplayCondition_near: 100,
              setHeight: 10000,
            },
          }
        },
        popup: [
          { field: "name", name: "名称" },
        ],
      })
      this.map.addLayer(this.waterFactoryLayer)
    },
    showWaterStoragePond() {
      this.storagePondLayer = new mars3d.layer.GeoJsonLayer({
        name: "水池",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Awater_storage_pond&maxFeatures=50&outputFormat=application%2Fjson",
        show: false,
        zIndex: 100,
        symbol: {
          styleOptions: {
            clampToGround: true,
            image: require("../../../assets/img/pipe_network/pipe9.png"),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            label: {
              font_size: 15,
              text: "{name}",
              color: "#09FDFB",
              outline: true,
              outlineColor: "#000000",
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 10000000,
              distanceDisplayCondition_near: 100,
              setHeight: 10000,
            },
          }
        },
        // popup: [
        //   { field: "name", name: "名称" },
        // ],
      })
      this.storagePondLayer.bindPopup(function(event) {
        const attr = event.graphic.attr || {}
        return mars3d.Util.getTemplateHtml({ title: "矢量图层", template: `<div><div>{name}</div><button id="btnDetails">更多</button></div>`, attr })
      })
      this.map.addLayer(this.storagePondLayer)
      this.storagePondLayer.on(mars3d.EventType.popupOpen, (event) => {     
        const point = event.graphic;
        const btnDetails = document.querySelector("#btnDetails")
        if (btnDetails) {
          btnDetails.addEventListener("click", (e) => {
            this.$router.push({
              name: "HomeWaterFactory",
              query: {
                factory: point._name
              }
            })
          })
        }
        // this.jump(event);
      })
    },
    showValve() {
      this.valveLayer = new mars3d.layer.GeoJsonLayer({
        name: "阀门",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Avalve_info&maxFeatures=5000&outputFormat=application%2Fjson",
        show: false,
        zIndex: 300,
        symbol: {
          styleOptions: {
            clampToGround: true,
            label: {
              font_size: 15,
              text: "{name}",
              color: "#09FDFB",
              outline: true,
              outlineColor: "#000000",
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 10000000,
              distanceDisplayCondition_near: 100,
              setHeight: 10000,
            },
            scale: 0.4,
            image: require("../../../assets/img/pipe_network/pipe1.png"),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
        },
        popup: [
          { field: "name", name: "名称" },
        ],
      })
      this.map.addLayer(this.valveLayer)
    },
    showPump() {
      this.pumpLayer = new mars3d.layer.GeoJsonLayer({
        name: "泵站",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Awater_supply_pumping_station&maxFeatures=5000&outputFormat=application%2Fjson",
        show: false,
        zIndex: 300,
        symbol: {
          styleOptions: {
            clampToGround: true,
            label: {
              font_size: 15,
              text: "{name}",
              color: "#09FDFB",
              outline: true,
              outlineColor: "#000000",
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 10000000,
              distanceDisplayCondition_near: 100,
              setHeight: 10000,
            },
            scale: 0.45,
            image: require("../../../assets/img/pipe_network/pipe5.png"),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
        },
        popup: [
          { field: "name", name: "名称" },
        ],
      })
      this.map.addLayer(this.pumpLayer)
    },
    showLevelInsm() {
      this.insmLevelLayer = new mars3d.layer.GeoJsonLayer({
        name: "液位计",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aequipment&maxFeatures=50&outputFormat=application%2Fjson&cql_filter=insm_type=7",
        show: false,
        zIndex: 100,
        symbol: {
          styleOptions: {
            label: {
              font_size: 15,
              text: "{name}",
              color: "#09FDFB",
              outline: true,
              outlineColor: "#000000",
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 10000000,
              distanceDisplayCondition_near: 100,
              setHeight: 10000,
            },
            clampToGround: true,
            image: require("../../../assets/img/pipe_network/pipe6.png"),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
        },
        popup: [
          { field: "name", name: "名称" },
        ],
      })
      // this.insmLevelLayer.bindPopup(function(event) {
      //   const attr = event.graphic.attr || {}
      //   attr["名称"] = attr["name"]
      //   return mars3d.Util.getTemplateHtml({ title: "矢量图层", template: `
      //         <div class="grapopup">{name} m</div>
      //       `, attr })
      // })
      this.map.addLayer(this.insmLevelLayer)
    },
    showPressureInsm() {
      this.insmPressureLayer = new mars3d.layer.GeoJsonLayer({
        name: "压力计",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aequipment&maxFeatures=50&outputFormat=application%2Fjson&cql_filter=insm_type=2",
        show: false,
        zIndex: 100,
        symbol: {
          styleOptions: {
            label: {
              font_size: 15,
              text: "{name}",
              color: "#09FDFB",
              outline: true,
              outlineColor: "#000000",
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 10000000,
              distanceDisplayCondition_near: 100,
              setHeight: 10000,
            },
            clampToGround: true,
            image: require("../../../assets/img/pipe_network/pipe4.png"),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
        },
        popup: [
          { field: "name", name: "名称" },
        ],
      })
      this.map.addLayer(this.insmPressureLayer)
    },
    showFlowInsm() {
      this.insmFlowLayer = new mars3d.layer.GeoJsonLayer({
        name: "流量计",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aequipment&maxFeatures=50&outputFormat=application%2Fjson&cql_filter=insm_type=1",
        show: false,
        zIndex: 100,
        symbol: {
          styleOptions: {
            label: {
              font_size: 15,
              text: "{name}",
              color: "#09FDFB",
              outline: true,
              outlineColor: "#000000",
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 10000000,
              distanceDisplayCondition_near: 100,
              setHeight: 10000,
            },
            clampToGround: true,
            image: require("../../../assets/img/pipe_network/pipe3.png"),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
        },
        popup: [
          { field: "name", name: "名称" },
        ],
      })
      this.map.addLayer(this.insmFlowLayer)
    },
    showyinshuiLayer() {
      //  this.yinshuiTileLayer = new mars3d.layer.GeoJsonLayer({
      //   name: "引水管线",
      //   url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Atotal_pipe_network&maxFeatures=4000&outputFormat=application%2Fjson&cql_filter=supply=false",
      //   format: this.simplifyGeoJSON, // 用于自定义处理geojson
      //   graphicOptions : new mars3d.graphic.PolylinePrimitive({
      //       name:"{name}"
      //     }),
      //   symbol: {
      //     type: "polylineP",
      //     styleOptions: {
      //       clampToGround: true,
      //       width: 2,
      //       materialType: mars3d.MaterialType.LineFlow,
      //       materialOptions: {
      //         color: "#EBA827",
      //         // image: "img/textures/fence-line.png",
      //         speed: 10,
      //         repeat_x: 10,
      //       },
      //       // distanceDisplayCondition: true,
      //       // distanceDisplayCondition_far: 12000000,
      //       // distanceDisplayCondition_near: 100000,
      //       label: {
      //         font_size: 30,
      //         // text: "{layer}",
      //         color: "#ffffff",
      //         outline: true,
      //         outlineColor: "#000000",
      //         scaleByDistance: true,
      //         scaleByDistance_far: 60000000,
      //         scaleByDistance_farValue: 0.2,
      //         scaleByDistance_near: 1000000,
      //         scaleByDistance_nearValue: 1,
      //         distanceDisplayCondition: true,
      //         distanceDisplayCondition_far: 10000000,
      //         distanceDisplayCondition_near: 100000,
      //         setHeight: 10000,
      //       },
      //     },
      //   },
      //   popup: [
      //     { field: "name", name: "名称" },
      //     { field: "code", name: "编号" },
      //   ]
      // });
      // 改成加载wms图层
      this.yinshuiTileLayer = new mars3d.layer.WmsLayer({
        // url: "http://**************:8080/geoserver/img/wms",
        // url: "http://**************:8080/geoserver/xingshan/wms?service=WMS&version=1.1.0&request=GetMap&layers=xingshan%3Atotal_pipe_network&bbox=110.71158599853516%2C31.14643669128418%2C110.82113647460938%2C31.332233428955078&width=452&height=768&srs=EPSG%3A4326&styles=&format=application/openlayers",
        url: this.geourl + "/geoserver/xingshan/wms",
        layers: "xingshan:intake_network",
        parameters: {
          service: 'WMS',
          version: '1.1.0',
          request: 'GetMap',
          srs: 'EPSG:4326',
          // cql_filter: "type=0",
          transparent: true,
          format: 'image/png'
          // format: 'application/openlayers'
        },
        hasToGraphic: true,
        featureToGraphic: (feature, event) => {
          const data = feature.data
          return {
            // type: "polyline",
            symbol: {
              type: "polylineP",
              merge: true,
              styleOptions: {
                label: {
                  font_size: 15,
                  text: "{name},{length}m",
                  color: "#ffffff",
                  outline: true,
                  outlineColor: "#ffffff",
                  distanceDisplayCondition: true,
                  distanceDisplayCondition_far: 50000,
                  distanceDisplayCondition_near: 0,
                  setHeight: 10000,
                },  
              },
            },
          }
        },
        popup: [
          { field: "name", name: "名称" },
          { field: "length", name: "长度" },
          { field: "code", name: "编码" },
          { field: "material", name: "材质" },
          { field: "diameter", name: "管径" },
          { field: "deep", name: "深度" },
        ],
        // popup: "all",
        zIndex: 200,
        flyTo: true,
      })
      this.yinshuiTileLayer.on(mars3d.EventType.click, (event) => {
        // 高亮
        console.log(event)
        if(this.graphicOne) {
          this.highlightLayer.removeGraphic(this.graphicOne)
        }
        const coordinates = event.features[0].data.geometry.coordinates
        this.graphicOne = new mars3d.graphic.PolylineEntity({
          positions: coordinates,
          style: {
            color: "#2EFFE9", // 高亮颜色（黄色）
            width: 4, // 加粗线宽
            clampToGround: true, // 贴地
          }
        })
        this.highlightLayer.addGraphic(this.graphicOne)


        if(this.zongFlag) {
          this.zongList.push(event.features[0].data.properties.id)
        }
      })
      this.map.addLayer(this.yinshuiTileLayer);
    },
    closeLayer() {
      if (this.valveLayer) {
        this.valveLayer.show = false
      }
      if (this.waterMeterLayer) {
        this.waterMeterLayer.show = false
      }
      if (this.insmFlowLayer) {
        this.insmFlowLayer.show = false
      }
      if (this.insmPressureLayer) {
        this.insmPressureLayer.show = false
      }
      if (this.pumpLayer) {
        this.pumpLayer.show = false
      }
      if (this.insmLevelLayer) {
        this.insmLevelLayer.show = false
      }
      if (this.gongshiuTileLayer) {
        this.gongshiuTileLayer.show = false
      }
      if (this.yinshuiTileLayer) {
        this.yinshuiTileLayer.show = false
      }
      if (this.waterFactoryLayer) {
        this.waterFactoryLayer.show = false
      }
      if (this.storagePondLayer) {
        this.storagePondLayer.show = false
      }
    },
    showWaterMeter() {
      this.waterMeterLayer = new mars3d.layer.GeoJsonLayer({
        name: "水表打点",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Awater_meter &maxFeatures=3000&outputFormat=application%2Fjson",
        show: false,
        zIndex: 100,
        symbol: {
          styleOptions: {
            label: {
              font_size: 15,
              text: "{name}",
              color: "#09FDFB",
              outline: true,
              outlineColor: "#000000",
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 10000000,
              distanceDisplayCondition_near: 100,
              setHeight: 10000,
            },
            clampToGround: true,
            image: require("../../../assets/img/pipe_network/pipe2.png"),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
        },
        popup: [
          { field: "name", name: "名称" },
        ],
      })
      this.map.addLayer(this.waterMeterLayer)
    },
    showGongshuiLayer() {
      // this.gongshiuTileLayer = new mars3d.layer.GeoJsonLayer({
      //   name: "供水管线",
      //   url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Atotal_pipe_network&maxFeatures=4000&outputFormat=application%2Fjson&cql_filter=supply=true",
      //   format: this.simplifyGeoJSON, // 用于自定义处理geojson
      //   symbol: {
      //     type: "polylineP",
      //     styleOptions: {
      //       clampToGround: true,
      //       width: 2,
      //       materialType: mars3d.MaterialType.LineFlow,
      //       materialOptions: {
      //         color: "#00ED21",
      //         // image: "img/textures/fence-line.png",
      //         speed: 10,
      //         repeat_x: 10,
      //       },
      //       // distanceDisplayCondition: true,
      //       // distanceDisplayCondition_far: 12000000,
      //       // distanceDisplayCondition_near: 100000,
      //       label: {
      //         font_size: 30,
      //         // text: "{layer}",
      //         color: "#ffffff",
      //         outline: true,
      //         outlineColor: "#000000",
      //         scaleByDistance: true,
      //         scaleByDistance_far: 60000000,
      //         scaleByDistance_farValue: 0.2,
      //         scaleByDistance_near: 1000000,
      //         scaleByDistance_nearValue: 1,
      //         distanceDisplayCondition: true,
      //         distanceDisplayCondition_far: 10000000,
      //         distanceDisplayCondition_near: 100000,
      //         setHeight: 10000,
      //       },
      //     },
      //   },
      //   popup: [
      //     { field: "name", name: "名称" },
      //     { field: "code", name: "编号" },
      //   ]
      // });          
      // 改成加载wms图层
      this.gongshiuTileLayer = new mars3d.layer.WmsLayer({
        // url: "http://**************:8080/geoserver/img/wms",
        // url: "http://**************:8080/geoserver/xingshan/wms?service=WMS&version=1.1.0&request=GetMap&layers=xingshan%3Atotal_pipe_network&bbox=110.71158599853516%2C31.14643669128418%2C110.82113647460938%2C31.332233428955078&width=452&height=768&srs=EPSG%3A4326&styles=&format=application/openlayers",
        url: this.geourl + "/geoserver/xingshan/wms",
        layers: "xingshan:supply_network",
        parameters: {
          service: 'WMS',
          version: '1.1.0',
          request: 'GetMap',
          srs: 'EPSG:4326',
          // cql_filter: "type=1",
          transparent: true,
          format: 'image/png'
          // format: 'application/openlayers'
        },
        hasToGraphic: true,
        featureToGraphic: (feature, event) => {
          const data = feature.data
          return {
            // type: "polyline",
            symbol: {
              type: "polylineP",
              merge: true,
              styleOptions: {
                label: {
                  font_size: 15,
                  text: "{name},{length}m",
                  color: "#ffffff",
                  outline: true,
                  outlineColor: "#ffffff",
                  distanceDisplayCondition: true,
                  distanceDisplayCondition_far: 50000,
                  distanceDisplayCondition_near: 0,
                  setHeight: 10000,
                },  
              },
            },
          }
        },
        popup: [
          { field: "name", name: "名称" },
          { field: "length", name: "长度" },
          { field: "code", name: "编码" },
          { field: "material", name: "材质" },
          { field: "diameter", name: "管径" },
          { field: "deep", name: "深度" },
        ],
        // popup: "all",
        zIndex: 200,
        flyTo: true,
      })
      this.map.addLayer(this.gongshiuTileLayer);
      this.gongshiuTileLayer.on(mars3d.EventType.click, (event) => {
        // 高亮
        console.log(event)
        if(this.graphicOne) {
          this.highlightLayer.removeGraphic(this.graphicOne)
        }
        const coordinates = event.features[0].data.geometry.coordinates
        this.graphicOne = new mars3d.graphic.PolylineEntity({
          positions: coordinates,
          style: {
            color: "#2EFFE9", // 高亮颜色（黄色）
            width: 4, // 加粗线宽
            clampToGround: true, // 贴地
          }
        })
        this.highlightLayer.addGraphic(this.graphicOne)
        
        if(this.zongFlag) {
          this.zongList.push(event.features[0].data.properties.id)
        }
      })
  
    },
    simplifyGeoJSON(geojson) {
      try {
        geojson = turf.simplify(geojson, {
          tolerance: 0.0001,
          highQuality: true,
          mutate: true,
        });
      } catch (e) {
        //
      }
      return geojson;
    },
    changeUndergroundStatue(val) {
      this.underground.enabled = val
      this.undergroundDisabled = !val
    },
    fixUndergroundRate(val) {
      const v =  val/100
      if (this.underground) {
        this.underground.alpha = v
      }
      return v
    },
    changekaiwaStatue(val) {
      this.terrainPlanClip.enabled = val
      this.terrainPlanClip.diffHeight = this.kaiwaRate
    },
    drawPipe() {
      this.map.graphicLayer.startDraw({
        type: "polyline",
        style: {
          color: "#55ff33",
          width: 3,
          clampToGround: true
        },
        success: (graphic) => {
          console.log(JSON.stringify(graphic.coordinates))
          this.pipeCoordinate = graphic.coordinates
          this.addPipePopupFlag = true
          graphic.remove()
        }
      })
    },
    // 开挖画图
    kaiwaDraw() {
      this.clearWK()
      this.map.graphicLayer.startDraw({
        type: "polygon",
        style: {
          color: "#00FF00",
          opacity: 0.3,
          outline: true,
          outlineColor: "#ffffff",
          clampToGround: true
        },
        success: (graphic) => {
          // 绘制成功后回调
          const positions1 = graphic.positionsShow
          this.map.graphicLayer.clear()
          // console.log("挖坑绘制坐标为", JSON.stringify(mars3d.LngLatArray.toArray(positions1))) // 方便测试拷贝坐标
          // 挖地区域
          this.terrainPlanClip.positions = positions1;
          
          }
      })
    },
    // 清除挖坑
    clearWK() {
        this.terrainPlanClip.clear();
    },
    // 框选查询   多边行
    drawPolygon() {
      this.queryArea = []
      this.map.graphicLayer.startDraw({
        type: "polygon",
        style: {
          color: "#00FF00",
          opacity: 0.3,
          outline: true,
          outlineColor: "#ffffff",
          clampToGround: true
        },
        success: (graphic) => {
          // console.log("绘制矢量对象完成", graphic);
          graphic.toGeoJSON()
          const geometry = graphic._points
          if (geometry.length >= 3) {
            this.queryArea = geometry
            this.$emit('drawPolygonEnd', this.queryArea)
          }
          graphic.remove(true)
        }
      })
    },
    viewflay(currJD, currWD) {
        let position = [[currJD, currWD, 2500]]
        // console.log(position)
        this.map.flyToPositions(position)
    },
  },
};
</script>

<style lang="scss">
.businessMap {
  .mars3d-popup-background {
    background: rgba(2, 50, 128, 0.5);
    border: 1px solid #1377d0;
  }
}
#mars3dContainer {
  .cesium-viewer-toolbar {
    // left: 470px;
    left: auto;
    right: 50px;
    top: auto;
    bottom: 50px;
  }
  .cesium-baseLayerPicker-dropDown-visible {
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    left: 48px
  }
  .cesium-viewer-toolbar {
    right: unset;
    left: 20px;
  }
}
</style>

<style scoped lang="scss">
/**infoview浮动面板*/
.infoview {
  position: absolute;
  bottom: 5px;
  left: 480px;
  padding: 10px 15px;
  border-radius: 4px;
  border: 1px solid rgba(128, 128, 128, 0.5);
  color: #ffffff;
  background: rgba(0, 0, 0, 0.4);
  box-shadow: 0 3px 14px rgba(128, 128, 128, 0.5);
  z-index: 19870101;
}
.mars-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>
    