<template>
  <div class="leftPane">
    <!-- <div class="tab">
      <div
        v-for="item in tabs"
        :key="item.id"
        class="item fontDefault"
        :class="{ active: activeTab === item.id }"
        @click="handleClickTab(item.id)"
      >
        {{ item.name }}
      </div>
    </div> -->

    <div class="tabContainer">
      <!-- 爆管分析 -->
      <div v-if="activeTab === 3">
        <div class="innerTab">
          <div
            class="innerTabItem fontDefault"
            :class="{ innerTabActive: tab3Inner1 === 1 }"
            @click="handleClickInnerTabThree('tab3Inner1', 1)"
          >
            关阀分析
          </div>
          <div
            class="innerTabItem fontDefault"
            :class="{ innerTabActive: tab3Inner1 === 2 }"
            @click="handleClickInnerTabThree('tab3Inner1', 2)"
          >
            爆管历史
          </div>
        </div>
        <section style="min-height: 194px">
          <!-- 关阀分析 -->
          <div style="padding-top: 10px" v-show="tab3Inner1 === 1">
            <el-form :model="form" label-width="130px" size="medium">
              <el-form-item label="爆管点位置：">
                <el-input
                  v-model="form.value1"
                  placeholder="请输入内容"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item label="爆管点经纬度：">
                <el-input
                  v-model="form.value2"
                  placeholder="请输入内容"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item label="选择爆管点：">
                <el-input
                  v-model="form.value3"
                  placeholder="请输入内容"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary">关阀分析</el-button>
                <el-button type="primary">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </section>

        <div class="innerTab">
          <div
            class="innerTabItem fontDefault"
            :class="{ innerTabActive: tab3Inner2 === 1 }"
            @click="handleClickInnerTabThree('tab3Inner2', 1)"
          >
            关闭阀门
          </div>
          <div
            class="innerTabItem fontDefault"
            :class="{ innerTabActive: tab3Inner2 === 2 }"
            @click="handleClickInnerTabThree('tab3Inner2', 2)"
          >
            用户信息
          </div>
        </div>
        <!-- 关闭阀门 -->
        <section style="padding-top: 10px" v-show="tab3Inner2 === 1">
          <div class="listHeader">
            <span style="width: 90px">阀门名称</span>
            <span style="width: 40px">编号</span>
            <span style="width: 100px">位置</span>
            <span style="width: 60px">影响用户</span>
            <span style="width: 130px">停水通知</span>
          </div>
          <div class="listItem" v-for="(item, index) in tableList" :key="index">
            <div style="width: 90px">{{ item.name }}</div>
            <div style="width: 40px">{{ item.code }}</div>
            <div style="width: 100px">{{ item.position }}</div>
            <div style="width: 60px">{{ item.user }}</div>
            <div style="width: 130px">{{ item.msg }}</div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabs: [
        {
          name: "管网专题",
          id: 1,
        },
        {
          name: "统计查询",
          id: 2,
        },
        {
          name: "爆管分析",
          id: 3,
        },
      ],
      activeTab: 3,
      // 爆管分析
      tab3Inner1: 1,
      form: {},
      tab3Inner2: 1,
      tableList: [
        {
          name: "阀门一",
          code: "FM001",
          position: "古夫镇合心3村",
          user: 18,
          msg: "发送停水通知",
        },
        {
          name: "阀门二",
          code: "FM002",
          position: "古夫镇合心3村",
          user: 18,
          msg: "发送停水通知",
        },
        {
          name: "阀门三",
          code: "FM003",
          position: "古夫镇合心3村",
          user: 18,
          msg: "发送停水通知",
        },
        {
          name: "阀门四",
          code: "FM004",
          position: "古夫镇合心3村",
          user: 18,
          msg: "发送停水通知",
        },
        {
          name: "阀门五",
          code: "FM005",
          position: "古夫镇合心3村",
          user: 18,
          msg: "发送停水通知",
        },
      ],
    };
  },
  methods: {
    handleClickTab(tabId) {
      this.activeTab = tabId;
    },
    handleClickInnerTabThree(key, id) {
      this[key] = id;
    },
  },
};
</script>

<style lang="scss">
.leftPane {
  .el-form {
    .el-form-item {
      margin-bottom: 10px;
    }
  }
}
</style>

<style lang="scss" scoped>
.leftPane {
  width: 440px;
  height: 100%;
  position: absolute;
  top: 10px;
  left: 30px;
  z-index: 9;
  color: #ffffff;
  .fontDefault {
    color: #ccecfc;
    font-size: 18px;
    text-align: center;
    line-height: 40px;
  }
  .tab {
    display: flex;
    justify-content: space-between;
    .item {
      width: 140px;
      height: 40px;
      cursor: pointer;
      background-image: url("~@/assets/img/pipe_network/tab_default.png");
      background-size: 100%;
    }
    .active {
      color: #ffffff;
      font-weight: bold;
      background-image: url("~@/assets/img/pipe_network/tab_active.png");
    }
  }
  .tabContainer {
    height: calc(100% - 40px);
    padding: 10px;
    background-color: rgba(4, 15, 45, 0.5);
    .innerTab {
      display: flex;
      border-bottom: 1px solid #2a5dd2;
      .innerTabItem {
        width: 120px;
        height: 40px;
        cursor: pointer;
        border-bottom: 5px solid transparent;
      }
      .innerTabActive {
        color: #ffffff;
        font-weight: bold;
        border-bottom-color: #2a5dd2;
      }
    }
    .listHeader {
      font-size: 14px;
      text-align: center;
      background-image: url("../../../assets/img/table.png");
      background-size: 100%;
      height: 34px;
      display: flex;
      align-items: center;
    }
    .listItem {
      font-size: 12px;
      display: flex;
      align-items: center;
      height: 30px;
      background-color: rgba(2, 50, 128, 0.23);
      border: 1px solid #023280;
      margin-top: 5px;
      div {
        text-align: center;
      }
    }
  }
}
</style>
