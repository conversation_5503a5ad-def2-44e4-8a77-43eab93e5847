<template>
  <div class="PipeNetworkScreen">
    <!-- <LeftPane /> -->

    <LineMap class="map" :point="currentPoint" :checkedList="checkedList"/>
    <!-- 查询统计 -->
    <Query v-if="pipeNetworkMenuActive === 1" @emitPoint="getPoint" />
    <!-- 官网分析 -->
    <LeftPane v-if="pipeNetworkMenuActive === 2" />

    <!-- 图例 -->
    <div class="legend" v-if="pipeNetworkMenuActive === 3 || pipeNetworkMenuActive === 4">
      <el-tree
        :data="treeData"
        :props="defaultProps"
        show-checkbox
        node-key="label"
        class="legendtree"
        ref="tree"
        default-expand-all
        @check="handleNodeClick"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <!-- v-if="node.level === 2" -->
          <img
            v-if="node.level === 2 && node.data.type"
            :src="require('../../../assets/img/pipe_network/' + node.data.type + '.png')"
            style="width: 20px; height: 20px;margin-right:5px;"
            alt=""
          />
          <span class="labeltext">{{ node.label }}</span>
        </span>
      </el-tree>
    </div>
  </div>
</template>·

<script>
import LeftPane from "./leftPane";
import LineMap from "./innerlayer";
import { mapGetters } from 'vuex'

import Query from './pane/query'

export default {
  name: "PipeNetworkScreen",
  components: { LeftPane, LineMap, Query },
  data() {
    return {
      treeData: [
        {
          // id: 1,
          label: "设备仪器",
          children: [
            {
              // id: 2,
              label: "阀门",
              type: 'pipe1'
            },
            {
              // id: 3,
              label: "水表",
              type: 'pipe2'
            },
            {
              // id: 4,
              label: "流量计",
              type: 'pipe3'
            },
            {
              // id: 5,
              label: "压力计",
              type: 'pipe4'
            },
            {
              // id: 6,
              label: "水泵",
              type: 'pipe5'
            },
            {
              // id: 7,
              label: "液位计",
              type: 'pipe6'
            }
          ],
        },
        {
          // id: 8,
          label: "管网",
          children: [
            {
              // id: 9,
              label: "供水管线",
              type: 'pipe7'
            },
            {
              // id: 10,
              label: "引水管线",
              type: 'pipe8'
            },
          ],
        },
        {
          // id: 11,
          label: "厂站",
          children: [
            {
              // id: 12,
              label: "水厂",
              type: 'pipe9'
            },
            {
              // id: 13,
              label: "水池",
              type: 'pipe10'
            },
          ],
        },
      ],
      defaultProps: {
        children: "children",
        label: "label",
      },
      currentPane: 'HomePane',
      currentPoint: {},
      checkedList: [],
    };
  },
  computed: {
    ...mapGetters({
      pipeNetworkMenuActive: 'app/getPipeNetworkMenuActive'
    }),
    showQuery: {
      get: function() {
        return this.$store.state.app.pipeNetworkMenuActive === 1
      },
      set: function() {
        return false
      }
    }
  },
  mounted() {
    this.$refs.tree.setCheckedKeys(['供水管线', '引水管线']);
  },
  methods: {
    handleNodeClick(data, checkedList) {
      const arr = []     
      if (checkedList.checkedNodes) {
        checkedList.checkedNodes.forEach(element => {
          if (element.type) {
            arr.push(element.type)
          }
        });
      }
      this.checkedList = arr
    },
    getPoint(p) {
      this.currentPoint = p
    },
    append(data) {
      console.log(data);
    },
  },
};
</script>

<style lang="scss" scoped>
.PipeNetworkScreen {
  height: calc(100vh - 84px);
  position: relative;
  .map {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
  .legend {
    position: absolute;
    top: 20px;
    right: 30px;
    width: 220px;
    height: 455px;
    background: rgba(4, 15, 45, 0.5);
    border-radius: 4px;
    padding: 20px;
  }
  .legendtree {
    background: transparent;
    .labeltext {
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
    }
    ::v-deep.el-tree-node__content:hover {
      background: transparent;
    }
    ::v-deep.el-tree-node:focus > .el-tree-node__content {
      background-color: transparent;
    }
    ::v-deep.el-tree-node__label {
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
    }
    ::v-deep.el-checkbox__inner {
      width: 16px;
      height: 16px;
      background: rgba(0, 57, 112, 0.5);
      border: 1px solid #0285ff;
    }
  }
}
</style>
