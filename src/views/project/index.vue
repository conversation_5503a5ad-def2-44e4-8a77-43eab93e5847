<template>
  <div class="project">
    <div class="title">
      <div class="logout" @click="handleLogout">
        <i class="el-icon-switch-button" style="font-size: 20px;margin-right: 10px;"></i>
        <!-- <div>退出登录</div> -->
      </div>
    </div>
    <div class="cardBox">
      <div class="item" v-for="item, index in list" :key="index" @click="handleClick(item)">
        <img :src="item.img" alt="">
      </div>
    </div>
  </div>
</template>

<script>
import { removeToken } from '@/utils/auth'

export default {
  name: 'Project',
  data() {
    return {
      list: [
        {
          img: require('@/assets/img/project/project1.png'),
          isLink: false,
          linkUrl: '/main'
        },
        {
          img: require('@/assets/img/project/project2.png'),
          isLink: true,
          // linkUrl: 'http://**************:33380/#/login?redirect=%2Findex'
          linkUrl: 'https://xssysw.com'
        },
        {
          img: require('@/assets/img/project/project3.png'),
          isLink: true,
          linkUrl: 'http://**************:9096'
        },
        {
          img: require('@/assets/img/project/project4.png'),
          isLink: true,
          linkUrl: 'https://**************:1443/'
        }
      ]
    }
  },
  methods: {
    handleLogout() {
      removeToken()
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    handleClick(item) {
      if(!item.isLink) {
        this.$router.push(item.linkUrl)
      } else {
        window.open(item.linkUrl)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.project {
  height: 100%;
  background-color: #021F3D;
  background-image: url('~@/assets/img/project/bg.jpg');
  background-size: 100% 100%;
  .title {
    height: 155px;
    background-image: url('~@/assets/img/project/title.png');
    background-size: 100% 100%;
    position: relative;
    .logout {
      color: #ffffff;;
      position: absolute;
      top: 2px;
      right: 20px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }
  .cardBox {
    height: calc(100% - 155px);
    // padding-top: 50px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    cursor: pointer;
  }
}
</style>