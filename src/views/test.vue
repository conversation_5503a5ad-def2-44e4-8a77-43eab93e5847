<template>
  <div class="test">
    <!-- <DHPlayer channelId="1000004$1$0$8" ref="player" /> -->
    <DHPlayerWall ref="player" />
    <button @click="handleClick">点击</button>
  </div>
</template>

<script>
import DHPlayer from '@/components/player/DHPlayer'
import DHPlayerWall from '@/components/player/DHPlayerWall'

export default {
  name: 'Test',
  components: { DHPlayer, DHPlayerWall },
  methods: {
    handleClick() {
      this.$refs.player.chooseWindow(1)
    }
  }
}
</script>

<style lang="scss" scoped>
.test {
  width: 800px;
  height: 600px;
}
</style>