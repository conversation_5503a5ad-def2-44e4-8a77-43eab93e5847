<template>
  <div class="minFlow">
    <div class="areaTree">
      <div style="padding: 10px;">
        <el-input
          placeholder="请输入站点名称"
          suffix-icon="el-icon-search"
          v-model="filterText"
          size="medium">
        </el-input>
      </div>
      <el-scrollbar class="treeContainer">
        <!-- 滚动条要包裹的tree内容 --> 
        <el-tree 
          :data="tree"
          node-key="id"
          :props="elTreeProps"
          @node-click="nodeClick"
          :filter-node-method="filterNode"
          ref="tree"
        ></el-tree>
      </el-scrollbar>
    </div>

    <div class="dataContainer">
      <div class="queryContainer">
        <span>时间：</span>
        <el-date-picker
          v-model="time"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          size="medium"
          style="margin: 0 10px;"
          >
        </el-date-picker>
        <el-button type="primary" icon="el-icon-search" size="medium" @click="handleQuery(true)">查询</el-button>
      </div>

      <div class="chartContainer">
        <div style="font-size: 20px;">{{ currentArea.name }}流量变化趋势</div>
        <CommonLine yAxisName="单位：m³/h" :xAxisData="xAxisData" :seriesData="lineChartData" height="274px" :legendData="['最小流量', '背景漏损量']" />
      </div>

      <el-table
        height="300px"
        :data="minFlowList"
        size="mini"
      >
        <el-table-column label="编号" prop="areaName"></el-table-column>
        <el-table-column label="区域名称" prop="areaName"></el-table-column>
        <el-table-column label="采集时间" prop="date"></el-table-column>
        <el-table-column label="流量(m³/h)" prop="minFlow"></el-table-column>
        <el-table-column label="合法用水量(m³/h)" prop="reasonableFlow"></el-table-column>
        <!-- <el-table-column label="计算时间" prop="dateTime"></el-table-column> -->
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <span class="fixTextButton" @click="openDialog(row)">修改</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination        
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageInfo.pageNo"
          :page-sizes="[10, 20, 30, 40]"
          :page-size.sync="pageInfo.pageSize"
          layout="total, sizes, ->, prev, pager, next, jumper"
          :total="pageInfo.total">
        </el-pagination>
      </div>
    </div>

    <!-- 修改弹窗 -->
    <el-dialog
      title="修改"
      :visible.sync="dialogVisible"
      width="636px"
    >
      <el-form :model="fixForm" label-width="auto">
        <el-form-item label="合法用水量：">
          <el-input v-model="fixForm.name1"></el-input>
        </el-form-item>
        <el-form-item label="夜间最小流量计算方法：">
          <el-select v-model="fixForm.name2"></el-select>
        </el-form-item>
        <el-form-item label="最小流量测算时段：">
          <el-date-picker
            v-model="fixForm.name3"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
      </el-form>

      <div class="actionBox">
        <div class="buttonBase buttonCancel">取消</div>
        <div class="buttonBase buttonSubmit">确定</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAreaTree, getAreaNightMinFlowPageList, getAreaNightMinFlowForChart } from '@/api/leakageLoss/analysis'
import CommonLine from './chart/commonLine'

export default {
  components: { CommonLine },
  data() {
    return {
      currentArea: {},
      filterText: '',
      tree: [],
      elTreeProps: {
        children: 'subSet',
        label: 'name'
      },
      time: [],
      minFlowList: [{}],
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      xAxisData: [],
      lineChartData: [],
      dialogVisible: false,
      fixForm: {}
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    time(value) {
      if(!value) {
        this.time = []
      }
    }
  },
  async mounted() {
    await this.getTree()
  },
  methods: {
    getTree() {
      getAreaTree().then(res => {
        this.tree = res.data
        if(res.data[0]) {
          // 默认第一个区域
          this.currentArea = res.data[0]
          this.getChartData()
          this.getPageList()
        }
      })
    },
    // tree 节点点击
    nodeClick(data, node, nodeComponent) {
      console.log(data, node, nodeComponent)
      this.currentArea = data
      this.getPageList({
        areaId: data.id
      })

      this.getChartData()
    },
    // 节点搜索
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 查询按钮
    handleQuery(flag) {
      let pageQueryData = {
        areaId: this.currentArea.id
      }
      let charQuerytData = {}
      if(this.time[0]) {
        pageQueryData.beginTime = this.time[0]
        charQuerytData.beginTime = this.time[0]
      }
      if(this.time[1]) {
        pageQueryData.endTime = this.time[1]
        charQuerytData.endTime = this.time[1]
      }
      this.getPageList(pageQueryData)
      if(flag) {
        this.getChartData(charQuerytData)
      }

    },
    openDialog(row) {
      this.dialogVisible  = true
    },
    // 压力分页
    getPageList(payload) {
      let data = {
        pageSize: this.pageInfo.pageSize,
        pageNo: this.pageInfo.pageNo
      }
      if(payload) {
        data = {
          ...data,
          areaIdList: payload.areaId ? [payload.areaId] : [],
          beginTime: payload.beginTime,
          endTime: payload.endTime
        }
      }
      getAreaNightMinFlowPageList(data).then(res => {
        const { status, data, count } = res
        if(status === 200) {
          this.minFlowList = data
          this.pageInfo.total = count
        }
      })
    },
    // 折线图
    getChartData(payload) {
      let data = {
        areaIdList: [this.currentArea.id]
      }
      if(payload) {
        data = {
          ...data,
          beginTime: payload.beginTime,
          endTime: payload.endTime
        }
      }
      console.log(data)
      this.xAxisData = []
      this.lineChartData = []
      getAreaNightMinFlowForChart(data).then(res => {
        console.log(res)
        if(res.status === 200) {
          // this.lineChartData = [
          //   {
          //     name: '最小流量',
          //     type: 'line',
          //     symbol: 'circle',
          //     data: res.data[0].minFlowList
          //   },
          //   {
          //     name: '背景漏损量',
          //     type: 'line',
          //     symbol: 'circle',
          //     data: res.data[0].reasonableFlowList
          //   }
          // ]
          this.lineChartData = res.data[0].minFlowList
          this.xAxisData = res.data[0].dateList
        }
      })
    },
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.handleQuery()
    },
    handleCurrentChange() {
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss">
.minFlow {
  .treeContainer {
    .el-scrollbar__wrap {
      overflow-x: auto;
    }
    // 覆盖el-tree样式
    .el-tree {
      background: transparent;
      color: #fff;
      .el-tree-node:focus > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff; //节点的字体颜色
        font-weight: bold;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #113b7a;
      }
      .el-tree-node__content:hover {
        background-color: #113b7a;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.minFlow {
  height: 100%;
  display: flex;
  .areaTree {
    width: 200px;
    height: 100%;
    background-color: rgba(42, 93, 210, 0.1);
    border: 1px solid rgba(42, 93, 210, 0.5);
    .treeContainer {
      height: calc(100% - 56px);
    }
  }
  .dataContainer {
    padding-left: 20px;
    flex-grow: 1;
    .queryContainer {
      padding-bottom: 10px;
    }
  }
  .chartContainer {
    height: calc(100% - 382px);
  }
  .fixTextButton {
    font-size: 14px;
    color: #11BAFF;
    cursor: pointer;
  }
  .actionBox {
    display: flex;
    justify-content: center;
    align-content: center;
    padding-bottom: 30px;
    .buttonBase {
      width: 100px;
      height: 32px;
      color: #FFFFFF;
      border-radius: 4px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
    }
    .buttonCancel {
      background: linear-gradient(360deg, #004B94 0%, rgba(5,58,127,0.302) 39%, #0B61A1 99%);
      border: 1px solid #0B61A1;
      margin-right: 20px;
    }
    .buttonSubmit {
      background: linear-gradient(360deg, #043474 0%, #0E4C9A 39%, #1382E6 99%);
      border: 1px solid #2A5DD2;
    }
  }
}
</style>