<template>
  <div class="wlAnalysis">    
    <div class="treeBox">
      <el-input
        placeholder="请输入区域"
        suffix-icon="el-icon-search"
        v-model="filterText"
        size="medium">
      </el-input>
      <el-scrollbar class="treeContainer">
        <!-- 滚动条要包裹的tree内容 --> 
        <el-tree 
          :data="tree"
          node-key="id"
          :props="elTreeProps"
          @node-click="nodeClick"
          :filter-node-method="filterNode"
          ref="areaTree"
        ></el-tree>
      </el-scrollbar>
    </div>

    <div class="middle">
      <div class="tab">
        <div class="first" :class="{ active: currentType === 'tab1' }" @click="handleTabClick('tab1')">漏损分析报告</div>
        <div v-for="t, index in tabList" :key="index" class="other" :class="{ active: currentType === t.value }" @click="handleTabClick(t.value)">{{ t.label }}</div>
      </div>
      <div class="middleContainer">
        <Report :areaId="areaId" v-if="currentType === 'tab1'" />
        <NightMinFlow :areaId="areaId" v-if="currentType === 'tab2'" />
        <Pressure :areaId="areaId" v-if="currentType === 'tab3'"/>
        <Flow :areaId="areaId" v-if="currentType === 'tab4'"/>
        <DayWaterSupply :areaId="areaId" v-if="currentType === 'tab5'" />
        <HourWaterSupply :areaId="areaId" v-if="currentType === 'tab6'" />
        <Balance :areaId="areaId" v-if="currentType === 'tab7'" />
      </div>
    </div>

    <AreaInfo class="rightBox" :id="areaId" />
  </div>
</template>

<script>
// 漏损分析主页面
import Pressure from './components/pressure.vue'
import AreaInfo from './components/areaInfo.vue'
import Report from './components/report.vue'
import Balance from './components/balance.vue'
import NightMinFlow from './components/nightMinFlow.vue'
import Flow from './components/flow.vue'
import DayWaterSupply from './components/dayWaterSupply.vue'
import HourWaterSupply from './components/hourFlow.vue'

import { getAreaTree } from '@/api/leakageLoss/analysis'

export default {
  name: 'WlAnalysisIndex',
  components: {
    AreaInfo,
    Report,
    Balance,
    NightMinFlow,
    Pressure,
    Flow,
    DayWaterSupply,
    HourWaterSupply
  },
  data() {
    return {
      filterText: '',
      areaId: null,
      tree: [],
      elTreeProps: {
        children: 'subSet',
        label: 'name'
      },
      tabList: [
        {
          label: '最小夜间流量',
          value: 'tab2'
        },
        {
          label: '压力分析',
          value: 'tab3'
        },
        {
          label: '流量分析',
          value: 'tab4'
        },
        {
          label: '日供水量分析',
          value: 'tab6'
        },
        {
          label: '时供水量分析',
          value: 'tab5'
        },
        {
          label: '水平衡分析',
          value: 'tab7'
        },
      ],
      currentType: 'tab1'
    }
  },
  watch: {
    filterText(val) {
      this.$refs.areaTree.filter(val)
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      // 获取区域菜单
      getAreaTree().then(res => {
        this.tree = res.data
        // 设置默认选中
        if(res.data.length > 0 && res.data[0].id) {
          this.areaId = res.data[0].id
          this.$nextTick(() => {
            this.$refs.areaTree.setCurrentKey(res.data[0].id)
          })
        }        
      })
    },
    // 左侧区域点击
    nodeClick(item, node, self) {
      this.areaId = item.id
    },
    filterNode(value, data) {
      if(!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleTabClick(tab) {
      this.currentType = tab
    }
  }
}
</script>

<style lang="scss">
.wlAnalysis {
  .treeContainer {
    .el-scrollbar__wrap {
      overflow-x: auto;
    }
    // 覆盖el-tree样式
    .el-tree {
      background: transparent;
      color: #fff;
      .el-tree-node:focus > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff;
      }
      .el-tree-node__content:hover {
        background-color: #113b7a;
      }
    }
  }
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
  .el-table {
    .el-table__header {
      .cell {
        font-weight: 400;
        padding: 0 5px;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.wlAnalysis {
  height: 100%;
  display: flex;
  .treeBox {
    width: 240px;
    background: rgba(42,93,210,0.1);
    border: 1px solid rgba(42,93,210,0.5);
    height: 100%;
    padding: 10px;
    .treeContainer {
      padding-top: 10px;
      height: calc(100% - 36px);
    }
  }
  .middle {
    flex: 1;
    padding: 0 20px;
    .tab {
      color: #ffffff;    
      display: flex;
      div {
        padding: 0 20px;
        height: 34px;
        line-height: 34px;
        opacity: 0.6;
        text-align: center;
        cursor: pointer;
      }
      .active {
        opacity: 1;
        color: #fff;
      }
      .first {
        background-image: url('~@/assets/img/leakageLoss/first-tab.png');
        background-size: 100% 34px;      
      }
      .other {
        background-image: url('~@/assets/img/leakageLoss/other-tab.png');
        background-size: 100% 34px;      
      }
    }
    .tab {
      border-bottom: 2px solid #3084B5;
      height: 34px;
    }
    .middleContainer {
      height: calc(100% - 34px);
      border: 1px solid  #193F8D;
      border-top: none;
      background: rgba(42,93,210,0.1);
    }
  }
  .rightBox {
    width: 240px;
    background: rgba(42,93,210,0.1);
  }
}
</style>