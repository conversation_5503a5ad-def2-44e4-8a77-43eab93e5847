<template>
  <div class="dayWaterSupply">
    <div class="queryBox">
      <span>选择日期：</span>
      <el-date-picker
        v-model="date"
        type="date"
        :clearable="false"
        value-format="yyyy-MM-dd"
        size="medium">
      </el-date-picker>
      <el-button type="primary" icon="el-icon-search" size="medium" style="margin-left: 20px;" @click="getList">查询</el-button>
    </div>

    <div class="chartBox">
      <CommonLine :seriesData="seriesData" :xAxisData="xAxisData" :legendData="legend" yAxisName="供水量：m³" />
    </div>
  </div>
</template>

<script>
// 时供水量
import CommonLine from '../chart/commonLine.vue'
import { getAreaHourWaterSupply } from '@/api/leakageLoss/analysis'
import { parseTime } from '@/utils'

export default {
  name: 'DayWaterSupply',
  props: {
    areaId: {
      type: Number
    }
  },
  components: { CommonLine },
  data() {
    return {
      date: '',
      seriesData: [
        {
          name: '当日',
          type: 'bar',
          barWidth: 15,
          itemStyle: {
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(42, 93, 210, 0.3)'
                },
                {
                  offset: 1,
                  color: '#2A9DFF'
                }
              ]
            }
          },
          data: []
        },
        {
          name: '昨日',
          type: 'line',
          color: '#F6F09C',
          data: []
        },
        {
          name: '前日',
          type: 'line',
          color: '#1EE7E7',
          data: []
        }
      ],
      legend: ['当日', '昨日', '前日'],
      xAxisData: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '14:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00',]
    }
  },
  watch: {
    areaId() {
      this.getList()
    }
  },
  mounted() {
    this.date = parseTime(new Date(), '{y}-{m}-{d}')
    this.getList()
  },
  methods: {
    getList() {
      const payload = {
        areaIds: [this.areaId],
        date: this.date
      }
      getAreaHourWaterSupply(payload).then(res => {
        const { data, status } = res
        if(status === 200) {
          this.seriesData[0].data = data[0].thisDay.map(item => item[1])
          this.seriesData[1].data = data[0].yesterday.map(item => item[1])
          this.seriesData[2].data = data[0].beforeYesterday.map(item => item[1])
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dayWaterSupply {
  height: 100%;
  padding: 20px;
  .chartBox {
    margin-top: 20px;
    height: calc(100% - 56px);
  }
}
</style>