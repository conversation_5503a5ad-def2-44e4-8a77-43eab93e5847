<template>
  <div class="hourFlow">
    <div class="queryBox">
      <span>选择时间：</span>
      <el-date-picker
        v-model="date"
        type="month"
        :clearable="false"
        value-format="yyyy-MM"
        placeholder="选择月"
        size="medium">
      </el-date-picker>
      <el-button type="primary" icon="el-icon-search" size="medium" style="margin-left: 20px;" @click="getList">查询</el-button>
    </div>

    <div class="chartBox">
      <LineChart :seriesData="seriesData" :xAxisData="xAxisData" :legendData="legend" yAxisName="流量：m³/h" />
    </div>
  </div>
</template>

<script>
// 日供水量
import LineChart from '../chart/commonLine.vue'
import { getAreaDayWaterSupply } from '@/api/leakageLoss/analysis'
import { parseTime } from '@/utils'

export default {
  name: 'HourWaterSupply',
  props: {
    areaId: {
      type: Number
    }
  },
  components: { LineChart },
  data() {
    return {
      date: '',
      seriesData: [
        {
          name: '当月',
          type: 'bar',
          barWidth: 15,
          itemStyle: {
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(42, 93, 210, 0.3)'
                },
                {
                  offset: 1,
                  color: '#2A9DFF'
                }
              ]
            }
          },
          data: []
        },
        {
          name: '上月',
          type: 'line',
          color: '#F6F09C',
          data: []
        },
        {
          name: '前月',
          type: 'line',
          color: '#1EE7E7',
          data: []
        }
      ],
      xAxisData: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31'],
      legend: ['当月', '上月', '前月'],
    }
  },
  watch: {
    areaId() {
      this.getList()
    }
  },
  mounted() {
    this.date = parseTime(new Date(), '{y}-{m}')
    this.getList()
  },
  methods: {
    getList() {
      getAreaDayWaterSupply({
        areaIds: [this.areaId],
        date: this.date + '-01'
      }).then(res => {
        const { data, status } = res
        if(status === 200) {
          this.seriesData[0].data = data[0].thisMonth.map(item => item[1])
          this.seriesData[1].data = data[0].lastMonth.map(item => item[1])
          this.seriesData[2].data = data[0].lastLastMonth.map(item => item[1])
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.hourFlow {
  height: 100%;
  padding: 20px;
  .chartBox {
    margin-top: 20px;
    height: calc(100% - 56px);
  }
}
</style>