<template>
  <div class="pressure">
    <div class="queryBox">
      <div>
        <span>选择时间：</span>
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="-"
          :clearable="false"
          start-placeholder="选择日期"
          end-placeholder="选择日期"
          size="medium">
        </el-date-picker>
        <el-button type="primary" icon="el-icon-search" size="medium" style="margin-left:20px" @click="handleQuery">查询</el-button>
      </div>
      <el-radio-group v-model="showType" size="medium">
        <el-radio-button label="chart">图表</el-radio-button>
        <el-radio-button label="table">表格</el-radio-button>
      </el-radio-group>
    </div>
    <!-- 图表 -->
    <div class="innerBox" v-if="showType === 'chart'">
      <TimeLine :seriesData="lineChart" :legendData="legendData" yAxisName="压力：MPa" />
    </div>
    <!-- 表格 -->
    <div class="tableBox" v-if="showType === 'table'">
      <el-table
        style="width: 100%;"
        height="100%"
        :data="tableData"
        size="medium"
        :header-cell-style="{
          textAlign:'center',
          height:'60px'
        }"
        :row-style="{height:'50px'}"
      >
        <el-table-column label="编号" type="index" width="50"></el-table-column>
        <el-table-column prop="" label="设备名称" property="insmName"></el-table-column>
        <el-table-column prop="" label="采集时间" property="dataTime"></el-table-column>
        <el-table-column prop="" label="监测站点" property="facilityName"></el-table-column>        
        <el-table-column prop="" label="压力值(MPa)" property="pressure"></el-table-column>
      </el-table>
    </div>
    <div v-if="showType === 'table'">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import TimeLine from '../chart/timeLineChart.vue'

import { getShortcutTs } from '@/utils/time'

import { getAreaPressureList } from '@/api/leakageLoss/analysis'
import { getPressurePageList } from '@/api/leakageLoss/pressure'

export default {
  name: 'WlPressure',
  props: {
    areaId: {
      type: Number
    }
  },
  components:{
    TimeLine
  },
  data() {
    return {
      showType: 'chart',
      timeRange: [],
      legendData: [],
      lineChart: [],
      tableData: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
    }
  },
  watch: {
    areaId: {
      immediate: true,
      handler(value) {
        if(value) this.getList()
      }
    }
  },
  created() {
    this.timeRange = getShortcutTs(7)
  },
  mounted() {
    // this.getList()
  },
  methods: {
    handleQuery() {
      this.getList()
    },
    getList() {
      const data = {
        beginTime: this.timeRange[0],
        endTime: this.timeRange[1],
        areaIds: [this.areaId],
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize
      }
      // 获取区域压力图表数据
      getAreaPressureList(data).then(res => {
        this.lineChart = res.data.map(item => {
          return {
            name: item.insmName,
            type: 'line',
            data: item.datas
          }
        })
        this.legendData = res.data.map(item => item.insmName)
      })
      // 获取压力表格数据
      getPressurePageList(data).then(res => {
        this.tableData = res.data
        this.pageInfo.total = res.count;
      })
    },
    handleSizeChange(pageNum) {
      this.pageInfo.pageNo = 1
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    },
  }
}
</script>
<style lang="scss" scoped>

.pressure {
  padding: 20px;
  height: 100%;
  .queryBox {
    display: flex;
    justify-content: space-between;
  }
  .innerBox{
    margin-top: 30px;
    height: 90%;
  }
  .tableBox{
    position: relative;
    margin-top: 40px;
    overflow: hidden;
    height: 84%;
  }
}

</style>
