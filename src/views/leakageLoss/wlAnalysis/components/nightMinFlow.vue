<template>
  <div class="nightMinFlow">
    <div class="queryBox">
      <span>选择时间：</span>
      <el-date-picker
        v-model="timeRange"
        :clearable="false"
        type="datetimerange"
        :default-time="['00:00:00', '23:59:59']"
        value-format="yyyy-MM-dd HH:mm:ss"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        size="medium">
      </el-date-picker>
      <el-button type="primary" icon="el-icon-search" size="medium" style="margin-left: 20px;" @click="getList">查询</el-button>
    </div>

    <div class="chart">
      <TimeLine :seriesData="seriesData1" :legendData="legend1" yAxisName="流量：m³/h" height="50%" />
      <TimeLine :seriesData="seriesData2" :legendData="legend2" yAxisName="压力：MPa" height="50%" />
    </div>
  </div>
</template>

<script>
// 夜间最小流量
import LineChart from '../chart/commonLine.vue'
import TimeLine from '../chart/timeLineChart.vue'

import { getShortcutTs } from '@/utils/time'
import { getAreaPressureList, getAreaFlowList } from '@/api/leakageLoss/analysis'

export default {
  name: 'NightMinFlow',
  props: {
    areaId: {
      type: Number
    }
  },
  components: { LineChart, TimeLine },
  data() {
    return {
      timeRange: [],
      legend1: ['净流量', '最小流量'],
      xAxisData1: ['2024-03-01', '2024-03-02', '2024-03-03', '2024-03-04', '2024-03-05', '2024-03-06', '2024-03-07'],
      seriesData1: [],
      legend2: [],
      seriesData2: []
    }
  },
  mounted() {
    this.timeRange = getShortcutTs(7)
    // this.getList()
  },
  watch: {
    areaId: {
      immediate: true,
      handler(value) {
        if(value) this.getList()
      }
    }
  },
  methods: {
    getList() {
      const data = {
        beginTime: this.timeRange[0],
        endTime: this.timeRange[1],
        areaIds: [this.areaId]
      }
      getAreaPressureList(data).then(res => {
        this.seriesData2 = res.data.map(item => {
          return {
            name: item.insmName,
            type: 'line',
            data: item.datas
          }
        })
        this.legend2 = res.data.map(item => item.insmName)
      })
      getAreaFlowList(data).then(res => {
        this.seriesData1 = [
          {
            name: '净流量',
            type: 'line',
            data: res.data[0].netFlow || []
          },
          {
            name: '最小流量',
            type: 'line',
            data: res.data[0].mnFlow || []
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.nightMinFlow {
  height: 100%;
  padding: 20px;
  .chart {
    height: calc(100% - 36px);
  }
}
</style>