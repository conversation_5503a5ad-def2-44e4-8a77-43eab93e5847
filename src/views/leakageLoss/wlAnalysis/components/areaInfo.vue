<template>
  <div class="areaInfo">
    <div>
      <div class="title">漏损分析</div>
      <div class="cell" v-for="item, index in list1" :key="index">
        <span>{{ item.label }}</span>
        <span><span class="value">{{ item.value }}</span><span class="unit">{{ item.unit }}</span></span>
      </div>
    </div>

    <!-- 仪表盘 -->
    <div>
      <div class="chartBox">
        <div class="axisLabel" style="top: 30px;left: 40px;">优</div>
        <div class="axisLabel" style="top: 3px;left: 93px;">良</div>
        <div class="axisLabel" style="top: 30px;right: 40px;">差</div>
        <GaugeChart :value="realLossPercent" height="100px" />
      </div>
      <div class="rate">目标漏损率：{{ targetLossPercent }}%</div>
      <div class="info">未超过目标漏损率</div>
    </div>

    <div class="block">
      <div class="title">用水分析</div>
      <div class="cell" v-for="item, index in list2" :key="index">
        <span>{{ item.label }}</span>
        <span><span class="value">{{ item.value }}</span><span class="unit">{{ item.unit }}</span></span>
      </div>
    </div>

    <div class="block">
      <div class="title">基本信息</div>
      <div class="cell" v-for="item, index in list3" :key="index">
        <span>{{ item.label }}</span>
        <span><span class="value">{{ item.value }}</span><span class="unit">{{ item.unit }}</span></span>
      </div>
    </div>
  </div>
</template>

<script>
// 右侧分区信息
import GaugeChart from '../chart/gaugeChart.vue'

import { getAreaInfo } from '@/api/leakageLoss/analysis'

export default {
  name: 'AreaInfo',
  components: { GaugeChart },
  props: ['id'],
  data() {
    return {
      // 漏损分析
      list1: [
        {
          label: '当前供水量',
          value: 0,
          unit: 'm³'
        },
        {
          label: '当前售水量',
          value: 0,
          unit: 'm³'
        },
        {
          label: '免费注册水量',
          value: 0,
          unit: '%'
        },
        {
          label: '综合漏损量',
          value: 0,
          unit: 'm³'
        }
      ],
      targetLossPercent: 0, // 目标漏损率
      realLossPercent: 0, // 实际漏损率
      // 用水分析
      list2: [
        {
          label: '实抄户表',
          value: 0,
          unit: '个'
        },
        {
          label: '抄见率',
          value: 0,
          unit: '%'
        },
        {
          label: '异常用户',
          value: 0,
          unit: '个'
        },
        {
          label: '户表异常率',
          value: 0,
          unit: '%'
        }
      ],
      // 基本信息
      list3: [
        {
          label: '区域面积',
          value: 0,
          unit: 'k㎡'
        },
        {
          label: '管线总长',
          value: 0,
          unit: 'km'
        },
        {
          label: '考核表',
          value: 0,
          unit: '个'
        },
        {
          label: '户表',
          value: 0,
          unit: '个'
        },
        {
          label: '负责人',
          value: '',
          unit: ''
        },
        // {
        //   label: '创建时间',
        //   value: '2024-01-18',
        //   unit: ''
        // },
      ]
    }
  },
  watch: {
    id: {
      handler(val) {
        this.getData(val)
      },
      // immediate: true
    }
  },
  methods: {
    getData(id) {
      getAreaInfo(id).then(res => {
        if(res.status === 200) {
          this.list1[0].value = res.data.supply
          this.list1[1].value = res.data.sell
          this.list1[2].value = res.data.freePercent
          this.list1[3].value = res.data.loss

          this.targetLossPercent = res.data.targetLossPercent
          this.realLossPercent = res.data.realLossPercent

          this.list2[0].value = res.data.realTimeMeter
          this.list2[1].value = res.data.realTimeMeterPercent
          this.list2[2].value = res.data.unusuallyMeter
          this.list2[3].value = res.data.unusuallyMeterPercent

          this.list3[0].value = res.data.areaSize
          this.list3[1].value = res.data.pipeLength
          this.list3[2].value = res.data.assessMeter
          this.list3[3].value = res.data.totalMeter
          this.list3[4].value = res.data.charge
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.areaInfo {
  padding: 20px;
  overflow: hidden;
  font-family: Microsoft YaHei;
  .block {
    padding-top: 10px;
  }
  .title {
    font-weight: 700;
    color: #31B3FF;
  }
  .cell {
    background-image: url('~@/assets/img/leakageLoss/cellBg.png');
    background-size: 100%;
    height: 30px;
    padding: 0 8% 0 15px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 5px;
    .value {
      font-family: DIN;
      font-weight: 700;
      font-size: 16px;
      color: #FFFFFF;
    }
    .unit {
      color: #ACC6EA;
      padding-left: 4px;
    }
  }
  .chartBox {
    position: relative;
    .axisLabel {
      position: absolute;
      color: #ACC6EA;
      font-size: 12px;
    }
  }
  .rate {
    height: 32px;
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    line-height: 32px;
    background-image: url('~@/assets/img/leakageLoss/rateBg.png');
    background-size: 100%;
  }
  .info {
    height: 41px;
    font-size: 14px;
    text-align: center;
    line-height: 41px;
    background-image: url('~@/assets/img/leakageLoss/infoBg.png');
    background-size: 100%;
  }
}
</style>