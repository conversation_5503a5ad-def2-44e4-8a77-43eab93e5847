<template>
  <!-- 流量分析 -->
  <div class="flow">
    <div class="queryBox">
      <div>
        <span>选择时间：</span>
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          range-separator="-"
          :default-time="['00:00:00', '23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          :clearable="false"
          start-placeholder="选择日期"
          end-placeholder="选择日期"
          size="medium"
        >
        </el-date-picker>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="medium"
          style="margin-left: 20px"
          @click="getList"
          >查询</el-button
        >
      </div>
      <el-radio-group v-model="showType" size="medium">
        <el-radio-button label="chart">图表</el-radio-button>
        <el-radio-button label="table">表格</el-radio-button>
      </el-radio-group>
    </div>
    <div class="innerBox" v-if="showType === 'chart'">
      <TimeLine
        :seriesData="lineChart"
        yAxisName="流量：m³/h"
        :legendData="legendData"
      />
    </div>
    <!-- 表格 -->
    <div class="tableBox" v-if="showType === 'table'">
      <el-table
        style="width: 100%; position: absolute"
        height="520px"
        :data="tableData"
        size="medium"
        :header-cell-style="{
          textAlign: 'center',
          height: '60px',
        }"
      >
        <el-table-column label="编号" type="index" width="50"></el-table-column>
        <el-table-column
          prop=""
          label="设备名称"
          property="insmName"
        ></el-table-column>
        <el-table-column
          prop=""
          label="采集时间"
          property="dataTime"
        ></el-table-column>
        <el-table-column
          prop=""
          label="监测站点"
          property="name"
        ></el-table-column>
        <el-table-column
          prop=""
          label="流量(m³/h)"
          property="speed"
        ></el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div v-if="showType === 'table'">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import TimeLine from "../chart/timeLineChart.vue";

import { getShortcutTs } from "@/utils/time";

import {
  getFlowRateForChart,
  getFlowRatePageList,
} from "@/api/dispatch/realTimeMonitor";

export default {
  name: "WlFlow",
  props: {
    areaId: {
      type: Number,
    },
  },
  components: {
    TimeLine,
  },
  data() {
    return {
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      showType: "chart",
      timeRange: [],
      legendData: [],
      lineChart: [],
      tableData: [],
    };
  },
  watch: {
    areaId() {
      this.getList();
    },
  },
  mounted() {
    this.timeRange = getShortcutTs(7);
    this.getList();
  },
  methods: {
    getList() {
      const data = {
        beginTime: this.timeRange[0],
        endTime: this.timeRange[1],
        areaIds: [this.areaId],
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize
      };
      // 获取流量表格数据
      getFlowRatePageList(data).then((res) => {
        this.tableData = res.data;
        this.pageInfo.total = res.count;
      });
      // 获取区域流量图表数据
      getFlowRateForChart(data).then((res) => {
        this.lineChart = res.data.map((item) => {
          return {
            name: item.insmName,
            type: "line",
            data: item.datas,
          };
        });
        this.legendData = res.data.map((item) => item.insmName);
      });
    },
    handleSizeChange(pageNum) {
      this.pageInfo.pageNo = 1
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    },
  },
};
</script>
<style lang="scss" scoped>
.flow {
  padding: 20px;
  height: 100%;

  .queryBox {
    display: flex;
    justify-content: space-between;
  }

  .innerBox {
    margin-top: 30px;
    height: 90%;
  }

  .tableBox {
    width: 100%;
    position: relative;
    margin-top: 40px;
    overflow: hidden;
    height: 80%;
  }
}
</style>