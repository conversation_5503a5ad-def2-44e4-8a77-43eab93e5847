<template>
  <div class="balance">
    <div class="query">
      <div>
        <span>选择时间：</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          value-format="yyyy-MM-dd"
          @change="dateChange"
          :clearable="false"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="medium">
        </el-date-picker>
        <el-button type="primary" icon="el-icon-search" size="medium" style="margin-left: 20px;" @click="getList">查询</el-button>
      </div>

      <div class="timeShortcuts">
        <div
          class="paramItem"
          :class="{ selected: item.day === currentShortcut }"
          v-for="item in shortcuts"
          :key="item.value"
          @click="changeShortcut(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="cellBox">
      <div class="areaName">{{ areaName }}</div>
      <div class="treemap">
        <div class="item cell1 cellWidth">
          <span>总供水量</span>
          <span class="value">{{ cellData.allWater }}<span class="unit">万m³</span></span>
        </div>
        <div class="halfWidth">
          <div class="special">
            <div class="item cell2 specialCell">
              <span>注册用水量</span>
              <span class="value">{{ cellData.registUse }}<span class="unit">万m³</span></span>
            </div>
            <div class="specialCell">
              <div class="item cell3">
                <span>计费用水量</span>
                <span class="value">{{ cellData.chargeUse }}<span class="unit">万m³</span></span>
              </div>
              <div class="item cell4">
                <span>免费用水量</span>
                <span class="value">{{ cellData.noChargeUse }}<span class="unit">万m³</span></span>
              </div>
            </div>
          </div>
          <div class="item cell5">
              <span>漏损水量</span>
              <span class="value">{{ cellData.allLoss }}<span class="unit">万m³</span></span>
            </div>
        </div>
        <div class="cellWidth">
          <div class="item cell6">
            <span>收益水量</span>
            <span class="value">{{ cellData.chargeUse }}<span class="unit">万m³</span></span>
          </div>
          <div class="item cell7">
            <span>产销差</span>
            <span class="value">0<span class="unit">万m³</span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <div class="tableBox">
      <el-table
        style="width: 100%;position: absolute;"
        height="100%"
        :data="tableData"
        @row-click="rowClick"
        size="small"
      >
        <el-table-column prop="date" label="日期" ></el-table-column>
        <el-table-column prop="allWater" label="供水量(m³)"></el-table-column>
        <el-table-column prop="" label="售水量(m³)"></el-table-column>        
        <el-table-column prop="" label="产销差水量(m³)"></el-table-column>
        <el-table-column prop="" label="产销差率(%)"></el-table-column>
      </el-table>
    </div>
    <el-pagination
      background
      @size-change="handleSizeChange"
      :current-page.sync="pageInfo.pageNo"
      :page-size.sync="pageInfo.pageSize"
      layout="total, prev, pager, next, jumper"
      :total="pageInfo.total"
    >
    </el-pagination>
  </div>
</template>

<script>
// 水平衡分析
import { getShortcutTs } from '@/utils/time'

import { getBalancePage } from '@/api/leakageLoss/analysis'

export default {
  name: 'Balance',
  props: ['areaId'],
  data() {
    return {
      dateRange: [],
      shortcuts: [
        {
          label: '7天',
          day: 7
        },
        {
          label: '15天',
          day: 15
        },
        {
          label: '30天',
          day: 30
        }
      ],
      currentShortcut: 7,
      areaName: '--',
      cellData: {
        allWater: '--',
        registUse: '--',
        chargeUse: '--',
        noChargeUse: '--',
        allLoss: '--',        
      },
      info: [
        {
          label: '总供水量',
          value: 1480.74
        },
        {
          label: '注册用水量',
          value: 120.11
        },
        {
          label: '计费用水量',
          value: 1459.76
        },
        {
          label: '免费用水量',
          value: 223.74
        },
        {
          label: '漏损水量',
          value: 199.78
        },
        {
          label: '收益水量',
          value: 1258.86
        },
        {
          label: '产销差',
          value: 222.08
        }
      ],
      tableData: [],
      pageInfo: {
        pageSize: 10,
        pageNo: 1,
        total: 0
      }
    }
  },
  watch: {
    areaId() {
      this.getList()
    }
  },
  mounted() {
    // 设置默认值
    this.dateRange = getShortcutTs(this.currentShortcut, '{y}-{m}-{d}')
    this.getList()
  },
  methods: {
    changeShortcut(item) {
      this.currentShortcut = item.day
      this.dateRange = getShortcutTs(item.day, '{y}-{m}-{d}')
    },
    dateChange() {
      this.currentShortcut = null
    },
    getList() {
      getBalancePage({
        pageNo: 1,
        pageSize: 10,
        areaId: [this.areaId],
        startDate: this.dateRange[0],
        endDate: this.dateRange[1]
      }).then(res => {
        this.tableData = res.data
        this.pageInfo.total = res.count
        if(this.tableData.length > 0) {
          this.setCellData(this.tableData[0])
        }
      })
    },
    rowClick(row) {
      console.log(row)
      this.setCellData(row)
    },
    setCellData(source) {
      this.cellData.allWater = source.allWater
      this.cellData.registUse = source.registUse
      this.cellData.chargeUse = source.chargeUse
      this.cellData.noChargeUse = source.noChargeUse
      this.cellData.allLoss = source.allLoss
      this.areaName = `${source.areaName}(${source.date})`
    },
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.balance {
  height: 100%;
  padding: 20px;
  .query {
    display: flex;
    justify-content: space-between;
    .timeShortcuts {
      display: flex;
      justify-content: flex-end;
      .paramItem {
        width: 70px;
        height: 34px;
        border: 2px solid transparent;
        background: rgba(2, 50, 128, 0.8);
        color: #ACC6EA;
        text-align: center;
        line-height: 30px;
        margin-left: 10px;
        cursor: pointer;
      }
      .selected {
        border-color: #3ad2e7;
        color: #fff
      }
    }    
  }
  .cellBox {
    margin-top: 20px;
    .areaName {
      height: 40px;
      background-color: rgba(36,88,194,0.5);
      text-align: center;
      line-height: 44px;
      font-weight: 700;
      font-size: 18px;
    }
    .treemap {
      height: 300px;
      margin-top: 10px;
      display: flex;
      .cellWidth {
        width: calc((100% - 30px) / 4); 
      }
      .halfWidth {
        flex: 1;
        padding: 0 10px;
      }
      .special {
        display: flex;
        .specialCell {
          width: calc((100% - 10px) / 2);
        }
      }
      .value {
        font-family: DIN;
        font-weight: 700;
        font-size: 24px;
      }
      .unit {
        font-size: 16px;
        margin-left: 6px;
      }
      .item {        
        font-size: 18px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .cell1 {
        height: 300px;
        background-color: rgba(2, 50, 128, 0.6);
      }
      .cell2 {
        height: 145px;
        margin-right: 10px;
        background-color: rgba(0, 116, 255, 0.6);
      }
      .cell3 {
        height: 67.5px;
        background-color: rgba(0, 162, 254, 0.6);
      }
      .cell4 {
        margin-top: 10px;
        height: 67.5px;
        background-color: rgba(31, 189, 235, 0.6);
      }
      .cell5 {
        margin-top: 10px;
        height: 145px;
        background-color: rgba(42, 93, 210, 0.6);
      }
      .cell6 {
        height: 67.5px;
        background-color: rgba(0, 211, 96, 0.6);
      }
      .cell7 {
        margin-top: 10px;
        height: 222.5px;
        background-color: rgba(62, 118, 176, 0.6);
      }
    }
  }
  .tableBox {
    position: relative;
    overflow: hidden;
    width: 100%;
    margin-top: 20px;
    height: calc(100% - 450px);
  }
}
</style>