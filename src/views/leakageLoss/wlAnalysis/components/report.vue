<template>
  <div class="report">
    <div class="queryBox">
      <span>选择时间：</span>
      <el-date-picker
        v-model="timeRange"
        type="daterange"
        value-format="yyyy-MM-dd"
        :clearable="false"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        size="medium">
      </el-date-picker>
      <el-button type="primary" icon="el-icon-search" size="medium" style="margin-left: 20px;" @click="getList">查询</el-button>
    </div>

    <!-- 表格 -->
    <div class="tableBox">
      <el-table
        style="width: 100%;position: absolute;"
        height="100%"
        :data="tableData"
        size="medium"
      >
        <el-table-column label="编号" type="index" width="50"></el-table-column>
        <el-table-column prop="areaName" label="分区名称" width="100"></el-table-column>
        <el-table-column prop="grade" label="分区级别" width="100"></el-table-column>
        <el-table-column prop="minNightFlow" label="夜间最小流量(m³/h)"></el-table-column>        
        <el-table-column prop="minNightFlowPercent" label="夜间最小流量占比"></el-table-column>
        <el-table-column prop="lossWaterHour" label="漏失流量(m³/h)"></el-table-column>
        <el-table-column prop="lossWaterDay" label="漏失水量(m³/天)"></el-table-column>
        <el-table-column prop="lossWaterMonth" label="漏失水量(m³/月)"></el-table-column>
        <el-table-column prop="lossRate" label="漏失率(%)"></el-table-column>
      </el-table>
    </div>

    <el-pagination        
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="pageInfo.pageNo"
      :page-sizes="[10, 20, 30, 40]"
      :page-size.sync="pageInfo.pageSize"
      layout="total, sizes, ->, prev, pager, next, jumper"
      :total="pageInfo.total">
    </el-pagination>

    <div class="bottomInfo">
      <div class="title">分析结论</div>
      <div class="card">
        <div class="item" v-for="item, index in info" :key="index">
          <img :src="item.img" alt="">
          <div class="infoBox">
            <div style="color: #B0D7FF;">{{ item.label }}</div>
            <div><span class="value">{{ item.value }}</span><span class="unit">{{ item.unit }}</span></div>
            <div v-if="item.state && item.state === 'down'" class="down">{{ item.stateValue }}%<i class="el-icon-bottom"></i></div>
            <div v-if="item.state && item.state === 'up'" class="up">{{ item.stateValue }}%<i class="el-icon-top"></i></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getReportPage } from '@/api/leakageLoss/analysis'
import { getShortcutTs } from '@/utils/time'

export default {
  name: 'WlReport',
  props: ['areaId'],
  data() {
    return {
      timeRange: [],
      tableData: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      // 分析结论
      info: [
        {
          label: '夜间用量',
          value: 13.8,
          unit: 'm³',
          state: 'down',
          stateValue: 1.24,
          img: require('@/assets/img/leakageLoss/infoImg1.png')
        },
        {
          label: '今日供水量',
          value: 13.8,
          unit: 'm³',
          state: '',
          stateValue: 1.24,
          img: require('@/assets/img/leakageLoss/infoImg2.png')
        },
        {
          label: '今日供水量',
          value: 13.8,
          unit: 'm³',
          state: 'up',
          stateValue: 0.27,
          img: require('@/assets/img/leakageLoss/infoImg2.png')
        },
        {
          label: '平均供水量(近30天)',
          value: 13.8,
          unit: 'm³',
          state: '',
          stateValue: 1.24,
          img: require('@/assets/img/leakageLoss/infoImg3.png')
        }
      ]
    }
  },
  watch: {
    areaId() {
      this.getList()
    }
  },
  mounted() {
    this.timeRange = getShortcutTs(7, '{y}-{m}-{d}')
    this.getList()
  },
  methods: {
    getList() {
      getReportPage({
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        areaId: [this.areaId],
        startDate:  this.timeRange[0],
        endDate:  this.timeRange[1]
      }).then(res => {
        this.tableData = res.data
        this.pageInfo.total = res.count
      })
    },
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.report {
  height: 100%;
  padding: 20px;
  .tableBox {
    position: relative;
    overflow: hidden;
    width: 100%;
    margin-top: 20px;
    height: calc(100% - 216px);
  }
  .bottomInfo {
    height: 124px;
    .title {
      font-weight: 700;
      font-size: 18px;
      color: #31B3FF;
    }
    .card {
      background-color: rgba(2,50,128,0.3);
      height: 100px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .item {
        display: flex;
        img {
          width: 82px;
          height: 88px;
        }
        .infoBox {
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding-left: 14px;
          font-size: 16px;
          .value {
            font-family: DIN;
            font-weight: 700;
            font-size: 24px;
          }
          .unit {
            font-size: 14px;
            color: #DEEBFF;
            padding-left: 13px;
          }
          .down {
            color: #00FAA2;
          }
          .up {
            color: #FA6400;
          }
        }
      }
    }
  }
}
</style>