<template>
  <div class="flowPressure">
    <div class="areaTree">
      <div style="padding: 10px;">
        <el-input
          placeholder="请输入站点名称"
          suffix-icon="el-icon-search"
          v-model="filterText"
          size="medium"
          >
        </el-input>
      </div>
      <el-scrollbar class="treeContainer">
        <!-- 滚动条要包裹的tree内容 --> 
        <el-tree 
          :data="tree"
          node-key="id"
          :props="elTreeProps"
          @node-click="nodeClick"
        ></el-tree>
      </el-scrollbar>
    </div>

    <div class="dataContainer">
      <div class="queryContainer">
        <!-- <span>压力计：</span>
        <el-select v-model="pressureInsm" value-key="insmId" placeholder="请选择压力计" size="medium" style="width: 200px;margin-right: 10px;">
          <el-option
            v-for="pInsm in pressureInsmOptions"
            :key="pInsm.insmId"
            :label="pInsm.insmName"
            :value="pInsm">
          </el-option>
        </el-select>
        <span>流量计：</span>
        <el-select v-model="flowInsm" value-key="insmId" placeholder="请选择流量计" size="medium" style="width: 200px;margin-right: 10px;">
          <el-option
            v-for="fInsm in flowInsmOptions"
            :key="fInsm.insmId"
            :label="fInsm.insmName"
            :value="fInsm">
          </el-option>
        </el-select> -->
        <span>时间：</span>
        <el-date-picker
          style="margin-right: 10px;"
          v-model="time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :clearable="false"
          size="medium">
        </el-date-picker>
        <el-button type="primary" icon="el-icon-search" size="medium" @click="handleQuery">查询</el-button>
      </div>

      <div class="chartContainer">
        <div style="font-size: 20px;">{{ currentArea.name }}压力流量变化趋势</div>
        <InsmValueChart :seriesData="lineChartData" :legendData="legendData" height="274px" />
      </div>

      <section class="tableContainer">
        <div class="top">
          <div style="font-size: 20px;">{{ currentArea.name }} {{ currentTab == 'pressure' ? '压力表' : '流量表' }}</div>
          <div class="tabContainer">
            <div class="tabButton" :class="{ tabButtonActive: currentTab == 'pressure' }"  style="margin-right: 7px;" @click="tabChange('pressure')">压力</div>
            <div class="tabButton" :class="{ tabButtonActive: currentTab == 'flow' }" @click="tabChange('flow')">流量</div>
          </div>
        </div>

          <div v-show="currentTab == 'pressure'" style="height: calc(100% - 30px);">
            <!-- 压力数据表格 -->
            <el-table
              height="calc(100% - 50px)"
              :data="pressureList"
              size="mini"
            >
              <el-table-column label="编号" type="index" width="100"></el-table-column>
              <el-table-column label="设备名称" prop="insmName"></el-table-column>
              <el-table-column label="采集时间" prop="dataTime"></el-table-column>
              <el-table-column label="监测站点" prop="facilityName"></el-table-column>
              <el-table-column label="压力值1 (Pa)" prop="pressure"></el-table-column>
              <el-table-column label="压力值2 (Pa)"></el-table-column>
            </el-table>
            <div class="pagination-container">
              <el-pagination        
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="pageInfo.pageNum"
                :page-sizes="[10, 20, 30, 40]"
                :page-size.sync="pageInfo.pageSize"
                layout="total, sizes, ->, prev, pager, next, jumper"
                :total="pageInfo.total">
              </el-pagination>
            </div>
          </div>
          
          <div v-show="currentTab == 'flow'" style="height: calc(100% - 30px);">
            <el-table
              height="calc(100% - 50px)"
              :data="flowList"
              size="mini"
            >
              <el-table-column label="编号"></el-table-column>
              <el-table-column label="设备名称"></el-table-column>
              <el-table-column label="采集时间"></el-table-column>
              <el-table-column label="监测站点"></el-table-column>
              <el-table-column label="入口流量(m³/h)"></el-table-column>
            </el-table>
            <div class="pagination-container">
              <el-pagination        
                background
                @size-change="handleSizeChange1"
                @current-change="handleCurrentChange1"
                :current-page.sync="pageInfo1.pageNum"
                :page-sizes="[10, 20, 30, 40]"
                :page-size.sync="pageInfo1.pageSize"
                layout="total, sizes, ->, prev, pager, next, jumper"
                :total="pageInfo1.total">
              </el-pagination>
            </div>
          </div>
      </section>

    </div>
  </div>
</template>

<script>
import { getAreaTree, getAreaInsm, getAreaInsmValue } from '@/api/leakageLoss/analysis'
import { getPressurePageList } from '@/api/leakageLoss/pressure'

import InsmValueChart from './insmValueChart'
import _ from 'loadsh'

export default {
  components: { InsmValueChart },
  data() {
    return {
      // 当前选中区域
      currentArea: {},
      filterText: '',
      tree: [],
      elTreeProps: {
        children: 'subSet',
        label: 'name'
      },
      // 仪器列表
      pressureInsm: null,
      flowInsm: null,
      pressureInsmOptions: [],
      flowInsmOptions: [],
      time: [],
      // chartData
      xAxisData: [],
      lineChartData: [
        {
          name: '压力1',
          type: 'line',
          symbol: 'circle',
          smooth: true,
          areaStyle: {
            // opacity: 0.1
            color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                    offset: 0, color: 'rgba(49, 179, 255, 1)' // 0% 处的颜色
                }, {
                    offset: 1, color: 'rgba(0, 89, 84, 0)' // 100% 处的颜色
                }],
                global: false // 缺省为 false
            }
          },
          data: [
            ['2023-11-01 09:00:20', 10],
            ['2023-11-02 09:00:20', 20],
            ['2023-11-03 09:00:20', 30],
            ['2023-11-04 09:00:20', 20]
          ]
        },
        {
          name: '压力2',
          type: 'line',
          symbol: 'circle',
          smooth: true,
          areaStyle: {
            color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                    offset: 0, color: 'rgba(82, 208, 223, 1)' // 0% 处的颜色
                }, {
                    offset: 1, color: 'rgba(0, 89, 84, 0)' // 100% 处的颜色
                }],
                global: false // 缺省为 false
            }
          },
          data: [
            ['2023-11-01 10:00:20', 20],
            ['2023-11-02 09:00:20', 20],
            ['2023-11-03 09:00:20', 20],
            ['2023-11-04 09:00:20', 20]
          ]
        },
        {
          name: '流量',
          type: 'line',
          symbol: 'circle',
          smooth: true,
          yAxisIndex: 1,
          areaStyle: {
            color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                    offset: 0, color: 'rgba(246, 189, 22, 1)' // 0% 处的颜色
                }, {
                    offset: 1, color: 'rgba(0, 89, 84, 0)' // 100% 处的颜色
                }],
                global: false // 缺省为 false
            }
          },
          data: [
            ['2023-11-01 09:00:20', 15],
            ['2023-11-02 09:00:20', 15],
            ['2023-11-03 09:00:20', 15],
            ['2023-11-04 09:00:20', 15]
          ]
        }
      ],
      legendData: ['压力1', '压力2', '流量'],
      // 表格
      currentTab: 'pressure',
      pressureList: [],
      pageInfo: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      flowList: [],
      pageInfo1: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
    }
  },
  created() {
    this.getTree()

  },
  mounted() {
    // this.getPressureList()
  },
  methods: {
    getTree() {
      getAreaTree().then(res => {
        this.tree = res.data
        if(res.data[0]) {
          // 默认第一个区域
          this.currentArea = res.data[0]
        }
      })
    },
    // tree 节点点击
    nodeClick(data, node, nodeComponent) {
      this.currentArea = data
      // this.getAreaInsmList(data.id)
      this.getPressureList()
    },
    // 获取区域压力数据分页列表
    getPressureList() {
      getPressurePageList({
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize,
        beginTime: this.time[0] ?  this.time[0] + ' 00:00:00' : null,
        endTime: this.time[1] ?  this.time[1] + ' 23:59:59': null,
        areaIds: [this.currentArea.id]
      }).then(res => {
        const { status, data, count } = res
        if(status === 200) {
          this.pressureList = data
          this.pageInfo.total = count
        }
      })
    },
    handleSizeChange(currentSize) {
      this.pageInfo.pageNum = 1
      this.getPressureList()
    },
    handleCurrentChange(currentPage) {      
      this.getPressureList()
    },
    handleSizeChange1(currentSize) {
      this.pageInfo1.pageNum = 1
      this.getPressureList()
    },
    handleCurrentChange1(currentPage) {      
      this.getPressureList()
    },
    // 获取区域设备
    getAreaInsmList(areaId) {
      this.pressureInsm = null
      this.flowInsm = null
      getAreaInsm(areaId).then(res => {
        console.log('insm', res)
        const { status, data } = res
        if(status === 200 ) {
          this.pressureInsmOptions = data.pressureList
          this.flowInsmOptions = data.flowList
        }
      })
    },
    handleQuery() {
      this.getPressureList()
    },
    // tab 切换
    tabChange(value) {
      this.currentTab = value
    },
    // 折线图
    getChartData(payload) {
      
    }
  }
}
</script>

<style lang="scss">
.flowPressure {
  .treeContainer {
    .el-scrollbar__wrap {
      overflow-x: auto;
    }
    // 覆盖el-tree样式
    .el-tree {
      background: transparent;
      color: #fff;
      .el-tree-node:focus > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff; //节点的字体颜色
        font-weight: bold;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #113b7a;
      }
      .el-tree-node__content:hover {
        background-color: #113b7a;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.flowPressure {
  height: 100%;
  display: flex;
  .areaTree {
    width: 200px;
    height: 100%;
    background-color: rgba(42, 93, 210, 0.1);
    border: 1px solid rgba(42, 93, 210, 0.5);
    .treeContainer {
      height: calc(100% - 56px);
    }
  }
  .dataContainer {
    padding-left: 20px;
    // flex-grow: 1;
    width: calc(100% - 200px);
    .queryContainer {
      // padding-bottom: 10px;
    }
  }
  .chartContainer {
    height: 300px;
    // min-height: 400px;
  }
  .tableContainer {
    height: calc(100% - 336px);
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      .tabContainer {
        display: flex;
      }
      .tabButton {
        background: rgba(2, 50, 128, 0.80);
        width: 90px;
        height: 30px;
        cursor: pointer;
        color: #ACC6EA;
        text-align: center;
        line-height: 30px;
      }
      .tabButtonActive {
        line-height: 26px;
        color: #ffffff;
        background: linear-gradient(180deg, #2A5DD2 0%, #06245A 100%);
        border: 2px solid;
        border-image: linear-gradient(180deg, rgba(82.00000271201134, 208.0000028014183, 223.00000190734863, 1), rgba(54.00000058114529, 124.00000020861626, 174.00000482797623, 0)) 2 2;
      }
    }
  }
}
</style>