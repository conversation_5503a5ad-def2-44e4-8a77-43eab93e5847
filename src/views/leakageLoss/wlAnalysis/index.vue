<template>
  <div class="WlAnalysis app-container">
    <div class="tab">
      <div class="first" :class="{ active: currentType === 'tab1' }" @click="handleTabClick('tab1')">最小流量</div>
      <div class="other" :class="{ active: currentType === 'tab2' }" @click="handleTabClick('tab2')">压力</div>
    </div>
    <div class="compenent-container">
      <MinFlow v-if="currentType === 'tab1'" />
      <FlowPressure v-else />
    </div>
  </div>
</template>

<script>
import MinFlow from './minFlow'
import FlowPressure from './flowPressure'

export default {
  components: { 
    MinFlow,
    FlowPressure
  },
  data() {
    return {
      currentType: 'tab1'
    }
  },
  methods: {
    handleTabClick(type) {
      this.currentType = type
    }
  }
}
</script>

<style lang="scss">
.WlAnalysis { 
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.WlAnalysis {
  padding: 0 30px;
  height: calc(100vh - 84px);
  .compenent-container {
    color: #ffffff;
    padding: 20px;
    height: calc(100% - 76px);
    border: 1px solid #2A5DD2;
    border-top-color: #3084B5;
  }
}
</style>
