<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    value: {
      type: Number,
      default: 0
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    yAxisName: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    value: {
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption(
        {
          grid: {
            bottom: 10
          },
          series: [
            {
              type: 'gauge',
              radius: '100%',
              center: ['50%', '70%'],
              startAngle: 180,
              endAngle: 0,
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              },
              splitNumber: 3,
              axisLabel: {
                show: false
              },
              axisLine: {
                lineStyle: {
                  width: 10,
                  color: [
                    [0.33, '#52D0DF'],
                    [0.66, '#31B3FF'],
                    [1, '#E13C3F']
                  ]
                }
              },
              detail: {
                fontSize: 20,
                fontWeight: 700,
                formatter: function (value) {
                  return value + '%'
                }
              },
              pointer: {
                length: '70%',
                width: 4
              },
              data: [
                {
                  name: '',
                  value: this.value
                }
              ]
            },
          ]
        }
      )
    }
  }
}
</script>
