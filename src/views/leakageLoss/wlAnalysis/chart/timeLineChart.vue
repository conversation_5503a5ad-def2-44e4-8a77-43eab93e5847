<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
import resize from "@/utils/chartResize";
import { parseTime } from '@/utils'
import { format } from 'echarts/lib/export';

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    seriesData: {
      type: Array,
      default: () => [],
    },
    xAxisData: {
      type: Array,
      default: () => [],
    },
    yAxisName: {
      type: String,
      default: "",
    },
    legendData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart();
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el);
      this.chart.setOption(
        {
          color: ["#1B7EF2", "#29F1FA", "#1CFF27", "#FF5733"],
          tooltip: {
            trigger: "axis",
            formatter: function(params) {
              let temp = []
              for (let index = 0; index < params.length; index++) {
                temp.push(`${params[index].marker}${params[index].seriesName}: ${params[index].value[1]}(${params[index].value[0]})`)           
              }
              return temp.join('<br />')
            }
          },
          legend: {
            textStyle: {
              color: "#fff",
            },
            type: 'scroll',
            pageTextStyle: {
              color: '#fff'
            },
            data: this.legendData,
          },
          grid: {
            left: "3%",
            right: "4%",
            // bottom: "3%",
            // top: '10%',
            containLabel: true,
          },
          xAxis: {
            type: 'time',
            boundaryGap: false,
            axisLine: {
              lineStyle: {
                color: "#B0D7FF"
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#B0D7FF'
            },
            splitLine: {
              show: false
            }
          },
          yAxis: {
            name: this.yAxisName,
            nameTextStyle: {
              color: '#fff'
            },
            type: "value",
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#B0D7FF'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(176, 215, 255, 0.30)",
              },
            },
          },
          dataZoom: [
            {
              start: 0,
              end: 20,
              textStyle: {
                color: '#fff'
              }
            }
          ],
          series: this.seriesData,
        },
        {
          notMerge: true,
        }
      );
    },
  },
};
</script>
