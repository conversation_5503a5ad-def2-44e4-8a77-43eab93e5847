<template>
  <div class="leakMap">
    <div id="map"></div>

    <!-- <div class="baseBox" @click="changeBase" :style="{ right: mapMode ? '30px' : '480px' }">
      <img src="@/assets/img/home/<USER>/earth.png" alt="">
    </div> -->

  </div>
</template>

<script>
import Map from '@arcgis/core/Map'
import MapView from '@arcgis/core/views/MapView'
import WebTileLayer from '@arcgis/core/layers/WebTileLayer'
import WFSLayer from '@arcgis/core/layers/WFSLayer'
import Point from '@arcgis/core/geometry/Point'
import Popup from '@arcgis/core/widgets/Popup'

export default {
  data() {
    return {
      map: null,
      view: null,
      JL1ImageLayer: null,
      tdtImageNoteLayer: null,
      areaLayer: null
    }
  },
  mounted() {
    this.geoUrl = process.env.VUE_APP_GEO_URL
    this.initMap()
  },
  methods: {
    initMap() {
      // 吉林一号影像瓦片
      // this.JL1ImageLayer = new WebTileLayer({
      //   id: 'JL1ImageLayer',
      //   urlTemplate: 'https://api.jl1mall.com/getMap/{z}/{x}/{y}?mk=73ad26c4aa6957eef051ecc5a15308b4&tk=24277043ba78c68ff6ed3b50ce486283&pro=ca3a3754837f49d7ac3068edce0e65f7&sch=wmts',
      // })
      // 天地图影像标注
      this.tdtImageNoteLayer = new WebTileLayer({
        id: 'tdtImageNoteLayer',
        title: 'tdtImageNoteLayer',
        urlTemplate: 'http://{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
        subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7']
      })

      this.areaLayer = new WFSLayer({
        id: 'areaLayer',
        url: this.geoUrl + '/geoserver/xingshan/wfs',
        name: 'xingshan:town',
        outFields: ['*'],
        renderer: {
          type: 'simple',
          symbol: {
            type: 'simple-fill',
            color: [0, 255, 0, 0.05],
            outline: {
              color: [255, 255, 255],
              width: 1
            }
          }
        }
      })

      this.map = new Map({
        basemap: {
          baseLayers: [
            // this.JL1ImageLayer,
            this.tdtImageNoteLayer
          ]
        }
      })
      this.view = new MapView({
        container: 'map',
        map: this.map,
        // 约束
        constraints: {
          minZoom: 11,
          maxZoom: 18
        },
        // popup: new Popup({}),
        // zoom: 10,
        // scale: 144447.638572,
        ui: {
          components: []
        }
      })

      const targetPoint = new Point({
        latitude: 31.31583431501697,
        longitude: 110.79744352250614
      })
      this.view.goTo({
        target: targetPoint,
        zoom: 12
      })

      this.view.when(() => {
        this.map.add(this.areaLayer)
      })

      this.view.on('click', (event) => {
        // console.log(this.view.center)
        // console.log(this.view)

        this.view.hitTest(event).then(response => {
          const results = response.results
          if(results.length > 0) {
            const ret = results[0]
            console.log(ret)
            // this.showPopup(ret)
          }
        })
        
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.leakMap {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  #map {
    width: 100%;
    height: 100%;
  }
  .baseBox {
    position: absolute;
    top: 20px;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
}
</style>