<template>
  <div class="wlStatistic">
    <div id="mars3dContainer" class="mars3d-container"></div>
    <button @click="showInfo">click</button>
  </div>
</template>
    
<script>
import { getAreaLastData } from '@/api/leakageLoss/screen'

// mars3d
export default {
  data() {
    return {
      deepHeight: 100,
      map: null,
      tmd: 0.5,
      opean: true,
      mapOptions: {
        scene: {
          //默认视角参数
          center: {
            // lat: 31.328109,
            // lng: 110.770702,
            // alt: 510892,
            // heading: 358,
            // pitch: -50,
            lat: 30.86450183783875,
            lng: 110.79447461389059,
            alt: 86272.00351360001,
            heading: 0,
            pitch: -60,
            roll: 359.9999662093228
          },
          sceneMode: 3,
        },
        control: {
          // baseLayerPicker: true, // basemaps底图切换按钮
          // homeButton: true, // 视角复位按钮
          // sceneModePicker: true, // 二三维切换按钮
          // navigationHelpButton: true, // 帮助按钮
          // fullscreenButton: true, // 全屏按钮
          // contextmenu: { hasDefault: true } // 右键菜单
          terrainProviderViewModels: [],
          contextmenu: {
            hasDefault:false
          }
        },
        basemaps: [
          { "id": 10, "name": "地图底图", "type": "group" },
          {
            "id": 2017,
            "pid": 10,
            "name": "暗色底图",
            "type": "gaode",
            // "icon": require("../../../assets/img/basemaps/blackMarble.png"),
            "layer": "vec",
            "invertColor": true,
            "filterColor": "#4e70a6",
            "brightness": 0.6,
            "contrast": 1.8,
            "gamma": 0.3,
            "hue": 1,
            "saturation": 0,
            "show": true
          }
        ]
      },
      param1: 1,
      underground: null,
      terrainPlanClip: null,
      terrainClip: null,
      eventTarget: new mars3d.BaseClass(),
      queryMapserver: null,
      geourl: '',
      villagePosition: [
        { name: '白鹤村', jwd: [110.7929993, 31.1529007] },
        { name: '竹溪村', jwd: [1110.7659988, 31.1315994] },
        { name: '双坪村', jwd: [110.7429962, 31.1299] },
        { name: '泗湘溪村', jwd: [110.7799988, 31.1485004] },
        { name: '秀龙村', jwd: [110.7539978, 31.1835003] },
        { name: '平邑口村', jwd: [110.7419968, 31.1718006] },
        { name: '普安村', jwd: [110.7160034, 31.1931] },
        { name: '琚坪村', jwd: [110.6650009, 31.2084999] },
        { name: '岩岭村', jwd: [110.6780014, 31.1884003] },
        { name: '建阳坪村', jwd: [110.8219986, 31.1261997] },
        { name: '黄家河村', jwd: [110.8860016, 31.1121006] },
        { name: '李家山村', jwd: [110.8330002, 31.1653996] },
        { name: '杨道河村', jwd: [110.8679962, 31.1972008] },
        { name: '漆树坪村', jwd: [110.8889999, 31.1662006] },
        { name: '石家坝村', jwd: [110.8710022, 31.1443005] },

        { name: '陈家湾村', jwd: [110.7289963, 31.2390003] },
        { name: '响龙村', jwd: [110.8040009, 31.2068996] },
        { name: '响滩村', jwd: [110.7559967, 331.2388] },
        { name: '金乐村', jwd: [110.7639999, 31.2019005] },
        { name: '昭君村', jwd: [110.697998, 31.2667007] },
        { name: '滩坪村', jwd: [110.6360016, 31.2591] },
        { name: '青华村', jwd: [110.7829971, 31.2479] },
        { name: '大礼村', jwd: [110.7249985, 31.2127991] },
        { name: '黄家堑村', jwd: [110.6729965, 31.2350998] },
        { name: '香溪社区居委会', jwd: [110.7549973, 31.2324009] },
        { name: '小河社区居委会', jwd: [110.7610016, 31.2240009] },
        
        { name: '龙珠社区居委会', jwd: [110.7440033, 31.3565998] },
        { name: '深渡河村', jwd: [110.7429962, 31.2777004] },
        { name: '麦仓村', jwd: [110.723999, 31.3101006] },
        { name: '北斗坪社区居委会', jwd: [110.762001, 31.3253994] },
        { name: '中阳垭村', jwd: [110.7440033, 31.4547005] },
        { name: '龙池村', jwd: [110.7770004, 31.2773991] },
        { name: '古洞村', jwd: [110.7440033, 31.3738003] },
        { name: '咸水村', jwd: [110.663002, 31.4305] },
        { name: '平水村', jwd: [110.8050003, 31.4773006] },
        { name: '夫子社区居委会', jwd: [110.7279968, 31.3407001] },
      ],
      areaData: [],
    };
  },
  created() {
    // this.getData()
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.initMap();
  },
  beforeDestroy() {
    if(this.map) {
      this.map.destroy()
    }
  },
  methods: {
    showInfo() {
      const viewer = (this.map.viewer)
      const cameraPosition = viewer.camera.positionWC
      const cartographic = Cesium.Cartographic.fromCartesian(cameraPosition)
      const longitude = Cesium.Math.toDegrees(cartographic.longitude)
      const latitude = Cesium.Math.toDegrees(cartographic.latitude)
      const height = cartographic.height
      console.log('latitude: ' + latitude)
      console.log('longitude: ' + longitude)
      console.log('height: ' + height)
      console.log('roll: ' + Cesium.Math.toDegrees(viewer.camera.roll))
      console.log('pitch: ' + Cesium.Math.toDegrees(viewer.camera.pitch))
      console.log('heading: ' + Cesium.Math.toDegrees(viewer.camera.heading))
    },
    getData() {
      getAreaLastData().then(res => {
        console.log(res)
        if(res.status === 200) {
          this.areaData = res.data.map(item => ({
            name: item.areaName,
            value: item.loss
          }))
        }
      })
    },
    initMap() {
      this.map = new mars3d.Map("mars3dContainer", this.mapOptions);
      this.map.container.style.backgroundColor = "#546a53"; // 背景色

      this.addBaseLayer(this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Atown&maxFeatures=50&outputFormat=application%2Fjson", 1, 'town');
      this.addBaseLayer(this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aarea_2&maxFeatures=50&outputFormat=application%2Fjson", 2, 'village');
      // this.addBaseLayer(this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aarea_3&maxFeatures=50&outputFormat=application%2Fjson", 3);

      // 创建矢量数据图层
      this.graphicLayer = new mars3d.layer.GraphicLayer()
      this.map.addLayer(this.graphicLayer)
      

      getAreaLastData().then(res => {
        console.log(res)
        if(res.status === 200) {
          this.areaData = res.data.map(item => ({
            name: item.areaName,
            value1: item.loss, // 漏损量
            value2: item.waterMeterDataTotal, // 售水量
            total: item.loss + item.waterMeterDataTotal // 供水量
          }))
          // this.showZT(this.areaData)
        }
      })

      // console.log(this.map)
      this.map.viewer.screenSpaceEventHandler.setInputAction((click) => {
        console.log(click, this.map)
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
    },
    // 创建并添加面图层
    addBaseLayer(str, zIndex, layerId) {
      let lineWidth = 1
      let lineColor = '#ffffff'
      let lineOpacity = 1
      if (zIndex == 1) {
        lineWidth = 3
        // lineColor = '#00ED21'
        lineColor = 'red'
        lineOpacity = 1
      } else if(zIndex == 2) {
        lineWidth = 2
        lineColor = '#ff0000'
        lineOpacity = 0
      } else {
        lineWidth = 1
        lineColor = '#000000'
        lineOpacity = 1
      }
      const graphicLayerGeo = new mars3d.layer.GeoJsonLayer({
        name: "区域",
        id: layerId,
        zIndex: zIndex,
        url: str,
        // format: this.simplifyGeoJSON, // 用于自定义处理geojson
        symbol: {
          type: "polygon",
          styleOptions: {
              clampToGround: true,
              classificationType: Cesium.ClassificationType.BOTH,
              setHeight: 0,
              opacity: 0.5,
              randomColor: zIndex === 1,
              outline: true,
              outlineStyle: {
                  width: lineWidth,
                  color: lineColor,
                  opacity: lineOpacity
              },
              highlight: {
                  type: "click",
                  // color: "#ffff00",
                  opacity: 1
              },
              label: {
                text: "{name}",
                // color: "#FFFFFF",
                font_size: 14,
                // scaleByDistance: true,
                // scaleByDistance_far: 20000000,
                // scaleByDistance_farValue: 0.1,
                // scaleByDistance_near: 1000,
                // scaleByDistance_nearValue: 1,

                // distanceDisplayCondition: false,
                // distanceDisplayCondition_far: 10000,
                // distanceDisplayCondition_near: 0,
            },
          },
          callback: function(attr, styleOpt) {
            if (attr.color) {
              return {
                color : attr.color,
              }
            }
          },
        },
        // popup: [
        //   { field: "name", name: "名称" },
        // ],
        // flyTo: true,
      });
      this.map.addLayer(graphicLayerGeo);
      // console.log(graphicLayerGeo)
      // 绑定事件
      graphicLayerGeo.on(mars3d.EventType.load, function (event) {
        // console.log("数据加载完成", event)
      })
      graphicLayerGeo.on(mars3d.EventType.click, function (event) {
          // 单击事件
        //  console.log("单击了图层", event)
      })
      // 绑定弹窗内容
      graphicLayerGeo.bindPopup((event) => {
        console.log(event)
        const attr = event.graphic.attr || {}
        return mars3d.Util.getTemplateHtml({
          title: '当前区域',
          template: `
            <div class="infoBody" style="padding:0 10px;">
              <div class="row" style="padding-top:10px;">当前区域</div>
              <div class="row" style="padding-top:10px;">{name}</div>
            </div>
          `,
          attr
        })
      }, {})
    },
    simplifyGeoJSON(geojson) {
      try {
        geojson = turf.simplify(geojson, {
          tolerance: 0.0001,
          highQuality: true,
          mutate: true,
        });
      } catch (e) {
        //
      }
      return geojson;
    },
    // 展示柱体
    showZT(data) {
      for(let i = 0; i < data.length; i++) {
        const attr = data[i]
        console.log(attr)
        const jwd = this.getJWDByName(attr.name)
        const value1 = attr['value1']
        const value2 = attr['value2']
        const total = attr['total']

        const html = `${attr.name}<br/>
                      <span style="color:#FF6D5D">供水量：${total}</span><br/>
                      <span style="color:#FFB861">售水量：${value2}</span><br/>
                      <span style="color:#63AEFF">漏损量：${value1}</span>`
        const height1 = Math.floor(value1 * 10)
        const height2 = Math.floor(value2 * 10)

        const p1 = Cesium.Cartesian3.fromDegrees(jwd[0], jwd[1], height1 / 2)
        const p2 = Cesium.Cartesian3.fromDegrees(jwd[0], jwd[1], height1 +  height2 / 2)

        // 添加柱体
        this.createZT(p1, height1, "#63AEFF", html)
        this.createZT(p2, height2, "#FF6D5D", html)

        // 添加文字
        const graphic = new mars3d.graphic.LabelPrimitive({
          position: Cesium.Cartesian3.fromDegrees(jwd[0], jwd[1], height1 + height2),
          style: {
            text: total,
            font_size: 18,
            font_family: "楷体",
            color: "#00ff00",
            outline: true,
            outlineColor: "#000000",
            outlineWidth: 1,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            pixelOffset: new Cesium.Cartesian2(0, -20)
          }
        })
        this.graphicLayer.addGraphic(graphic)
        graphic.bindTooltip(html)
      }
    },
    // 创建柱体
    createZT(position, height, color, html) {
      console.log('createZT')
      const graphic = new mars3d.graphic.CylinderEntity({
        position,
        style: {
          length: height,
          topRadius: 500.0,
          bottomRadius: 500.0,
          color
        }
      })
      this.graphicLayer.addGraphic(graphic)

      graphic.bindTooltip(html)

      return graphic
    },
    // 根据名字获取经纬度
    getJWDByName(name) {
      const ret = this.villagePosition.find(item => item.name === name)
      if(ret) {
        return ret.jwd
      }
      return []
    }
  },
};
</script>

<style lang="scss">
.wlStatistic {
  .mars3d-popup-background {
    background: rgba(2, 50, 128, 0.5);
    border: 1px solid #1377d0;
  }
  .mars3d-popup-tip-container {
    display: none;
  }
}
#mars3dContainer {
  .cesium-viewer-toolbar {
    // left: 465px;
  }
  .cesium-baseLayerPicker-dropDown-visible {
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    left: 48px
  }
}
</style>

<style scoped lang="scss">
.wlStatistic {
  position: absolute;
  width: 100%;
  height: 100%;
}
.showInfo {
  position: absolute;
  top: 0;
  right: 0;
}
  /**infoview浮动面板*/
  .infoview {
    position: absolute;
    top: 5px;
    left: 480px;
    padding: 10px 15px;
    border-radius: 4px;
    border: 1px solid rgba(128, 128, 128, 0.5);
    color: #ffffff;
    background: rgba(0, 0, 0, 0.4);
    box-shadow: 0 3px 14px rgba(128, 128, 128, 0.5);
    z-index: 19870101;
  }
  .mars-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  </style>
      