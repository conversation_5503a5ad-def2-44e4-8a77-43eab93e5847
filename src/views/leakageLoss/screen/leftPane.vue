<template>
  <div class="leftPane app-container">
    <!-- 年水平均表 -->
    <div class="waterInfo screen-common">
      <div class="title"><span>水平衡表</span></div>
      <div class="waterBox common-card">
        <PieChart height="200px" />
        <div class="cellBox">
          <div class="item" :class="`cell${index+1}`" v-for="item, index in info" :key="index">
            <span>{{item.label}}</span>
            <span style="font-weight: bold;">{{item.value}}万m³</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 年产销差率排名  -->
    <div class="center screen-common">
      <div class="title"><span>年产销差率排名</span></div>
      <div class="centerBox common-card">
        <RankBar :categoryData="rankCategoryData" :seriesData="rankSeriesData" width="410px" />
      </div>
    </div>

    <!-- 每月产销差   -->
    <div class="bottom screen-common">
      <div class="title"><span>每月产销差</span></div>
      <div class="bottomBox common-card">
        <LineChart :legendData="legendData" :xAxisData="xAxisData" />
      </div>
    </div>

  </div>
</template>

<script>
import PieChart from './components/pieChart'
import RankBar from './components/rankBar'
import LineChart from './components/lineChart'

import { getYearNRW, getMonthNRW } from '@/api/leakageLoss/screen'

export default {
  components: { PieChart, RankBar, LineChart },
  data() {
    return {
      info: [
        {
          label: '总供水量',
          value: 1480.74
        },
        {
          label: '注册用水量',
          value: 120.11
        },
        {
          label: '计费用水量',
          value: 1459.76
        },
        {
          label: '免费用水量',
          value: 223.74
        },
        {
          label: '漏损水量',
          value: 199.78
        },
        {
          label: '收益水量',
          value: 1258.86
        },
        {
          label: '产销差',
          value: 222.08
        }
      ],
      rankCategoryData: [],
      rankSeriesData: [],
      tempData: [],
      legendData: ['产水量', '售水量'],
      xAxisData: ['1', '2', '3', '4', '5', '6', '7'],
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      getYearNRW().then(res => {
        const list = res.data || []
        this.rankCategoryData = list.map(item => item.name)
        this.rankSeriesData = list.map(item => item.value)
      })
      getMonthNRW().then(res => {
        const mapData = res.data || {}
        Object.keys(mapData).forEach(key => {
          this.tempData.push({
            areaName: key,
            value: mapData[key]
          })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.leftPane {
  position: absolute;
  width: 430px;
  height: 100%;
  left: 30px;
  .waterInfo {
    height: 49%;
    .waterBox {
      height: 360px;
      .cellBox {
        height: 160px;
        padding: 10px;
        position: relative;
        .item {
          width: 98px;
          position: absolute;
          font-size: 14px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        .cell1 {
          height: 140px;
          background-color: rgba(2, 50, 128, 0.6);
        }
        .cell2 {
          top: 10px;
          left: 114px;
          height: 75px;
          background-color: rgba(0, 116, 255, 0.6);
        }
        .cell3 {
          top: 10px;
          left: 218px;
          height: 35px;
          background-color: rgba(0, 162, 254, 0.6);
        }
        .cell4 {
          top: 51px;
          left: 218px;
          height: 35px;
          background-color: rgba(31, 189, 235, 0.6);
        }
        .cell5 {
          bottom: 10px;
          left: 114px;
          width: 202px;
          height: 59px;
          background-color: rgba(2, 50, 128, 0.6);
        }
        .cell6 {
          top: 10px;
          right: 10px;
          height: 35px;
          background-color: rgba(0, 211, 96, 0.6);
        }
        .cell7 {
          bottom: 10px;
          right: 10px;
          height: 99px;
          background-color: rgba(62, 118, 176, 0.6);
        }
      }
    }
  }
  .center {
    height: 25.5%;
    .centerBox {
      height: 190px;
      padding-left: 20px;
    }
  }
  .bottom {
    // height: calc(100% - 620px);
    height: 25.5%;
    .bottomBox {
      height: calc(100% - 35px);
    }
  }
}
</style>
