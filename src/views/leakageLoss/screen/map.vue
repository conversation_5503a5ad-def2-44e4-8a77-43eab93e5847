<template>
  <div id="leakmap">
    <!-- <div class="legend">
      <el-tree
        :data="treeData"
        :props="defaultProps"
        show-checkbox
        class="legendtree"
        @node-click="handleNodeClick"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <img
            v-if="node.level === 2"
            src="../../../assets/img/pipe_network/pipe1.png"
            style="width: 20px; height: 20px; margin-right: 5px"
            alt=""
          />
          <span class="labeltext">{{ node.label }}</span>
        </span>
      </el-tree>
    </div> -->
  </div>
</template>

<script>
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import Ground from "@arcgis/core/Ground";
import WMSLayer from "@arcgis/core/layers/WMSLayer";
import WFSLayer from "@arcgis/core/layers/WFSLayer";
import Vue from "vue";
import TileInfo from "@arcgis/core/layers/support/TileInfo";
import WebTileLayer from "@arcgis/core/layers/WebTileLayer";
import Graphic from "@arcgis/core/Graphic";
import Polygon from "@arcgis/core/geometry/Polygon";
import Point from "@arcgis/core/geometry/Point";
import Draw from "@arcgis/core/views/draw/Draw";
export default {
  data() {
    return {
      map: null,
      view: null,
      area: null,
      fenxian: null,
      xslabel: null,
      water1: null,
      water2: null,
      treeData: [
        {
          label: "分区1",
          children: [
            // {
            //   label: "阀门",
            // },
            // {
            //   label: "水表",
            // },
            // {
            //   label: "流量计",
            // },
            // {
            //   label: "压力计",
            // },
            // {
            //   label: "雨量计",
            // },
          ],
        },
        {
          label: "分区2",
          children: [
            // {
            //   label: "节点",
            // },
            // {
            //   label: "管线",
            // },
          ],
        },
        {
          label: "分区3",
          children: [
            // {
            //   label: "水厂",
            // },
            // {
            //   label: "水池",
            // },
          ],
        },
      ],
      defaultProps: {
        children: "children",
        label: "label",
      },
      geourl: "",
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.createmap();
  },
  methods: {
    handleNodeClick(data) {
      console.log(data);
    },
    createmap() {
      // 县界
      this.fenxian = new WMSLayer({
        url: this.geourl + "/geoserver/xschina/wms",
      });
      // 注记
      this.xslabel = new WMSLayer({
        url: this.geourl + "/geoserver/xslabel/wms",
      });
      // 水系
      this.water1 = new WMSLayer({
        url: this.geourl + "/geoserver/water1/wms",
      });
      // 水系
      this.water2 = new WMSLayer({
        url: this.geourl + "/geoserver/wate2/wms",
      });
      this.map = new Map({
        ground: new Ground({
          layers: [],
          surfaceColor: "transparent",
          opacity: 0,
        }),
        layers: [],
      });
      this.view = new MapView({
        container: "leakmap",
        map: this.map,
        background: {
          type: "color",
          color: [255, 252, 244, 0],
        },
        extent: {
          xmax: 111.15191207080001,
          xmin: 110.39483293,
          ymax: 31.591080100800003,
          ymin: 30.9630324254,
        },
        spatialReference: {
          wkid: 4326,
        },
      });
      this.view.ui.remove("attribution");
      this.view.ui.empty("top-left");
      this.view.map.add(this.fenxian);
      this.view.map.add(this.xslabel);
      this.view.map.add(this.water1);
      this.view.map.add(this.water2);
      this.area = new WFSLayer({
        url: this.geourl + "/geoserver/area/wms",
        outFields: "*",
        // effect: "drop-shadow(2px, 2px, 3px) bloom(2.5 0 0.5)",
        popupTemplate: {
          title: "{name}",
          content: [
            {
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "value",
                  // label: "值"
                },
                {
                  fieldName: "normal",
                },
                {
                  fieldName: "data_time",
                },
              ],
            },
          ],
        },
      });
      this.view.map.add(this.area);
      this.view.on("click", async (e) => {
        console.log(e);
        this.view.hitTest(e).then((res) => {
          console.log(res);
        });
      });
    },
  },
};
</script>
<style lang="scss">
.esri-view-width-medium .esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0.3);
}
.esri-popup__header {
  background-color: rgba(4, 15, 45, 0.3) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  background-color: rgba(4, 15, 45, 0) !important;
}
.esri-popup__icon,
.esri-icon-close {
  color: #fff !important;
}
.esri-popup__content {
  margin: 0;
}
.esri-feature {
  background-color: rgba(4, 15, 45, 0.3);
  padding: 0 15px 12px;
}
.esri-widget__table {
  color: #fff;
}
.esri-popup__header-title {
  background-color: rgba(4, 15, 45, 0) !important;
  color: #fff;
}
.esri-popup__pointer-direction {
  background-color: rgba(4, 15, 45, 0.3);
}

.esri-popup__footer {
  display: none;
}
.esri-popup__feature-menu {
  display: none;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
.esri-view-surface {
  outline: none !important;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: auto 0px Highlight !important;
  outline: auto 0px -webkit-focus-ring-color !important;
}
[class*="esri-popup--is-docked-top-"] .esri-popup__footer,
[class*="esri-popup--aligned-bottom-"] .esri-popup__footer {
  border-bottom: solid 1px #6e6e6e4d;
}
.esri-popup__header {
  border-top-left-radius: 5px !important;
  border-top-right-radius: 5px;
  background-color: #fff;
  color: #fff;
}
.esri-popup--shadow {
  box-shadow: 0 0 0 0 rgb(155, 155, 155) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  outline: none !important;
}
.esri-ui .esri-popup {
  border-radius: 5px !important;
}
.esri-popup {
  position: absolute !important;
}
.esri-popup__button {
  background-color: transparent !important;
  outline: none;
}
</style>
<style scoped lang="scss">
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
.legend {
  position: absolute;
  bottom: 20px;
  right: 30px;
  width: 220px;
  height: 155px;
  background: rgba(4, 15, 45, 0.5);
  border-radius: 4px;
  padding: 20px;
}
.legendtree {
  background: transparent;
  .labeltext {
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
  }
  ::v-deep.el-tree-node__content:hover {
    background: transparent;
  }
  ::v-deep.el-tree-node:focus > .el-tree-node__content {
    background-color: transparent;
  }
  ::v-deep.el-tree-node__label {
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
  }
  ::v-deep.el-checkbox__inner {
    width: 16px;
    height: 16px;
    background: rgba(0, 57, 112, 0.5);
    border: 1px solid #0285ff;
  }
}
</style>