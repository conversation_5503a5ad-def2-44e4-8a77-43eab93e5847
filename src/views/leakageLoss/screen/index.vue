<template>
  <div class="WlScreen">
    <!-- <img
      :src="isMapMode ? full.active : full.default"
      :class="isMapMode ? 'imgactive' : 'imgdefault'"
      alt="fullscreen"
      title="扩展"
      style="padding: 4px"
      @click="toggleMode"
    /> -->

    <LeakMap />

    <!-- 面板控制 -->
    <div class="mapMode" @click="changeMapMode" :class="{ mapModeFull: isFull }">
      <img v-if="isFull" src="@/assets/img/home/<USER>/full-active.png" alt="">
      <img v-else src="@/assets/img/home/<USER>/full.png" alt="">
    </div>

    <LeftPane v-if="!isFull" />
    <RightPane v-if="!isFull" />
    
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="leakage" />
  </div>
</template>

<script>
import LeftPane from "./leftPane";
import RightPane from "./rightPane";
import LeakMap from "./layer";
// import LeakMap from './mapNew'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: "WlScreen",
  components: { LeftPane, RightPane, LeakMap, FloatingSideMenu },
  data() {
    return {
      isMapMode: false,
      isFull: false,
      full: {
        default: require("../../../assets/img/pipe_network/full.png"),
        active: require("../../../assets/img/pipe_network/full-active.png"),
      },
    };
  },
  methods: {
    changeMapMode() {
      this.isFull = !this.isFull
    },
    toggleMode(){
      this.isMapMode = !this.isMapMode
    }
  }
};
</script>

<style lang="scss" scoped>
.WlScreen {
  height: calc(100vh - 84px);
  color: #ffffff;
  position: relative;
  .mapMode {
    position: absolute;
    top: 20px;
    left: 480px;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .mapModeFull {
    left: 30px;
  }
  .imgdefault {
    padding: 4px;
    position: absolute;
    z-index: 99;
    left: 510px;
    top: 20px;
    display: block;
    width: 50px;
  }
  .imgactive {
    padding: 4px;
    position: absolute;
    z-index: 99;
    left: 20px;
    top: 20px;
    display: block;
    width: 50px;
  }
  .map {
    position: absolute;
    left: 500px;
    top: 0;
    width: 910px;
    height: calc(100% - 30px);
  }
  .activemap {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}
</style>
