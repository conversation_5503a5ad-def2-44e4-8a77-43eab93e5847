<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'
import { Decimal } from 'decimal.js'
import { number } from 'echarts/lib/export'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Number,
      default: 0
    },
    color: {
      type: String,
      default: '#65b8f8'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      // deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        polar: {
          radius: 72,
          center: ["50%", "50%"],
        },
        angleAxis: {
          max: 100,
          show: false,
        },
        radiusAxis: {
          type: "category",
          show: true,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        title: {
          text: this.seriesData + '%',
          textStyle: {
            color: this.color,
            fontFamily: 'Microsoft YaHei',
            fontWeight: 400
          },
          left: 'center',
          top: 'middle'
        },
        series: [
          {
            name: "",
            type: "bar",
            roundCap: true,
            barWidth: 8,
            showBackground: true,
            backgroundStyle: {
              color: "rgba(42, 93, 210, 0.5)",
            },
            data: [this.seriesData],
            coordinateSystem: "polar",
            itemStyle: {
              color: this.color
            },
          }
        ],
      })
    }
  }
}
</script>
