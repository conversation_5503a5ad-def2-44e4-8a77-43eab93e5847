<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        color: ['#00E4FF', '#18D565'],
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          textStyle: {
            color: '#fff'
          },
          top: 5,
          right: 15,
          data: this.legendData
        },
        grid: {
          top: 30,
          left: 0,
          right: 15,
          bottom: 30,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#ACC6EA',
              width: 1
            }
          },
          axisLabel: {
            color: '#DEEBFF',
            interval: 0,
            showMaxLabel: true
          },
          axisTick: {
            show: false
          },
          data: this.xAxisData
        },
        yAxis: {
          type: 'value',
          min: 0,        
          axisLine: {
            lineStyle: {
              color: '#ACC6EA',
              width: 1
            }
          },
          axisLabel: {
            color: '#DEEBFF'
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#2D3C5C',
            }
          },
        },
        series: [
          {
            name: '产水量',
            type: 'line',
            symbol: 'circle',
            symbolSize: 6,
            areaStyle: {
              opacity:0.15
            },
            data: [45, 42, 38, 40, 47, 43, 45]
          },
          {
            name: '售水量',
            type: 'line',
            symbol: 'circle',
            symbolSize: 6,
            areaStyle: {
              opacity:0.15
            },
            data: [30, 28, 21, 25, 31, 29, 30]
          }
        ]
      })
    }
  }
}
</script>
