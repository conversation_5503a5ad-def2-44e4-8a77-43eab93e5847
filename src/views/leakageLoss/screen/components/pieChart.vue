<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        tooltip: {
          trigger: 'item'
        },
        series: [
          // 内
          {
            color: ['#00D360', '#3E76B0'],
            type: 'pie',
            radius: [0, '45%'],
            center: ['50%', '60%'],
            label: {
              position: 'inner'
            },
            labelLine: {
              show: false
            },
            data: [
              {
                name: '收益差',
                value: 70
              },
              {
                name: '产销差',
                value: 30
              }
            ]
          },
          // 外
          {
            color: ['#00CFFF', '#0269E9', '#C6ECDF'],
            type: 'pie',
            radius: ['65%', '79%'],
            center: ['50%', '60%'],
            labelLine: {
              length: 10,
              length2: 60,
              lineStyle: {
                color: '#ACC6EA'
              }
            },
            label: {
              // '{top | {b}} \n {c}万m³({d}%)'
              formatter: '{top|{b}} \n {bottom|{c}万m³}({d}%)',
              padding: -50,
              // distanceToLabelLine: 5,
              rich: {
                top: {
                  padding: 5
                },
                bottom: {
                  padding: 5
                }
              }
            },
            data: [
              {
                name: '计费用水量',
                value: 70
              },
              {
                name: '免费用水量',
                value: 20
              },
              {
                name: '漏损水量',
                value: 10
              }
            ]
          }
        ]
      })
    }
  }
}
</script>
