<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    gaugeData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    gaugeData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'gauge',
            radius: '100%',
            center: ['25%', '60%'],
            startAngle: 180,
            endAngle: 0,
            axisLine: {
              show: true,
              lineStyle: {
                width: 15,
                color: [
                  [
                    this.gaugeData[0],
                    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                      {
                        offset: 0,
                        color: "#0CF5FE",
                      },
                      {
                        offset: 1,
                        color: "#44F075",
                      }
                    ])
                  ],
                  [1, '#2A4E71']
                ]
              }
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            pointer: {
              show: false,
            },
            title: {
              textStyle: {
                color: '#ACC6EA',
                fontSize: 14,
              },
              offsetCenter: [0, "25%"]
            },
            detail: {
              color: '#01F6F4',
              fontSize: 24,
              fontWeight: 'bold',
              offsetCenter: [0, '-10%'],
            },
            data: [{
              name: "漏损率(%)",
              value: (this.gaugeData[0] * 100).toFixed(2),
            }
            ]
          },
          {
            type: 'gauge',
            radius: '100%',
            center: ['75%', '60%'],
            startAngle: 180,
            endAngle: 0,
            axisLine: {
              show: true,
              lineStyle: {
                width: 15,
                color: [
                  [
                    this.gaugeData[1],
                    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                      {
                        offset: 0,
                        color: "#F28E26",
                      },
                      {
                        offset: 1,
                        color: "#FD644F",
                      }
                    ])
                  ],
                  [1, '#2A4E71']
                ]
              }
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            pointer: {
              show: false,
            },
            title: {
              textStyle: {
                color: '#ACC6EA',
                fontSize: 14,
              },
              offsetCenter: [0, "25%"]
            },
            detail: {
              color: '#F67B39',
              fontSize: 24,
              fontWeight: 'bold',
              offsetCenter: [0, '-10%'],
            },
            data: [
              {
                name: "产销差率(%)",
                value: (this.gaugeData[1] * 100).toFixed(2),
              }
            ]
          }
        ]
      })
    }
  }
}
</script>
