<template>
  <div class="rightPane app-container">
    <!-- DMA漏损分析 -->
    <div class="top screen-common">
      <div class="title"><span>DMA漏损分析</span></div>
      <div class="topBox common-card">
        <section class="zone">
          <div class="item zoneItem" v-for="item, index in zoneList" :key="index">
            <span class="valueText">{{item.value}}</span>
            <span class="labelText">{{item.label}}</span>
          </div>
        </section>
        <section class="zone" style="margin-top: 10px;">
          <div class="item zoneItem" v-for="item, index in infoList" :key="index">
            <div class="innerTop">
              <span class="valueText">{{item.value}}</span>
              <span>
                <i v-if="item.isUp" class="el-icon-top iocn up"></i>
                <i v-else class="el-icon-bottom iocn down"></i>
                <span class="rateText">{{item.rate}}%</span>
              </span>
            </div>
            <span class="labelText">{{item.label}}</span>
          </div>
        </section>

        <div class="chartBox">
          <GaugeChart height="120px" :gaugeData="gaugeData" />
        </div>
      </div>
    </div>

    <!-- 报警信息 -->
    <div class="center screen-common">
      <div class="title"><span>漏损占比分析</span></div>
      <!-- 饼图 -->
      <div class="centerBox common-card">
        <div class="bigPieBox">
          <div class="total">
            <div class="top">
              <span>{{ warnCount }}</span>
              <span style="font-size: 16px;">条</span>
            </div>
            <div style="font-size: 14px;">报警总数</div>
          </div>
          <BigPie :seriesData="pieData" />
        </div>
        <!-- 列表 -->
        <!-- <div class="list">
          <div class="listTitle">
            <div>分区流量报警</div>
            <div class="warnCount">{{ warnList.length }}</div>
          </div>
          <div class="listWrapper">
            <el-scrollbar class="scrollBox">
              <div class="listItem" v-for="item, index in warnList" :key="index">
                <div style="width: 85px;">{{item.name}}</div>
                <div style="width: 85px;">{{item.station}}</div>
                <div style="flex-grow: 1;">{{item.msg}}</div>
                <div style="width: 90px;">{{item.time}}</div>
              </div>
            </el-scrollbar>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 漏损报警统计 -->
    <div class="bottom screen-common">
      <div class="title"><span>漏损报警统计</span></div>
      <div class="bottomBox common-card">
        <div>
          <SmallCircle width="90px" height="90px" :seriesData="blue" />
          <div class="label">蓝色关注</div>
        </div>
        <div>
          <SmallCircle width="90px" height="90px" :seriesData="orange" color="#eda44e" />
          <div class="label">黄色预警</div>
        </div>
        <div>
          <SmallCircle width="90px" height="90px" :seriesData="red" color="#ec808d" />
          <div class="label">红色报警</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PieChart from './components/pieChart'
import GaugeChart from './components/gaugeChart'
import BigPie from './components/bigPie'
import SmallCircle from './components/smallCircle'

import { getLeakageLossWarnCount, getLeakageLossWarnCountForLevel } from '@/api/leakageLoss/screen'

export default {
  components: { PieChart, GaugeChart, BigPie, SmallCircle },
  data() {
    return {
      zoneList: [
        {
          label: '一级分区',
          value: 3
        },
        {
          label: '二级分区',
          value: 11
        },
        {
          label: '三级分区',
          value: 62
        }
      ],
      infoList: [
        {
          label: '日供水量(m3)',
          value: 5654,
          isUp: true,
          rate: 0.3
        },
        {
          label: '日漏水量(m3)',
          value: 0.75,
          isUp: false,
          rate: 1.24
        },
        {
          label: '日产销差(m3)',
          value: 97.83,
          isUp: false,
          rate: 0.5
        }
      ],
      gaugeData: [0.182, 0.137],
      warnList: [
        {
          name: '流量仪表-1',
          station: '古夫镇',
          msg: '已离线2小时',
          time: '08-30 13:07:04'
        },
        {
          name: '流量仪表-1',
          station: '古夫镇',
          msg: '已离线2小时',
          time: '08-30 13:07:04'
        },
        {
          name: '流量仪表-1',
          station: '古夫镇',
          msg: '已离线2小时',
          time: '08-30 13:07:04'
        },
        {
          name: '流量仪表-1',
          station: '古夫镇',
          msg: '已离线2小时',
          time: '08-30 13:07:04'
        },
        {
          name: '流量仪表-1',
          station: '古夫镇',
          msg: '已离线2小时',
          time: '08-30 13:07:04'
        },
        {
          name: '流量仪表-1',
          station: '古夫镇',
          msg: '已离线2小时',
          time: '08-30 13:07:04'
        },
        {
          name: '流量仪表-1',
          station: '古夫镇',
          msg: '已离线2小时',
          time: '08-30 13:07:04'
        },
        {
          name: '流量仪表-1',
          station: '古夫镇',
          msg: '已离线2小时',
          time: '08-30 13:07:04'
        }
      ],
      warnCount: 0,
      pieData: [],
      blue: 0,
      orange: 0,
      red: 0
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      getLeakageLossWarnCount().then(res => {
        const list = res.data || []
        this.pieData = list
        this.warnCount = list.reduce((total, item) => {
          return total + item.value
        }, 0)
      })

      getLeakageLossWarnCountForLevel().then(res => {
        const list = res.data || []
        list.forEach(item => {
          if (item.name == '3') {
            this.blue = item.value
          }
          if (item.name == '2') {
            this.orange = item.value
          }
          if (item.name == '1') {
            this.red = item.value
          }
        })
      })
    }
  }
}
</script>

<style lang="scss">
.rightPane {
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
</style>

<style lang="scss" scoped>
.rightPane {
  position: absolute;
  width: 430px;
  height: 100%;
  right: 30px;
  .top {
    .topBox {
      height: 282px;
      padding: 10px;
    }
    .item {
      width: 120px;
      height: 64px;
      background-color: rgba(17, 62, 102, 0.3);
    }
    .zone {
      display: flex;
      justify-content: space-between;
    }
    .zoneItem {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 14px 0 12px 16px;
    }
    .valueText {
      font-size: 18px;
      font-weight: bold;
    }
    .labelText {
      font-size: 12px;
      color: #ACC6EA;
    }
    .innerTop {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .iocn {
      font-weight: bold;
    }
    .up {
      color: #D33803;
    }
    .down {
      color: #69FF00;
    }
    .rateText {
      font-size: 12px;
      font-weight: bold;
      padding-right: 8px;
    }
  }
  .center {
    height: calc((100% - 317px) / 2);
    // height: 245px;
    .centerBox {
      padding-top: 10px;
      height: calc(100% - 35px);
    }
    .bigPieBox {
      height: 100%;
      position: relative;
      .total {
        width: 100px;
        height: 100px;
        border: 1px solid #2A5DD2;
        border-radius: 50%;
        background-color: rgba(0, 27, 70, 0.5);
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50px, -50px);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .top {
          color: #00CFFF;
          font-size: 36px;
          font-weight: bold;
        }
      }
    }
    .list {
      height: calc(100% - 210px);
      padding: 10px;
      margin: 0 10px;
      background-color: rgba(8, 28, 82, 0.45);
      .listTitle {
        color: #52D0DF;
        font-size: 18px;
        font-weight: bold;
        padding: 9px 20px;
        display: flex;
        align-items: center;
        .warnCount {
          width: 24px;
          height: 18px;
          text-align: center;
          line-height: 18px;
          background-color: #E8202B;
          font-size: 12px;
          color: #FFFFFF;
          border-radius: 9px;
          margin-left: 20px;
        }
      }
      .listWrapper {
        height: calc(100% - 42px);
        // overflow-y: auto;
        .scrollBox {
          height: 100%;
        }
      }
      .listItem {
        font-size: 12px;
        display: flex;
        align-items: center;
        height: 30px;
        background-color: rgba(2,50,128,0.23);
        border: 1px solid #023280;
        margin-bottom: 7px;
        padding-left: 9px;
      }
    }
  }
  .bottom {
    height: calc((100% - 317px) / 2);
    .bottomBox {
      height: calc(100% - 35px);
      display: flex;
      align-items: center;
      justify-content: space-around;
      .label {
        text-align: center;
        padding-top: 5px;
      }
    }
  }
}
</style>
