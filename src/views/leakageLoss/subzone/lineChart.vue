<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
import resize from "@/utils/chartResize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    seriesData: {
      type: Array,
      default: () => [],
    },
    xAxisData: {
      type: Array,
      default: () => [],
    },
    yAxisName: {
      type: String,
      default: "",
    },
    legendData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart();
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el);
      this.chart.setOption(
        {
          color: ["#1B7EF2", "#29F1FA", "#1CFF27", "#FF5733"],
          tooltip: {
            trigger: "axis",
            // formatter: function(params) {
            //   console.log(params)
            //   return '1'
            // }
          },
          legend: {
            textStyle: {
              color: "#fff",
            },
            data: this.legendData,
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            // top: '10%',
            containLabel: true,
          },
          xAxis: {
            boundaryGap: false,
            axisLine: {
              lineStyle: {
                color: "#B0D7FF"
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#B0D7FF'
            },
            data: this.xAxisData,
          },
          yAxis: {
            name: this.yAxisName,
            nameTextStyle: {
              color: '#fff'
            },
            type: "value",
            axisLine: {
              show: false
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#B0D7FF'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(176, 215, 255, 0.30)",
              },
            },
          },
          series: this.seriesData,
        },
        {
          notMerge: true,
        }
      );
    },
  },
};
</script>
