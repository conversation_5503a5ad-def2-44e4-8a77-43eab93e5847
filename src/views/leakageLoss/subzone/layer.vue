<template>
  <div>
    <div id="mars3dContainer" class="mars3d-container"></div>
  </div>
</template>
    
<script>
export default {
  data() {
    return {
      deepHeight: 100,
      map: null,
      tmd: 0.5,
      opean: true,
      mapOptions: {
        scene: {
          //默认视角参数
          center: {
            lat: 31.248109,
            lng: 110.790702,
            alt: 15000,
            heading: 0,
            pitch: -90,
          },
          sceneMode: 2,
        },
        control: {
          // baseLayerPicker: true, // basemaps底图切换按钮
          // homeButton: true, // 视角复位按钮
          // sceneModePicker: true, // 二三维切换按钮
          // navigationHelpButton: true, // 帮助按钮
          // fullscreenButton: true, // 全屏按钮
          // contextmenu: { hasDefault: true } // 右键菜单
          terrainProviderViewModels: [],
          contextmenu: {
            hasDefault:false
          }
        },
        basemaps: [
          { "id": 10, "name": "地图底图", "type": "group" },
          {
            "id": 2017,
            "pid": 10,
            "name": "暗色底图",
            "type": "gaode",
          //   "icon": require("../../../assets/img/basemaps/blackMarble.png"),
            "layer": "vec",
            "invertColor": true,
            "filterColor": "#4e70a6",
            "brightness": 0.6,
            "contrast": 1.8,
            "gamma": 0.3,
            "hue": 1,
            "saturation": 0,
            "show": true
          }
        ]
      },
      param1: 1,
      underground: null,
      terrainPlanClip: null,
      terrainClip: null,
      eventTarget: new mars3d.BaseClass(),
      queryMapserver: null,
      geourl: '',
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.initMap();
  },
  methods: {
    initMap() {
      this.map = new mars3d.Map("mars3dContainer", this.mapOptions);
      this.map.container.style.backgroundColor = "#546a53"; // 背景色
      // this.addXinshanBeijingbeijing();
      // this.showGeoJsonLayer();

      this.addBaseLayer(this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aarea_1&maxFeatures=50&outputFormat=application%2Fjson",1);
      this.addBaseLayer(this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aarea_2&maxFeatures=50&outputFormat=application%2Fjson",2);
      this.addBaseLayer(this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aarea_3&maxFeatures=50&outputFormat=application%2Fjson",3);

    },
    // 视角飞向给的区域名
    flyToGraphic(name) {
      this.map.flyHome();
      this.map.eachLayer((layer) => {
        if(layer._graphicList && layer._graphicList.length > 0) {
          const list = layer._graphicList._array
          for (let index = 0; index < list.length; index++) {
            const item = list[index];
            if (item._name == name) {
              this.map.flyToGraphic(item, {
                scale: 1.6,
                minHeight: 45000,
                maxHeight: 66000
              })
              item.openPopup()
              return
            }
          }
        }
      }, {}, true) 
    },
    addBaseLayer(str, zIndex) {
      const graphicLayerGeo = new mars3d.layer.GeoJsonLayer({
        name: "区域",
        zIndex: zIndex,
        url: str,
        format: this.simplifyGeoJSON, // 用于自定义处理geojson
        graphicOptions: new mars3d.graphic.PolygonEntity({
          // id: "{id}",
          name:"{name}"
        }),
        symbol: {
          type: "polygon",
          styleOptions: {
              clampToGround: true,
              classificationType: Cesium.ClassificationType.BOTH,
              setHeight: 0,
              opacity: 0.5,
              randomColor: true,
              outline: true,
              outlineStyle: {
                  width: 1,
                  color: "#fffdf5",
                  opacity: 1
              },
              highlight: {
                  type: "click",
                  // color: "#ffff00",
                  opacity: 1
              },
              label: {
                text: "{name}",
                color: "#000000",
                font_size: 15,
                scaleByDistance: true,
                scaleByDistance_far: 60000000,
                scaleByDistance_farValue: 0.2,
                scaleByDistance_near: 1000000,
                scaleByDistance_nearValue: 0.9
            },
          },
          callback: function(attr, styleOpt) {
            if (attr.color) {
              return {
                color : attr.color,
              }
            }
          },
        },
        popup: [
          { field: "name", name: "名称" },
          ],
      //   flyTo: true,
      });
      this.map.addLayer(graphicLayerGeo);
      // console.log(graphicLayerGeo)
      // 绑定事件
      graphicLayerGeo.on(mars3d.EventType.load, function (event) {
          console.log("数据加载完成1", event)
      })
      graphicLayerGeo.on(mars3d.EventType.click, function (event) {
          // 单击事件
          console.log("单击了图层", event)
      })
    },
    addXinshanBeijingbeijing() {
      const graphicLayerGeo = new mars3d.layer.GeoJsonLayer({
        name: "兴山背景",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Axingzheng&maxFeatures=50&outputFormat=application%2Fjson",
        format: this.simplifyGeoJSON, // 用于自定义处理geojson
        symbol: {
          type: "polygonC",
          styleOptions: {
              opacity: 0.1,
              randomColor: true,
              outline: true,
              outlineStyle: {
                  width: 2,
                  color: "#fffdf5",
                  opacity: 1,

              },
              // highlight: {
              //     type: "click",
              //     color: "#ffff00",
              //     opacity: 1,
              // },
              label: {
                  font_size: 15,
                  text: "{name}",
              }
          },
          callback: function(attr, styleOpt) {
              if (attr.color) {
                  return { color : attr.color}
              }
          },
        },
      //   popup: [
      //     { field: "name", name: "名称" },
      //     ],
      //   flyTo: true,
      });
      this.map.addLayer(graphicLayerGeo);
    },
    showGeoJsonLayer() {
      const graphicLayerGeo = new mars3d.layer.GeoJsonLayer({
        name: "区域",
        url: this.geourl + "/geoserver/area/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=area%3Aarea&maxFeatures=50&outputFormat=application%2Fjson",
        format: this.simplifyGeoJSON, // 用于自定义处理geojson
        symbol: {
          type: "polygonC",
          styleOptions: {
              opacity: 0.3,
              randomColor: true,
              outline: true,
              outlineStyle: {
                  width: 2,
                  color: "#fffdf5",
                  opacity: 1,

              },
              highlight: {
                  type: "click",
                  color: "#ffff00",
                  opacity: 1,
              },
              label: {
                  font_size: 15,
                  text: "{name}",
              }
          },
          callback: function(attr, styleOpt) {
              if (attr.color) {
                  return { color : attr.color}
              }
          },
        },
        popup: [
          { field: "name", name: "名称" },
          ],
      //   flyTo: true,
      });
      this.map.addLayer(graphicLayerGeo);
      // console.log(graphicLayerGeo)
      // 绑定事件
      graphicLayerGeo.on(mars3d.EventType.load, function (event) {
          console.log("数据加载完成", event)
      })
      graphicLayerGeo.on(mars3d.EventType.click, function (event) {
          // 单击事件
          console.log("单击了图层", event)
      })
    },
    simplifyGeoJSON(geojson) {
      try {
        geojson = turf.simplify(geojson, {
          tolerance: 0.0001,
          highQuality: true,
          mutate: true,
        });
      } catch (e) {
        //
      }
      return geojson;
    },
  },
};
</script>

<style lang="scss">
#mars3dContainer {
  .cesium-viewer-toolbar {
    left: 465px;
  }
  .cesium-baseLayerPicker-dropDown-visible {
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    left: 48px
}
}
</style>

<style scoped lang="scss">
  /**infoview浮动面板*/
  .infoview {
    position: absolute;
    top: 5px;
    left: 480px;
    padding: 10px 15px;
    border-radius: 4px;
    border: 1px solid rgba(128, 128, 128, 0.5);
    color: #ffffff;
    background: rgba(0, 0, 0, 0.4);
    box-shadow: 0 3px 14px rgba(128, 128, 128, 0.5);
    z-index: 19870101;
  }
  .mars-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  </style>
      