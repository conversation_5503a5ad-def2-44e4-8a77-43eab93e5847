<template>
  <div class="newpopup">
    <div class="title">
      <span>详情</span>
      <i class="el-icon-close close" @click="closepopup" />
    </div>
    <div class="gedang"></div>
    <div class="content" style="margin-bottom: 12px;">
      <span v-for="(val) in obj" :key="val">{{ val }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "Popup",
  props: ["obj", "view"],
  methods: {
    closepopup() {
      this.view.popup.visible = false;
    },
  },
};
</script>

<style scoped lang="scss">
.newpopup {
  width: 100%;
  height: 100%;
  max-width: 250px;
  max-height: 200px;
  background: url("../../../assets/img/tc.png");
  background-size: 100% 100%;
  padding: 20px 32px;
    position: relative;
    left: 1px;
    top: 30px;
    pointer-events:none;
  .title {
    display: flex;
    justify-content: space-between;
    padding: 8px 0 8px 0;
    color: #4d6489;
    // height: 44px;
    font-size: 20px;
    line-height: 44px;
    span {
      width: 33px;
      height: 16px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #61f9f8;
      line-height: 16px;
    }

    .close {
      line-height: 16px;
      color: #ffffff;
      font-size: 16px;
      pointer-events:visible;
    }
  }
  .gedang {
    width: 178px;
    height: 1px;
    // margin-left: 12px;
        margin-left: -20px;
    background-image: linear-gradient(
      to bottom left,
      rgba(97, 249, 248, 0),
      rgba(97, 249, 248, 1)
    );
  }
  .content {
    padding: 10px 0;
    color: #ffffff;
    display: grid
  }
}
</style>