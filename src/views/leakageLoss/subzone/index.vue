<template>
  <div class="WlPressure app-container">
    <div class="tab">
      <div class="first" :class="{ active: currentType === 'tab1' }" @click="handleTabClick('tab1')">分区总览</div>
      <div class="other" :class="{ active: currentType === 'tab2' }" @click="handleTabClick('tab2')">新建分区</div>
    </div>
    <div class="compenent-container">
      <PressureMap v-if="currentType === 'tab1'" />
      <PressureList v-else />
    </div>
  </div>
</template>

<script>
import PressureMap from './map'
import PressureList from './list'

export default {
  components: { 
    PressureMap,
    PressureList
  },
  data() {
    return {
      currentType: 'tab1'
    }
  },
  methods: {
    handleTabClick(type) {
      this.currentType = type
    }
  }
}
</script>

<style lang="scss">
.WlPressure { 
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.WlPressure {
  padding: 0 30px;
  height: calc(100vh - 84px);
  .compenent-container {
    color: #ffffff;
    // padding: 20px;
    height: calc(100% - 76px);
    border: 1px solid #2A5DD2;
    border-top-color: #3084B5;
    position: relative;
  }
}
</style>
