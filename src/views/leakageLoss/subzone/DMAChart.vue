<template>
  <div class="subzone">
    <div class="treeBox">
      <el-input
        placeholder="请输入区域"
        suffix-icon="el-icon-search"
        v-model="filterText"
        size="medium">
      </el-input>
      <el-scrollbar class="treeContainer">
        <!-- 滚动条要包裹的tree内容 --> 
        <el-tree 
          :data="tree"
          node-key="id"
          :props="elTreeProps"
          @node-click="nodeClick"
          :filter-node-method="filterNode"
          ref="areaTree"
        ></el-tree>
      </el-scrollbar>
    </div>

    <div class="right">
      <div class="map">
        <!-- <img style="width: 100%;height: 100%;" src="@/assets/img/leakageLoss/map.png" alt=""> -->
        <Layer style="height: 100%"  ref="map"></Layer>
      </div>
      <div class="chartBox">
        <div class="item">
          <div class="label"><span class="text">城市供水量/售水量趋势</span></div>
          <div class="innerBox">
            <LineChart :seriesData="lineChart1" :xAxisData="time1" :legendData="legendData" yAxisName="单位：m³" />
          </div>
        </div>
        <div class="item">
          <div class="label"><span class="text">每日供水趋势</span></div>
          <div class="innerBox">
            <LineChart :seriesData="lineChart2" :xAxisData="time2" yAxisName="单位：m³/天" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// DMA管理图表
import LineChart from './lineChart.vue'
import Layer from './layer.vue'
import { getAreaTree, getWaterSupply, getWaterSell, getWaterSupplyDay } from '@/api/leakageLoss/analysis'
import { parseTime } from '@/utils'
import { getShortcutTs } from '@/utils/time'

export default {
  components: { LineChart, Layer },
  data() {
    return {
      filterText: '',
      tree: [],
      elTreeProps: {
        children: 'subSet',
        label: 'name'
      },
      legendData: ['供水量', '售水量'],
      lineChart1: [
        {
          name: '供水量',
          type: 'line',
          symbol: 'none',
          smooth: true,
          lineStyle: {
            color: '#1B7EF2'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                  offset: 0, color: 'rgba(18, 156, 255, 0.31)'
              }, {
                  offset: 1, color: 'rgba(18, 156, 255, 0)'
              }],
            }
          },
          data: []
        },
        {
          name: '售水量',
          type: 'line',
          symbol: 'none',
          smooth: true,
          lineStyle: {
            color: '#29F1FA'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                  offset: 0, color: 'rgba(11, 197, 197, 0.32)'
              }, {
                  offset: 1, color: 'rgba(11, 197, 197, 0)'
              }],
            }
          },
          data: []
        }
      ],
      time1: [],
      lineChart2: [
        {
          name: '供水量',
          type: 'line',
          symbol: 'none',
          smooth: true,
          lineStyle: {
            color: '#1B7EF2'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                  offset: 0, color: 'rgba(18, 156, 255, 0.31)'
              }, {
                  offset: 1, color: 'rgba(18, 156, 255, 0)'
              }],
            }
          },
          data: []
        },
      ],
      time2: [],
    }
  },
  watch: {
    filterText(val) {
      this.$refs.areaTree.filter(val)
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      getAreaTree().then(res => {
        this.tree = res.data
      })
      const payload = {
        ...this.getQueryTime()
      }
      // 供水量
      getWaterSupply(payload).then(res => {
        this.time1 = res.data.map(item => parseTime(item[0], '{y}-{m}'))
        this.lineChart1[0].data = res.data.map(item => item[1])
      })
      // 售水量
      getWaterSell(payload).then(res => {
        this.lineChart1[1].data = res.data.map(item => item[1])
      })
      // 供水趋势
      const ts = getShortcutTs(30)
      const date = {
        startTime: ts[0],
        endTime: ts[1]
      }
      getWaterSupplyDay(date).then(res => {
        this.time2 = res.data.map(item => item[0].slice(5))
        this.lineChart2[0].data = res.data.map(item => item[1])
      })
    },
    // 区域点击
    nodeClick(item, node, self) {
      this.$refs.map.flyToGraphic(item.name)
    },
    filterNode(value, data) {
      if(!value) return true
      return data.name.indexOf(value) !== -1
    },
    getQueryTime() {
      const now = new Date()
      const endTime = `${now.getFullYear()}-${now.getMonth() + 1}-1`
      let month = now.getMonth()
      let year = now.getFullYear()
      if(month === 11) {
        month = 1
      } else {
        month = month + 2
        year = year - 1
      }
      const startTime = `${year}-${month}-1`
      return {
        startTime: parseTime(startTime),
        endTime: parseTime(endTime)
      }
    }
  }
}
</script>

<style lang="scss">
.subzone {
  .treeContainer {
    .el-scrollbar__wrap {
      overflow-x: auto;
    }
    // 覆盖el-tree样式
    .el-tree {
      background: transparent;
      color: #fff;
      .el-tree-node:focus > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #113b7a;
      }
      .el-tree-node__content:hover {
        background-color: #113b7a;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.subzone {
  height: 100%;
  display: flex;
  .treeBox {
    width: 240px;
    background: rgba(42,93,210,0.1);
    border: 1px solid rgba(42,93,210,0.5);
    height: 100%;
    padding: 10px;
    .treeContainer {
      padding-top: 10px;
      height: calc(100% - 36px);
    }
  }
  .right {
    flex: 1;
    padding-left: 20px;
    .map {
      height: calc((100% - 20px) * 0.66);
    }
    .chartBox {
      height: calc((100% - 20px) * 0.34);
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      .item {
        width: calc((100% - 20px) / 2);
        height: 100%;
        border: 1px solid #193F8D;
        .label {
          font-weight: 700;
          font-size: 18px;
          height: 44px;
          line-height: 44px;
          padding-left: 19px;
          background: linear-gradient( 270deg, #164F8F 0%, #10243E 100%);
          color: #31B3FF;
        }
        .innerBox {
          height: calc(100% - 44px);
          background: rgba(0,57,112,0.5);
        }
      }
    }
  }
}
</style>