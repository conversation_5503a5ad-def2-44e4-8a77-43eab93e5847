<template>
  <div class="DMATable">
    <div class="query">
      <div>
        <span>分区名称：</span>
        <el-input v-model="dmaName" placeholder="请输入名称" size="medium" style="width: 240px;margin-right: 20px;"></el-input>
        <span>分区等级：</span>
        <el-select v-model="dmaGrade" placeholder="请选择分区等级" size="medium" style="width: 240px;margin-right: 20px;">
          <el-option label="一级" :value="1"></el-option>
          <el-option label="二级" :value="2"></el-option>
          <el-option label="三级" :value="3"></el-option>
        </el-select>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery" size="medium">查询</el-button>
      </div>
      <div>
        <!-- <el-button type="primary" icon="el-icon-bell" size="medium" :disabled="!rowAreaId" @click="handleConfig">报警设置</el-button> -->
        <el-button type="success" icon="el-icon-plus" @click="handleAdd" size="medium">新增</el-button>
      </div>
    </div>

    <div class="tableBox">
      <el-table
        style="width: 100%;position: absolute;"
        height="100%"
        :data="tableData"
        highlight-current-row
        size="medium"
        @row-click="select"
      >
        <el-table-column label="序号" type="index" width="55"></el-table-column>
        <el-table-column prop="name" label="分区名称"></el-table-column>
        <el-table-column prop="grade" label="分区等级" :formatter="formatterGrade"></el-table-column>
        <el-table-column prop="" label="DMA状态">
          <template slot-scope="{ row }">{{ row.isUsed ? '是' : '否' }}</template>
        </el-table-column>
        <el-table-column prop="" label="分区状态">
          <template slot-scope="{ row }">
            <span :style="{ color: row.planStatus ? '#00FF00': '' }">{{ row.planStatus ? '运营中' : '规划中' }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="" label="供水方式"></el-table-column>
        <el-table-column prop="" label="水表品牌"></el-table-column>
        <el-table-column prop="" label="居民用户数"></el-table-column>
        <el-table-column prop="" label="考核表数"></el-table-column>
        <el-table-column prop="" label="实际入住数"></el-table-column> -->
        <!-- <el-table-column prop="meterCount" label="水表数量"></el-table-column> -->
        <el-table-column prop="reasonableLeak" label="区域合理漏损量(m³/d)"></el-table-column>
        <el-table-column prop="leakTarget" label="区域漏损目标(%)"></el-table-column>
        <el-table-column prop="desc" label="说明"></el-table-column>
        <el-table-column prop="" label="操作" width="220">
          <template slot-scope="{ row }">
            <span style="color: #00E4FF;" class="operateText" @click="handleAssessLink(row, $event)">节点流量表关联</span>
            <span style="color: #00E4FF;" class="operateText" @click="handleHbLink(row, $event)">户表关联</span>
            <span style="color: #F6BD16;" class="operateText" @click="handleEdit(row, $event)">编辑</span>
            <span style="color: #F6BD16;" class="operateText" @click="handleAlarm(row, $event)">报警设置</span>
            <!-- <span style="color: #E02020;" class="operateText">删除</span> -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="pageInfo.pageNo"
      :page-sizes="[10, 20, 30, 40]"
      :page-size.sync="pageInfo.pageSize"
      layout="total, sizes, ->, prev, pager, next, jumper"
      :total="pageInfo.total">
    </el-pagination>

    <!-- 新增弹窗 -->
    <el-dialog :visible.sync="addVisible" title="新增" width="30%" @close="closeAddDialog">
      <el-form :model="addForm" ref="addFormRef" :rules="addFormRules" label-width="auto">
        <el-form-item label="分区名称：" prop="name">
          <el-input v-model="addForm.name"></el-input>
        </el-form-item>
        <el-form-item label="分区等级：" prop="grade">
          <el-radio-group v-model="addForm.grade" @change="gradeChange">
            <el-radio :label="1">一级</el-radio>
            <el-radio :label="2">二级</el-radio>
            <el-radio :label="3">三级</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="父分区：" v-if="addForm.grade !== 1" prop="parentAreaId">
          <el-select v-model="addForm.parentAreaId" placeholder="请选择">
            <el-option v-for="item in parentList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="DMA状态：" prop="isUsed">
          <el-radio v-model="addForm.isUsed" :label="true">是</el-radio>
          <el-radio v-model="addForm.isUsed" :label="false">否</el-radio>
        </el-form-item>
        <el-form-item label="分区状态：" prop="planStatus">
          <el-radio v-model="addForm.planStatus" :label="false">规划中</el-radio>
          <el-radio v-model="addForm.planStatus" :label="true">运营中</el-radio>
        </el-form-item>
        <!-- <el-form-item label="区域合理漏损量(m³/d)：" prop="reasonableLeak">
          <el-input v-model="editForm.reasonableLeak"></el-input>
        </el-form-item> -->
        <el-form-item label="目标漏损率(%)：" prop="leakTarget">
          <el-input v-model="addForm.leakTarget"></el-input>
        </el-form-item>
        <el-form-item label="管道长度(km)：" prop="leakTarget">
          <el-input v-model="addForm.gdLength"></el-input>
        </el-form-item>
        <el-form-item label="夜间水量定额(升/户/小时)：" prop="leakTarget">
          <el-input v-model="addForm.nightAmount"></el-input>
        </el-form-item>
        <el-form-item label="说明：" prop="desc">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入内容"
            v-model="addForm.desc">
          </el-input>
        </el-form-item>
      </el-form>
      <div class="buttonBox">
        <el-button size="medium" @click="cancelAdd">取消</el-button>
        <el-button type="primary" size="medium" @click="handleSubmitAdd">确定</el-button>
      </div>
    </el-dialog>

    <!-- 编辑弹窗 -->
    <el-dialog :visible.sync="editVisible" title="编辑" width="30%">
      <el-form :model="editForm" ref="editFormRef" :rules="editFormRules" label-width="auto">
        <el-form-item label="分区名称：" prop="name">
          <el-input v-model="editForm.name"></el-input>
        </el-form-item>
        <el-form-item label="分区等级：" prop="grade">
          <el-radio-group v-model="editForm.grade" @change="gradeChangeWhenEdit">
            <el-radio :label="1">一级</el-radio>
            <el-radio :label="2">二级</el-radio>
            <el-radio :label="3">三级</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="父分区：" v-if="editForm.grade !== 1" prop="parentAreaId">
          <el-select v-model="editForm.parentAreaId" placeholder="请选择">
            <el-option v-for="item in parentList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="DMA状态：" prop="isUsed">
          <el-radio v-model="editForm.isUsed" :label="true">是</el-radio>
          <el-radio v-model="editForm.isUsed" :label="false">否</el-radio>
        </el-form-item>
        <el-form-item label="分区状态：" prop="planStatus">
          <el-radio v-model="editForm.planStatus" :label="false">规划中</el-radio>
          <el-radio v-model="editForm.planStatus" :label="true">运营中</el-radio>
        </el-form-item>
        <!-- <el-form-item label="区域合理漏损量(m³/d)：" prop="reasonableLeak">
          <el-input v-model="editForm.reasonableLeak"></el-input>
        </el-form-item> -->
        <el-form-item label="目标漏损率(%)：" prop="leakTarget">
          <el-input v-model="editForm.leakTarget"></el-input>
        </el-form-item>
        <el-form-item label="管道长度(km)：" prop="leakTarget">
          <el-input v-model="editForm.gdLength"></el-input>
        </el-form-item>
        <el-form-item label="夜间水量定额(升/户/小时)：" prop="leakTarget">
          <el-input v-model="editForm.nightAmount"></el-input>
        </el-form-item>
        <el-form-item label="说明：" prop="desc">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入内容"
            v-model="editForm.desc">
          </el-input>
        </el-form-item>
      </el-form>
      <div class="buttonBox">
        <el-button size="medium" @click="cancel">取消</el-button>
        <el-button type="primary" size="medium" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 报警设置弹窗 -->
    <el-dialog :visible.sync="configVisible" title="报警设置" width="50%">
      <el-form :model="configForm" ref="configFormRef" :rules="configFormRules" label-width="auto">
        <el-form-item label="夜间最小流量计算时间：" prop="timeRange">
          <el-time-picker
            is-range
            v-model="configForm.timeRange"
            value-format="HH:mm:ss"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 317px;">
          </el-time-picker>
        </el-form-item>

        <div style="color: white;margin-bottom: 30px;font-weight: bold;">存量漏失报警-夜间最小流量阈值</div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="红色报警(m³/h)：" prop="yellowValue">
              <el-input v-model="configForm.stokeLostRange[2]" placeholder="请输入报警阈值"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="持续天数：" prop="yellowValue">
              <el-input v-model="configForm.stokeDaysRange[2]" placeholder="请输入持续天数"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="黄色预警(m³/h)：" prop="yellowValue">
              <el-input v-model="configForm.stokeLostRange[1]" placeholder="请输入报警阈值"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="持续天数：" prop="yellowValue">
              <el-input v-model="configForm.stokeDaysRange[1]" placeholder="请输入持续天数"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="蓝色关注(m³/h)：" prop="yellowValue">
              <el-input v-model="configForm.stokeLostRange[0]" placeholder="请输入报警阈值"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="持续天数：" prop="yellowValue">
              <el-input v-model="configForm.stokeDaysRange[0]" placeholder="请输入持续天数"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div style="color: white;margin-bottom: 30px;font-weight: bold;">增量漏失报警-夜间最小流量阈值</div>
        <div style="color: #fff;margin-bottom: 10px;">日最小流量平均数的取值范围：前<el-input style="width: 80px;margin: 0 10px;" v-model="configForm.incrementalDays"></el-input>天</div>
        <el-row style="color: #fff;margin-bottom: 20px;">
          <el-col :span="12">
            <div class="innerDesc">日最小流量范围(m³/h)</div>
            <div class="innerRow">
              <el-input class="innerRowItem" v-model="configForm.incrementalRange[0]"></el-input>
              <div class="innerRowItem" style="text-align: center;">{{ '≤ X <' }} </div>
              <el-input class="innerRowItem" v-model="configForm.incrementalRange[1]"></el-input>
            </div>
            <div class="innerRow">
              <el-input class="innerRowItem" v-model="configForm.incrementalRange[1]"></el-input>
              <div class="innerRowItem" style="text-align: center;">{{ '≤ X <' }} </div>
              <el-input class="innerRowItem" v-model="configForm.incrementalRange[2]"></el-input>
            </div>
            <div class="innerRow">
              <el-input class="innerRowItem" v-model="configForm.incrementalRange[2]"></el-input>
              <div class="innerRowItem" style="text-align: center;">{{ '≤ X' }} </div>
              <div class="innerRowItem"></div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="innerDesc">红色报警阈值</div>
            <div class="rightRow">
              <el-input v-model="configForm.incrementalTimes[0]" placeholder="" style="width: 100px;"></el-input>
              <div>倍</div>
            </div>
            <div class="rightRow">
              <el-input v-model="configForm.incrementalTimes[2]" placeholder="" style="width: 100px;"></el-input>
              <div>倍</div>
            </div>
            <div class="rightRow">
              <el-input v-model="configForm.incrementalTimes[4]" placeholder="" style="width: 100px;"></el-input>
              <div>倍</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="innerDesc">黄色预警阈值</div>
            <div class="rightRow">
              <el-input v-model="configForm.incrementalTimes[1]" placeholder="" style="width: 100px;"></el-input>
              <div>倍</div>
            </div>
            <div class="rightRow">
              <el-input v-model="configForm.incrementalTimes[3]" placeholder="" style="width: 100px;"></el-input>
              <div>倍</div>
            </div>
            <div class="rightRow">
              <el-input v-model="configForm.incrementalTimes[5]" placeholder="" style="width: 100px;"></el-input>
              <div>倍</div>
            </div>
          </el-col>
        </el-row>
        
        <div style="color: white;margin-bottom: 30px;font-weight: bold;">漏失水量报警阈值</div>
        <el-form-item label="红色报警(m³)：" prop="yellowValue">
          <el-input v-model="configForm.lostRange[2]" placeholder="请输入报警阈值" style="width: 262px;"></el-input>
        </el-form-item>
        <el-form-item label="黄色预警(m³)：" prop="yellowValue">
          <el-input v-model="configForm.lostRange[1]" placeholder="请输入报警阈值" style="width: 262px;"></el-input>
        </el-form-item>
        <el-form-item label="蓝色关注(m³)：" prop="yellowValue">
          <el-input v-model="configForm.lostRange[0]" placeholder="请输入报警阈值" style="width: 262px;"></el-input>
        </el-form-item>
      </el-form>
      <div class="buttonBox">
        <el-button size="medium" @click="cancelConfig">取消</el-button>
        <el-button type="primary" size="medium" @click="setConfig">确定</el-button>
      </div>
    </el-dialog>

    <!-- 考核表关联弹窗 -->
    <el-dialog :visible.sync="assessVisible" title="节点流量表关联" width="50%">
      <div class="switchBox pointer-event">
        <div class="item" :class="{ active: khCurrentSelect == 1 }" @click="switchAssess(1)">已关联</div>
        <div class="item" :class="{ active: khCurrentSelect == 2 }" @click="switchAssess(2)">未关联</div>
        <el-button style="margin-left: 50px;" v-if="khCurrentSelect == 1" type="danger" size="mini" :disabled="!insmCanBind"  @click="unBindInsm">删除</el-button>
        <div v-else>
          <el-button style="margin-left: 50px;" type="success" size="mini" @click="bindMeter" :disabled="!meterCanBind">关联</el-button>
          <span style="color: #fff;margin-left: 20px;">请先设置设备位置后勾选</span>
        </div>
      </div>
      <div v-show="khCurrentSelect == 1">
        <el-table
          height="400px"
          ref="bindInsmRef"
          :data="bindInsmTableData"
          highlight-current-row
          row-key="id"
          @selection-change="handleInsmChange"
          size="medium"
        >
          <el-table-column type="selection" :reserve-selection="true" width="55"></el-table-column>
          <el-table-column label="序号" type="index" width="55"></el-table-column>
          <el-table-column prop="id" label="设备id"></el-table-column>
          <el-table-column prop="uniqueNo" label="设备编码"></el-table-column>
          <el-table-column prop="name" label="设备名称"></el-table-column>
          <el-table-column prop="inOrOut" label="设备位置">
            <template slot-scope="{ row }">{{ row.inOrOut === 1 ? '入口' : '出口' }}</template>
          </el-table-column>
        </el-table>
      </div>
      <div v-show="khCurrentSelect == 2">
        <div class="queryBox">
          <div class="fLabel">设备名称</div>
          <el-input v-model="queryInsmForm.name" placeholder="" size="mini" style="width: 200px;"></el-input>
          <div class="fLabel">设备编码</div>
          <el-input v-model="queryInsmForm.uniqueNo" placeholder="" size="mini" style="width: 200px;"></el-input>
          <el-button type="primary" size="mini" @click="handleQueryInsm" style="margin-left: 20px;">查询</el-button>
        </div>

        <el-table
          height="400px"
          ref="unBindInsmRef"
          :data="unBindInsmTableData"
          highlight-current-row
          row-key="id"
          @selection-change="handleInsmChange"
          size="medium"
        >
          <el-table-column type="selection" :selectable="checkSelectable" :reserve-selection="true" width="55"></el-table-column>
          <!-- <el-table-column label="序号" type="index" width="55"></el-table-column> -->
          <el-table-column prop="id" label="设备id"></el-table-column>
          <el-table-column prop="uniqueNo" label="设备编码"></el-table-column>
          <el-table-column prop="name" label="设备名称" width="250"></el-table-column>
          <el-table-column prop="" label="设备位置" width="200">
            <template slot-scope="{ row }">
              <el-select v-model="row.inOrOut" size="mini" style="width: 90px;" @change="updateRow($event, row.id)">
                <el-option :value="0" label="未设定"></el-option>
                <el-option :value="1" label="入口"></el-option>
                <el-option :value="2" label="出口"></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          @size-change="insmListSizeChange"
          @current-change="insmListPageChange"
          :current-page.sync="insmPageInfo.pageNo"
          :page-sizes="[10, 20, 30, 40]"
          :page-size.sync="insmPageInfo.pageSize"
          layout="total, sizes, ->, prev, pager, next, jumper"
          :total="insmPageInfo.total"
          style="padding-bottom: 20px;">
        </el-pagination>
      </div>
    </el-dialog>

    <!-- 户表关联弹窗 -->
    <el-dialog :visible.sync="hbVisible" title="户表关联" width="50%">
      <div class="switchBox pointer-event">
        <div class="item" :class="{ active: hbCurrentSelect == 1 }" @click="switchHb(1)">已关联</div>
        <div class="item" :class="{ active: hbCurrentSelect == 2 }" @click="switchHb(2)">未关联</div>
        <el-button style="margin-left: 50px;" v-if="hbCurrentSelect == 1" type="danger" size="mini" :disabled="!meterCanBind" @click="unBindMeter">删除</el-button>
        <el-button style="margin-left: 50px;" v-else type="success" size="mini" @click="bindMeter" :disabled="!meterCanBind">关联</el-button>
      </div>
      <div v-show="hbCurrentSelect == 1">
        <el-table
          height="400px"
          :data="bindMeterTableData"
          ref="linkMeterRef"
          highlight-current-row
          size="medium"
          row-key="id"
          @selection-change="handleMeterChange"
        >
          <el-table-column type="selection" :reserve-selection="true" width="55"></el-table-column>
          <!-- <el-table-column label="序号" type="index" width="55"></el-table-column> -->
          <el-table-column prop="userName" label="用户名"></el-table-column>
          <el-table-column prop="userCode" label="用户编号"></el-table-column>
          <el-table-column prop="uniqueNo" label="水表编号"></el-table-column>
          <el-table-column prop="address" label="用水地址"></el-table-column>
        </el-table>
      </div>
      <div v-show="hbCurrentSelect == 2">
        <div class="queryBox">
          <div class="fLabel">用户名</div>
          <el-input v-model="queryMeterForm.userName" placeholder="" size="mini" style="width: 200px;"></el-input>
          <div class="fLabel">用户编号</div>
          <el-input v-model="queryMeterForm.userCode" placeholder="" size="mini" style="width: 200px;"></el-input>
          <div class="fLabel">水表编号</div>
          <el-input v-model="queryMeterForm.uniqueNo" placeholder="" size="mini" style="width: 200px;"></el-input>
          <el-button type="primary" size="mini" @click="handleQueryMeter" style="margin-left: 20px;">查询</el-button>
        </div>

        <el-table
          height="400px"
          :data="unBindMeterTableData"
          ref="unLinkMeterRef"
          highlight-current-row
          size="medium"
          row-key="id"
          @selection-change="handleMeterChange"
        >
          <el-table-column type="selection" :reserve-selection="true" width="55"></el-table-column>
          <!-- <el-table-column label="序号" type="index" width="55"></el-table-column> -->
          <el-table-column prop="userCode" label="用户编号"></el-table-column>
          <el-table-column prop="uniqueNo" label="水表编号"></el-table-column>
          <el-table-column prop="userName" label="用户名"></el-table-column>
          <el-table-column prop="address" label="用水地址"></el-table-column>
        </el-table>
      </div>
      <el-pagination
        background
        @size-change="meterListSizeChange"
        @current-change="meterListPageChange"
        :current-page.sync="meterPageInfo.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="meterPageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="meterPageInfo.total"
        style="padding-bottom: 20px;">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
// DMA管理表格
import { getAreaPageList, getAreaDetailById, editArea, addArea, getAreaListByGrade,
  getMinFlowConfigByAreaId, addMinFlowConfig, updateMinFlowConfig,
  getAreaConfig, addAreaConfig, updateAreaConfig,
  getAreaInsmBindList, getInsmPage, bindFlowInsm, unBindFlowInsm,
  bindWaterMeter, unBindWaterMeter, getAreaWaterMeters
} from '@/api/leakageLoss/area'

export default {
  data() {
    return {
      // 表格
      dmaName: '',
      dmaGrade: null,
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      // 编辑
      editVisible: false,
      assessVisible: false,
      hbVisible: false,
      editForm: {},
      editFormRules: {
        name: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],
        parentAreaId: [
          {
            validator: (rule, value, callback) => {
              if ([2, 3].includes(this.editForm.grade) && !value) {
                callback(new Error('需设置父分区'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        reasonableLeak: [
          { 
            validator: (rule, value, callback) => {
              if (!value) {
                // 允许为空
                callback()
              } else if (isNaN(value) || Number(value) <= 0) {
                // 如果填写了值，且不是大于0的数字
                callback(new Error('必须是大于0的数字'))
              } else {
                // 验证通过
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        leakTarget: [
          { 
            validator: (rule, value, callback) => {
              if (!value) {
                callback()
              } else if (isNaN(value) || Number(value) < 0 || Number(value) > 100) {
                callback(new Error('必须是0到100之间的数字'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      addForm: {
        grade: 1,
        parentAreaId: null,
        planStatus: false
      },
      addVisible: false,
      parentList: [],
      addFormRules: {
        name: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],
        grade: [{ required: true, message: '请选择区域级别', trigger: 'change' }],
        parentAreaId: [
          {
            validator: (rule, value, callback) => {
              if ([2, 3].includes(this.addForm.grade) && !value) {
                callback(new Error('需设置父分区'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        reasonableLeak: [
          { 
            validator: (rule, value, callback) => {
              if (!value) {
                // 允许为空
                callback()
              } else if (isNaN(value) || Number(value) <= 0) {
                // 如果填写了值，且不是大于0的数字
                callback(new Error('必须是大于0的数字'))
              } else {
                // 验证通过
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        leakTarget: [
          { 
            validator: (rule, value, callback) => {
              if (!value) {
                callback()
              } else if (isNaN(value) || Number(value) < 0 || Number(value) > 100) {
                callback(new Error('必须是0到100之间的数字'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },

      // 报警设置
      rowAreaId: null,
      configStatus: 0, // 0 新建 1 修改
      areaId: null, // 当前报警配置的区域id
      configId: null, // 当前报警配置的id
      configForm: {
        stokeLostRange: [null, null, null], // 存量报警阈值
        stokeDaysRange: [null, null, null], // 存量漏损持续天数
        incrementalDays: null, // 增量取值天数
        incrementalRange: [null, null, null], // 增量报警阈值
        incrementalTimes: [null, null, null, null, null, null], // 增量报警倍数
        lostRange: [null, null, null], // 漏失水量报警阈值
      },
      configVisible: false,
      configFormRules: {
        timeRange: [{ required: true, message: '请选择日期', trigger: 'change' }],
      },
      timeRange: ['', ''],

      /* 绑定 */
      bindInsmTableData: [], // 已绑定设备
      unBindInsmTableData: [], // 未绑定设备
      insmList: [], // insm 多选
      bindMeterTableData: [],  // 已绑定水表
      unBindMeterTableData: [], // 未绑定水表
      waterMeterIds: [], // 水表多选
      
      khCurrentSelect: 1,
      hbCurrentSelect: 1,
      queryInsmForm: {},
      queryMeterForm: {},
      currentAreaId: null, // 当前区域id
      meterPageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      },
      insmPageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      },
    }
  },
  computed: {
    insmCanBind() {
      return this.insmList.length > 0
    },
    meterCanBind() {
      return this.waterMeterIds.length > 0
    },
  },
  created() {
    this.getList()
    this.getAreaByGrade(1)
  },
  methods: {
    // 考核关联按钮
    handleAssessLink(row, event) {
      this.currentAreaId = row.id
      // 清空之前多选数据
      this.insmList = []
      if(this.khCurrentSelect === 1) {
        this.getInsmBindList()
      } else {
        this.queryInsmForm = {}
        this._getInsmPage()
      }
      this.assessVisible = true
    },
    // 户表关联按钮
    handleHbLink(row, event) {
      // if(row.grade !== 3) {
      //   this.$message.warning('三级分区才可进行户表关联')
      //   return
      // }
      this.currentAreaId = row.id
      // 清空之前的多选数据
      this.waterMeterIds = []
      // this.hbCurrentSelect = 1
      this.queryLinkedWaterMeters()
      this.hbVisible = true
    },
    // 切换流量计tab
    switchAssess(index) {
      this.khCurrentSelect = index
      this.insmList = []
      this.queryInsmForm = {}

      if(index === 1) {
        this.getInsmBindList()
      } else {
        // 未绑定流量计
        this._getInsmPage()
      }
    },
    // 切换水表tab
    switchHb(index) {
      this.hbCurrentSelect = index
      this.waterMeterIds = []

      // 清空水表查询条件
      this.queryMeterForm = {}
      this.queryLinkedWaterMeters()
    },

    /* 分区管理逻辑 */
    // 获取分区分页数据
    getList() {
      getAreaPageList({
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        name: this.dmaName,
        grade: this.dmaGrade
      }).then(res => {
        const { status, data, count } = res
        if(status === 200) {
          this.tableData = data
          this.pageInfo.total = count
        }
      })
    },
    // 查询按钮
    handleQuery() {
      this.getList()
    },
    // 新增分区按钮
    handleAdd() {
      this.addVisible = true
    },
    closeAddDialog() {
      this.$refs.addFormRef.resetFields()
    },
    cancelAdd() {
      this.addVisible = false
    },
    // 新增
    handleSubmitAdd() {
      this.$refs.addFormRef.validate(async valid => {
        if(valid) {
          const { status, data, msg } = await addArea(this.addForm)
          if(status === 200) {
            this.$message.success('新增成功')
            this.getList()
          } else {
            this.$message.warning(msg)
          }
          this.addVisible = false
        }
      })
    },
    formatterGrade(row) {
      let gradeMap = {
        1: '一级',
        2: '二级',
        3: '三级',
        4: '四级',
        5: '五级'
      }
      return gradeMap[row.grade]
    },
    // 点击表格行
    select(val) {
      val && (this.rowAreaId = val.id)
    },
    // 报警设置按钮
    handleAlarm(row, e) {
      this.areaId = row.id
      // 判断是否存在配置
      getAreaConfig(row.id).then(res => {
        if(!res.data.id) {
          // 没有查询到配置，则是新建状态
          this.configForm = {
            stokeLostRange: [null, null, null], // 存量报警阈值
            stokeDaysRange: [null, null, null], // 存量漏损持续天数
            incrementalDays: null, // 增量取值天数
            incrementalRange: [null, null, null], // 增量报警阈值
            incrementalTimes: [null, null, null, null, null, null], // 增量报警倍数
            lostRange: [null, null, null], // 漏失水量报警阈值
          }
          this.configForm.timeRange = ['00:00:00', '02:00:00']
          this.configStatus = 0
        } else {
          // 编辑状态
          let conf = res.data
          this.configId = conf.id // 配置更新接口需要这条配置的id
          this.configForm = {
            stokeLostRange: conf.stokeLostRange,
            stokeDaysRange: conf.stokeDaysRange,
            incrementalDays: conf.incrementalDays,
            incrementalRange: conf.incrementalRange,
            incrementalTimes: conf.incrementalTimes,
            lostRange: conf.lostRange,
            timeRange: [conf.startTime, conf.endTime]
          }
          this.configStatus = 1
        }
        this.configVisible = true
      })
    },
    // 判断数组从小到大
    isStrictlyIncreasing(arr) {
      return arr.every((item, index) => index === 0 || Number(item) > Number(arr[index - 1]))
    },
    setConfig() {
      // 校验
      const flag1 = this.isStrictlyIncreasing(this.configForm.stokeLostRange) // 存量漏失报警阈值
      const flag2 = this.isStrictlyIncreasing(this.configForm.incrementalRange) // 增量报警
      const flag3 = this.isStrictlyIncreasing(this.configForm.lostRange) // 漏失水量报警阈值
      if(!flag1) {
        this.$message.warning('请正确填写存量漏失报警阈值')
        return
      }
      if(!flag2) {
        this.$message.warning('请正确填写日最小流量范围')
      }
      if(!flag3) {
        this.$message.warning('请正确填写漏失水量报警阈值')
      }

      // 表单检验
      this.$refs.configFormRef.validate(valid => {
        if(valid) {
          let payload = {
            areaId: this.areaId,
            startTime: this.configForm.timeRange[0],
            endTime: this.configForm.timeRange[1],
            incrementalDays: this.configForm.incrementalDays,
            incrementalRange: this.configForm.incrementalRange,
            incrementalTimes: this.configForm.incrementalTimes,
            stokeLostRange: this.configForm.stokeLostRange,
            stokeDaysRange: this.configForm.stokeDaysRange,
            lostRange: this.configForm.lostRange,
          }
          if(this.configStatus === 0) {
            // 新增
            addAreaConfig(payload).then(res => {
              if(res.status) {
                this.$message.success('设置成功')
              } else {
                this.$message.success( res.msg || '设置失败')
              }
            })
          } else {
            // 编辑
            payload.id = this.configId
            updateAreaConfig(payload).then(res => {
              if(res.status) {
                this.$message.success('设置成功')
              } else {
                this.$message.success( res.msg || '设置失败')
              }
            })
          }
          this.configVisible = false
        }
      })
    },
    cancelConfig() {
      this.configVisible = false
    },
    // 分区 radio 变化
    gradeChange(value) {
      this.addForm.parentAreaId = ''
      this.getAreaByGrade(value - 1)
    },
    // 获取某级分区列表
    getAreaByGrade(grade) {
      getAreaListByGrade(grade).then(res => {
        this.parentList = res.data || []
      })
    },
    gradeChangeWhenEdit(value) {
      this.editForm.parentAreaId = ''
      this.getAreaByGrade(value - 1)
    },
    // 编辑按钮
    async handleEdit(row, event) {
      event.stopPropagation()
      this.getAreaByGrade(row.grade - 1)
      const { status, data } =  await getAreaDetailById(row.id)
      if(status === 200) {
        this.editForm = {
          id: row.id,
          name: data.name,
          grade: data.grade,
          parentAreaId: data.parentAreaId,
          inInsm: data.inInsm,
          outInsm: data.outInsm,
          isUsed: data.isUsed,
          planStatus: data.planStatus,
          reasonableLeak: data.reasonableLeak,
          leakTarget: data.leakTarget,
          desc: data.desc
        }
        // this.editForm = data
        this.editVisible = true
      }
    
    },
    async cancel() {
      this.editVisible = false 
    },
    handleSubmit() {
      console.log(this.editForm)
      this.$refs.editFormRef.validate(async valid => {
        
        if(valid) {
          const { status, data, msg } = await editArea(this.editForm)
          if(status === 200) {
            this.$message.success('编辑成功')
            this.getList()
          } else {
            this.$message.warning(msg)
          }
          this.editVisible = false
        }
      })
      
    },

    /* insm 绑定逻辑 */
    // 获取区域绑定流量计列表
    getInsmBindList() {
      getAreaInsmBindList(this.currentAreaId).then(res => {
        this.bindInsmTableData = res.data || []
      })
    },
    handleQueryInsm() {
      this.insmPageInfo.pageNo = 1
      this._getInsmPage()
    },
    // 获取设备分页数据-未绑定
    _getInsmPage() {
      const payload = {
        pageNo: this.insmPageInfo.pageNo,
        pageSize: this.insmPageInfo.pageSize,
        insmType: 'PIPELINE_ELECTROMAGNETIC_FLOWMETER',
        ...this.queryInsmForm
      }
      if(this.khCurrentSelect === 2) {
        // payload.inOrOut = 0
        getInsmPage(payload).then(res => {
          this.unBindInsmTableData = res.data.map(item => {
            return {
              ...item,
              inOrOut: 0
            }
          })
          this.insmPageInfo.total = res.count
        })
      } else {

      }
    },
    // 绑定
    bindInsm() {
      const payload = {
        areaId: this.currentAreaId,
        insmList: this.insmList
      }
      
      // 需要设置 inOrOut
      let result = this.insmList.every(item => item.inOrOut !== 0)
      if(!result) {
        this.$message.warning('请设置所有选中节点流量表的设备位置')
      } else {
        bindFlowInsm(payload).then(res => {
          if(res.status === 200) {
            this.$message.success('关联节点流量表成功')
            this.$refs.unBindInsmRef.clearSelection()
            this._getInsmPage()
          } else {
            this.$message.warning('关联节点流量表失败')
          }
        })
      }
    },
    // 解绑
    unBindInsm() {
      const ids = this.insmList.map(item => item.insmId)
      unBindFlowInsm({
        areaId: this.currentAreaId,
        insmList: ids
      }).then(res => {
        if(res.status === 200) {
          this.$message.success('删除节点流量表成功')
          this.$refs.bindInsmRef.clearSelection()
          this.getInsmBindList()
        } else {
          this.$message.warning('删除节点流量表失败')
        }     
      })
    },
    // 未选择位置
    checkSelectable(row) {
      return row.inOrOut
    },
    // 更改 inOrOut
    updateRow($event, value) {
      const targetIndx = this.insmList.findIndex(item => item.id === value)
      console.log(targetIndx)
      if(targetIndx > -1) {
        this.insmList[targetIndx].inOrOut = $event
      }
    },
    // 多选
    handleInsmChange(val) {
      this.insmList = val.map(item => {
        return {
          insmId: item.id,
          inOrOut: item.inOrOut
        }
      })
    },
    insmListSizeChange() {
      this.insmPageInfo.pageNo = 1
      this._getInsmPage()
    },
    insmListPageChange() {
      this._getInsmPage()
    },
    /* 水表绑定逻辑 */
    // 获取区域水表
    queryLinkedWaterMeters() {
      const payload = {
        orderBy: 'userCode',
        asc: true,
        pageNo: this.meterPageInfo.pageNo,
        pageSize: this.meterPageInfo.pageSize,
        area: this.currentAreaId,
        ...this.queryMeterForm
      }
      if(this.hbCurrentSelect === 2) payload.area = ''
      getAreaWaterMeters(payload).then(res => {
        if(this.hbCurrentSelect === 2) {
          this.unBindMeterTableData = res.data
        } else {
          this.bindMeterTableData = res.data
        }
        this.meterPageInfo.total = res.count
      })
    },
    // 水表多选
    handleMeterChange(val) {
      if(this.hbCurrentSelect === 2) {
        this.waterMeterIds = val.map(item => item.id)
      } else {
        this.waterMeterIds = val.map(item => item.id)
      }
    },
    handleQueryMeter() {
      this.meterPageInfo.pageNo = 1
      this.queryLinkedWaterMeters()
    },
    // 绑定水表
    bindMeter() {
      const payload = {
        areaId: this.currentAreaId,
        waterMeterIds: this.waterMeterIds
      }
      bindWaterMeter(payload).then(res => {
        if(res.status === 200) {
          this.$message.success('关联水表成功')
          this.$refs.unLinkMeterRef.clearSelection()
          this.queryLinkedWaterMeters()
        } else {
          this.$message.warning('关联水表失败')
        }
      })
    },
    // 解绑水表
    unBindMeter() {
      const payload = {
        areaId: this.currentAreaId,
        waterMeterIds: this.waterMeterIds
      }
      unBindWaterMeter(payload).then(res => {
        if(res.status === 200) {
          this.$message.success('删除水表成功')
          this.$refs.linkMeterRef.clearSelection()
          this.queryLinkedWaterMeters()
        } else {
          this.$message.warning('删除水表失败')
        }
      })
    },
    meterListSizeChange() {
      this.meterPageInfo.pageNo = 1
      this.queryLinkedWaterMeters()
    },
    meterListPageChange() {
      this.queryLinkedWaterMeters()
    },
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    }
  }
}
</script>

<style lang="scss">
.DMATable {
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
  .el-table {
    font-size: 14px !important;
  }
  .el-button--primary.is-disabled {
    background-color: #c8c9cc !important;
    background-image: none !important;
  }
  .el-radio__label {
    color: #ffffff;
  }
  .el-textarea__inner {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.DMATable {
  height: 100%;
  .query {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  .tableBox {
    width: 100%;
    position: relative;
    overflow: hidden;
    height: calc(100% - 92px);
    .operateText {
      margin-left: 20px;
      cursor: pointer;
    }
  }
  .buttonBox {
    text-align: center;
    padding-bottom: 20px;
  }
  .innerDesc {
    text-align: center;
  }
  .innerRow {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 10px;
    .innerRowItem {
      width: 25%;
    }
  }
  .rightRow {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
  }
}
.queryBox {
  display: flex;
  align-items: center;
  .fLabel {
    margin: 10px;
    color: #fff;
  }
}
.switchBox {
  align-items: center;
  padding: 0 20px 0 10px;
  /* background: rgba(8, 55, 123, 0.8); */
  backdrop-filter: blur(2px);
  margin-bottom: 10px;
  height: 40px;
  // width: 400px;
  display: flex;
  .item {
    color: #fff;
    width: 200px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border: 1px solid #999;
    cursor: pointer;
    font-size: 16px;
  }
  .active {
    background: linear-gradient(90deg, #000000 0%, #3582c4 0%, #316fbf 100%);
    border-color: #3582c4;
    color: #ffffff;
  }
}
</style>