<template>
  <div class="bigUser">
    <div class="left">
      <div class="top">
        <div class="top-left">
          <div class="bigWaterMeter">
            <div class="bigwaterimg">
              <img src="../../../../assets/img/leakageLoss/bigwm.png" alt="">
            </div>
            <span class="text" style="font-size: 32px;">1248</span>
            <span style="margin-top: -5px;"> 大口径水表总数</span>
          </div>
          <div class="RemoteWaterMeter">
            <div class="remotewaterimg">
              <img src="../../../../assets/img/leakageLoss/remotewm.png" alt="">
            </div>
            <span class="text" style="font-size: 32px;">3609</span>
            <span style="margin-top: -5px;">远传水表总数</span>
          </div>
        </div>
        <div class="waterRank">
          <span class="ranktitle text">1-12月份大口径水表水量排名</span>
          <div class="data">
            <div class="item" v-for="item, index in  waterRankList " :key="index">
              <div class="itemStyle">
                <div class="rankItem">
                  <div class="rankLabel">{{ item.label }}</div>
                  <div class="rankRate">
                    <div class="inner" :style="{ width: item.rate + '%' }"></div>
                  </div>
                  <div>{{ item.value }}万m³</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 大口径水表总售水量 -->
          <div class="rankCount">
            <div class="countItem">
              <img src="../../../../assets/img/leakageLoss/light.png" alt="" class="countItemimg1">
              <div class="count">
                <div class="countnum" style="color: #FFA64C;">
                  10</div>
                <div class="counttitle">总数
                </div>
              </div>

            </div>
            <div class="countItem">
              <img src="../../../../assets/img/leakageLoss/light.png" alt="" class="countItemimg1">
              <div class="count">
                <div class="countnum" style="color: #F6F09C ;">
                  10</div>
                <div class="counttitle">远传</div>
              </div>


            </div>
            <div class="countItem">
              <img src="../../../../assets/img/leakageLoss/light.png" alt="" class="countItemimg1">
              <div class="count">
                <div class="countnum" style="color: #1BE2F7 ;">
                  0</div>
                <div class="counttitle">非远传</div>
              </div>
              <img src="../../../../assets/img/leakageLoss/light.png" alt="" style="float: left;">

            </div>

            <div class="countLine"
              style="width: 35%;height: 100%;float: left;margin-left: 2%; position: relative;color: #CCECFC;">
              <div style="text-align: center;display:flex;margin-top: 15%;;">
                <div style="width:30px;">0%</div>
                <div style="width:70%;background: rgba(39,140,255,0.2);height: 15px;display: flex;">
                  <div style="width: 15%;background: #00C2D7;height: 15px;"></div>
                  <div class="imgline"
                    style="margin: -35px 0 0 -14px;width:28px;height:35px;text-align: center;line-height: 35px;font-size: 12px;">
                    15%</div>
                  <div
                    style="border: 1px solid #00EEFD;width: 14px;height: 14px;margin-left: -20px;background: #CCECFC;border-radius: 50%;">
                  </div>
                </div>
                <div style="width:20px;">100%</div>
              </div>
              <div style="width: 100%;text-align: center;">大口径水表总售水量</div>

            </div>

          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="waterRankChart">
          <div class="text" style="margin: 20px;">
            水量占比
          </div>
          <div class="remoteRankpie">
            <pieChart />
            <div class="pieCharttitle">
              1-12月份全市售水量分布
            </div>
          </div>
        </div>
        <div class="waterRankLine">
          <lineChart />
        </div>
      </div>
    </div>
    <div class="right_right">
      <div class="WMDistribution">
        <div class="title">
          <span class="text ">
            大口径水表行业分布
          </span>
          <button
            style="left:65%; background: linear-gradient(90deg, #012866 0%, #1382E6 50%, #002869 100%);">数量</button>
          <button style="left: 82%;">水量</button>
        </div>
        <div class="distributionChart">
          <Chart :seriesData="seriesData1"  />
        </div>
      </div>
      <div class="remoteStatus">
        <div class="text" style="padding-top: 20px;padding-left: 20px;"> 当前远传水表状态</div>
        <div style="width: 100%;height: 230px;margin-top: 20px;">
          <Chart :seriesData="seriesData2" />
        </div>
        <div class="statusInfo">
          <div class="statusItem">
            <div class="statusItemleft">
              连续半年抄表数据为零
              <span>664</span>
            </div>
            <div class="statusItemright">
              连续半年抄表数据为零固定值
              <span>1021</span>
            </div>
          </div>
          <div class="statusItem">
            <div class="statusItemleft">
              连续半年抄表波动率较大
              <span>0</span>
            </div>
            <div class="statusItemright">
              远传与抄表不一致
              <span>57</span>
            </div>

          </div>
          <div class="statusItem">
            <div class="statusItemleft">
              大表小流量
              <span>0</span>
            </div>
            <div class="statusItemright">
              小表大流量
              <span>22</span>
            </div>

          </div>

        </div>
      </div>
    </div>
  </div>



</template>

<script>
import pieChart from '../bigUser/chart/pieChart.vue';
import Chart from '../bigUser/chart/Chart.vue';
import lineChart from '../bigUser/chart/lineChart.vue';

export default {
  components: {
    pieChart,
    lineChart,
    Chart
  },
  data() {
    return {
      waterRankList: [
        {
          label: '阳光居民小学',
          rate: 96,
          value: 248.79
        },
        {
          label: '兴山青华高中学校',
          rate: 93,
          value: 248.79
        },
        {
          label: '湖北兴业大彬制冷公司',
          rate: 90,
          value: 248.79
        },
        {
          label: '兴山古夫镇社区服务中心',
          rate: 87,
          value: 248.79
        },
        {
          label: '兴发家园集体宿舍',
          rate: 85,
          value: 248.79
        },
        {
          label: '陈家湾养殖厂',
          rate: 80,
          value: 248.79
        },
      ],
      seriesData1: [
        {
          color: ['#3254DD', '#48E5E5', '#0098FF', '#FFF67F', '#FFA64C'],
          type: 'pie',
          radius: '80%',
          center: ['30%', '40%'],
          labelLine: {
            show: false
          },
          label: {
            show: false,
          },
          data: [
            { value: 1228, name: '消防表' },
            { value: 1115, name: '居民用水' },
            { value: 1046, name: '经营用水' },
            { value: 898, name: '工业用水' },
            { value: 1692, name: ' 其他' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ],
      seriesData2: [
        {
          color: ['#0098FF', '#FFF67F', '#FFA64C'],
          type: 'pie',
          radius: '80%',
          center: ['30%', '40%'],
          labelLine: {
            show: false
          },
          label: {
            show: false
          },
          data: [
            { value: 326, name: '在线设备' },
            { value: 48, name: '三日离线' },
            { value: 12, name: '今日离线' },

          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ],
     
    }
  }

}
</script>

<style lang="scss" scoped>
.text {
  background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  font-size: 24px;
}

.imgline {
  background: url('../../../../assets/img/leakageLoss/dialog.png') no-repeat;
}

.rankLabel {
  font-size: 14px;
  width: 30%;
  text-align: right;
  height: 30px;
}



.bigUser {
  margin: -20px;
  position: relative;
  height: calc(100% + 40px);
  width: calc(100% + 40px);

  .left {
    position: absolute;
    height: 100%;
    width: 65%;


    .top {
      height: 410px;
      position: relative;

      .top-left {
        width: 24%;
        height: 100%;
        position: absolute;
        background: rgba(42, 93, 210, 0.1);

        .bigWaterMeter,
        .RemoteWaterMeter {
          text-align: center;
          height: 180px;
          width: 120px;

          margin-top: 20px;
          margin-left: 30%;

          .bigwaterimg,
          .remotewaterimg {
            width: 118px;
            height: 125px;
          }

          span {
            display: block;
          }
        }
      }


      .waterRank {
        width: 74%;
        height: 100%;
        left: 26%;
        position: absolute;
        background: rgba(42, 93, 210, 0.1);

        .ranktitle {
          font-size: 20px;
          display: block;
          margin-top: 10px;
          margin-left: 20px;
        }

        .data {
          padding: 20px;
          height: 55%;

          .item {
            display: flex;
            padding-bottom: 5px;
            margin-bottom: 4px;
            height: 30px;



            .itemStyle {
              background: rgba(42, 93, 210, 0.1);
              padding-left: 10px;
              flex-grow: 1;

              .rankItem {
                display: flex;
                font-size: 14px;
                padding-bottom: 18px;
              }

              .rankLabel {
                width: 23%;
                text-align: right;
                margin-right: 10px;
              }

              .rankRate {
                width: 60%;
                margin-right: 15px;
                height: 12px;
                margin-top: 4px;
                background: rgba(42, 93, 210, 0.1);
              }

              .inner {

                border: 1px solid #267CD5;
                padding-top: 2px;
                position: relative;
                height: 12px;

                background: linear-gradient(90deg, rgba(45, 72, 173, 0.1) 0%, #00B4FF 100%);


                &::after {
                  display: block;
                  content: '';
                  width: 2px;
                  height: 12px;
                  background-color: #D8F0FF;
                  position: absolute;
                  right: -1px;
                  top: -1px;
                }
              }

            }
          }
        }


        .rankCount {
          height: 25%;
          margin: 25px 20px;
          display: flex;
          background: rgba(42, 93, 210, 0.1);

          .countItem {
            width: 20%;
            height: 100%;
            display: flex;

            .countItemimg1 {
              margin-left: -5%;
              float: left;
            }

            .count {
              float: left;
              text-align: center;
              margin: auto;
              width: 85%;
              height: 100%;

              .countnum {
                display: block;
                font-weight: 700;
                font-size: 36px;

                text-align: center;
                font-style: normal;
                margin-top: 15%;
              }

              .counttitle {
                font-size: 18px;
                color: #CCECFC;
                text-align: center;
                margin-top: 4px;
              }
            }
          }

        }


      }

    }



    .bottom {
      width: 100%;
      margin-top: 20px;
      height: calc(100% - 430px);
      background: rgba(42, 93, 210, 0.1);
      position: relative;

      .waterRankChart {
        width: 23%;
        height: 100%;
        position: absolute;

        .remoteRankpie {
          width: 100%;
          height: 60%;
          margin-top: 20px;
          .pieCharttitle {
            font-size: 16px;
            color: #ACC6EA;
            text-align: center;

          }
        }

      }

      .waterRankLine {
        width: 76%;
        height: 100%;
        position: absolute;
        left: 25%;
      }
    }
  }

  .right_right {
    position: absolute;
    height: 100%;
    left: 66.5%;
    width: 33.5%;

    .WMDistribution {
      height: 37%;
      background: rgba(42, 93, 210, 0.1);

      .title {
        padding-top: 20px;
        padding-left: 20px;
        width: 100%;
        height: 40px;
        position: relative;

        button {
          position: absolute;
          width: 90px;
          height: 34px;
          background: rgba(2, 50, 128, 0.8);
          color: #fff;
          border: none;

          &:hover {
            background: linear-gradient(90deg, #012866 0%, #1382E6 50%, #002869 100%);
            border: 1px solid #1382E6;
          }
        }
      }

      .distributionChart {
        width: 100%;
        height: 230px;
        margin-top: 40px;
      }
    }

    .remoteStatus {
      margin-top: 2.5%;
      height: 61.5%;
      background: rgba(42, 93, 210, 0.1);

      .statusInfo {
        height: calc(100% - 320px);
        width: 100%;

        .statusItem {
          background: url('../../../../assets/img/leakageLoss/bottom.png') no-repeat;
          height: 25%;
          margin-left: 20px;
          margin-bottom: 20px;
          line-height: 44px;
          font-size: 12px;

          .statusItemleft {
            float: left;
            width: 225px;
            height: 100%;
            margin-left: 10px;

            span {
              float: right;
            }

          }

          .statusItemright {
            width: 228px;
            height: 100%;
            float: left;
            margin-left: 25px;

            span {
              float: right;
            }
          }
        }
      }
    }
  }
}
</style>