<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'


export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '100%'
        },
        seriesData: {
            type: Array,
            default: () => []
        },
        xAxisData: {
            type: Array,
            default: () => []
        },
        legendData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            chart: null,
            data: [
                { value: 1228, name: '消防表' },
                { value: 1115, name: '居民用水' },
                { value: 1046, name: '经营用水' },
                { value: 898, name: '工业用水' },
                { value: 1692, name: ' 其他' }
            ],
        }
    },
    watch: {
        seriesData: {
            deep: true,
            handler(val) {
                this.initChart()
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$el)
            this.chart.setOption({

                tooltip: {
                    trigger: 'item'
                },

                legend: {
                    top: 5,
                    orient: 'vertical',
                    itemWidth: 10,
                    itemHeight: 10,

                    textStyle: {
                        padding: [8, 0, 0, 0],
                        fontSize: 12,
                        fontWeight: "normal",
                        color: "#fff",

                    },
                    right: 110
                },
                grid: {
                    top: 35,
                    left: 40,
                    right: 30,
                    bottom: 20
                },
                series: this.seriesData


            })
        }
    }
}

</script>