<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'
import { F } from 'core-js/modules/_export'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '100%'
        },
        seriesData: {
            type: Array,
            default: () => []
        },
        xAxisData: {
            type: Array,
            default: () => []
        },
        legendData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            chart: null,

        }
    },
    watch: {
        seriesData: {
            deep: true,
            handler(val) {
                this.initChart()
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$el)
            this.chart.setOption({
                tooltip: {
                    trigger: 'item'
                },
                title: {
                    text: "3609",
                    top:'center',
                    left: 'center',
                    itemGap: -1,
                    subtext: "远传水表售水量",
                    textStyle: {
                        fontSize: 34,
                        color: ["#00cfff"]
                    },
                    subtextStyle: {
                        color: "#fff",
                        fontSize: 12,
                    },
                },
                // grid: {
                //     bottom: 150,
                //     left: 0,
                //     right: "10%"
                // },
                legend: {
                    show: false
                },
                series: [
                    //主要显示层
                    {
                        color: ['#F6F09C', '#1EE1F5', '#496DFF'],
                        type: 'pie',
                        radius: ['55%', '80%'],
                        center: ['50%', '50%'],
                        labelLine: {
                            show: false
                        },
                        label: {
                            show: false
                        },

                        data: [
                            {
                                name: '远传水表售水量',
                                value: 50
                            },
                            {
                                name: '非远传水表售水量',
                                value: 100
                            },
                            {
                                name: '其他',
                                value: 50
                            }
                        ]
                    },


                ]


            })
        }
    }
}

</script>