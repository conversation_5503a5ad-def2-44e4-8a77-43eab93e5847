<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '100%'
        },
        seriesData: {
            type: Array,
            default: () => []
        },
        xAxisData: {
            type: Array,
            default: () => []
        },
        legendData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            chart: null
        }
    },
    watch: {
        seriesData: {
            deep: true,
            handler(val) {
                this.initChart()
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$el)
            this.chart.setOption({
                color: ["#05CFF7", "#0269E9", "#C6ECDF", "#01F871"],
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    top: 5,
                    itemWidth: 16,
                    itemHeight: 8,
                    textStyle: {
                        fontSize: 12,
                        fontWeight: "normal",
                        color: "#ACC6EA",
                    },
                },
                grid: {
                    top: 35,
                    left: 100,
                    bottom: 20
                },
                xAxis: {
                    type: 'category',

                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    axisLine: {
                        lineStyle: {
                            color: '#B0D7FF'
                        }
                    },
                    axisLabel: {
                        interval: 0
                    },
                    axisTick: {
                        show: false
                    },
                },
                yAxis: [

                    {
                        type: "value",
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#B0D7FF",
                                fontSize: "12",
                            },
                        },
                        axisLine: {
                            show: false
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#B0D7FF'
                            }
                        }
                    },
                    {
                        type: "value",
                        axisLine: {
                            show: false
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#B0D7FF",
                                fontSize: "12",
                            },
                        },
                        splitLine: {
                            show: false,
                            lineStyle: {
                                color: '#B0D7FF'
                            }
                        }
                    },
                    {
                        type: "value",
                        axisLine: {
                            show: false
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#B0D7FF",
                                fontSize: "12",
                            },
                        },
                        splitLine: {
                            show: false,
                            lineStyle: {
                                color: '#B0D7FF'
                            }
                        }
                    },
                ],
                series: [
                    {
                        name: '远传水表售水量',
                        type: 'bar',
                        data: [950, 850, 680, 800, 550, 680, 550, 700, 700, 700, 780, 700],
                        itemStyle: {
                            color: '#F6F09C'
                        },
                        barWidth: 12
                    },
                    {
                        barGap: '-100%',
                        name: '非远传水表售水量',
                        type: 'bar',
                        data: [810, 700, 580, 700, 480, 580, 480, 580, 580, 580, 660, 580],
                        itemStyle: {
                            color: '#1EE1F5'
                        },
                        barWidth: 12
                    },
                    {
                        name: '其他',
                        barGap: '-100%',
                        type: 'bar',
                        data: [500, 480, 380, 420, 300, 370, 350, 370, 370, 370, 400, 380],
                        itemStyle: {
                            color: '#496DFF'
                        },
                        barWidth: 12
                    },

                ],
            })
        }
    }


}

</script>