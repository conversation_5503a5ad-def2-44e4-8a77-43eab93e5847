<template>
  <div class="DMAContainer">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="leakage" />
    <DMAChart v-if="DMAShowType === 'chart'" />
    <DMATable v-if="DMAShowType === 'table'" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import DMAChart from './DMAChart.vue'
import DMATable from './DMATable.vue'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'DMAContainer',
  components: {
    DMAChart,
    DMATable,
    FloatingSideMenu
  },
  computed: {
    ...mapGetters('app', ['DMAShowType']),
  }
}
</script>

<style lang="scss" coped>
.DMAContainer {
  height: calc(100vh - 84px);
}
</style>