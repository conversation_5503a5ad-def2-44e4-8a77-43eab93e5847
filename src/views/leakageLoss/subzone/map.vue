<template>
  <div class="subzonemapContainer">
    <div id="subzonemap"></div>
    <div class="legend">
      <div class="legendTop">
        <el-input
          placeholder="请输入区域名称"
          suffix-icon="el-icon-search"
          size="small"
          style="width: 70%;"
          v-model="areaName">
        </el-input>
        <div class="smallIconBox">
          <i class="el-icon-plus actionIcon" style="margin-right: 10px;"></i>
          <i class="el-icon-arrow-down actionIcon"></i>
        </div>
      </div>
      <!-- 分区 tree -->
      <el-tree
        :data="treeData"
        :props="defaultProps"
        show-checkbox
        class="legendtree"
        @node-click="handleNodeClick"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <img
            v-if="node.level === 2"
            src="../../../assets/img/pipe_network/pipe1.png"
            style="width: 20px; height: 20px; margin-right: 5px"
            alt=""
          />
          <span class="labeltext">{{ node.label }}</span>
        </span>
      </el-tree>
      <!-- <el-button type="primary" plain @click="opendialog()">新建分区</el-button> -->
    </div>

    <div class="toolBox">
      <div>古洞口水库</div>
      <div class="right">
        <i class="el-icon-edit-outline actionIcon" style="color: #E6A23C;"></i><span>编辑属性</span>
        <img src="@/assets/img/leakageLoss/guajie.png" class="actionIcon" width="24px" alt=""><span>挂接传感器</span>
        <i class="el-icon-delete actionIcon" style="color: #F56C6C;"></i><span>编辑属性</span>
      </div>
    </div>

    <div class="DMABox">
      <div class="title">编辑DMA</div>
      <el-scrollbar class="wrapper">
        <el-form :model="dmaForm" label-width="auto" size="small" style="padding-right: 10px;">
          <el-form-item label="上级区域：">
            <el-select v-model="dmaForm.value1" placeholder="请选择"></el-select>
          </el-form-item>
          <el-form-item label="分区名称：" required>
            <el-select v-model="dmaForm.value2" placeholder="请选择"></el-select>
          </el-form-item>
          <el-form-item label="分区负责人：">
            <el-input v-model="dmaForm.value3" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="营业所：" required>
            <el-select v-model="dmaForm.value4" placeholder="请选择"></el-select>
          </el-form-item>
          <el-form-item label="完成状态：" required>
            <el-select v-model="dmaForm.value5" placeholder="请选择"></el-select>
          </el-form-item>
          <el-form-item label="开始日期：" required>
            <el-date-picker
              v-model="dmaForm.value6"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="建成日期：" required>
            <el-date-picker
              v-model="dmaForm.value7"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="区域范围：" required>
            <el-select v-model="dmaForm.value8" placeholder="请选择"></el-select>
          </el-form-item>

          <el-form-item label="用户数量：">
            <el-input v-model="dmaForm.value9" placeholder="请输入内容">
              <span slot="suffix">户</span>
            </el-input>
          </el-form-item>
          <el-form-item label="管网长度：">
            <el-input v-model="dmaForm.value10" placeholder="请输入内容">
              <span slot="suffix">km</span>
            </el-input>
          </el-form-item>
          <el-form-item label="干网长度：">
            <el-input v-model="dmaForm.value11" placeholder="请输入内容">
              <span slot="suffix">km</span>
            </el-input>
          </el-form-item>
          <el-form-item label="服务支管数：">
            <el-select v-model="dmaForm.value8" placeholder="请选择"></el-select>
          </el-form-item>
          <el-form-item label="最小服务压力：">
            <el-input v-model="dmaForm.value13" placeholder="请输入内容">
              <span slot="suffix">Mpa</span>
            </el-input>
          </el-form-item>
          <el-form-item label="目标漏损率：">
            <el-input v-model="dmaForm.value14" placeholder="请输入内容">
              <span slot="suffix">%</span>
            </el-input>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      
      <div class="actionBox">
        <el-button type="primary" size="small">取消</el-button>
        <el-button type="primary" size="small">确定</el-button>
      </div>
    </div>
    <el-dialog title="区域范围：" :visible.sync="dialogFormVisible" width="700px">
      <el-form :model="form" label-width="100px" label-position="left">
        <el-form-item label="所属分区：">
          <el-input
            class="forminput"
            v-model="form.name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="分区名称：">
          <el-input
            class="forminput"
            v-model="form.name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="填充颜色：">
          <div style="display: flex; align-items: center">
            <el-input
              class="forminput"
              v-model="form.color"
              autocomplete="off"
            ></el-input>
            <el-color-picker
              v-model="form.color"
              style="margin-left: 20px"
            ></el-color-picker>
          </div>
        </el-form-item>
        <el-form-item label="分区范围：">
          <div style="display: flex; align-items: center">
            <el-input
              class="forminput"
              v-model="form.name"
              autocomplete="off"
            ></el-input>
            <div class="location"><i class="el-icon-location-outline"></i></div>
          </div>
        </el-form-item>
        <el-form-item label="入口流量计：">
          <el-input
            class="forminput"
            v-model="form.name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="出口流量计：">
          <el-input
            class="forminput"
            v-model="form.name"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import Ground from "@arcgis/core/Ground";
import WMSLayer from "@arcgis/core/layers/WMSLayer";
import WFSLayer from "@arcgis/core/layers/WFSLayer";
import Popup from "./popup.vue";
import Vue from "vue";

import { getAreaTree } from '@/api/leakageLoss/analysis'

export default {
  components: {
    Popup,
  },
  data() {
    return {
      form: {},
      dialogFormVisible: false,
      areaName: '',
      treeData: [],
      defaultProps: {
        children: "subSet",
        label: "name",
      },
      map: null,
      view: null,
      imglayer: null,
      pressure: null,
      geourl: "",
      // 编辑dma
      dmaForm: {}
    };
  },
  mounted() {
    this.getAreaList()
    this.geourl = process.env.VUE_APP_GEO_URL
    this.createmap();
  },
  methods: {
    createmap() {
      this.fenxian = new WMSLayer({
        url: this.geourl + "/geoserver/xschina/wms",
        id: "blue",
      });
      this.shuixi = new WMSLayer({
        url: this.geourl + "/geoserver/xslabel/wms",
        id: "blue",
      });
      this.cun = new WMSLayer({
        url: this.geourl + "/geoserver/cun/wms",
        id: "blue",
      });
      this.water1 = new WMSLayer({
        url: this.geourl + "/geoserver/water1/wms",
        id: "blue",
      });
      this.water2 = new WMSLayer({
        url: this.geourl + "/geoserver/wate2/wms",
        id: "blue",
      });
      this.map = new Map({
        ground: new Ground({
          layers: [],
          surfaceColor: "transparent",
          opacity: 0,
        }),
        layers: [],
      });
      this.view = new MapView({
        container: "subzonemap",
        map: this.map,
        background: {
          type: "color",
          color: [255, 252, 244, 0],
        },
        extent: {
          xmax: 111.15191207080001,
          xmin: 110.39483293,
          ymax: 31.591080100800003,
          ymin: 30.9630324254,
        },
        spatialReference: {
          wkid: 4326,
        },
      });
      this.view.ui.remove("attribution");
      this.view.ui.empty("top-left");
      this.view.map.add(this.fenxian);
      this.view.map.add(this.shuixi);
      this.view.map.add(this.cun);
      this.view.map.add(this.water1);
      this.view.map.add(this.water2);
      this.area = new WFSLayer({
        url: this.geourl + "/geoserver/area/wms",
        outFields: "*",
        popupTemplate: {
          title: "{name}",
          content: [
            {
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "value",
                },
                {
                  fieldName: "normal",
                },
                {
                  fieldName: "data_time",
                },
              ],
            },
          ],
        },
      });
      this.view.map.add(this.area);
      this.view.on("click", async (e) => {
        console.log(e);
        this.view.hitTest(e).then((res) => {
          console.log(res);
        });
      });
    },
    getAreaList() {
      getAreaTree().then(res => {
        console.log(res)
        this.treeData = res.data
      })
    },
    handleNodeClick(data) {
      console.log(data);
    },
    opendialog() {
      this.dialogFormVisible = true;
    },
  },
};
</script>
<style lang="scss">
.subzonemapContainer {
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
.esri-view-width-medium .esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0.3);
}
.esri-popup__header {
  background-color: rgba(4, 15, 45, 0.3) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  background-color: rgba(4, 15, 45, 0) !important;
}
.esri-popup__icon,
.esri-icon-close {
  color: #fff !important;
}
.esri-popup__content {
  margin: 0;
}
.esri-feature {
  background-color: rgba(4, 15, 45, 0.3);
  padding: 0 15px 12px;
}
.esri-widget__table {
  color: #fff;
}
.esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0) !important;
}
.esri-popup__header-title {
  background-color: rgba(4, 15, 45, 0) !important;
  color: #fff;
}
.esri-popup__pointer-direction {
  background-color: rgba(4, 15, 45, 0.3);
}

.esri-popup__footer {
  display: none;
}
.esri-popup__feature-menu {
  display: none;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
.esri-view-surface {
  outline: none !important;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: auto 0px Highlight !important;
  outline: auto 0px -webkit-focus-ring-color !important;
}
[class*="esri-popup--is-docked-top-"] .esri-popup__footer,
[class*="esri-popup--aligned-bottom-"] .esri-popup__footer {
  border-bottom: solid 1px #6e6e6e4d;
}
.esri-popup__header {
  border-top-left-radius: 5px !important;
  border-top-right-radius: 5px;
  background-color: #fff;
  color: #fff;
}
.esri-popup--shadow {
  box-shadow: 0 0 0 0 rgb(155, 155, 155) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  outline: none !important;
}
.esri-ui .esri-popup {
  border-radius: 5px !important;
}
.esri-popup {
  position: absolute !important;
}
.esri-popup__button {
  background-color: transparent !important;
  outline: none;
}
</style>
<style scoped lang="scss">
.forminput {
  width: 400px;
}
.location {
  width: 40px;
  height: 40px;
  margin-left: 20px;
  border: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
  i {
    color: #fff;
    font-size: 30px;
  }
}
#subzonemap {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
}
.legend {
  position: absolute;
  top: 20px;
  left: 20px;
  bottom: 20px;
  width: 320px;
  // height: 455px;
  background: rgba(4, 15, 45, 0.5);
  border-radius: 4px;
  padding: 20px;
  .legendTop {
    display: flex;
    justify-content: space-between;
    padding-bottom: 20px;
  }
  .smallIconBox {
    height: 32px;
    border: 1px solid #1382E6;
    border-radius: 4px;
    padding: 0 10px;
    line-height: 32px;
    .actionIcon {
      font-size: 20px;
      cursor: pointer;
    }
  }
}
.legendtree {
  background: transparent;
  .labeltext {
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
  }
  ::v-deep.el-tree-node__content:hover {
    background: transparent;
  }
  ::v-deep.el-tree-node:focus > .el-tree-node__content {
    background-color: transparent;
  }
  ::v-deep.el-tree-node__label {
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
  }
  ::v-deep.el-checkbox__inner {
    width: 16px;
    height: 16px;
    background: rgba(0, 57, 112, 0.5);
    border: 1px solid #0285ff;
  }
}
.toolBox {
  position: absolute;
  top: 0;
  left: 340px;
  width: 1130px;
  height: 44px;
  background-color: rgba(42, 93, 210, 0.3);
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .actionIcon {
    font-size: 24px;
    cursor: pointer;
    margin: 0 10px;
  }
  .right {
    display: flex;
    align-items: center;
  }
}
.DMABox {
  width: 390px;
  background-color: rgba(4, 15 ,45, 0.5);
  border: 1px solid #2A5DD2;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 10px 20px 0;
  .title {
    font-size: 24px;
    font-weight: 700;
    padding-bottom: 10px;
  }
  .wrapper {
    height: calc(100% - 91px);
    overflow-y: auto;
  }
  .actionBox {
    height: 50px;
    text-align: center;
  }
}
</style>