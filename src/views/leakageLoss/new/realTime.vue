<template>
  <div class="realTime">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="leakage" />
    <div class="treeBox">
      <el-input
        placeholder="请输入区域"
        suffix-icon="el-icon-search"
        v-model="filterText"
        size="medium">
      </el-input>
      <el-scrollbar class="treeContainer">
        <!-- 滚动条要包裹的tree内容 --> 
        <el-tree 
          :data="tree"
          node-key="id"
          :props="elTreeProps"
          @node-click="nodeClick"
          :filter-node-method="filterNode"
          ref="areaTree"
        ></el-tree>
      </el-scrollbar>
    </div>

    <div class="center">
      <div class="tab">
        <div class="first" :class="{ active: currentTab === 'tab1' }" @click="handleTabClick('tab1')">压力分析</div>
        <div class="other" :class="{ active: currentTab === 'tab2' }" @click="handleTabClick('tab2')">流量分析</div>
      </div>
      <div class="centerContainer">
        <Pressure :areaId="areaId" v-if="currentTab === 'tab1'"/>
        <Flow :areaId="areaId" v-if="currentTab === 'tab2'"/>
      </div>
    </div>

    <AreaInfo class="rightBox" :id="areaId" />
  </div>
</template>

<script>
import AreaInfo from '../wlAnalysis/components/areaInfo.vue'
import Pressure from '../wlAnalysis/components/pressure.vue'
import Flow from '../wlAnalysis/components/flow.vue'

import { getAreaTree } from '@/api/leakageLoss/analysis'

import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'RealTime',
  components: {
    AreaInfo,
    Pressure,
    Flow,
    FloatingSideMenu
  },
  data() {
    return {
      currentTab: 'tab1',
      filterText: '',
      areaId: null,
      tree: [],
      elTreeProps: {
        children: 'subSet',
        label: 'name'
      },
    }
  },
  watch: {
    filterText(val) {
      this.$refs.areaTree.filter(val)
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      // 获取区域菜单
      getAreaTree().then(res => {
        this.tree = res.data
        // 设置默认选中
        if(res.data.length > 0 && res.data[0].id) {
          this.areaId = res.data[0].id
          this.$nextTick(() => {
            this.$refs.areaTree.setCurrentKey(res.data[0].id)
          })
        }        
      })
    },
    nodeClick(item, node, self) {
      this.areaId = item.id
    },
    filterNode(value, data) {
      if(!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleTabClick(tab) {
      this.currentTab = tab
    }
  }
}
</script>

<style lang="scss">
.realTime {
  .treeContainer {
    .el-scrollbar__wrap {
      overflow-x: auto;
    }
    // 覆盖el-tree样式
    .el-tree {
      background: transparent;
      color: #fff;
      .el-tree-node:focus > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff;
      }
      .el-tree-node__content:hover {
        background-color: #113b7a;
      }
    }
  }
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
  .el-table {
    .el-table__header {
      .cell {
        font-weight: 400;
        padding: 0 5px;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.realTime {
  padding: 0 30px;
  height: calc(100vh - 84px);
  display: flex;
  color: #fff;
  .tab {
    color: #ffffff;    
    display: flex;
    div {
      padding: 0 20px;
      height: 34px;
      line-height: 34px;
      opacity: 0.6;
      text-align: center;
      cursor: pointer;
    }
    .active {
      opacity: 1;
      color: #fff;
    }
    .first {
      background-image: url('~@/assets/img/leakageLoss/first-tab.png');
      background-size: 100% 34px;      
    }
    .other {
      background-image: url('~@/assets/img/leakageLoss/other-tab.png');
      background-size: 100% 34px;      
    }
  }
  .treeBox {
    width: 240px;
    background: rgba(42,93,210,0.1);
    border: 1px solid rgba(42,93,210,0.5);
    height: 100%;
    padding: 10px;
    .treeContainer {
      padding-top: 10px;
      height: calc(100% - 36px);
    }
  }
  .center {
    flex: 1;
    padding: 0 20px;
    .centerContainer {
      height: calc(100% - 34px);
      border: 1px solid  #193F8D;
      background: rgba(42,93,210,0.1);
    }
  }
  .rightBox {
    width: 240px;
    background: rgba(42,93,210,0.1);
  }
}
</style>