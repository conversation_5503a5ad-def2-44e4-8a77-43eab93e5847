<template>
  <div class="special">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="leakage" />
    <div class="treeBox">
      <el-input
        placeholder="请输入区域"
        suffix-icon="el-icon-search"
        v-model="filterText"
        size="medium">
      </el-input>
      <el-scrollbar class="treeContainer">
        <!-- 滚动条要包裹的tree内容 --> 
        <el-tree 
          :data="areaTree"
          node-key="id"
          :props="elTreeProps"
          @node-click="nodeClick"
          :filter-node-method="filterNode"
          ref="areaTreeRef"
        ></el-tree>
      </el-scrollbar>
    </div>

    <!-- tab切换 -->
    <div class="center">
      <div class="tab">
        <!-- 有户表：不显示总分表差 无户表：不显示产销差-->
        <div class="first" v-show="!hasMeter" :class="{ active: currentTab === 'tab1' }" @click="handleTabClick('tab1')">总分表差</div>
        <div class="other" :class="{ active: currentTab === 'tab3' }" @click="handleTabClick('tab3')">峰谷分析</div>
        <div class="other" v-show="hasMeter" :class="{ active: currentTab === 'tab4' }" @click="handleTabClick('tab4')">产销差</div>
        <!-- <div v-for="t, index in tabList" :key="index" class="other" :class="{ active: currentTab === t.value }" @click="handleTabClick(t.value)">{{ t.label }}</div> -->
        <el-date-picker
          v-model="dateRange"
          @change="dateChange"
          :clearable="false"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="margin-left: 50px;"
          size="medium">
        </el-date-picker>
      </div>

      <div class="centerContainer">
        <div class="chartBox">
          <CommonLine :xAxisData="dateData" :seriesData="chartData" />
        </div>
        <div class="tableBox">
          <template v-if="currentTab === 'tab1'">
            <el-table :data="tableData" height="100%" key="tab1">
              <el-table-column label="日期" prop="date"></el-table-column>
              <el-table-column label="总表水量(m³)" prop="flowIn"></el-table-column>
              <el-table-column label="分表水量(m³)" prop="flowOut"></el-table-column>
              <el-table-column label="总分表差(m³)" prop="flowDiff"></el-table-column>
              <el-table-column label="总分表差率(%)" prop="flowDiffRate"></el-table-column>
            </el-table>
          </template>
          <template v-if="currentTab === 'tab3'">
            <el-table :data="tableData" height="100%" key="tab3">
              <el-table-column label="日期" prop="dateTime"></el-table-column>
              <el-table-column label="最小流量(m³/h)" prop="minFlow"></el-table-column>
              <el-table-column label="最大流量(m³/h)" prop="maxFlow"></el-table-column>
              <el-table-column label="平均流量(m³/h)" prop="avgFlow"></el-table-column>
              <el-table-column label="夜间最小流量(m³/h)" prop="minNightFlow"></el-table-column>
            </el-table>
          </template>
          <template v-if="currentTab === 'tab4'">
            <el-table :data="tableData" height="100%" key="tab4">
              <el-table-column label="日期" prop="date"></el-table-column>
              <el-table-column label="供水量(m³)" prop="supply"></el-table-column>
              <el-table-column label="售水量(m³)" prop="revenue"></el-table-column>
              <el-table-column label="产销差率(%)" prop="supplyRate"></el-table-column>
            </el-table>
          </template>
        </div>
        <div style="height: 50px;padding: 10px 20px 0;">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageInfo.pageNo"
            :page-sizes="[10, 20, 30, 40]"
            :page-size.sync="pageInfo.pageSize"
            layout="total, sizes, ->, prev, pager, next, jumper"
            :total="pageInfo.total"
          >
          </el-pagination>
        </div>
      </div>
    </div>

    <div class="right">
      <div>
        <div class="title">漏损分析</div>
        <div class="cell">
          <span>供水量</span>
          <span><span class="value">{{ info.supply }}</span><span class="unit">m³</span></span>
        </div>
        <div class="cell">
          <span>漏水量</span>
          <span><span class="value">{{ info.lost }}</span><span class="unit">m³</span></span>
        </div>
        <div class="cell">
          <span>综合漏损率</span>
          <span><span class="value">{{ info.lostRate }}</span><span class="unit">%</span></span>
        </div>
        <div class="cell">
          <span>产销差率</span>
          <span><span class="value">{{ info.supplyRate }}</span><span class="unit">%</span></span>
        </div>
      </div>

      <!-- 仪表盘 -->
      <div>
        <div class="chartBox">
          <GaugeChart height="260px" :value="realLossPercent" />
        </div>
        <div class="rate">【目标漏损率：{{ targetLossPercent }}%】</div>
      </div>

      <div class="block">
        <div class="title">漏损率变化曲线(%)</div>
        <LineChart :seriesData="line1Data" :xAxisData="dateList" height="200px" />
      </div>

      <div class="block">
        <div class="title">产销差率变化曲线(%)</div>
        <LineChart :seriesData="line2Data" :xAxisData="dateList" height="200px" />
      </div>
    </div>
  </div>
</template>

<script>
// 专题分析
import { getAreaTree } from '@/api/leakageLoss/analysis'

import { getFlowMorePage, getLostPage, getGlobalLost } from '@/api/leakageLoss/analysis'

import { getDaysDate } from '@/utils/timeHelper'

import CommonLine from './chart/commonChart'
import GaugeChart from './chart/gauge'
import LineChart from './chart/lineChart'

import _ from 'loadsh'

import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'Special',
  components: { CommonLine, GaugeChart, LineChart, FloatingSideMenu },
  data() {
    return {
      filterText: '',
      areaId: null,
      hasMeter: true,
      areaTree: [],
      elTreeProps: {
        children: 'subSet',
        label: 'name'
      },
      currentTab: '',
      tabList: [
        // { label: '物理漏损', value: 'tab2' },
        { label: '峰谷分析', value: 'tab3' },
        { label: '产销差', value: 'tab4' },
      ],
      dateRange: [],
      chartData: [],
      dateData: [],
      tableData: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },

      // 右侧漏损数据
      info: {
        supply: 0,
        lost: 0,
        lostRate: 0,
        supplyRate: 0
      },
      targetLossPercent: 20, // 目标漏损率
      realLossPercent: 0, // 综合漏损率
      dateList: [],
      line1Data: [
        {
          name: '漏损率',
          type: 'line',
          symbol: 'none',
          color: '#5b8ff9',
          data: []
        }
      ],
      line2Data: [
        {
          name: '产销差率',
          type: 'line',
          symbol: 'none',
          color: '#30bf78',
          data: []
        }
      ]
    }
  },
  created() {
    this.dateRange = getDaysDate(30)
  },
  mounted() {
    this.initData()
    // this.queryData()
    this.setLineData()
  },
  methods: {
    initData() {
      // 获取区域菜单
      getAreaTree().then(res => {
        this.areaTree = res.data
        // 设置默认选中
        if(res.data.length > 0 && res.data[0].id) {
          this.areaId = res.data[0].id
          this.hasMeter = res.data[0].hasMeter
          this.currentTab = this.hasMeter ? 'tab3' : 'tab1'
          this.queryData()
          this.$nextTick(() => {
            this.$refs.areaTreeRef.setCurrentKey(res.data[0].id)
          })
        }        
      })      
    },
    dateChange() {
      this.queryData()
    },
    // 查询数据
    queryData() {
      const payload = {
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        areaId: this.areaId,
        beginDate: this.dateRange[0],
        endDate: this.dateRange[1],
        
      }
      if(this.currentTab === 'tab1') {
        getLostPage(payload).then(res => {
          let resultData = res.data || []
          this.pageInfo.total = res.count
          // resultData = resultData.map(item => {
          //   return {
          //     ...item,
          //     diff: item.flow - item.meter,
          //     rate: item.flow ? (((item.flow - item.meter) / item.flow) * 100).toFixed(2) : 0
          //   }
          // })
          this.setChartData(resultData)
        })
      } else if(this.currentTab === 'tab2') {
        
      } else if (this.currentTab === 'tab3') {
        getFlowMorePage(payload).then(res => {
          let resultData = res.data || []
          this.pageInfo.total = res.count
          this.setChartData(resultData)
        })
      } else if (this.currentTab === 'tab4') {
        getLostPage(payload).then(res => {
          let resultData = res.data || []
          this.pageInfo.total = res.count
          // resultData = resultData.map(item => {
          //   return {
          //     ...item,
          //     flowDiff: item.supply - item.revenue
          //   }
          // })
          this.setChartData(resultData)
        })
      }

      // 右侧面板数据
      getGlobalLost({
        areaId: this.areaId,
        beginDate: this.dateRange[0],
        endDate: this.dateRange[1],
      }).then(res => {
        const { status, data } = res
        if(status === 200) {
          this.info.lost = data.lost
          this.info.supply = data.supply
          this.info.lostRate = data.lostRate
          this.info.supplyRate = data.supplyRate

          this.realLossPercent = data.lostRate
          this.dateList = data.lostList.map(item => item[0])
          this.line1Data[0].data = data.lostList.map(item => item[1])
          this.line2Data[0].data = data.supplyList.map(item => item[1])
        }
      })
      
    },
    nodeClick(item, node, self) {
      console.log(item, node, self)
      this.areaId = item.id
      this.hasMeter = item.hasMeter
      this.queryData()
    },
    filterNode(value, data) {
      if(!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleTabClick(tab) {
      this.currentTab = tab
      this.pageInfo.pageNo = 1
      this.queryData()
    },
    // 设置图表数据
    setChartData(data) {
      this.tableData = []
      this.tableData = data
      let temp = _.cloneDeep(data).reverse()
      this.dateData = temp.map(item => item.date)

      switch (this.currentTab) {
        case 'tab1':
          this.chartData = [
            {
              name: '总表水量',
              type: 'bar',
              barGap: 0,
              barWidth: 15,
              color: '#5b8ff9',
              data: temp.map(item => item.flowIn)
            },
            {
              name: '分表水量',
              type: 'bar',
              barGap: 0,
              barWidth: 15,
              color: '#5ad8a6',
              data: temp.map(item => item.flowOut)
            },
            {
              name: '总分表差',
              type: 'bar',
              barGap: 0,
              barWidth: 15,
              color: '#5d7092',
              data: temp.map(item => item.flowDiff)
            },
            {
              name: '总分表差率',
              type: 'line',
              color: '#f6bd16',
              symbol: 'none',
              yAxisIndex: 1,
              data: temp.map(item => item.flowDiffRate)
            }
          ]
          break
        case 'tab2':
          break
        case 'tab3':
          this.dateData = temp.map(item => item.dateTime)
          this.chartData = [
            {
              name: '最小流量',
              type: 'line',
              color: '#5b8ff9',
              symbol: 'none',
              data: temp.map(item => item.minFlow)
            },
            {
              name: '最大流量',
              type: 'line',
              color: '#5ad8a6',
              symbol: 'none',
              data: temp.map(item => item.maxFlow)
            },
            {
              name: '平均流量',
              type: 'line',
              color: '#5d7092',
              symbol: 'none',
              data: temp.map(item => item.avgFlow)
            },
            {
              name: '夜间最小流量',
              type: 'line',
              color: '#f6bd16',
              symbol: 'none',
              data: temp.map(item => item.minNightFlow)
            }
          ]
          break
        case 'tab4':
          this.chartData = [
            {
              name: '供水量',
              type: 'bar',
              barGap: 0,
              barWidth: 15,
              color: '#5b8ff9',
              data: temp.map(item => item.supply)
            },
            {
              name: '售水量',
              type: 'bar',
              barGap: 0,
              barWidth: 15,
              color: '#5ad8a6',
              data: temp.map(item => item.revenue)
            },
            {
              name: '产销差率',
              type: 'line',
              color: '#f6bd16',
              symbol: 'none',
              yAxisIndex: 1,
              data: temp.map(item => item.supplyRate)
            }
          ]
          break
        default:
          console.log('default')
      }
    },
    // 设置右侧折线图数据
    setLineData() {
      this.line1Data[0].data = [30, 15, 24, 35, 38, 14, 30]
      this.line2Data[0].data = [30, 15, 24, 35, 38, 14, 30]
    },
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.queryData()
    },
    handleCurrentChange() {
      this.queryData()
    }
  }
}
</script>

<style lang="scss">
.special {
  .treeContainer {
    .el-scrollbar__wrap {
      overflow-x: auto;
    }
    // 覆盖el-tree样式
    .el-tree {
      background: transparent;
      color: #fff;
      .el-tree-node:focus > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff;
      }
      .el-tree-node__content:hover {
        background-color: #113b7a;
      }
    }
  }
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
  .el-table {
    .el-table__header {
      .cell {
        font-weight: 400;
        padding: 0 5px;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.special {
  padding: 0 30px;
  height: calc(100vh - 84px);
  display: flex;
  .treeBox {
    width: 240px;
    overflow: hidden;
    flex-shrink: 0;
    background: rgba(42,93,210,0.1);
    border: 1px solid rgba(42,93,210,0.5);
    height: 100%;
    padding: 10px;
    .treeContainer {
      padding-top: 10px;
      height: calc(100% - 36px);
    }
  }
  .center {
    flex: 1;
    padding: 0 20px;
    .tab {
      color: #ffffff;    
      display: flex;
      div {
        padding: 0 20px;
        height: 34px;
        line-height: 34px;
        opacity: 0.6;
        text-align: center;
        cursor: pointer;
      }
      .active {
        opacity: 1;
        color: #fff;
      }
      .first {
        background-image: url('~@/assets/img/leakageLoss/first-tab.png');
        background-size: 100% 34px;      
      }
      .other {
        background-image: url('~@/assets/img/leakageLoss/other-tab.png');
        background-size: 100% 34px;      
      }
    }
    .centerContainer {
      height: calc(100% - 34px);
      border: 1px solid  #193F8D;
      background: rgba(42,93,210,0.1);
      .chartBox {
        height: calc(100% - 450px);
      }
      .tableBox {
        height: 400px;
      }
    }
  }
  .right {
    width: 360px;
    flex-shrink: 0;
    background: rgba(42,93,210,0.1);
    padding: 20px;
    overflow: hidden;
    color: #fff;
    .title {
      font-weight: 700;
      color: #31B3FF;
    }
    .cell {
      background-image: url('~@/assets/img/leakageLoss/cellBg.png');
      background-size: 100% 30px;
      height: 30px;
      padding: 0 8% 0 20px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 5px;
      .value {
        font-family: DIN;
        font-weight: 700;
        font-size: 16px;
        color: #FFFFFF;
      }
      .unit {
        color: #ACC6EA;
        padding-left: 4px;
      }
    }
    .block {
      padding-top: 10px;
    }
    .chartBox {
      // position: relative;
      height: 185px;
    }
    .rate {
      height: 32px;
      font-weight: 700;
      font-size: 14px;
      text-align: center;
      line-height: 32px;
    }
  }
  .tableHeight {
    // height: 100%;
  }
}
</style>