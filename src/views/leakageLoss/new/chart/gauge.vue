<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    title: {
      type: String,
      default: ''
    },
    value: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    value: {
      // deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        title: {
          text: '综合漏损率',
          left: 'center',
          bottom: 70,
          textStyle: {
            color: '#6092fa',
            fontSize: 12
          }
        },
        series: [
          {
            type: 'gauge',
            radius: '80%',
            center: ['50%', '45%'],
            startAngle: 210,
            endAngle: -30,
            axisLine: {
              lineStyle: {
                width: 12,
                color: [
                  [0.3, '#578aef'],
                  [0.7, '#ebb618'],
                  [1, '#dd6151']
                ]
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            pointer: {
              width: 4,
              itemStyle: {
                color: 'auto'
              }
            },
            detail: {
              // show: false,
              formatter: '{value}%',
              fontSize: 20,
              fontWeight: 'bold',
              padding: [-10, 0, 0, 0],
            },
            data: [
              {
                value: this.value
              }
            ]
          },
          // 刻度
          {
            type: 'gauge',
            radius: '65%',
            center: ['50%', '45%'],
            splitNumber: 5,
            startAngle: 180,
            endAngle: 0,
            axisLine: {
              show: false
            },
            axisTick: {
              length: 6
            },
            axisLabel: {
              distance: -20,
              color: '#fff',
              formatter: '{value}%'
            },
            splitLine: {
              show: false
            },
            pointer: {
              show: false
            },
            title: {
              show: false
            },
            detail: {
              show: false
            }
          }
        ]
      })
    }
  }
}
</script>
