<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
import resize from "@/utils/chartResize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    seriesData: {
      type: Array,
      default: () => [],
    },
    xAxisData: {
      type: Array,
      default: () => [],
    },
    yAxisName: {
      type: String,
      default: "",
    },
    legendData: {
      type: Array,
      // default: () => [],
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart();
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el);
      this.chart.setOption(
        {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            textStyle: {
              color: "#fff",
            },
            top: 5,
            left: 10,
            // bottom: 10,
          },
          grid: {
            left: 0,
            right: 0,
            bottom: 20,
            // top: 20,
            containLabel: true,
          },
          xAxis: [
            {
              // boundaryGap: false,
              axisLine: {
                lineStyle: {
                  color: "#B0D7FF"
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                color: '#B0D7FF'
              },
              data: this.xAxisData,
            },
          ],
          yAxis: [
            {
              name: this.yAxisName,
              nameTextStyle: {
                color: '#fff'
              },
              type: "value",
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              axisLabel: {
                color: '#B0D7FF',
                formatter: '{value}%'
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "rgba(176, 215, 255, 0.30)",
                },
              },
            }
          ],
          series: this.seriesData,
        },
        {
          notMerge: true,
        }
      );
    },
  },
};
</script>
