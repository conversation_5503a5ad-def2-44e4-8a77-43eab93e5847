<template>
  <div class="preformancePage">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="leakage" />
    <Score />
  </div>
</template>

<script>
import Score from '../performance/score'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  components: { Score, FloatingSideMenu }
}
</script>

<style scoped>
.preformancePage {
  padding: 40px;
  height: calc(100vh - 82px);
  color: #fff;
}
</style>