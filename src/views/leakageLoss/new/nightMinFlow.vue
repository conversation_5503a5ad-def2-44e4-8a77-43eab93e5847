<template>
  <div class="nightMinFlowPage">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="leakage" />
    <div class="treeBox">
      <el-input
        placeholder="请输入区域"
        suffix-icon="el-icon-search"
        v-model="filterText"
        size="medium">
      </el-input>
      <el-scrollbar class="treeContainer">
        <!-- 滚动条要包裹的tree内容 --> 
        <el-tree 
          :data="tree"
          node-key="id"
          :props="elTreeProps"
          @node-click="nodeClick"
          :filter-node-method="filterNode"
          ref="areaTree"
        ></el-tree>
      </el-scrollbar>
    </div>

    <div class="center">
      <div class="centerContainer">
        <div class="queryBox">
          <div class="oButton" :class="{ active: is2To5 }" @click="changeValue(true)">夜间2~5h</div>
          <div class="oButton" :class="{ active: !is2To5 }" style="margin-left: 6px;" @click="changeValue(false)">24h</div>
          <el-date-picker
            v-model="dateRange"
            :clearable="false"
            type="daterange"
            value-format="yyyy-MM-dd"
            @change="dateRangeChange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="margin-left: 50px;height: 34px;"
            size="medium">
          </el-date-picker>
          <div class="oButton" style="margin-left: auto;" :class="{ active: isChart }" @click="changeType(true)">曲线</div>
          <div class="oButton" :class="{ active: !isChart }" style="margin-left: 6px;" @click="changeType(false)">表格</div>
        </div>

        <div class="innerBox">
          <MNFLine v-if="isChart" :seriesData="seriesData" />
          <div v-else class="tableBox">
            <div style="height: calc(100% - 50px);">
              <el-table :data="tableData" style="height: 100%;">
                <el-table-column label="瞬时总流入" prop="flowInTotal"></el-table-column>
                <el-table-column label="瞬时总流出" prop="flowOutTotal"></el-table-column>
                <el-table-column label="瞬时净流量" prop="flow"></el-table-column>
                <el-table-column label="监测时间" prop="dateTime"></el-table-column>
              </el-table>
            </div>
            <div style="height: 50px;padding: 10px 20px 0;">
              <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="pageInfo.pageNo"
                :page-sizes="[10, 20, 30, 40]"
                :page-size.sync="pageInfo.pageSize"
                layout="total, sizes, ->, prev, pager, next, jumper"
                :total="pageInfo.total"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>

    <AreaInfo class="rightBox" :id="areaId" />
  </div>
</template>

<script>
import AreaInfo from '../wlAnalysis/components/areaInfo'
// import NightMinFlow from '../wlAnalysis/components/nightMinFlow.vue'
import MNFLine from './chart/mnfLine'

import { getAreaTree, getFlowMinChart, getFlowMinPage } from '@/api/leakageLoss/analysis'

import { getDaysDate } from '@/utils/timeHelper'

import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'NightMinFlowPage',
  components: {
    AreaInfo,
    // NightMinFlow,
    MNFLine,
    FloatingSideMenu
  },
  data() {
    return {
      filterText: '',
      areaId: null,
      tree: [],
      elTreeProps: {
        children: 'subSet',
        label: 'name'
      },
      is2To5: false,
      dateRange: [],
      isChart: true,
      seriesData: [],
      tableData: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
    }
  },
  watch: {
    filterText(val) {
      this.$refs.areaTree.filter(val)
    }
  },
  created() {
    this.dateRange = getDaysDate(7)
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      // 获取区域菜单
      getAreaTree().then(res => {
        this.tree = res.data
        // 设置默认选中
        if(res.data.length > 0 && res.data[0].id) {
          this.areaId = res.data[0].id
          this.getData()
          this.$nextTick(() => {
            this.$refs.areaTree.setCurrentKey(res.data[0].id)
          })
        }        
      })
    },
    // 区域点击
    nodeClick(item, node, self) {
      this.areaId = item.id
      this.getData()
    },
    // 过滤
    filterNode(value, data) {
      if(!value) return true
      return data.name.indexOf(value) !== -1
    },
    changeValue(value) {
      this.is2To5 = value
      this.getData()
    },
    changeType(value) {
      this.isChart = value
      this.getData()
    },
    dateRangeChange() {
      console.log(this.dateRange)
      this.getData()
    },
    getData() {
      const payload = {
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        areaId: this.areaId,
        beginDate: this.dateRange[0],
        endDate: this.dateRange[1],
        night: this.is2To5
      }
      if(this.isChart) {
        // 曲线
        getFlowMinChart(payload).then(res => {
          let resultObject = res.data || {}
          let resultArray = Object.entries(resultObject)
          this.seriesData = resultArray.map(item => {
            return {
              type: 'line',
              name: item[0],
              symbol: 'none',
              data: item[1]
            }
          })
        })
      } else {
        // 分页
        getFlowMinPage(payload).then(res => {
          this.tableData = res.data
          this.pageInfo.total = res.count
        })
      }
    },
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.getData()
    },
    handleCurrentChange() {
      this.getData()
    }
  }
}
</script>

<style lang="scss">
.nightMinFlowPage {
  .treeContainer {
    .el-scrollbar__wrap {
      overflow-x: auto;
    }
    // 覆盖el-tree样式
    .el-tree {
      background: transparent;
      color: #fff;
      .el-tree-node:focus > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #113b7a;
        color: #409eff;
      }
      .el-tree-node__content:hover {
        background-color: #113b7a;
      }
    }
  }
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
  .el-table {
    .el-table__header {
      .cell {
        font-weight: 400;
        padding: 0 5px;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.nightMinFlowPage {
  padding: 0 30px;
  height: calc(100vh - 84px);
  display: flex;
  color: #fff;
  .treeBox {
    width: 240px;
    background: rgba(42,93,210,0.1);
    border: 1px solid rgba(42,93,210,0.5);
    height: 100%;
    padding: 10px;
    .treeContainer {
      padding-top: 10px;
      height: calc(100% - 36px);
    }
  }
  .center {
    flex: 1;
    padding: 0 20px;
    // border: 1px solid  #193F8D;
    // background: rgba(42,93,210,0.1);
    .centerContainer {
      padding: 20px;
      height: 100%;
      border: 1px solid  #193F8D;
      background: rgba(42,93,210,0.1);
    }
  }
  .innerBox {
    height: calc(100% - 34px);
    .tableBox {
      padding-top: 20px;
      height: 100%;
    }
  }
  .queryBox {
    display: flex;
  }
  .oButton {
    width: 100px;
    height: 34px;
    text-align: center;
    line-height: 30px;
    border: 2px solid transparent;
    background-color: #032D6F;
    cursor: pointer;
  }
  .active {
    color: #FFFFFF;
    border-color: #41D1E5;
  }
  .rightBox {
    width: 240px;
    background: rgba(42,93,210,0.1);
  }
}
</style>