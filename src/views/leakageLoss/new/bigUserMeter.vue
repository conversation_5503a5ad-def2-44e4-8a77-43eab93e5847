<template>
  <div class="bigWaterMeter">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="leakage" />
    <BigUser />
  </div>
</template>

<script>
import BigUser from '../subzone/bigUser'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  components: { BigUser, FloatingSideMenu }
}
</script>

<style scoped>
.bigWaterMeter {
  padding: 40px;
  height: calc(100vh - 82px);
  color: #fff;
}
</style>