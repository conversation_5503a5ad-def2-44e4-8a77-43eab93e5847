<template>
  <div class="dispatchPage">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="leakage" />
    <Dispatch />
  </div>
</template>

<script>
import Dispatch from '../dispatch'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  components: { Dispatch, FloatingSideMenu }
}
</script>

<style scoped>
.dispatchPage {
  padding: 40px;
  height: calc(100vh - 82px);
  color: #fff;
}
</style>