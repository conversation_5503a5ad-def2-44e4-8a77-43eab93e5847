<template>
  <div class="platform">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="leakage" />
    <DMATable />
  </div>
</template>

<script>
import DMATable from '../subzone/DMATable'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  components: { DMATable, FloatingSideMenu }
}
</script>

<style scoped>
.platform {
  height: calc(100vh - 84px);
  color: #ffff;
  padding: 0 30px 30px;
}
</style>