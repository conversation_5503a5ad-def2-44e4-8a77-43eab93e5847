<template>
  <div>
    <div id="mars3dContainer" class="mars3d-container"></div>

    <el-table
    :data="warnList"
    class="infoview"
    >
    <el-table-column
      prop="areaName"
      label="区域"
      width="180">
    </el-table-column>
    <el-table-column
      prop="value"
      label="漏损量 (m3)"
      width="180">
    </el-table-column>
  </el-table>
  </div>
</template>
  
<script>
import { getAreaWarn } from '@/api/leakageLoss/diagnosis'

export default {
  data() {
    return {
      deepHeight: 100,
      map: null,
      tmd: 0.5,
      opean: true,
      mapOptions: {
        scene: {
          //默认视角参数
          center: {
            lat: 31.328109,
            lng: 110.770702,
            alt: 58000,
            heading: 0,
            pitch: -90,
          },
          sceneMode: 2,
        },
        control: {
          terrainProviderViewModels: [],
          contextmenu: {
            hasDefault:false
          }
        },
        basemaps: [
      { "id": 10, "name": "地图底图", "type": "group" },
      {
        "id": 2017,
        "pid": 10,
        "name": "暗色底图",
        "type": "gaode",
      //   "icon": require("../../../assets/img/basemaps/blackMarble.png"),
        "layer": "vec",
        "invertColor": true,
        "filterColor": "#4e70a6",
        "brightness": 0.6,
        "contrast": 1.8,
        "gamma": 0.3,
        "hue": 1,
        "saturation": 0,
        "show": true
      }
    ]
      },
      param1: 1,
      underground: null,
      terrainPlanClip: null,
      terrainClip: null,
      eventTarget: new mars3d.BaseClass(),
      queryMapserver: null,
      geourl: '',
      layer1: null,
      layer2: null,
      layer3: null,
      timerId: null,
      displayTimerId: null,
      warnList: [],
      warnDisplayIndex: 0,
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.initMap();
    this.timeUpdate();
    this.startTimedCall()
  },
  beforeDestroy() {
    if(this.map) {
      this.map.destroy()
    }
  },
  methods: {
    initMap() {
      this.map = new mars3d.Map("mars3dContainer", this.mapOptions);
      this.map.container.style.backgroundColor = "#546a53"; // 背景色
      this.addBaseLayer(this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aarea_1&maxFeatures=50&outputFormat=application%2Fjson", 1);
      this.addBaseLayer(this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aarea_2&maxFeatures=50&outputFormat=application%2Fjson", 2);
      this.addBaseLayer(this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aarea_3&maxFeatures=50&outputFormat=application%2Fjson", 3); 
    },
    timeUpdate() {
      getAreaWarn().then(res => {
        const { status, data} = res
        if (status == 200) {
          if (data.length != 0) {
            this.warnList = data
            // this.getWarn(data)
          }
        }
      })
    },
    pollingDisplay() {
      if (this.warnList.length == 0) {
        return
      }
      if (this.warnDisplayIndex >= this.warnList.length) {
        this.warnDisplayIndex = 0
      }
      this.map.closePopup()
      this.map.closeHighlight()
      const aa = this.warnList[this.warnDisplayIndex];
      this.warnDisplayIndex++
      if (aa.areaName == null) {
        return
      }
      let gra = this.layer1.getGraphicByAttr(aa.areaName, "name")
        if (gra) {          
            gra.openHighlight(false)
            gra.unbindPopup(false);
            gra.bindPopup(this.getWarmHtml(aa), {
              autoClose: false,
            }).openPopup()
        } else {
            gra = this.layer2.getGraphicByAttr(aa.areaName, "name")
            if (gra) {      
                gra.openHighlight(false)
                gra.unbindPopup(false);
                gra.bindPopup(this.getWarmHtml(aa), {
                  autoClose: false,
                }).openPopup()
            } else {
                gra = this.layer3.getGraphicByAttr(aa.areaName, "name")
                if (gra) {
                    gra.openHighlight(false)
                    gra.unbindPopup(false);
                    gra.bindPopup(this.getWarmHtml(aa), {
                      autoClose: false,
                    }).openPopup()
                }
            }
        }
        
    },
    startTimedCall() {
      this.timerId = setInterval(this.timeUpdate, 60000); 
      this.displayTimerId = setInterval(this.pollingDisplay, 3000)
    },

    getWarmHtml(data) {
      const innhtml = `<table style="width:100px;">
              <tr><th scope="col" colspan="2"  style="text-align:center;font-size:15px;">漏损告警</th></tr>
              <tr><td >` + data.areaName + `</td><td >`+ data.value + ` m3 </td></tr>
            </table>`
      return innhtml;
    },
    // getWarn(datas) {
    //   this.map.closePopup()
    //   this.map.closeHighlight()
    //   datas.forEach(data => {
    //     let gra = this.layer1.getGraphicByAttr(data.areaName, "name")
    //     if (gra) {          
    //         gra.openHighlight(false)
    //         gra.unbindPopup(false);
    //         gra.bindPopup(this.getWarmHtml(data), {
    //           autoClose: false,
    //         }).openPopup()
    //     } else {
    //         gra = this.layer2.getGraphicByAttr(data.areaName, "name")
    //         if (gra) {      
    //             gra.openHighlight(false)
    //             gra.unbindPopup(false);
    //             gra.bindPopup(this.getWarmHtml(data), {
    //               autoClose: false,
    //             }).openPopup()
    //         } else {
    //             gra = this.layer3.getGraphicByAttr(data.areaName, "name")
    //             if (gra) {
    //                 gra.openHighlight(false)
    //                 gra.unbindPopup(false);
    //                 gra.bindPopup(this.getWarmHtml(data), {
    //                   autoClose: false,
    //                 }).openPopup()
    //             }
    //         }
    //     }
    //   });
    // },
    addBaseLayer(geoUrl, zIndex) {
      if (zIndex == 1) {
        this.layer1 = this.createLayer(3, '#00ED21', 1, geoUrl, zIndex, '区域1')
        this.map.addLayer(this.layer1)
      } else if(zIndex == 2) {
        this.layer2 = this.createLayer(2, '#ff0000', 0, geoUrl, zIndex, '区域2')
        this.map.addLayer(this.layer2)
      } else {
        this.layer3 = this.createLayer(1, '#000000', 1, geoUrl, zIndex, '区域3')
        this.map.addLayer(this.layer3)
      }
    },
    createLayer(lineWidth, lineColor, lineOpacity, geoUrl, zIndex, name) {
      const geolayer = new mars3d.layer.GeoJsonLayer({
        name: name,
        zIndex: zIndex,
        url: geoUrl,
        format: this.simplifyGeoJSON, // 用于自定义处理geojson
        symbol: {
          type: "polygon",
          styleOptions: {
              clampToGround: true,
              classificationType: Cesium.ClassificationType.BOTH,
              setHeight: 0,
              opacity: 0.5,
              randomColor: true,
              outline: true,
              outlineStyle: {
                  width: lineWidth,
                  color: lineColor,
                  opacity: lineOpacity
              },
              highlight: {
                  type: "click",
                  opacity: 1
              },
              label: {
                text: "{name}",
                color: "#000000",
                font_size: 15,
                scaleByDistance: true,
                scaleByDistance_far: 60000000,
                scaleByDistance_farValue: 0.2,
                scaleByDistance_near: 1000000,
                scaleByDistance_nearValue: 0.9
            },
          },
          callback: function(attr, styleOpt) {
            if (attr.color) {
              return {
                color : attr.color,
              }
            }
          },
        },
      });
      // console.log(geolayer)
      return geolayer
    },
    simplifyGeoJSON(geojson) {
      try {
        geojson = turf.simplify(geojson, {
          tolerance: 0.0001,
          highQuality: true,
          mutate: true,
        });
      } catch (e) {
        //
      }
      return geojson;
    },
  },
};
</script>

<style lang="scss">
#mars3dContainer {
  .cesium-viewer-toolbar {
    left: 465px;
  }
  .cesium-baseLayerPicker-dropDown-visible {
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    left: 48px
}
}
</style>

<style scoped lang="scss">
  /**infoview浮动面板*/
  .infoview {
    position:absolute;
    top: 180px;
    right: 42px;
    width: 360px;
    // padding: 10px 15px;
    // border-radius: 4px;
    // border: 1px solid rgba(128, 128, 128, 0.5);
    color: #ffffff;
    background: rgba(0, 0, 0, 0.4);
    box-shadow: 0 3px 14px rgba(128, 128, 128, 0.5);
    z-index: 19870101;
  }
  .mars-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  </style>
      