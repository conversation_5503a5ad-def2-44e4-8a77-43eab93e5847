<template>
  <div id="pressuremap"></div>
</template>

<script>
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import Ground from "@arcgis/core/Ground";
import WMSLayer from "@arcgis/core/layers/WMSLayer";
import WFSLayer from "@arcgis/core/layers/WFSLayer";
import Popup from "./popup.vue";
import Vue from "vue";
export default {
  components: {
    Popup,
  },
  data() {
    return {
      map: null,
      view: null,
      imglayer: null,
      pressure: null,
      geourl: "",
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.createmap();
  },
  methods: {
    createmap() {
      // 县界
      this.fenxian = new WMSLayer({
        url: this.geourl + "/geoserver/xschina/wms",
      });
      // 注记
      this.xslabel = new WMSLayer({
        url: this.geourl + "/geoserver/xslabel/wms",
      });
      // 水系
      this.water1 = new WMSLayer({
        url: this.geourl + "/geoserver/water1/wms",
      });
      // 水系
      this.water2 = new WMSLayer({
        url: this.geourl + "/geoserver/wate2/wms",
      });
      this.map = new Map({
        ground: new Ground({
          layers: [],
          surfaceColor: "transparent",
          opacity: 0,
        }),
        layers: [],
      });
      this.view = new MapView({
        container: "pressuremap",
        map: this.map,
        background: {
          type: "color",
          color: [255, 252, 244, 0],
        },
        extent: {
          xmax: 111.15191207080001,
          xmin: 110.39483293,
          ymax: 31.591080100800003,
          ymin: 30.9630324254,
        },
        spatialReference: {
          wkid: 4326,
        },
      });
      this.view.ui.remove("attribution");
      this.view.ui.empty("top-left");
      this.view.map.add(this.fenxian);
      this.view.map.add(this.xslabel);
      this.view.map.add(this.water1);
      this.view.map.add(this.water2);
      this.pressure = new WFSLayer({
        url: this.geourl + "/geoserver/pressure/wms",
        outFields: "*",
        popupTemplate: {
          title: "{label}",
          content: [
            {
              type: "fields",
              fieldInfos: [
                {
                  label: '压力值',
                  fieldName: "value",
                },
                {
                  label: '在线状态',
                  fieldName: "normal",
                },
                {
                  label: '监测时间',
                  fieldName: "data_time",
                },
              ],
            },
          ],
        },
        renderer: {
          type: "unique-value",
          field: "normal",
          defaultSymbol: {
            type: "simple-marker",
            color: "#00FF00",
            outline: {
              color: "#00FF00",
              width: 0.5,
            },
          },
          uniqueValueInfos: [
            {
              value: 1,
              symbol: {
                type: "simple-marker",
                color: "#00FF00",
                outline: {
                  color: "#00FF00",
                  width: 0.5,
                },
              },
            },
            {
              value: null,
              symbol: {
                type: "simple-marker",
                color: "gray",
                outline: {
                  color: "gray",
                  width: 0.5,
                },
              },
            },
            {
              value: 0,
              symbol: {
                type: "simple-marker",
                color: "red",
                outline: {
                  color: "red",
                  width: 0.5,
                },
              },
            },
          ],
        },
      });
      this.view.map.add(this.pressure);
      this.view.on("click", async (e) => {
        console.log(e);
        // 检测指定点位命中了哪些元素
        this.view.hitTest(e).then((res) => {
          console.log(res);
        });
      });
    },
  },
};
</script>
<style lang="scss">
.esri-view-width-medium .esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0.3);
}
.esri-popup__header {
  background-color: rgba(4, 15, 45, 0.3) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  background-color: rgba(4, 15, 45, 0) !important;
}
.esri-popup__icon,
.esri-icon-close {
  color: #fff !important;
}
.esri-popup__content {
  margin: 0;
}
.esri-feature {
  background-color: rgba(4, 15, 45, 0.3);
  padding: 0 15px 12px;
}
.esri-widget__table {
  color: #fff;
}
.esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0) !important;
}
.esri-popup__header-title {
  background-color: rgba(4, 15, 45, 0) !important;
  color: #fff;
}
.esri-popup__pointer-direction {
  background-color: rgba(4, 15, 45, 0.3);
}

.esri-popup__footer {
  display: none;
}
.esri-popup__feature-menu {
  display: none;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
.esri-view-surface {
  outline: none !important;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: auto 0px Highlight !important;
  outline: auto 0px -webkit-focus-ring-color !important;
}
[class*="esri-popup--is-docked-top-"] .esri-popup__footer,
[class*="esri-popup--aligned-bottom-"] .esri-popup__footer {
  border-bottom: solid 1px #6e6e6e4d;
}
.esri-popup__header {
  border-top-left-radius: 5px !important;
  border-top-right-radius: 5px;
  background-color: #fff;
  color: #fff;
}
.esri-popup--shadow {
  box-shadow: 0 0 0 0 rgb(155, 155, 155) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  outline: none !important;
}
.esri-ui .esri-popup {
  border-radius: 5px !important;
}
.esri-popup {
  position: absolute !important;
}
.esri-popup__button {
  background-color: transparent !important;
  outline: none;
}
</style>
<style scoped lang="scss">
#pressuremap {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
}
</style>