<template>
  <div class="pressureList">
    <div class="areaTree">
      <el-scrollbar class="treeContainer">
        <!-- 滚动条要包裹的tree内容 --> 
        <el-tree 
          :data="tree"
          node-key="id"
          :props="elTreeProps"
          @node-click="nodeClick"
        ></el-tree>
      </el-scrollbar>
    </div>
    <div class="table-container">
      <div class="queryContainer">
        <span>时间：</span>
        <el-date-picker
          v-model="time"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          size="medium"
          style="margin: 0 10px;"
          >
        </el-date-picker>
        <el-button type="primary" icon="el-icon-search" size="medium" @click="handleQuery">查询</el-button>
      </div>

      <el-table
        :data="pressureList"
        size="medium"
        style="height: calc(100% - 96px);"
      >
        <el-table-column label="仪器名称" prop="insmName"></el-table-column>
        <el-table-column label="类型" prop="sourceTypeName"></el-table-column>
        <el-table-column label="站点名称" prop="facilityName"></el-table-column>
        <el-table-column label="压力值(mPa)" prop="pressure"></el-table-column>
        <el-table-column label="监测时间" prop="dataTime"></el-table-column>
      </el-table>

      <div class="paginationContainer">
        <el-pagination        
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageInfo.pageNo"
          :page-sizes="[10, 20, 30, 40]"
          :page-size.sync="pageInfo.pageSize"
          layout="total, sizes, ->, prev, pager, next, jumper"
          :total="pageInfo.total">
        </el-pagination>
      </div>
    </div>

  </div>
</template>

<script>
import { getAreaTree } from '@/api/leakageLoss/analysis'
import { getPressurePageList } from '@/api/leakageLoss/pressure'

export default {
  data() {
    return {
      currentAreaId: null,
      tree: [],
      elTreeProps: {
        children: 'subSet',
        label: 'name'
      },
      time: [],
      pressureList: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
    }
  },
  watch: {
    time(value) {
      if(!value) {
        this.time = []
      }
    }
  },
  mounted() {
    this.getTree()
    this.getPageList()
  },
  methods: {
    getTree() {
      getAreaTree().then(res => {
        this.tree = res.data
      })
    },
    getPageList(payload) {
      let data = {
        pageSize: this.pageInfo.pageSize,
        pageNo: this.pageInfo.pageNo
      }
      if(payload) {
        data = {
          ...data,
          areaIdList: payload.areaId ? [payload.areaId] : [],
          beginTime: payload.beginTime,
          endTime: payload.endTime
        }
      }
      getPressurePageList(data).then(res => {
        console.log(res)
        const { status, data, count } = res
        if(status === 200) {
          this.pressureList = data
          this.pageInfo.total = count
        }
      })
    },
    nodeClick(data, node, nodeComponent) {
      console.log(data, node, nodeComponent)
      this.currentAreaId = data.id
      this.getPageList({
        areaId: data.id
      })
    },
    handleQuery() {
      let pageQueryData = {
        areaId: this.currentAreaId
      }
      let charQuerytData = {}
      if(this.time[0]) {
        pageQueryData.beginTime = this.time[0]
        charQuerytData.beginTime = this.time[0]
      }
      if(this.time[1]) {
        pageQueryData.endTime = this.time[1]
        charQuerytData.endTime = this.time[1]
      }
      this.getPageList(pageQueryData)
    },
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.getPageList()
    },
    handleCurrentChange() {
      this.getPageList()
    }
  }
}
</script>

<style lang="scss">
.pressureList {
  .treeContainer {
    .el-scrollbar__wrap {
      overflow-x: auto;
    }
    .el-tree {
      background: transparent;
      .el-tree-node {
        color: #fff;

      }
      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #113b7a;
      }
      .el-tree-node__content:hover {
        background-color: #113b7a;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.pressureList {
  height: 100%;
  padding: 20px;
  display: flex;
  .areaTree {
    width: 200px;
    height: 100%;
    .treeContainer {
      height: 100%;
    }
  }
  .table-container {
    height: 100%;
    padding-left: 20px;
    flex-grow: 1;
    .queryContainer {
      padding-bottom: 10px;
    }
    .paginationContainer {
      height: 50px;
      padding: 7px 0;
    }
  }
}
</style>
