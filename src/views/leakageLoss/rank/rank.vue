<template>
  <div class="rank">
    <el-row>
      <el-col :span="8">
        <div class="tableQuery">
          <span>时间：</span>
          <el-date-picker
            v-model="time"
            type="daterange"
            size="mini"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </div>
        <el-table
          :data="tableData"
          size="mini"
        >
          <!-- <el-table-column label="分区名称"></el-table-column> -->
          <el-table-column label="分区名称" prop="name"></el-table-column>
          <el-table-column label="漏水量(m³)" prop="value1"></el-table-column>
          <el-table-column label="供水量(m³)" prop="value2"></el-table-column>
          <el-table-column label="漏损率(%)" prop="rate"></el-table-column>
        </el-table>
      </el-col>
      <el-col :span="16">
        <el-row>
          <el-col :span="12">
            <BigGauge height="300px" :value="value" />
          </el-col>
          <el-col :span="12">
            <NormalPie height="300px" :seriesData="pieData" />
          </el-col>
          <el-col :span="24">
            <SingleLine height="400px" :seriesData="lineData" :xAxisData="xAxisData" />
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import BigGauge from './components/bigGauge'
import NormalPie from './components/normalPie'
import SingleLine from './components/commonLine'

export default {
  components: {
    BigGauge,
    NormalPie,
    SingleLine
  },
  data() {
    return {
      time: '',
      tableData: [],
      value: 23.7,
      pieData: [
        {
          name: '漏水量',
          value: 24
        },
        {
          name: '用水量',
          value: 76
        }              
      ],
      lineData: [],
      xAxisData: []
    }
  },
  created() {
    this.tableData = new Array(16).fill(
      {
        name: '分区1',
        value1: 170281.9,
        value2: 333595.0,
        rate: 51.0
      }
    )
    let temp = []
    for(let i = 0; i < 30; i++) {
      temp.push((Math.random() * 50).toFixed(2))
    }
    this.lineData = temp
    this.xAxisData = new Array(30).fill('1')
  }
}
</script>

<style lang="scss" scoped>
.rank {
  height: 100%;
  min-height: 757px;
  padding: 20px;
  .tableQuery {
    padding-bottom: 10px;
  }
}
</style>