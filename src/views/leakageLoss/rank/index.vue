<template>
  <div class="WlRank app-container">
    <div class="tab">
      <div class="first" :class="{ active: currentType === 'tab1' }" @click="handleTabClick('tab1')">漏损排行榜</div>
      <div class="other" :class="{ active: currentType === 'tab2' }" @click="handleTabClick('tab2')">水量平衡分析</div>
    </div>
    <div class="compenent-container">
      <Rank v-if="currentType === 'tab1'" />
      <Water v-else />
    </div>
  </div>
</template>

<script>
import Rank from './rank'
import Water from './water'

export default {
  components: { 
    Rank,
    Water
  },
  data() {
    return {
      currentType: 'tab1'
    }
  },
  methods: {
    handleTabClick(type) {
      this.currentType = type
    }
  }
}
</script>

<style lang="scss">
.WlRank { 
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.WlRank {
  padding: 0 30px;
  height: calc(100vh - 84px);
  .compenent-container {
    color: #ffffff;
    // padding: 20px;
    height: calc(100% - 76px);
    border: 1px solid #2A5DD2;
    border-top-color: #3084B5;
    position: relative;
  }
}
</style>
