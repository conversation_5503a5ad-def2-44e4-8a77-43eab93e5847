<template>
  <div class="water">
    <el-row style="height: 100%;">
      <el-col :span="10">
        <div style="padding-bottom: 10px;">
          <span>时间：</span>
          <el-date-picker
            v-model="time"
            type="month"
            size="mini"
            placeholder="选择月">
          </el-date-picker>
        </div>
        <el-table
          :data="tableData"
          size="mini"
        >
          <!-- <el-table-column label="分区名称"></el-table-column> -->
          <el-table-column label="分区名称" prop="name"></el-table-column>
          <el-table-column label="漏水量(m³)" prop="value1"></el-table-column>
          <el-table-column label="供水量(m³)" prop="value2"></el-table-column>
          <el-table-column label="漏损率(%)" prop="rate"></el-table-column>
          <el-table-column label="售水量(m³)" prop="value2"></el-table-column>
          <el-table-column label="产销差率(%)" prop="rate"></el-table-column>
        </el-table>
      </el-col>
      <el-col :span="14" style="height: 100%;">
        <TreeMap :treeMapData="treeMapData" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import TreeMap from './components/treeMap'

export default {
  components: { TreeMap },
  data() {
    return {
      time: '',
      tableData: [],
      treeMapData: [
        {
          name: '系统供水量',
          value: 10
        },
        {
          name: '合法水量',
          value: 5
        },
        {
          name: '漏损水量',
          value: 5
        },
        {
          name: '收费合法水量',
          value: 3
        },
        {
          name: '收费计量水量',
          value: 3
        },
        {
          name: '收费未计量水量',
          value: 3
        },
        {
          name: '收益水量',
          value: 3
        }
      ]
    }
  },
  created() {
    this.tableData = new Array(16).fill(
      {
        name: '分区1',
        value1: 170281.9,
        value2: 333595.0,
        rate: 51.0
      }
    )
  }
}
</script>

<style lang="scss" scoped>
.water {
  height: 100%;
  min-height: 757px;
  padding: 20px;
  .treeMapBox {

  }
}
</style>