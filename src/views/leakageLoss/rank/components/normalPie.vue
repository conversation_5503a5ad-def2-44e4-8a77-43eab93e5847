<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        tooltip: {
          trigger: 'item'
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        title: {
          text: '水平衡表(%)',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          top: 30,
          left: 0,
          textStyle: {
            color: '#fff'
          },
          orient: 'vertical',
          data: ['漏水量', '用水量']
        },
        series: [
          {
            color: ['#FB7B1E', '#87cefa', '#00D360', '#03A4FF', '#00E4FF', '#C6ECDF'],
            type: 'pie',
            center: ['50%', '50%'],
            labelLine: {
              length: 10,
              length2: 60,
              lineStyle: {
                color: '#ACC6EA'
              }
            },
            label: {
              position: 'inside',
              formatter: '{d}%',
            },
            data: this.seriesData
          }
        ]
      })
    }
  }
}
</script>
