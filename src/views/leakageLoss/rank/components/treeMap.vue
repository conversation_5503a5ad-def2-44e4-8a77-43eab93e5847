<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    treeMapData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    value: {
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        tooltip: {
          trigger: 'item'
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        title: {
          left: 20,
          text: '水平衡统计表',
          textStyle: {
            color: '#fff'
          }
        },
        series: [
          {
            type: 'treemap',
            left: 20,
            top: 40,
            right: 0,
            bottom: 0,
            roam: false,
            nodeClick: false,
            breadcrumb: {
              show: false,
            },
            label: {
              formatter: '{b} \n ({c}m³)'
            },
            data: this.treeMapData
          }
        ]
      })
    }
  }
}
</script>
