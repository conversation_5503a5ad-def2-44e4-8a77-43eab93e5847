<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    value: {
      type: Number,
      // default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    value: {
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        // tooltip: {
        //   trigger: 'item'
        // },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        series: [
          {
            type: 'gauge',
            radius: '90%',
            axisLine: {
              lineStyle: {
                width: 15
              }
            },
            axisTick: {
              length: 25,
              lineStyle: {
                color: 'auto',
              }
            },
            splitLine: {
              length: 30,
              lineStyle: {
                color: 'auto'
              }
            },
            detail: {
              formatter: function(value) {
                return value + '%'
              }
            },
            title: {
              textStyle: {
                color: "#fff",
                fontWeight: 'bold',
                fontSize: 20
              }
            },
            data: [{ value: this.value, name: '产销差率' }]
          }
        ]
      })
    }
  }
}
</script>
