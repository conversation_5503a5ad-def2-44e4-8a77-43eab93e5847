<template>
  <div class="leakageLossLayout">
    <!-- 菜单区域 -->
    <div class="menu">
      <div class="collapse">
        <i v-if="!isCollapse" class="el-icon-s-fold collapseIcon" @click="changeCollapse"></i>
        <i v-else class="el-icon-s-unfold collapseIcon" @click="changeCollapse"></i>
      </div>
      <el-menu
        :default-active="activeMenu"
        router
        class="el-menu-vertical"
        :collapse="isCollapse"
        background-color="#112C51"
        text-color="#fff"
        active-text-color="#fff"
        @select="menuSelect"
      >
        <!-- <el-menu-item index="/wlStatistic/wlManage/subzone">
          <img class="menuIcon" src="@/assets/img/leakageLoss/menu1.png" alt="">
          <span slot="title">分区管理</span>
        </el-menu-item> -->
        <el-submenu index="/wlStatistic/wlManage/subzone">
          <template slot="title">
            <img class="menuIcon" src="@/assets/img/leakageLoss/menu1.png" alt="">
            <span slot="title">分区管理</span>
          </template>
          <el-menu-item index="/wlStatistic/wlManage/subzone">DMA管理</el-menu-item>
          <el-menu-item index="/wlStatistic/wlManage/bigUser">大用户管理</el-menu-item>
        </el-submenu>
        <el-menu-item index="/wlStatistic/wlManage/pressureMap">
          <img class="menuIcon" src="@/assets/img/leakageLoss/menu2.png" alt="">
          <span slot="title">压力地图</span>
        </el-menu-item>
        <el-menu-item index="/wlStatistic/wlManage/diagnosis">
          <img class="menuIcon" src="@/assets/img/leakageLoss/menu2.png" alt="">
          <span slot="title">智能诊断</span>
        </el-menu-item>
        <el-menu-item index="/wlStatistic/wlManage/dispatch">
          <img class="menuIcon" src="@/assets/img/leakageLoss/menu3.png" alt="">
          <span slot="title">调度管理</span>
        </el-menu-item>
        <el-submenu index="/wlStatistic/wlManage/standard">
          <template slot="title">
            <img class="menuIcon" src="@/assets/img/leakageLoss/menu4.png" alt="">
            <span slot="title">绩效管理</span>
          </template>
          <el-menu-item index="/wlStatistic/wlManage/standard">标准管理</el-menu-item>
          <el-menu-item index="/wlStatistic/wlManage/score">评分管理</el-menu-item>
        </el-submenu>
        <el-menu-item index="/wlStatistic/wlManage/wlAnalysis">
          <img class="menuIcon" src="@/assets/img/leakageLoss/menu5.png" alt="">
          <span slot="title">漏损分析</span>
        </el-menu-item>
        <el-menu-item index="/wlStatistic/wlManage/warning">
          <img class="menuIcon" src="@/assets/img/leakageLoss/menu6.png" alt="">
          <span slot="title">漏损报警</span>
        </el-menu-item>
      </el-menu>
    </div>
    <div class="page">
      <div class="nav" style="width: 100%;">
        <!-- <img class="backArrow" src="@/assets/img/home/<USER>" alt="" @click="back"> -->
        <span>当前位置：管网漏损 <i class="el-icon-arrow-left"></i> 漏损中心  <i class="el-icon-arrow-left"></i> <span style="color: #fff;">{{ subName }}</span></span>
        <div v-if="isSubzone" class="rightBox">
          <div class="oButton" :class="{ active: DMAShowType === 'chart' }" @click="changeValue('chart')">图表</div>
          <div class="oButton" :class="{ active: DMAShowType === 'table' }" style="margin-left: 6px;" @click="changeValue('table')">表格</div>
        </div>
      </div>
      <div class="pageContainer" style="width: 100%;">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'LeakageLossLayout',
  data() {
    return {
      isCollapse: false
    }
  },
  computed: {
    ...mapGetters('app', ['DMAShowType']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    subName() {
      return this.$route.meta.name
    },
    isSubzone() {
      return this.$route.name === 'Subzone'
    },
    pageStyle() {
      if(!this.isCollapse) {
        return {
          width: 'calc(100% - 240px)'
        }
      } else {
        return {
          width: 'calc(100% - 66px)'
        }
      }
    }
  },
  methods: {
    changeCollapse() {
      this.isCollapse = !this.isCollapse
    },
    menuSelect(index, indexPath) {
      // console.log(index, indexPath)
    },
    back() {
      this.$router.go(-1)
    },
    changeValue(value) {
      this.$store.dispatch('app/setDMAShowType', value)
    },
  }
}
</script>

<style lang="scss">
.leakageLossLayout {
  .el-menu--collapse {
    // width: 64px !important;
  }
  .el-menu {
    border-right: none;
    .is-active {
      background-color: #2155BB !important;
    }
  }

}
</style>

<style lang="scss" scoped>
.leakageLossLayout {
  height: calc(100vh - 84px);
  padding-bottom: 30px;
  display: flex;
  .menu {
    background-color: rgba(42,93,210,0.1);
    border: 1px solid rgba(42,93,210,0.5);
    .collapse {
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      .collapseIcon {
        font-size: 18px;
        color: #fff;
        cursor: pointer;
      }
    }
    .el-menu-vertical:not(.el-menu--collapse) {
      width: 240px;
    }
    .menuItem {
      height: 50px;
      background-color: rgba(46,94,164,0.2);
      margin-bottom: 10px;
      &.active {
        background: linear-gradient( 270deg, #2458C2 0%, #003970 100%);
      }
    }
    .menuIcon {
      width: 18px;
      margin-right: 5px;
    }
  }
  .page {
    color: #fff;
    width: 100%;
    height: 100%;
    flex: 1;
    padding: 0 20px;
    .nav {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 19px;
      color: #ACC6EA;
      .backArrow {
        width: 28px;
        height: 17px;
        margin-right: 5px;
        cursor: pointer;
      }
      .rightBox {
        display: flex;
      }
      .oButton {
        width: 100px;
        height: 34px;
        text-align: center;
        line-height: 30px;
        border: 2px solid transparent;
        background-color: #032D6F;
        cursor: pointer;
      }
      .active {
        color: #FFFFFF;
        border-color: #41D1E5;
      }
    }
    .pageContainer {
      height: calc(100% - 40px);
      border: 1px solid rgba(42,93,210,0.5);
      background: rgba(3,30,73,0.5);
      padding: 20px;
    }
  }
}
</style>