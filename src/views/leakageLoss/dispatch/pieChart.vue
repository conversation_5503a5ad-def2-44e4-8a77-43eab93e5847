<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
import resize from "@/utils/chartResize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    seriesData: {
      type: Array,
      default: () => [],
    },
    total: {
      type: Number,
      default: 0
    },
    subtext: {
      type: String,
      default: '',
    },
    xAxisData: {
      type: Array,
      default: () => [],
    },
    legendData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart();
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el);
      this.chart.setOption({
        tooltip: {
          trigger: "item",
        },
        title: {
          text: this.total + '件',
          top: "40%",
          subtext: this.subtext,
          x: "center",
          y: "center",
          textStyle: {
            fontSize: 25,
            color: ["#00cfff"],
          },
          subtextStyle: {
            color: "#fff",
            fontSize: 14,
          },
        },
        legend: {
          show: false,
        },

        series: [
          //主要显示层
          {
            color: ["#496DFF", "#EDE497", "#00E4FF", "#3CD495"],
            type: "pie",
            radius: ["60%", "75%"],
            center: ["50%", "50%"],
            labelLine: {
              length: 30,
              length2: 60,
              lineStyle: {
                color: "#deebff",
              },
            },
            label: {
              // '{top | {b}} \n {c}万m³({d}%)'
              formatter: "{top|{b}} \n {bottom|{c}}({d}%)",
              padding: -50,
              // distanceToLabelLine: 5,
              rich: {
                top: {
                  padding: 5,
                },
                bottom: {
                  padding: 5,
                },
              },
            },
            data: this.seriesData,
          },
          //边框设置
          {
            radius: ["42%", "42.3%"],
            center: ["50%", "50%"],
            type: "pie",
            label: {
              normal: {
                show: false,
              },
              emphasis: {
                show: false,
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
              emphasis: {
                show: false,
              },
            },
            animation: false,
            tooltip: {
              show: false,
            },
            itemStyle: {
              borderColor: "#295bce",
            },
            data: [{ value: 1 }],
          },
        ],
      });
    },
  },
};
</script>