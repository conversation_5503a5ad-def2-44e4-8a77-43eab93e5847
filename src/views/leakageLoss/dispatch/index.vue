<template>
  <div class="wlDispatch">
    <div class="query">
      <span>选择时间：</span>
      <el-date-picker
        v-model="timeRange"
        type="daterange"
        range-separator="—"
        start-placeholder="选择时间"
        end-placeholder="选择时间"
        size="medium"
      >
      </el-date-picker>
      <el-button
        type="primary"
        icon="el-icon-search"
        size="medium"
        style="margin-left: 20px"
        >查询</el-button
      >
    </div>

    <div class="content">
      <div class="side">
        <div class="cardTitle"> <span class="text">事件中心</span></div>
        <div class="countBox">
          <div class="innerBox">
            <img src="@/assets/img/leakageLoss/event.png" alt="">
            <div class="label">今日事件（件）</div>
            <div class="value">23</div>
          </div>
        </div>
        <div class="selectBox">
          <el-select v-model="eventType" placeholder="事件类型" class="halfWidth">
            <el-option
              v-for="item in eventTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-select v-model="eventState" placeholder="状态"  class="halfWidth">
            <el-option
              v-for="item in eventStateList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="list">
          <div class="listHeader">
            <span style="width: 50px;text-align: center;">序号</span>
            <span style="width: 70px;">事件名称</span>
            <span style="width: 82px;">事件类型</span>
            <span style="width: 70px;">状态</span>
            <span style="width: 95px;">时间</span>
            <span style="flex: 1;">操作</span>
          </div>
          <div class="listItem" v-for="item, index in eventList" :key="item.id">
            <div style="width: 50px;text-align: center;">{{ index + 1 }}</div>
            <div style="width: 70px;">{{ item.name }}</div>
            <div style="width: 82px;">{{ item.type }}</div>
            <div style="width: 70px;" :class="[`state${item.state}`]" >{{ item.stateStr }}</div>
            <div style="width: 95px;">{{ item.time }}</div>
            <div style="flex: 1;">
              <span class="textButton" @click="openDialog">建立工单</span>
              <span class="textButton">确认工单</span>
            </div>
          </div>
        </div>
        <div class="pageBox">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="pageInfo.total">
          </el-pagination>
        </div>
      </div>
      <div class="middle">
        <div class="cardTitle"><span class="text">数据分析</span></div>
        <PieChart :seriesData="seriesData1" :total="23" subtext="事件总数" height="200px" />
        <div class="chartTitle">今日事件类型分析</div>
        <PieChart :seriesData="seriesData2" :total="28" subtext="工单总数" height="200px" />
        <div class="chartTitle">今日工单类型分析</div>
      </div>
      <div class="side">
        <div class="cardTitle"><span class="text">工单管理</span></div>
        <div class="countBox">
          <div class="innerBox">
            <img src="@/assets/img/leakageLoss/order.png" alt="">
            <div class="label">今日事件（件）</div>
            <div class="value">28</div>
          </div>
        </div>
        <div class="selectBox">
          <el-select v-model="eventType" placeholder="工单类型" class="halfWidth">
            <el-option
              v-for="item in eventTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-select v-model="eventState" placeholder="状态"  class="halfWidth">
            <el-option
              v-for="item in eventStateList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="list">
          <div class="listHeader">
            <span style="width: 50px;text-align: center;">序号</span>
            <span style="width: 70px;">事件名称</span>
            <span style="width: 82px;">事件类型</span>
            <span style="width: 70px;">状态</span>
            <span style="width: 95px;">时间</span>
            <span style="flex: 1;">操作</span>
          </div>
          <div class="listItem" v-for="item, index in eventList" :key="item.id">
            <div style="width: 50px;text-align: center;">{{ index + 1 }}</div>
            <div style="width: 70px;">{{ item.name }}</div>
            <div style="width: 82px;">{{ item.type }}</div>
            <div style="width: 70px;" :class="[`state${item.state}`]" >{{ item.stateStr }}</div>
            <div style="width: 95px;">{{ item.time }}</div>
            <div style="flex: 1;">
              <span class="textButton">建立工单</span>
              <span class="textButton">确认工单</span>
            </div>
          </div>
        </div>
        <div class="pageBox">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="pageInfo.total">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      width="480px"
      top="10vh"
      title="建立工单">
      <el-form :model="form" label-width="auto" size="medium">
        <el-form-item label="事件名称：">
          <el-input v-model="form.name" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="事件类型：">
          <el-select v-model="form.type" placeholder=""></el-select>
        </el-form-item>
        <el-form-item label="所在位置：">
          <el-input v-model="form.location" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="监控地点：">
          <el-input v-model="form.location1" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="预警时间：">
          <el-date-picker
            v-model="form.time"
            type="datetime"
            placeholder="">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="维护人员：">
          <el-input v-model="form.person" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="报警图片：">
          <div class="fileItem" @click="fileTrigger">
            <div class="fileTrigger"><i class="el-icon-plus" style="color: #fff;font-size: 24px;"></i></div>
            <span style="line-height: normal;">0/3</span>
          </div>
           <input type="file" accept=".png, .jpg, .jpeg" @change="fileChange" ref="fileRef" id="fileRef" >
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入"
            v-model="form.remark">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="info" style="margin-bottom: 20px;" @click="dialogVisible = false">取消</el-button>
          <el-button type="primary">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import PieChart from './pieChart.vue'

export default {
  name: 'WlDispatch',
  components: { PieChart },
  data() {
    return {
      timeRange: [],
      eventType: '',
      eventTypeList: [],
      eventState: '',
      eventStateList: [],
      eventList: [
        {
          id: 1,
          name: '水质预警',
          type: '巡检数据上报',
          state: 1,
          stateStr: '待分配',
          time: '03-25 12:18:27'
        },
        {
          id: 2,
          name: '摄像头损坏',
          type: '设备报警',
          state: 3,
          stateStr: '已处理',
          time: '03-25 12:18:27'
        },
        {
          id: 3,
          name: '水质预警',
          type: '巡检数据上报',
          state: 2,
          stateStr: '处理中',
          time: '03-25 12:18:27'
        },
        {
          id: 4,
          name: '水质预警',
          type: '巡检数据上报',
          state: 2,
          stateStr: '处理中',
          time: '03-25 12:18:27'
        },
        {
          id: 5,
          name: '水质预警',
          type: '巡检数据上报',
          state: 2,
          stateStr: '处理中',
          time: '03-25 12:18:27'
        },
        {
          id: 6,
          name: '水质预警',
          type: '巡检数据上报',
          state: 1,
          stateStr: '待分配',
          time: '03-25 12:18:27'
        },
        {
          id: 7,
          name: '摄像头损坏',
          type: '设备报警',
          state: 3,
          stateStr: '已处理',
          time: '03-25 12:18:27'
        },
        {
          id: 8,
          name: '水质预警',
          type: '巡检数据上报',
          state: 2,
          stateStr: '处理中',
          time: '03-25 12:18:27'
        },
        {
          id: 9,
          name: '水质预警',
          type: '巡检数据上报',
          state: 2,
          stateStr: '处理中',
          time: '03-25 12:18:27'
        },
        {
          id: 10,
          name: '水质预警',
          type: '漏控信息',
          state: 2,
          stateStr: '处理中',
          time: '03-25 12:18:27'
        },
      ],
      pageInfo: {
        pageSize: 10,
        pageNo: 1,
        total: 0
      },
      seriesData1: [
        {
          name: '客服系统',
          value: 6
        },
        {
          name: '设备报警',
          value: 6
        },
        {
          name: '漏控信息',
          value: 6
        },
        {
          name: '断水信息',
          value: 5
        }
      ],
      seriesData2: [
        {
          name: '抢修派单',
          value: 7
        },
        {
          name: '设施维护派单',
          value: 6
        },
        {
          name: '侧漏派单',
          value: 8
        },
        {
          name: '断水派单',
          value: 7
        }
      ],
      dialogVisible: false,
      form: {}
    }
  },
  methods: {
    openDialog() {
      this.dialogVisible = true
    },
    fileChange() {},
    fileTrigger() {
      this.$refs.fileRef.click()
    }
  }
}
</script>

<style lang="scss">
.wlDispatch { 
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.wlDispatch {
  height: 100%;
  .query {
    font-size: 14px;
  }
  .content {
    display: flex;
    height: calc(100% -  51px);
    margin-top: 15px;
    .side {
      width: 35%;
      background-color: rgba(4,15,45,0.3);
    }
    .middle {
      width: 30%;
      margin: 0 20px;
      background-color: rgba(4,15,45,0.3);
      .chartTitle {
        text-align: center;
        font-size: 14px;
        color: #D8F0FF;
        line-height: 14px;
      }
    }
    .cardTitle {
      height: 40px;
      line-height: 40px;
      background: linear-gradient( 270deg, #164F8F 0%, #10243E 100%);
      padding-left: 28px;;
      .text {
        font-weight: 700;
        font-size: 18px;
        // background-image: linear-gradient(90deg, #FFFFFF 41%, #31B3FF 100%);
      }
    }
    .countBox {
      padding: 10px 65px;
      .innerBox {
        height: 130px;
        background-image: url('~@/assets/img/leakageLoss/countBg.png');
        background-size: 100%;
        display: flex;
        align-items: center;
        justify-content: space-around;
        img {
          width: 98px;
        }
        .label {
          font-size: 18px;
        }
        .value {
          font-family: DIN;
          font-weight: 700;
          font-size: 36px;
          color: #A3DDFF;
        }
      }
    }
    .selectBox {
      .halfWidth {
        width: calc((100% - 60px) / 2);
        margin-left: 20px;
      }
    }
    .pageBox {
      height: 36px;
      text-align: center;
    }
    .list {
      margin-top: 10px;
      height: calc(100% - 276px);
      overflow-y: auto;
    }
    .listHeader {
      font-size: 14px;
      background-image: url("../../../assets/img/table.png");
      background-size: 100%;
      height: 34px;
      display: flex;
      align-items: center;
      span:last-child {
        flex: 1;
      }
    }
    .listItem {
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 30px;
      background-color: rgba(2,50,128,0.23);
      border: 1px solid #023280;
      margin-top: 5px;
      .textButton {
        color: #11BAFF;
        cursor: pointer;
        margin-right: 10px;
      }
    }
    .state1 {
      color: #11E6FF;
    }
    .state2 {
      color: #F6BD16 ;
    }
    .state3 {
      color: #00FE00;
    }
    
  }
  .fileItem {
    display: flex;
    align-items: flex-end;
  }
  .fileTrigger {
    width: 80px;
    height: 80px;
    border: 1px solid #1382E6;
    text-align: center;
    line-height: 80px;
    cursor: pointer;
  }
  #fileRef {
    display: none;
  }
}
</style>