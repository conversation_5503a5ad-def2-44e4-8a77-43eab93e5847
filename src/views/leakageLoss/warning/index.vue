<template>
  <div class="wlWarning">
    <div class="cardBox">
      <div class="cardSection">
        <div class="item total">
          <div class="label">全部</div>
          <div class="value">{{ countInfo.total }}</div>
        </div>
        <div class="item blue">
          <div class="label">未处理</div>
          <div class="value">{{ countInfo.untreated }}</div>
        </div>
        <div class="item blue">
          <div class="label">已处理</div>
          <div class="value">{{ countInfo.processed }}</div>
        </div>
        <div class="item blue">
          <div class="label">已忽略</div>
          <div class="value">{{ countInfo.ignored }}</div>
        </div>
      </div>
      <div class="cardSection">
        <div class="item red">
          <div class="label">红色报警</div>
          <div class="value">{{ countInfo.red }}</div>
        </div>
        <div class="item yellow">
          <div class="label">黄色报警</div>
          <div class="value">{{ countInfo.yellow }}</div>
        </div>
      </div>
    </div>

    <div class="query">
      <el-form :inline="true" :model="queryForm">
        <el-form-item label="事件类型：">
           <el-select v-model="queryForm.type" placeholder="全部" size="medium">
              <el-option
                v-for="item in eventTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="事件来源：">
           <el-select v-model="queryForm.source" placeholder="全部" size="medium">
              <el-option
                v-for="item in eventSources"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="时间选择：">
          <el-date-picker
            v-model="queryForm.dateRange"
            type="daterange"
            value-format="yyyy-MM-dd"
            :clearable="false"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="medium">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery" size="medium">查询</el-button>
        </el-form-item>
      </el-form>

    </div>

    <div class="tableBox">
      <el-table
        style="width: 100%;position: absolute;"
        height="100%"
        :data="tableData"
        size="medium"
      >
        <el-table-column label="序号" type="index" width="55"></el-table-column>
        <el-table-column prop="type" label="事件类型" width="100"></el-table-column>
        <el-table-column prop="socrce" label="事件来源" width="120"></el-table-column>
        <el-table-column prop="address" label="小区地址"></el-table-column>
        <el-table-column prop="warningTime" label="报警时间" width="150"></el-table-column>
        <el-table-column prop="warningValue" label="报警值" width="100"></el-table-column>
        <el-table-column prop="recoveryTime" label="恢复时间" width="150"></el-table-column>
        <el-table-column prop="nightMinFlow" label="夜间最小流量(m³/h)" width="150"></el-table-column>        
        <el-table-column prop="warningState" label="报警状态">
          <template slot-scope="{ row }"><span>{{ row.warningState ? '' : '未处理' }}</span></template>
        </el-table-column>
        <el-table-column prop="processingState" label="处理状态">
           <template slot-scope="{ row }">
            <span style="color: #00FE00;" v-if="row.processingState">已处理</span>
            <span style="color: #F6BD16;"  v-else>已处理</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="操作">
           <template slot-scope="{ row }">
            <span style="color: #F6BD16;" class="operateText" @click="handleIgnore(row)">忽略</span>
            <span style="color: #00E4FF;" class="operateText">处理</span>
            <span style="color: #F25A55;" class="operateText">转工单</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-pagination        
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="pageInfo.pageNo"
      :page-sizes="[10, 20, 30, 40]"
      :page-size.sync="pageInfo.pageSize"
      layout="total, sizes, ->, prev, pager, next, jumper"
      :total="pageInfo.total">
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'WlWarning',
  data() {
    return {
      countInfo: {
        total: 17,
        untreated: 7,
        processed: 6,
        ignored: 4,
        red: 5,
        yellow: 6
      },
      eventTypes: [
        {
          value: 1,
          label: '漏损报警'
        },
        {
          value: 2,
          label: '存量报警'
        }
      ],
      eventSources: [
        {
          value: 1,
          label: '阳光居民总表'
        },
        {
          value: 2,
          label: '兴发家园总表'
        },
      ],
      queryForm: {
        type: null,
        source: null,
        dateRange: []
      },
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 2,
      },
      tableData: [
        {
          type: '漏失报警',
          socrce: '阳光居民总表',
          address: '阳光居民北门',
          warningTime: '2024-09-29 12:35:28',
          warningValue: 453,
          recoveryTime: '2024-09-29 12:35:28',
          nightMinFlow: 5.3,
          warningState: 0,
          processingState: 1
        },
        {
          type: '存量报警',
          socrce: '兴发家园总表',
          address: '兴发家园集体宿舍',
          warningTime: '2024-09-28 12:35:28',
          warningValue: 128,
          recoveryTime: '2024-09-28 12:35:28',
          nightMinFlow: 4.6,
          warningState: 0,
          processingState: 0
        }
      ]
    }
  },
  methods: {
    getList() {},
    handleQuery() {},
    handleIgnore(row) {},
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    }
  }
}
</script>

<style lang="scss">
.wlWarning {
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
  .el-table {
    font-size: 14px !important;
  }
}
</style>

<style lang="scss" scoped>
.wlWarning {
  height: 100%;
  .cardBox {
    display: flex;
    justify-content: space-between;
    .item {
      width: 180px;
      height: 101px;
      background-size: 100%;
      .label {
        height: 34px;
        color: #DBF5FD;
        text-align: center;
        line-height: 34px;
      }
      .value {
        font-family: DIN;
        font-weight: 700;
        font-size: 30px;
        color: #FFFFFF;
        text-align: center;
        padding-top: 10px;
      }
    }
    .total {
      background-image: url('~@/assets/img/leakageLoss/totalCard.png');
    }
    .blue {
      background-image: url('~@/assets/img/leakageLoss/blueCard.png');
      margin-left: 20px;
    }
    .red {
      background-image: url('~@/assets/img/leakageLoss/redCard.png');
    }
    .yellow {
      background-image: url('~@/assets/img/leakageLoss/yellowCard.png');
      margin-left: 20px;
    }
    .cardSection {
      display: flex;
    }
  }
  .query {
    padding: 20px 0;
  }
  .tableBox {
    width: 100%;
    position: relative;
    overflow: hidden;
    height: calc(100% - 235px);
    .operateText {
      margin-left: 20px;
      cursor: pointer;
    }
  }
}
</style>