<template>
  <div class="pressureMap">
    <div class="colorBarImage"></div>
    <div id="mars3dContainer" class="mars3d-container">
      <el-dialog :visible.sync="colorBoxFlag" title="颜色设置" width="30%">
        <span class="block">
          <span class="demonstration">一级</span>
          <el-color-picker v-model="color1"></el-color-picker>
        </span>
        <span class="block">
          <span class="demonstration">二级</span>
          <el-color-picker v-model="color2"></el-color-picker>
        </span>
        <span class="block">
          <span class="demonstration">三级</span>
          <el-color-picker v-model="color3"></el-color-picker>
        </span>
        <span class="block">
          <span class="demonstration">四级</span>
          <el-color-picker v-model="color4"></el-color-picker>
        </span>
        <span class="block">
          <span class="demonstration">五级</span>
          <el-color-picker v-model="color5"></el-color-picker>
        </span>
        <span class="block">
          <span class="demonstration">六级</span>
          <el-color-picker v-model="color6"></el-color-picker>
        </span>
        <div>
          <!-- <el-button
            type="primary"
            style="margin: 20px 0"
            size="medium"
            @click="change"
            >更换颜色</el-button
          > -->
        </div>
      </el-dialog>
      <!-- <div class="infoview"> -->
        <!-- <table class="mars-table"> -->
          <!-- <tr class="addPipe"> -->
            <!-- <el-button type="primary" size="medium" @click="resume"
              >继续播放</el-button
            >
            <el-button type="primary" size="medium" @click="colorBoxDisplay"
              >颜色设置</el-button
            > -->
            <!-- <div class="timeline-progress">
              <div class="progress-container" @click="pauseAtClick">
                <div
                  class="progress-bar"
                  :style="{ width: progress + '%' }"
                ></div>
              </div>
              <div class="progress">
                <div
                  class="progress-time"
                  v-for="(item, index) in timeArr"
                  :key="index"
                >
                  <div class="progress-time-item">{{ item }}</div>
                </div>
              </div>
            </div> -->
          <!-- </tr> -->
        <!-- </table> -->
      <!-- </div> -->
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { getPressureHeatData } from "@/api/leakageLoss/pressure";
export default {
  data: 'PressureMap',
  data() {
    return {
      mapOptions: {
        scene: {
          //默认视角参数
          center: {
            lat: 31.24575,
            lng: 110.7086,
            alt: 55000,
            heading: 0,
            pitch: -90,
          },
          sceneMode: 2,
          // globe: {
          //   depthTestAgainstTerrain: true, // 开启深度检测
          // },
        },
        control: {
          // baseLayerPicker: true, // basemaps底图切换按钮
          // homeButton: true, // 视角复位按钮
          // sceneModePicker: true, // 二三维切换按钮
          // navigationHelpButton: true, // 帮助按钮
          // fullscreenButton: true, // 全屏按钮
          // contextmenu: { hasDefault: true } // 右键菜单
          // terrainProviderViewModels: [],
           timeline: true,
           clockAnimate: true
        },
        terrain: {
          url: "http://data.mars3d.cn/terrain",
          show: true,
        },
        basemaps: [
          { id: 10, name: "地图底图", type: "group" },
          {
            pid: 10,
            name: "天地图影像",
            icon: require("../../../assets/img/basemaps/tdt_img.png"),
            type: "group",
            layers: [
              { name: "底图", type: "tdt", layer: "img_d" },
              { name: "注记", type: "tdt", layer: "img_z" },
            ],
            show: true,
          },
        ],
      },
      geourl: "",
      heatLayer: null,
      heatArr: [],
      timeArr: [],
      currentTime: 0, // 当前时间（秒）
      duration: 4, // 总时长（秒）
      progress: 0, // 进度条百分比
      isPaused: false, // 是否暂停
      timer: null,
      gradient: {
        0.0: "rgb(128, 0, 128)",
        0.2: "rgb(0, 0, 255)",
        0.4: "rgb(0, 255, 255)",
        0.6: "rgb(0, 255, 0)",
        0.8: "rgb(255, 255, 0)",
        1.0: "rgb(255, 0, 0)",
      },
      color1: "rgb(128, 0, 128)",
      color2: "rgb(0, 0, 255)",
      color3: "rgb(0, 255, 255)",
      color4: "rgb(0, 255, 0)",
      color5: "rgb(255, 255, 0)",
      color6: "rgb(255, 0, 0)",
      colorBoxFlag: false,
      colorMap: [
        '#00FFFF',
        '#00FF99',
        '#00FF00',
        '#FFFF00',
        '#FF6600',
        '#FF0000',
      ],
      allData: [
         {
          "code": "aa",
          "position": [
            [110.80095235882548,31.225921180021277,0],
            [110.80069542101707,31.226141833059565,0],
            [110.80021897472014,31.22620596773269,0],
            [110.79984859952845,31.22633468279151,0],
            [110.799628634985,31.226517517649985,0],
            [110.79933981051369,31.226657140118608,0],
            [110.79878100224987,31.226871767117657,0],
            [110.79879965765831,31.226968764487417,0],
            [110.79908145064964,31.227206245974468,0],
            [110.79920018380277,31.22740036776852,0],
            [110.79931862741525,31.227756125461454,0],
            [110.79931394258827,31.227949003717953,0],
            [110.79921284469997,31.228187988377076,0],
            [110.79869330579149,31.22883536664318,0],
            [110.79850888039701,31.229144850522207,0]
          ],
          "length": 996,
          "list": [
            {
              "interval": 30,
              "sd": [[0, 1],[0.2, 2],[0.4, 3],[0.6, 4],[0.8, 5],[1, 6]]
            },{
              "interval": 30,
              "sd": [[0, 6],[0.2, 5],[0.4, 4],[0.6, 3],[0.8, 2],[1, 1]]
            }
          ]
        }, 
        {
          "code": "bb",
          "position": [
              [110.79877213133014, 31.22687522536146, 0], 
              [110.79856074480482, 31.22634078133067, 0], 
              [110.79848739532139, 31.226179783743426, 0]
          ],
          "length": 300,
          "list": [
            {
              "interval": 30,
              "sd": [[0, 1],[0.2, 2],[ 0.4, 3],[0.6, 4],[0.8, 5],[1, 6]]
            },{
              "interval": 30,
              "sd": [[0, 6],[0.2, 5],[0.4, 4],[0.6, 3],[0.8, 2],[1, 1]]
            }
          ]
        }
      ],
      layer4: null,
      beginTime: null,
      endTme: null,
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL;
    this.initMap();
  },
  beforeDestroy() {
    if (this.map) {
      this.map.destroy();
    }
  },
  methods: {
    initMap() {
      this.map = new mars3d.Map("mars3dContainer", this.mapOptions);
      this.map.container.style.backgroundColor = "#546a53"; // 背景色
      this.addSupply();
      this.addIntake();
      this.getHeatData();
      // this.addGraphic();
 
    },
    getHeatData() {
      getPressureHeatData().then(res => {
        const { status, data } = res
        if (status == 200) {
          this.beginTime = data.beginTime;
          this.endTime = data.endTime;
          this.allData = data.pipes;
          console.log("pipes", this.allData)
          console.log("beginTime", this.beginTime)
          console.log("endTime", this.endTime)
          this.addGraphic();
        }
      })
    },
    addGraphic() {
      // 指定固定时间 ，方便写演示代码。
      this.layer4 = new mars3d.layer.GraphicLayer()
      this.layer4.zIndex = 100
      this.map.addLayer(this.layer4)
      // const startTime = Cesium.JulianDate.fromDate(new Date("2024-07-11 14:00:00"))
      for (let i = 0; i < this.allData.length; i++) {
        const pipe = this.allData[i];
        let sdList = pipe.list;
        // let stopTime
        // let tempTime = startTime.clone()
        const property = new Cesium.TimeIntervalCollectionProperty()
        for (let j = 0; j < sdList.length; j++) {
          const sd = sdList[j];
          const s = Cesium.JulianDate.fromDate(new Date(sd.start))
          const stopTime = Cesium.JulianDate.addSeconds(s, sd.interval, new Cesium.JulianDate())
          property.intervals.addInterval(
            new Cesium.TimeInterval({
                start: s, // 在 start至stop 这个时间段显示该图片
                stop: stopTime,
                isStartIncluded: true,
                isStopIncluded: false,
                data: this.getColorRamp(sd.sd ,pipe.length)
              })
          )
          // tempTime = stopTime
        }
        const graphic = new mars3d.graphic.PolylineEntity({
          positions: pipe.position,
          style: {
            width: 2,
            materialType: mars3d.MaterialType.Image2,
            materialOptions: {
              image: property
            }
          }
        })
        this.layer4.addGraphic(graphic)
      }

      // 时钟设置
      this.map.clock.startTime = Cesium.JulianDate.fromDate(new Date(this.beginTime))
      this.map.clock.stopTime = Cesium.JulianDate.fromDate(new Date(this.endTime))
      this.map.clock.currentTime = Cesium.JulianDate.fromDate(new Date(this.beginTime))
      this.map.clock.multiplier = 1800 // 当前速度，默认为1
      this.map.clock.clockRange = Cesium.ClockRange.LOOP_STOP // 到达终止时间后循环
      if (this.map.controls.timeline) {
        this.map.controls.timeline.zoomTo(Cesium.JulianDate.fromDate(new Date(this.beginTime)), Cesium.JulianDate.fromDate(new Date(this.endTime)))
        // this.map.controls.timeline.maxSpan(3600)
      }
     
    },
    getColorRamp(sd, distance) {
      let ramp = document.createElement('canvas');
      let ctx = ramp.getContext('2d');
      let grd = ctx.createLinearGradient(0, 0, distance, 0);
      for(let j = 0; j < sd.length; j++) {
        let c = sd[j];
        let colorCode = c[1]
        const rate = c[0]
        if (rate > 1) {
          rate = 1
        }
        grd.addColorStop(rate, this.colorMap[colorCode])
      }
      ctx.fillStyle = grd;
      ctx.fillRect(0, 0, distance, distance);
      return ramp;
      // var ramp = document.createElement('canvas');
      // var ctx = ramp.getContext('2d');
      // var grd = ctx.createLinearGradient(0, 0, distance, 0);
      // grd.addColorStop(0, c1);
      // grd.addColorStop(0.333, c2);
      // grd.addColorStop(0.666, c3);
      // grd.addColorStop(1, c4);
      // ctx.fillStyle = grd;
      // ctx.fillRect(0, 0, distance, distance);
      // return ramp;
    },
    
        // 供水管线
    addSupply() {
      const supplyLayer = new mars3d.layer.WmsLayer({
        url: this.geourl + "/geoserver/xingshan/wms",
        layers: "xingshan:supply_network",
        parameters: {
          service: 'WMS',
          version: '1.1.0',
          request: 'GetMap',
          srs: 'EPSG:4326',
          transparent: true,
          format: 'image/png'
          // format: 'application/openlayers'
        },
        hasToGraphic: true,
        featureToGraphic: (feature, event) => {
          const data = feature.data
          return {
            // type: "polyline",
            symbol: {
              type: "polylineP",
              merge: true,
              styleOptions: {
                label: {
                  font_size: 15,
                  text: "{name},{length}m",
                  color: "#ffffff",
                  outline: true,
                  outlineColor: "#ffffff",
                  distanceDisplayCondition: true,
                  distanceDisplayCondition_far: 50000,
                  distanceDisplayCondition_near: 0,
                  setHeight: 10000,
                },  
              },
            },
          }
        },
        popup: [
          { field: "name", name: "名称" },
          { field: "length", name: "长度" },
          { field: "code", name: "编码" },
          { field: "material", name: "材质" },
          { field: "diameter", name: "管径" },
          { field: "deep", name: "深度" },
        ],
        // popup: "all",
        zIndex: 20,
        flyTo: true,
      })
      this.map.addLayer(supplyLayer)
    },
    // 引水管线
    addIntake() {
      const intakeLayer = new mars3d.layer.WmsLayer({
        url: this.geourl + "/geoserver/xingshan/wms",
        layers: "xingshan:intake_network",
        parameters: {
          service: 'WMS',
          version: '1.1.0',
          request: 'GetMap',
          srs: 'EPSG:4326',
          transparent: true,
          format: 'image/png'
          // format: 'application/openlayers'
        },
        hasToGraphic: true,
        featureToGraphic: (feature, event) => {
          const data = feature.data
          return {
            // type: "polyline",
            symbol: {
              type: "polylineP",
              merge: true,
              styleOptions: {
                label: {
                  font_size: 15,
                  text: "{name},{length}m",
                  color: "#ffffff",
                  outline: true,
                  outlineColor: "#ffffff",
                  distanceDisplayCondition: true,
                  distanceDisplayCondition_far: 50000,
                  distanceDisplayCondition_near: 0,
                  setHeight: 10000,
                },  
              },
            },
          }
        },
        popup: [
          { field: "name", name: "名称" },
          { field: "length", name: "长度" },
          { field: "code", name: "编码" },
          { field: "material", name: "材质" },
          { field: "diameter", name: "管径" },
          { field: "deep", name: "深度" },
        ],
        // popup: "all",
        zIndex: 20,
        flyTo: true,
      })
      this.map.addLayer(intakeLayer)
    },
  },
};
</script>

<style lang="scss">
#mars3dContainer {
  .cesium-viewer-toolbar {
    // left: 470px;
    left: auto;
    right: 50px;
    top: auto;
    bottom: 50px;
  }
  .cesium-baseLayerPicker-dropDown-visible {
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    left: 48px;
  }
}
</style>
  
<style scoped lang="scss">
.pressureMap {
  height: 100%;
  position: relative;
}
.colorBarImage {
  width: 104px;
  height: 200px;
  position: absolute;
  top: 20px;
  left: 20px;
  background-image: url('~@/assets/img/leakageLoss/colorBar.png');
  z-index: 1;
}
.mars3d-container {
  height: 100%;
}
.time-progress {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.timeline-progress {
  margin-top: 10px;
}

.progress-container {
  width: 100%;
  height: 20px;
  background-color: #f5f5f5;
  position: relative;
  cursor: pointer;
}
.progress {
  display: flex;
}
.progress-time-item {
  color: #bfd310;
  background-color: rgba(255, 255, 255, 0);
  width: 40px;
}

.progress-bar {
  height: 100%;
  background-color: #4caf50;
  transition: width 0.2s ease-in-out;
}

.data-display {
  margin-top: 20px;
  padding: 10px;
  border: 1px solid #ccc;
}

/**infoview浮动面板*/
.infoview {
  position: absolute;
  bottom: 5px;
  width: 1000px;
  left: 300px;
  padding: 10px 15px;
  border-radius: 4px;
  border: 1px solid rgba(128, 128, 128, 0.5);
  color: #ffffff;
  background: rgba(0, 0, 0, 0.4);
  box-shadow: 0 3px 14px rgba(128, 128, 128, 0.5);
  z-index: 19870101;
}
.mars-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>