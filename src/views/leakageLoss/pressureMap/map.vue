<template>
  <div class="pressureArea">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="leakage" />
    <div id="map"></div>

    <div class="actionCard">
      <div class="title">压力调控</div>
      <div>
        <span>日期选择：</span>
        <el-date-picker
          v-model="currentDate"
          type="date"
          value-format="yyyy-MM-dd"
          size="mini"
          style="width: 150px;"
          placeholder="选择日期">
        </el-date-picker>
      </div>
      <div class="actionBox">
        <span>播放间隔：</span>
        <el-input-number size="mini" v-model="interval"></el-input-number>
        <i v-if="!isPlay" class="actionIcon el-icon-video-play" @click="handlePlay"></i>
        <i v-else class="actionIcon el-icon-video-pause" @click="handleStop"></i>
      </div>
    </div>

  </div>
</template>

<script>
import Map from '@arcgis/core/Map'
import MapView from '@arcgis/core/views/MapView'
import WebTileLayer from "@arcgis/core/layers/WebTileLayer"
import GeoJSONLayer from "@arcgis/core/layers/GeoJSONLayer"
import WMSLayer from "@arcgis/core/layers/WMSLayer"

import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { parseTime } from '@/utils'

import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'PressureMap',
  components: {
    FloatingSideMenu
  },
  data() {
    return {
      // 地图
      map: null,
      view: null,
      borderLayer: null,
      JL1TileLayer: null,
      pressureLayer: null,
      hotLayer: null,
      geoUrl: null,
      currentDate: null,
      // 定时器
      interval: 5,
      isPlay: false,
      timer: null
    }
  },
  created() {
    dayjs.extend(utc)
    this.geoUrl = process.env.VUE_APP_GEO_URL
    // this.currentDate = parseTime(new Date(), '{y}-{m}-{d}')
    this.currentDate = '2025-06-01'
  },
  mounted() {
    this.createMap()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    createMap() {
      // 天地图影像图-标注
      // this.tdtImageNoteLayer = new WebTileLayer({
      //   id: 'tdtImageNoteLayer',
      //   // urlTemplate: 'http://t{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
      //   urlTemplate: 'https://t{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
      //   subDomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      //   visible: true
      // })

      // 边界
      this.borderLayer = new GeoJSONLayer({
        id: 'borderLayer',
        name: 'borderLayer',
        url: '/border.json',
        renderer: {
          type: 'simple',
          symbol: {
            type: 'simple-fill',
            color: [255, 255, 255, 0],
            outline: {
              width: 2,
              color: '#00B3FF'
            }
          }
        }
      })

      // 吉林一号
      this.JL1TileLayer = new WebTileLayer({
        id: 'JL1TileLayer',
        urlTemplate: 'https://api.jl1mall.com/getMap/{z}/{x}/{y}?mk=73ad26c4aa6957eef051ecc5a15308b4&tk=24277043ba78c68ff6ed3b50ce486283&pro=ca3a3754837f49d7ac3068edce0e65f7&sch=wmts',
        // visible: false
      })
      
      this.map = new Map({
        basemap: {
          baseLayers: [
            this.JL1TileLayer,
            this.borderLayer
          ]
        }
      })
      this.view = new MapView({
        container: 'map',
        map: this.map,
        extent: {
          xmin: 110.42369972561853,
          ymin: 31.08945033264494,
          xmax: 111.11518062163354,
          ymax: 31.572430067740488
        },
        // 约束
        constraints: {
          geometry: {
            type: 'extent',
            xmin: 12179375.922550822,
            ymin: 3610680.8299349537,
            xmax: 12472894.11116601,
            ymax: 3745362.8737734854,
            spatialReference: {
              wkid: 3857
            }
          },
          minZoom: 11,
          maxZoom: 18
        }
      })

      this.view.on('click', (event) => {
        console.log(this.view)
      })

      this.view.when(() => {
        this.createPressureLayer()
      })
    },
    // 创建压力图层
    createPressureLayer() {
      console.log('createPressureLayer')
      if(this.pressureLayer) {
        this.view.map.remove(this.pressureLayer)
      }

      const time = `${this.currentDate}T00:00:00Z`

      this.pressureLayer = new WMSLayer({
        url: this.geoUrl + '/geoserver/xingshan/wms',
        sublayers: [
          { name: 'xingshan:pressure_data_unified' }
        ],
        customParameters: {
          time
        }
      })

      this.view.map.add(this.pressureLayer)
    },
    createLayer() {
      if(this.hotLayer) {
        this.map.remove(this.hotLayer)
      }

      const viewParams = `viewParams`
      this.hotLayer = new WMSLayer({
        url: this.geoUrl + '',
        id: 'hotLayer',
        sublayers: [
          { name: 'pressure_area_heat_map' }
        ],
        customParameters: {
          viewParams
        }
      })
      this.map.add(this.hotLayer)
    },
    handlePlay() {
      this.isPlay = true
      let currentTime = dayjs.utc(`${this.currentDate}T00:00:00Z`)
      this.timer = setInterval(() => {
        currentTime = currentTime.add(10, 'minute')
        this.pressureLayer.customParameters.time = currentTime.format('YYYY-MM-DDTHH:mm:ss[Z]')
        console.log('pressureLayer refresh', currentTime.format('YYYY-MM-DDTHH:mm:ss[Z]'))
        this.pressureLayer.refresh()
      }, this.interval * 1000)
    },
    handleStop() {
      console.log('handleStop')
      if (this.timer) {
        this.isPlay = false
        clearInterval(this.timer)
      }
    }
  },
}
</script>

<style lang="scss">
.pressureArea {
  .el-input-number__decrease, .el-input-number__increase {
    background: transparent;
    // color: #fff;
  }
  .el-input-number__decrease {
    border-right-color: #1382E6;
  }
  .el-input-number__increase {
    border-left-color: #1382E6;
  }
}
</style>

<style lang="scss" scoped>
.pressureArea {
  height: calc(100vh - 84px);
  position: relative;
  color: #fff;
  #map {
    height: 100%;
  }
  .actionCard {
    width: 300px;
    position: absolute;
    right: 30px;
    top: 30px;
    padding: 10px;
    background-color: rgba(4, 15, 45, 0.5);
    .title {
      padding: 10px 0;
      font-size: 20px;
      font-weight: 500;
    }
    .actionBox {
      margin-top: 10px;
      display: flex;
      align-items: center;
    }
    .actionIcon {
      font-size: 40px;
      margin-left: 20px;
      cursor: pointer;
    }
  }
}
</style>