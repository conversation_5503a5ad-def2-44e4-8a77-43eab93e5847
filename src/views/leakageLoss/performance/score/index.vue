<template>
  <div class="score">
    <div class="query">
      <span>评分名称：</span>
      <el-input v-model="queryFrom.name" placeholder="请输入评分名称" style="width: 200px;"></el-input>
      <span class="spanText">镇：</span>
      <el-select v-model="queryFrom.town" placeholder="请选择镇" style="width: 200px;">
        <el-option v-for="item in townList" :key="item.value" :value="item.value" :label="item.label"></el-option>
      </el-select>
      <span class="spanText">考评时间：</span>
      <el-date-picker
        v-model="timeRange"
        type="daterange"
        value-format="yyyy-MM-dd"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :clearable="false"
        style="width: 260px !important;"
      ></el-date-picker>
      <el-button type="primary" style="margin-left: 10px;" @click="getList()">查询</el-button>
      <el-button type="success" style="margin-left: 10px;" @click="handleAddScore">新增</el-button>
    </div>

    <div class="tableContainer">
      <el-table
        :data="tableData"
        height="100%"
        row-key="id"
      >
        <el-table-column label="评分名称" prop="name"></el-table-column>
        <el-table-column label="镇" prop="town.name"></el-table-column>
        <el-table-column label="考评时间" prop="yearMonth">
          <template slot-scope="{ row }">
            <span>{{ row.yearMonth.split(' ')[0] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="评分" prop="totalGetScore"></el-table-column>
        <el-table-column label="操作" width="150" align="center">
            <template slot-scope="{ row }">
              <span class="iconContainer editIcon" @click="handleEdit(row.id)"><i class="el-icon-edit"></i></span>
              <span class="iconContainer" @click="exportScoreExcel(row.id, row.name)"><i class="el-icon-download"></i></span>
              <span class="iconContainer deleteIcon" @click="handleDelete(row.id)"><i class="el-icon-delete"></i></span>
            </template>
          </el-table-column>
      </el-table>
    </div>

    <div class="paginationContainer">
      <el-pagination
        background
        @size-change="sizeChangeFn"
        @current-change="pageChangeFn"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,sizes,->, prev, pager, next,jumper"
        :total="total"
        :page-size.sync="pageInfo.pageSize"
        :current-page.sync="pageInfo.pageNo"
        :popper-append-to-body="false"
      />
    </div>

    <el-dialog
      title="新建"
      width="460px"
      :visible.sync="showDialog"
    >
      <el-form ref="ruleForm" :model="form" label-width="auto" :rules="rules">
        <el-form-item label="评分名称：" prop="name">
          <el-input v-model="form.name" placeholder="请输入评分名称"></el-input>
        </el-form-item>
        <el-form-item 
          label="考核标准："
          prop="tableName"
        >
          <el-select v-model="form.tableName" placeholder="请选择考核标准">
            <el-option v-for="item, index in standardList" :key="index" :value="item.tableName" :label="item.tableName"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="考核分类：">
          <el-input v-model="form.kind" placeholder="请输入考核分类"></el-input>
        </el-form-item> -->
        <el-form-item label="镇：" prop="town">
          <el-select v-model="form.town" placeholder="请选择镇">
            <el-option v-for="item in townList" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考评时间：" prop="dataTime">
          <el-date-picker
            v-model="form.dataTime"
            type="date"
            value-format="yyyy-MM-dd"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="请选择考评时间"
            :clearable="false"
          ></el-date-picker>
        </el-form-item>
        <div class="buttonBox">
          <el-button size="medium" @click="cancel">取消</el-button>
          <el-button type="primary" size="medium" @click="handleSubmit">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getTownList } from '@/api/common'
import { getStandardList, getScoreList, deleteScoreDetailById } from '@/api/leakageLoss/performance'

export default {
  name: 'Score',
  data() {
    return {
      tableData: [],
      queryFrom: {
        name: null,
        town: null,
        beginDate: null,
        endDate: null
      },
      townList: [],
      timeRange: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 10
      },
      total: 0,
      showDialog: false,
      form: {
        name: '',
        tableName: '',
        // kind: '',
        town: '',
        dataTime: ''
      },
      standardList: [],
      rules: {
        name: [{required: true, message: '请输入评分名称', trigger: 'blur'}],
        tableName: [{required: true, message: '请选择考核名称', trigger: 'blur'}],
        town: [{required: true, message: '请选择镇', trigger: 'change'}],
        dataTime: [{required: true, message: '请选择考评时间', trigger: 'change'}]
      }
    }
  },
  watch: {
    showDialog(newVal) {
      if(!newVal) {
        this.$refs.ruleForm.resetFields()
      }
    }
  },
  mounted() {
    this.getInitData()
    this.getList()
  },
  methods: {
    getInitData() {
      getTownList().then(res => {
        this.townList = res.data.childTown.map(item => {
          return {
            value: item.code,
            label: item.name
          }
        })
      })
    },
    getList() {
      const hasTimeRange = this.timeRange.length > 0
      if(hasTimeRange) {
        this.queryFrom.beginDate = this.timeRange[0]
        this.queryFrom.endDate = this.timeRange[1]
      }
      getScoreList({
        ...this.pageInfo,
        ...this.queryFrom
      }).then(res => {
        const { status, data, count } = res
        if(status === 200) {
          this.tableData = data
          this.total = count
        }
      })
    },
    // 打开新增弹窗
    handleAddScore() {
      this.showDialog = true
      // 获取考核标准
      getStandardList({}).then(res => {
        const { status, data } = res
        if(status === 200) {
          this.standardList = data
        }
      })
    },
    // 新增弹窗内部确定操作
    handleSubmit() {
      this.$refs.ruleForm.validate(valid => {
        if(valid) {
          localStorage.setItem('scoreBeforeForm', JSON.stringify(this.form))
          
          this.$router.push({
            name: 'AddScore',
            query: { standardName: this.form.tableName }
          })
          // judgeIfExist({
          //   departmentCode: this.form.departmentCode,
          //   taskType: this.form.taskType
          // }).then(res => {
          //   const { code, data } = res
          //   if(code === 200) {
          //     if(data.existFlag) {
          //       this.$router.push({
          //         name: 'ScoreAdd',
          //         query: { standardUuid: data.uuid }
          //       })
          //     } else {
          //       this.$message.error('当前选择的外包分类没有对应考核标准')
          //     }
          //   }
          // })
        }
      })      
    },
    cancel() {
      this.showDialog = false      
    },
    // 编辑操作
    handleEdit(id) {
      localStorage.removeItem('scoreBeforeForm')
      this.$router.push({
        name: 'EditScore',
        query: {
          id,
          state: 1
        }
      })
    },
    exportScoreExcel() {},
    // 删除操作
    handleDelete(id) {
      this.$confirm('此操作将永久删除该条记录，是否继续？', '提示', {
        type: 'error'
      }).then(() => {
        deleteScoreDetailById(id).then(res => {
          if(res.status === 200) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      })
    },
    sizeChangeFn() {
      this.pageInfo.pageNo = 1
      this.getData(true)
    },
    pageChangeFn() {
      this.getData(true)
    }
  }
}
</script>

<style lang="scss">
.score {
  .el-range-editor {
    .el-range-input {
      background-color: transparent;
      color: #ffffff;
    }
  }
}
</style>

<style lang="scss" scoped>
.score {
  height: 100%;
  .query {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    span {
      color: #ffffff;
    }
    .spanText {
      padding-left: 10px;
    }
  }
  .tableContainer {
    height: calc(100% - 120px);
    .iconContainer {
      display: inline-block;
      width: 24px;
      height: 24px;
      text-align: center;
      line-height: 22px;
      border: 1px solid #00A8FF;
      border-radius: 50%;
      color: #00A8FF;
      cursor: pointer;
    }
    .deleteIcon {
      color: #FF0000;
      border-color: #FF0000;
      margin-left: 10px;
    }
    .editIcon {
      border: 1px solid #FF7729;
      color:#FF7729;
      margin-right: 10px;
    }
  }
  .paginationContainer {
    height: 60px;
    padding: 12px 10px 0;
  }
  .buttonBox {
    text-align: center;
    padding-bottom: 20px;
  }
}
</style>