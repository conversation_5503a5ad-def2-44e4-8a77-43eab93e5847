<template>
  <div class="addScore">
    <div class="scoreName">
      <el-input v-model="form.name" size="medium" placeholder="请输入此评分名称" style="width: 409px;"></el-input>
    </div>

    <div class="selectBox">
      <span>考核分类：</span>
      <el-input v-model="form.table.kind" size="medium" placeholder="请输入考核分类" style="width: 200px;"></el-input>
      <span>镇：</span>
      <el-select v-model="form.town" size="medium" placeholder="请选择镇" style="width: 260px !important;">
        <el-option v-for="item in townList" :key="item.value" :value="item.value" :label="item.label"></el-option>
      </el-select>
      <span>考评时间：</span>
      <el-date-picker
        v-model="form.yearMonth"
        type="date"
        value-format="yyyy-MM-dd"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        size="medium"
        placeholder="请选择考评时间"
        :clearable="false"
      ></el-date-picker>
    </div>

    <div class="myTable scorllTable">
      <table style="width: 100%;">
        <thead>
          <tr>
            <th width="80">考核内容</th>
            <th>考核指标</th>
            <th>考核标准</th>
            <th width="50">分值</th>
            <th>考核依据</th>
            <th>评分说明</th>
            <th width="50">得分</th>
            <th>备注</th>
          </tr>
        </thead>
        <!-- 循环生成 -->
        <tbody>
          <template v-for="(content, contentIndex) in form.table.content">
            <template v-for="(indicator, indicatorIndex) in content.indicatorList">
              <template v-for="(standard, standardIndex) in indicator.standardAndScoreList">
                <tr v-if="indicatorIndex === 0 && standardIndex === 0" :key="contentIndex + '-' + indicatorIndex + '-' + standardIndex">
                  <td :rowspan="getRowCount(contentIndex)">
                    {{ content.contentName }}
                  </td>
                  <td
                    :rowspan="getIndicatorRowCount(contentIndex, indicatorIndex)"
                  >
                    {{ indicator.indicatorName }}
                  </td>
                  <td>{{ standard.name }}</td>
                  <td>{{ standard.score }}</td>
                  <td>{{ standard.assessmentBasic }}</td>
                  <td align="left">
                    <ul v-if="!isEdit">
                      <li
                        v-for="(detail, detailIndex) in standard.detail"
                        :key="detailIndex"
                      >
                        {{ detail }}
                      </li>
                    </ul>
                    <ul v-else>
                      <li
                        v-for="(scoreDetail, scoreDetailIndex) in standard.scoreDetail"
                        :key="scoreDetailIndex"
                      >
                        {{ scoreDetail.des }}
                      </li>
                    </ul>
                  </td>
                  <td>
                    <el-input v-model="standard.scoreGet" placeholder="" size="medium" style="width: 50px;"></el-input>
                  </td>
                </tr>
                <template v-else>
                  <tr :key="contentIndex + '-' + indicatorIndex + '-' + standardIndex">
                    <td
                      v-if="standardIndex === 0"
                      :rowspan="
                        getIndicatorRowCount(contentIndex, indicatorIndex)
                      "
                    >
                      {{ indicator.indicatorName }}
                    </td>
                    <td>{{ standard.name }}</td>
                    <td>{{ standard.score }}</td>
                    <td>{{ standard.assessmentBasic }}</td>
                    <td align="left">
                      <ul v-if="!isEdit">
                        <li
                          v-for="(detail, detailIndex) in standard.detail"
                          :key="detailIndex"
                        >
                          {{ detail }}
                        </li>
                      </ul>
                      <ul v-else>
                        <li
                          v-for="(scoreDetail, scoreDetailIndex) in standard.scoreDetail"
                          :key="scoreDetailIndex"
                        >
                          {{ scoreDetail.des }}
                        </li>
                      </ul>
                    </td>
                    <td>
                      <el-input v-model="standard.scoreGet" placeholder="" size="medium" style="width: 50px;"></el-input>
                    </td>
                  </tr>
                </template>
              </template>
            </template>
          </template>
        
        </tbody>
      </table>
    </div>

    <div class="formActionBox">
      <el-button size="medium" @click="handleBack">取消</el-button>
      <el-button type="primary" size="medium" @click="handleAdd">确定</el-button>
    </div>

  </div>
</template>

<script>
import { getTownList } from '@/api/common'
import { getStandardByName, addScore, updateScore, getScoreDetailById } from '@/api/leakageLoss/performance'

export default {
  name: 'AddScore',
  data() {
    return {
      townList: [],
      form: {
        name: '',
        town: '',
        yearMonth: '',
        table: {
          tableName: '',
          kind: '',
          content: []
        }
      }
    }
  },
  computed: {
    isEdit() {
      const query = this.$route.query
      return query && query.state == 1
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      const scoreBeforeForm = JSON.parse(localStorage.getItem('scoreBeforeForm'))
      if(scoreBeforeForm) {
        this.form.name = scoreBeforeForm.name
        this.form.kind = scoreBeforeForm.kind
        this.form.town = scoreBeforeForm.town
        this.form.yearMonth = scoreBeforeForm.dataTime
      }
      // 获取镇
      getTownList().then(res => {
        this.townList = res.data.childTown.map(item => {
          return {
            value: item.code,
            label: item.name
          }
        })
      })
      // 获取考核表详情
      const query = this.$route.query
      // 新增时获取考核标准表
      if(query && query.standardName) {
        getStandardByName(query.standardName).then(res => {
          const { status, data } = res
          if(status === 200) {

            this.form.table = data
            // 处理数据
            this.form.table.content = data.content.map((content, contentIndex) => {
              return {
                ...content,
                indicatorList: content.indicatorList.map((indicator, indicatorIndex) => {
                  return {
                    indicatorName: indicator.indicatorName,
                    standardAndScoreList: indicator.standardList.map(standard => {
                      delete standard.id
                      return {
                        ...standard,
                        scoreDetail: standard.detail.map(d => {
                          return {
                            des: d,
                            deductPoints: 0
                          }
                        }),
                        scoreGet: standard.score,
                        remark: 0
                      }
                    })
                  }
                })
              }
            })
          }
        })
      }
      // 编辑时获取考核评分记录
      if(this.isEdit) {
        console.log('isEdit')
        getScoreDetailById(query.id).then(res => {
          const { status, data } = res
          if(status === 200) {
            this.form = data
            this.form.town = data.town.code
          }
        })
      }
    },
    getRowCount(contentIndex) {
      let count = 0
      this.form.table.content[contentIndex].indicatorList.forEach((indicator) => {
        count += indicator.standardAndScoreList.length
      })
      return count
    },
    getIndicatorRowCount(contentIndex, indicatorIndex) {
      return this.form.table.content[contentIndex].indicatorList[indicatorIndex].standardAndScoreList.length
    },
    handleBack() {
      this.$router.push({
        name: 'Score'
      })
    },
    handleAdd() {
      if(this.form.name && this.form.yearMonth && this.form.town) {
        const query = this.$route.query
        
        // 编辑
        if(query && query.state == 1) {          
          updateScore(this.form).then(res => {
            if(res.status === 200) {
              this.$message.success('编辑评分成功')
              this.handleBack()
            }
          })
        }
        // 新建
        if(query && query.standardName) {
          // this.form.dataTime = this.form.dataTime + ' 00:00:00'
          addScore(this.form).then(res => {
            if(res.status === 200) {
              this.$message.success('新建评分成功')
              this.handleBack()
            }
          })
        }
      } else {
        this.$message.error('请填写上方选项')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.addScore {
  height: 100%;
  .scoreName {
    background-color: #215d9e;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .selectBox {
    height: 60px;
    background-color: #1b4d84;
    display: flex;
    align-items: center;
    padding-left: 10px;
    span {
      margin-left: 10px;
    }
  }
  .myTable {
    height: calc(100% - 180px);
    table {
      border-collapse: collapse;
      border: 1px solid #fff;
    }
    tr {
      height: 40px;
      text-align: center;
    }
    th {
      border: 1px solid #fff;
    }
    td {
      border: 1px solid #fff;
    }

    thead {
      tr {
        background-color: #215d9e;
        th {
          text-align: center;
        }
      }
    }
  }
  .scorllTable {
    overflow-y: auto;
    // height: 440px;
  }
  .formActionBox {
    width: 100%;
    height: 60px;
    text-align: center;
    line-height: 60px;
  }
}
</style>