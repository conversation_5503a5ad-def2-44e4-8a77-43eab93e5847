<template>
  <div class="standard">
    <div class="cardContainer">
      <!-- card -->
      <div class="commonCard standardCard" v-for="item in list" :key="item.uuid">
        <div class="title">{{ item.tableName }}</div>
        <div class="center">
          <span><i class="el-icon-document"></i>考核分类：{{ item.kind }}</span>
          <span><i class="el-icon-time"></i>制定时间：{{ item.createTime }}</span>
        </div>
        <div class="action">
          <div class="canClick" @click="showDetail(item.tableName)">查看详细</div>
          <div>
            <!-- <span class="canClick" style="margin-right: 10px;" @click="toEditStandard(item.tableName)">修改</span> -->
            <span class="canClick" @click="handleDelete(item.tableName)">删除</span>
          </div>
        </div>
      </div>
      <!-- addCard -->
      <div class="commonCard addCard">
        <div class="title">自定义规则</div>
        <div class="content">
          <img src="@/assets/img/leakageLoss/addIcon.png" alt="">
          <span style="font-size: 16px;">添加自定义考核标准</span>
          <el-button type="primary" size="small" @click="openUploadDialog">新建</el-button>
        </div>
      </div>
    </div>

    <!-- 考核标准详情 -->
    <el-dialog
      title="考核标准详情"
      :visible.sync="visibleDailog"
      width="1200px"
    >
      <table class="detailTable">
        <thead>
          <tr>
            <th width="80">考核内容</th>
            <th>考核指标</th>
            <th>考核标准</th>
            <th width="50">分值</th>
            <th>考核依据</th>
            <th>评分说明</th>
          </tr>
        </thead>
        <!-- 循环生成 -->
        <tbody>
          <!-- <template v-for="bigItem, i in detailObj.content">
            <template v-for="smallItem, j in bigItem.indicatorList">
              <template v-for="innerItem, k in smallItem.standardList">
                <tr :key="'-'+i+j+k">
                  <td>{{ bigItem.contentName }}</td>
                  <td>{{ smallItem.indicatorName }}</td>
                  <td>{{ innerItem.name }}</td>
                  <td>{{ innerItem.score }}</td>
                  <td>{{ innerItem.assessmentBasic }}</td>
                  <td>
                    <div v-for="item, index in innerItem.detail" :key="index">{{ item }}</div>
                  </td>
                </tr>
              </template>
            </template>
          </template> -->
          <template v-for="(content, contentIndex) in detailObj.content">
            <template v-for="(indicator, indicatorIndex) in content.indicatorList">
              <template v-for="(standard, standardIndex) in indicator.standardList">
                <tr v-if="indicatorIndex === 0 && standardIndex === 0" :key="contentIndex + '-' + indicatorIndex + '-' + standardIndex">
                  <td :rowspan="getRowCount(contentIndex)">
                    {{ content.contentName }}
                  </td>
                  <td
                    :rowspan="getIndicatorRowCount(contentIndex, indicatorIndex)"
                  >
                    {{ indicator.indicatorName }}
                  </td>
                  <td>{{ standard.name }}</td>
                  <td>{{ standard.score }}</td>
                  <td>{{ standard.assessmentBasic }}</td>
                  <td align="left">
                    <ul>
                      <li
                        v-for="(detail, detailIndex) in standard.detail"
                        :key="detailIndex"
                      >
                        {{ detail }}
                      </li>
                    </ul>
                  </td>
                </tr>
                <template v-else>
                  <tr :key="contentIndex + '-' + indicatorIndex + '-' + standardIndex">
                    <td
                      v-if="standardIndex === 0"
                      :rowspan="
                        getIndicatorRowCount(contentIndex, indicatorIndex)
                      "
                    >
                      {{ indicator.indicatorName }}
                    </td>
                    <td>{{ standard.name }}</td>
                    <td>{{ standard.score }}</td>
                    <td>{{ standard.assessmentBasic }}</td>
                    <td align="left">
                      <ul>
                        <li
                          v-for="(detail, detailIndex) in standard.detail"
                          :key="detailIndex"
                        >
                          {{ detail }}
                        </li>
                      </ul>
                    </td>
                  </tr>
                </template>
              </template>
            </template>
          </template>
        
        </tbody>
      </table>
    </el-dialog>

    <!-- 上传弹窗 -->
    <el-dialog
      title="上传考核标准"
      width="460px"
      :visible.sync="uploadVisible"
    >
      <el-form ref="uploadForm" :model="uploadForm" label-width="80px" :rules="rules">
        <el-form-item label="名称：" prop="name">
          <el-input v-model="uploadForm.name" placeholder="请输入考核标准名称"></el-input>
        </el-form-item>
        <el-form-item label="类型：">
          <el-input v-model="uploadForm.kind" placeholder="请输入考核标准类型"></el-input>
        </el-form-item>
        <el-form-item label="文件：">
          <el-button type="success" @click="chooseFile">选择文件</el-button>
          <div v-if="file">{{ file.name }}</div>
          <input type="file" accept=".xls, .xlsx" class="myInput" @change="fileChange" ref="fileRef" />
        </el-form-item>
        <div class="buttonBox">
          <el-button size="medium" @click="cancel">取消</el-button>
          <el-button type="primary" size="medium" @click="handleSubmit">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getStandardList, deleteStandardByName, getStandardByName, uploadStandard } from '@/api/leakageLoss/performance'

export default {
  name: 'Standard',
  data() {
    return {
      list: [],
      visibleDailog: false,
      detailObj: {},
      uploadVisible: false,
      file: null,
      uploadForm: {
        name: '',
        kind: ''
      },
      rules: {
        name: [{required: true, message: '请输入考核标准表名称', trigger: 'blur'}]
      }
    }
  },
  watch: {
    uploadVisible(newVal) {
      if(!newVal) {
        this.$refs.uploadForm.resetFields()
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getStandardList({}).then(res => {
        const { status, data } = res
        if(status === 200) {
          this.list = data
        }
      })
    },
    getRowCount(contentIndex) {
      let count = 0
      this.detailObj.content[contentIndex].indicatorList.forEach((indicator) => {
        count += indicator.standardList.length
      })
      return count
    },
    getIndicatorRowCount(contentIndex, indicatorIndex) {
      return this.detailObj.content[contentIndex].indicatorList[indicatorIndex].standardList.length
    },
    // 查看详情
    showDetail(tableName) {
      getStandardByName(tableName).then(res => {
        let { status, data } = res
        if(status === 200) {
          this.detailObj = data
        }
      })
      this.visibleDailog = true
    },
    // 删除
    handleDelete(tableName) {
      this.$confirm('此操作将永久删除该条记录，是否继续？', '提示', {
        type: 'error'
      }).then(() => {
        deleteStandardByName(tableName).then(res => {
          const { status, data } = res
          if(status === 200) {
            this.$message.success('删除成功')
            this.getData()
          }
        })
      }).catch(() => {})
    },
    openUploadDialog() {
      this.uploadVisible = true
    },
    chooseFile() {
      this.$refs.fileRef.click()
    },
    fileChange(e) {
      console.log('文件变化')
      this.file = e.target.files[0]
      e.target.value = ''
    },
    cancel() {
      this.uploadVisible = false
    },
    handleSubmit() {
      if(!this.file) {
        this.$message.warning('请选择文件')
        return
      }
      this.$refs.uploadForm.validate((valid) => {
        if (valid) {
          const fd = new FormData()
          fd.append('file', this.file)
          console.log(111)
          uploadStandard(this.uploadForm, fd).then(res => {
            console.log(res)
            if(res.status === 200) {
              this.$message.success('上传成功')
              this.cancel()
              this.getData()
            } else {
              this.$message.error('上传失败，请重试')
            }
          })
          
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    toEditStandard() {},
    // toAddStandard() {
    //   this.$router.push({ name: 'AddStandard' })
    // },
  },
}
</script>

<style lang="scss" scoped>
.standard {
  height: 100%;
  .canClick {
    cursor: pointer;
  }
  .cardContainer {
    display: flex;
    flex-wrap: wrap;
  }
  .commonCard {
    width: 352px;
    height: 236px;
    background-color: #042143;
    border: 1px solid #2FB3F7;
    border-radius: 8px;
    .title {
      height: 40px;
      color: #00FFFC;
      font-size: 18px;
      font-weight: bold;
      padding: 10px 0 0 16px;
    }
  }
  .standardCard {
    margin-right: 20px;
    margin-bottom: 20px;
    .center {
      height: 148px;
      font-size: 14px;
      display: flex;
      flex-direction: column;
      padding: 30px 20px;
      justify-content: space-between;
      i {
        margin-right: 10px;
      }
    }
    .action {
      height: 46px;
      padding: 0 20px;
      border-top: 1px solid #2FB3F7;
      border-radius: 0px 0px 8px 8px;
      background-color: #11355D;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .addCard {
    .content {
      height: 196px;
      display: flex;
      padding: 10px 0 24px;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
    }
  }
  .detailTable {
    width: 100%;
    color: #fff;
    border-collapse: collapse;
    border: 1px solid #fff;
    tr {
      height: 40px;
      text-align: center;
      th {
        border: 1px solid #fff;
      }
      td {
        border: 1px solid #fff;
      }
    }
  }
  .buttonBox {
    text-align: center;
    padding-bottom: 20px;
  }
  .myInput {
    display: none;
  }
}
</style>