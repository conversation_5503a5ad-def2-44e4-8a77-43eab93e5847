<template>
  <div class="AddStandard">
    <div class="nameBox">
      <el-input v-model="form.tableName" size="medium" placeholder="请输入此标准名称" style="width: 409px;"></el-input>
      <el-input v-model="form.kind" size="medium" placeholder="请输入此考核分类" style="width: 409px;"></el-input>
    </div>
    
    <table>
      <thead></thead>
    </table>
  </div>
</template>

<script>
export default {
  name: 'AddStandard',
  data() {
    return {
      form: {
        tableName: '',
        kind: '',
        content: []
      },
      
    }
  }
}
</script>

<style lang="scss" scoped>
.AddStandard {
  height: 100%;
}
</style>