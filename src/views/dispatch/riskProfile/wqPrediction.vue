<template>
  <div class="waterAmount">
    <div class="query">
      <span class="param">
        <span>站点：</span>
        <el-cascader
          style="width: 300px;"
          v-model="stationName"
          :options="stationList"
          :props="propObject"
          collapse-tags
          size="medium"
          clearable>
        </el-cascader>
      </span>
      <span class="param">
        <span>时间：</span>
        <span style="width: 200px; display: inline-box;">
          <el-date-picker   
            :clearable="false"             
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            size="medium"
            >
          </el-date-picker>
        </span>
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="handleQuery">查询</el-button>
    </div>

    <div class="chart-container">
      <WqLine :yAxisName="yAxixName" :seriesData="wqData" />
      <div class="paramSelect">
        <div
          class="item"
          :class="{ active: currentParam === item.value }"
          v-for="item in paramList" :key="item.id"
          @click="toggleParam(item)"
        >
          {{ item.name }}
        </div>
      </div>
      
    </div>

    <!-- <div class="table-container"> -->
      <el-table
        height="calc(100% - 386px)"
        :data="tableData"
        size="medium"
      >
        <el-table-column prop="dataTime" label="预测时间"></el-table-column>
        <el-table-column prop="facilityName" label="站点"></el-table-column>
        <el-table-column prop="sourceTypeName" label="类型"></el-table-column>
        <el-table-column prop="ph" label="PH值"></el-table-column>
        <el-table-column prop="turbidity" label="浊度(NTU)"></el-table-column>
        <el-table-column prop="temp" label="水温(℃)"></el-table-column>
        <el-table-column prop="powerRate" label="电导率(μs/cm)"></el-table-column>
        <el-table-column prop="chlorine" label="余氯(mg/L)"></el-table-column>
      </el-table>
    <!-- </div> -->

    <div class="pagination-container">
      <el-pagination        
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import WqLine from './components/wqLine'

import { parseTime } from '@/utils'

import { getStationList } from '@/api/common'
import { getWqPredictionPageList, getWqPredictionPic } from '@/api/dispatch/prediction'

export default {
  components: { WqLine },
  data() {
    return {
      stationName: [],
      stationList: [],
      propObject: {
        // checkStrictly: true,
        // multiple: true,
        children: 'list',
        label: 'name',
        value: 'id'
      },
      time: [],
      tableData: [],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      },
      waterLevelData: [],
      // 水质参数列表
      paramList: [
        {
          id: 1,
          name: 'PH值',
          value: 'ph',
          yAxixName: 'ph',
          preKey: 'prePh',
          actualKey: 'actualPh'
        },
        {
          id: 2,
          name: '浊度',
          value: 'turbidity',
          yAxixName: '浊度(NTU)',
          preKey: 'preTurbidity',
          actualKey: 'actualTurbidity'
        },
        {
          id: 3,
          name: '水温',
          value: 'temp',
          yAxixName: '水温(℃)',
          preKey: 'preTemp',
          actualKey: 'actualTemp'
        },
        {
          id: 4,
          name: '电导率',
          value: 'powerRate',
          yAxixName: '电导率(μs/cm)',
          preKey: 'prePowerRate',
          actualKey: 'actualPowerRate'
        },
        {
          id: 5,
          name: '余氯',
          value: 'chlorine',
          yAxixName: '余氯(mg/L)',
          preKey: 'preChlorine',
          actualKey: 'actualChlorine'
        }
      ],
      currentParam: 'ph',
      wqData: [],
      wqDataTemp: {},
      yAxixName: 'ph'
    }
  },
  created() {
    this.init()
  },
  mounted() {
    // this.getPageList()
  },
  methods: {
    init() {
      // this.time = getWeekTs()
      // 设置初始时间
      const now = new Date().getTime()
      const other = now + 60 * 60 * 24 * 7 * 1000
      this.time = [parseTime(now, '{y}-{m}-{d}'), parseTime(other, '{y}-{m}-{d}')]
      getStationList('WATER_QUALITY').then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName: item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
          // 设置默认值
          this.stationName = [this.stationList[0].id, this.stationList[0].list[0].id]
          this.handleQuery()
        }
      })
    },
    handleQuery() {
      this.getChartData()
      this.getPageList()
    },
    // 切换展示参数
    toggleParam(item) {
      this.currentParam = item.value
      this.wqData = [
        {
          name: '预测值',
          type: 'line',
          data: this.wqDataTemp[item.preKey]
        },
        {
          name: '实际值',
          type: 'line',
          data: this.wqDataTemp[item.actualKey]
        }
      ]
      this.yAxixName = item.yAxixName
    },
    getChartData() {
      let data = {
        sourceType: this.stationName[0] ? this.stationName[0] : null,
        facilityId: this.stationName[1] ? this.stationName[1] : null,
        beginTime: this.time[0] + ' 00:00:00',
        endTime: this.time[1] + ' 23:59:59',
      }
      getWqPredictionPic(data).then(res => {
        if(res.status === 200) {
          this.wqDataTemp = res.data
          this.wqData = [
            {
              name: '预测值',
              type: 'line',
              data: this.wqDataTemp.prePh
            },
            {
              name: '实际值',
              type: 'line',
              data: this.wqDataTemp.actualPh
            }
          ]
        }
      })
    },
    getPageList() {
      let data = {
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        beginTime: this.time[0] + ' 00:00:00',
        endTime: this.time[1] + ' 23:59:59',
        sourceType: this.stationName[0] ? this.stationName[0] : null,
        facilityId: this.stationName[1] ? this.stationName[1] : null
      }
      getWqPredictionPageList(data).then(res => {
        const { status, data, count } = res
        if(status === 200) {
          this.tableData = data
          this.pageInfo.total= count
        }
      })
    },
    
    handleSizeChange(size) {
      this.pageInfo.pageNum = 1
      this.getPageList()
    },
    handleCurrentChange() {
      this.getPageList()
    }
  }
}
</script>

<style lang="scss" scoped>
.waterAmount {
  height: 100%;
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .chart-container {
    height: 300px;
    position: relative;
    .paramSelect {
      position: absolute;
      right: 50px;
      top: 0;
      display: flex;
      .item {
        padding: 5px;
        border: 1px solid #2A5DD2;
        border-left: none;
        cursor: pointer;
        font-size: 14px;
        &:first-child {
          border-left: 1px solid #2A5DD2;
          border-radius: 5px 0 0 5px;
        }
        &:last-child {
          border-radius: 0 5px 5px 0;
        }
      }
      .active {
        background-color: #2A5DD2;
      }
    }
  }
}
</style>
