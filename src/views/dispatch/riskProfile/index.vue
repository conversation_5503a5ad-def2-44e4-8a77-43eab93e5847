<template>
  <div class="RiskProfile app-container">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="dispatch" />
    <div class="tab">
      <div class="first" :class="{ active: currentType === 'waterAmount' }" @click="handleTabClick('waterAmount')">水位预测</div>
      <div class="other" :class="{ active: currentType === 'wqPrediction' }" @click="handleTabClick('wqPrediction')">水质预测 </div>
    </div>
    <div class="compenent-container">
      <WaterAmount v-if="currentType === 'waterAmount'" />
      <WqPrediction v-else />
    </div>
  </div>
</template>

<script>
import { getFlowPredictionPageList } from '@/api/dispatch/prediction'

import WaterAmount from './waterAmount'
import WqPrediction from './wqPrediction'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  // 风险预测
  name: 'RiskProfile',
  components: {
    WaterAmount,
    WqPrediction,
    FloatingSideMenu
  },
  data() {
    return {
      currentType: 'waterAmount'
    }
  },
  methods: {
    handleTabClick(key) {
      this.currentType = key
    }
  },
  mounted() {
    // getFlowPredictionPageList({
    //   pageNo: 1,
    //   pageSize: 10
    // })
  }
}
</script>

<style lang="scss">
.RiskProfile { 
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.RiskProfile {
  padding: 0 30px;
  height: calc(100vh - 84px);
  .compenent-container {
    color: #ffffff;
    padding: 20px;
    height: calc(100% - 76px);
    border: 1px solid #2A5DD2;
    border-top-color: #3084B5;
  }
}
</style>
