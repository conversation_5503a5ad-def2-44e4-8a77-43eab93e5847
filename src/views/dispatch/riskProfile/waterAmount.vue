<template>
  <div class="waterAmount">
    <div class="query">
      <span class="param">
        <span>站点：</span>
        <el-cascader
          style="width: 300px;"
          v-model="stationName"
          :options="stationList"
          :props="propObject"
          collapse-tags
          size="medium"
          clearable>
        </el-cascader>
      </span>
      <span class="param">
        <span>时间：</span>
        <span style="width: 200px; display: inline-box;">
          <el-date-picker   
            :clearable="false"             
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            size="medium"
            >
          </el-date-picker>
        </span>
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="handleQuery">查询</el-button>
    </div>

    <div class="chart-container">
      <LineChart :seriesData="waterAmountData" />
    </div>

    <!-- <div class="table-container"> -->
      <el-table
        height="calc(100% - 386px)"
        :data="tableData"
        size="medium"
      >
        <el-table-column prop="dataTime" label="预测时间"></el-table-column>
        <el-table-column prop="facilityName" label="站点"></el-table-column>
        <el-table-column prop="sourceTypeName" label="类型"></el-table-column>
        <el-table-column prop="predictiveValue" label="预测值"></el-table-column>
      </el-table>
    <!-- </div> -->

    <div class="pagination-container">
      <el-pagination        
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import LineChart from './components/lineChart'
import { getWeekTs } from '@/utils/time'
import { parseTime } from '@/utils'

import { getStationList } from '@/api/common'
import { getFlowPredictionPageList, getFlowPredictionPic } from '@/api/dispatch/prediction'

export default {
  components: { LineChart },
  data() {
    return {
      stationName: [],
      stationList: [],
      propObject: {
        // checkStrictly: true,
        // multiple: true,
        children: 'list',
        label: 'name',
        value: 'id'
      },
      time: [],
      tableData: [],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      },
      waterAmountData: []
    }
  },
  created() {
    this.init()
  },
  mounted() {
    // this.getPageList()
  },
  methods: {
    init() {
      // this.time = getWeekTs()
      // 设置初始时间
      const now = new Date().getTime()
      const other = now + 60 * 60 * 24 * 7 * 1000
      this.time = [parseTime(now, '{y}-{m}-{d}'), parseTime(other, '{y}-{m}-{d}')]
      getStationList('WATER_LEVEL').then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName: item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
          // 设置默认值
          this.stationName = [this.stationList[0].id, this.stationList[0].list[0].id]
          this.handleQuery()
        }
      })
    },
    handleQuery() {
      this.getChartData()
      this.getPageList()
    },
    getChartData() {
      let data = {
        sourceType: this.stationName[0] ? this.stationName[0] : null,
        facilityId: this.stationName[1] ? this.stationName[1] : null,
        beginTime: this.time[0] + ' 00:00:00',
        endTime: this.time[1] + ' 23:59:59'
      }
      getFlowPredictionPic(data).then(res => {
        if(res.status === 200) {
          let temp = [
            {
              name: '预测值',
              type: 'line',
              data: res.data.preList
            },
            {
              name: '实际值',
              type: 'line',
              data: res.data.actualList
            }
          ]
          this.waterAmountData = temp
        }
      })
    },
    getPageList() {
      let data = {
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        beginTime: this.time[0] + ' 00:00:00',
        endTime: this.time[1] + ' 23:59:59',
        sourceType: this.stationName[0] ? this.stationName[0] : null,
        facilityId: this.stationName[1] ? this.stationName[1] : null
      }
      getFlowPredictionPageList(data).then(res => {
        const { status, data, count } = res
        if(status === 200) {
          this.tableData = data
          this.pageInfo.total= count
        }
      })
    },
    handleSizeChange(size) {
      this.pageInfo.pageNum = 1
      this.getPageList()
    },
    handleCurrentChange() {
      this.getPageList()
    }
  }
}
</script>

<style lang="scss" scoped>
.waterAmount {
  height: 100%;
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .chart-container {
    height: 300px;
  }
}
</style>
