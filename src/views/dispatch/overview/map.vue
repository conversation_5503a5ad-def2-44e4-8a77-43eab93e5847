<template>
  <div class="overviewMap">
    <div id="map"></div>

  </div>
</template>

<script>
import Map from "@arcgis/core/Map"
import MapView from "@arcgis/core/views/MapView"
import WMSLayer from "@arcgis/core/layers/WMSLayer"
import GeoJSONLayer from "@arcgis/core/layers/GeoJSONLayer"
import FeatureLayer from "@arcgis/core/layers/FeatureLayer"
import WebTileLayer from "@arcgis/core/layers/WebTileLayer"
import WFSLayer from '@arcgis/core/layers/WFSLayer'
import WMTSLayer from '@arcgis/core/layers/WMTSLayer'
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer'
import Graphic from "@arcgis/core/Graphic"
import Polygon from "@arcgis/core/geometry/Polygon"
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine"
import GroupLayer from '@arcgis/core/layers/GroupLayer'
import Popup from '@arcgis/core/widgets/Popup'
import Point from '@arcgis/core/geometry/Point'

import axios from 'axios'

export default {
  props: {
    mapMode: {
      type: Boolean
    }
  },
  data() {
    return {
      isImg: false,
      view: null,
      map: null,
      geoUrl: null,
    }
  },
  mounted() {
    this.geoUrl = process.env.VUE_APP_GEO_URL
    this.createMap()
  },
  methods: {
    createMap() {
      // 边界
      // this.borderLayer = new GeoJSONLayer({
      //   id: 'borderLayer',
      //   name: 'borderLayer',
      //   url: '/border.json',
      //   renderer: {
      //     type: 'simple',
      //     symbol: {
      //       type: 'simple-fill',
      //       color: [255, 255, 255, 0],
      //       outline: {
      //         width: 2,
      //         color: '#00B3FF'
      //       }
      //     }
      //   }
      // })
      // 天地图影像图-标注
      this.tdtImageNoteLayer = new WebTileLayer({
        id: 'tdtImageNoteLayer',
        name: 'tdtImageNoteLayer',
        urlTemplate: 'http://{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
        subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
        visible: true
      })

      this.map = new Map({
        basemap: {
          baseLayers: [
            this.tdtImageNoteLayer,
          ]
        }
      })
      this.view = new MapView({
        container: 'map',
        map: this.map,
        extent: {
          xmin: 110.42369972561853,
          ymin: 31.08945033264494,
          xmax: 111.11518062163354,
          ymax: 31.572430067740488
        },
        constraints: {
          minZoom: 11,
          maxZoom: 18
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.overviewMap {
  height: 100%;
  position: relative;
  #map {
    height: 100%;
  }
}
</style>