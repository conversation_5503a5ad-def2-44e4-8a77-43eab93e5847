<template>
  <div class="wqTopic">
    <div class="chartBox">
      <div>
        <SmallCircle :rateData="rateInfo.chlorinePassRate" width="100px" height="100px" />
        <div class="label">余氯合格率</div>
      </div>
      <div>
        <SmallCircle :rateData="rateInfo.turbidity" color="#51d351" width="100px" height="100px" />
        <div class="label">浊度合格率</div>
      </div>
      <div>
        <SmallCircle :rateData="rateInfo.phPassRate" color="#fec03d" width="100px" height="100px" />
        <div class="label">ph合格率</div>
      </div>
    </div>

    <div>
      <div class="dateBox">
        <div class="iconTitle"><span>水质检验日报</span></div>
        <el-date-picker
          v-model="dateValue"
          @change="dateChange"
          type="date"
          value-format="yyyy-MM-dd"
          :clearable="false"
          :picker-options="pickerOptions"
          size="mini"
          style="width: 150px;margin-left: auto;"
          placeholder="选择日期">
        </el-date-picker>
      </div>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="谭湾水厂" name="谭湾水厂"></el-tab-pane>
        <el-tab-pane label="双龙观水厂" name="双龙观水厂"></el-tab-pane>
        <el-tab-pane label="青华水厂" name="青华水厂"></el-tab-pane>
        <el-tab-pane label="大礼水厂" name="大礼水厂"></el-tab-pane>
        <el-tab-pane label="吴家坪水厂" name="吴家坪水厂"></el-tab-pane>
      </el-tabs>

      <div class="myList">
        <div class="myListHeader">
          <span class="myListHeaderItem">检测项目</span>
          <span class="myListHeaderItem">水源水</span>
          <span class="myListHeaderItem">出厂水</span>
          <span class="myListHeaderItem">检测时间</span>
        </div>
        <div class="myListRow" v-for="item, index in tableData" :key="index">
          <span>{{ item.param }}</span>
          <span>{{ item.value1 }}</span>
          <span>
            <span v-if="item.param === '浊度'">
              <span>{{ item.value2 }}</span>
              <i :class="setClass(item.value1, item.value2)" style="font-size: 14px;" v-if="hasArrow(item.value1, item.value2)"></i>
            </span>
            <span v-else>{{ item.value2 }}</span>
          </span>

          <span>{{ item.time }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SmallCircle from './charts/smallCircle'
import { getWqRealtimeDataPage } from '@/api/wq/monitor'
import { getWqTopicRate } from '@/api/dispatch/topic'
import { parseTime } from '@/utils'

export default {
  name: 'WqTopic',
  components: {
    SmallCircle
  },
  data() {
    return {
      dateValue: '',
      rateInfo: {
        turbidityPassRate: 0,
        chlorinePassRate: 0,
        phPassRate: 0
      },
      activeName: '谭湾水厂',
      tableData: [
        { param: 'pH', value1: '-', value2: '-', time: '-' },
        { param: '温度', value1: '-', value2: '-', time: '-' },
        { param: '余氯', value1: '-', value2: '-', time: '-' },
        { param: '浊度', value1: '-', value2: '-', time: '-' },
        { param: '电导率', value1: '-', value2: '-', time: '-' },
        { param: '肉眼可见物', value1: '-', value2: '-', time: '-' },
        { param: '菌落总数', value1: '-', value2: '-', time: '-' },
        { param: '总大肠菌数', value1: '-', value2: '-', time: '-' }
      ],
      listTemp: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    }
  },
  watch: {
    activeName(value) {
      console.log(value)
      this.filterDataByName()
    }
  },
  mounted() {
    this.dateValue = parseTime(new Date(), '{y}-{m}-{d}')
    this.getData()
  },
  methods: {
    handleClick(value) {},
    dateChange() {
      this.getData()
    },
    getData() {
      // 水质数据
      getWqRealtimeDataPage({
        pageNo: 1,
        pageSize: 999,
        dateTimeStart: this.dateValue + ' 00:00:00',
        dateTimeEnd: this.dateValue+ ' 23:59:59'
      }).then(res => {
        // 过滤出 WATER_FACTORY WATER_SOURCE
        this.listTemp = res.data.filter(item => item.sourceType !== 'END_USER')
        this.filterDataByName()
      })

      // 合格率
      getWqTopicRate().then(res => {
        if(res.status === 200) {
          this.rateInfo = res.data
        }
      })
    },
    filterDataByName() {
      const emptyData = [
        { param: 'pH', value1: '-', value2: '-', time: '-' },
        { param: '温度', value1: '-', value2: '-', time: '-' },
        { param: '余氯', value1: '-', value2: '-', time: '-' },
        { param: '浊度', value1: '-', value2: '-', time: '-' },
        { param: '电导率', value1: '-', value2: '-', time: '-' },
        { param: '肉眼可见物', value1: '-', value2: '-', time: '-' },
        { param: '菌落总数', value1: '-', value2: '-', time: '-' },
        { param: '总大肠菌数', value1: '-', value2: '-', time: '-' }
      ]
      this.tableData = emptyData

      let result = this.listTemp.filter(item => item.name === this.activeName)
      console.log(result)
      if(result.length) {
        let one = result[0]
        let other = result[1]
        if(one.sourceType === 'WATER_FACTORY') {
          one = result[1]
          other = result[0]
        }
        let time = parseTime(one.dataTime, '{h}:{i}:{s}')
        // if(one.sourceType === 'WATER_SOURCE') {
          // 水源水
          this.tableData[0].value1 = one.ph || '-'
          this.tableData[1].value1 = one.temp || '-'
          this.tableData[2].value1 = one.chlorine || '-'
          this.tableData[3].value1 = one.turbidity || '-'
          this.tableData[4].value1 = one.powerRate || '-'
          // 水厂水
          this.tableData[0].value2 = other.ph || '-'
          this.tableData[1].value1 = other.temp || '-'
          this.tableData[2].value2 = other.chlorine || '-'
          this.tableData[3].value2 = other.turbidity || '-'
          this.tableData[4].value2 = other.powerRate || '-'

          this.tableData[0].time = time
          this.tableData[1].time = time
          this.tableData[2].time = time
          this.tableData[3].time = time
          this.tableData[4].time = time
        // }
      }
    },
    hasArrow(value1, value2) {
      if(value1 === '-' || value2 === '-') return false
      return true
    },
    setClass(value1, value2) {
      if(value1 === '-' || value2 === '-') return []
      if(value1 < value2) {
        return ['el-icon-top', 'red']
      } else {
        return ['el-icon-bottom', 'green']
      }
    }
  }
}
</script>

<style lang="scss">
.wqTopic {
  .el-tabs {
    .el-tabs__item {
      color: #fff;
      &.is-active {
        color: #409EFF;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.wqTopic {
  .chartBox {
    display: flex;
    justify-content: space-around;
    .label {
      font-size: 14px;
      text-align: center;
    }
  }
  .dateBox {
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
  }
  .green {
    color: #00ff00;
  }
  .red {
    color: #ff0000;
  }
}
</style>
