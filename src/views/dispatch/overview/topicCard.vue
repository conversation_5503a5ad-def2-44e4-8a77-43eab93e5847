<template>
  <div class="topicCard">
    <div class="cardTitle">{{ topic | topicFilter }}</div>
    <div class="cardContainer">
      
      <SwTopic v-if="topic === 'sw'" />
      <WfTopic v-if="topic === 'wf'" />
      <PsTopic v-if="topic === 'ps'"/>
      <WqTopic v-if="topic === 'wq'" />
    </div>
  </div>
</template>

<script>
import SwTopic from './swTopic'
import WfTopic from './wfTopic'
import PsTopic from './psTopic'
import WqTopic from './wqTopic'

export default {
  name: 'TopicCard',
  props: {
    topic: {
      type: String,
      default: ''
    }
  },
  components: {
    SwTopic,
    WfTopic,
    PsTopic,
    WqTopic
  },
  filters: {
    topicFilter(value) {
      const labelMap = {
        'sw': '原水主题',
        'wf': '水厂主题',
        'ps': '蓄水池主题',
        'wq': '水质主题'
      }
      return labelMap[value] || ''
    }
  }
}
</script>


<style lang="scss">
.topicCard {
  .cardTitle {
    background: url("~@/assets/img/title.png") center no-repeat;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
  }
  .iconTitle::before {
    display: inline-block;
    content: '';
    width: 16px;
    height: 16px;
    margin-right: 5px;
    background-image: url('../../../assets/img/icon.png');
  }
  .myList {
    font-size: 14px;
    text-align: center;
    .myListHeader {
      height: 34px;
      font-weight: bold;
      background-image: url("~@/assets/img/table.png");
      background-size: 100%;
      display: flex;
      align-items: center;
      span {
        flex: 1;
      }
    }
    .myListRow {
      font-size: 12px;
      display: flex;
      align-items: center;
      height: 30px;
      background-color: rgba(0, 170, 255, 0.08);
      display: flex;
      align-items: center;
      margin-top: 5px;
      span {
        flex: 1;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.topicCard {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 430px;
  z-index: 1001;
  background-color: rgba(4, 15, 45, 0.3);
  border: 1px solid rgba(23, 110, 217, 0.3);
  backdrop-filter: blur(16px);
  .cardContainer {
    padding: 10px;
  }
}
</style>