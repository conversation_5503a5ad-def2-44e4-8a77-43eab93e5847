<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    rankData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    rankData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        grid: {
          top: 30,
          left: 0,
          right: 50,
          bottom: 18,
          containLabel: true
        },
        xAxis: {
          type: 'value',
          min: 0,
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#DEEBFF'
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(222, 235, 255, 0.5)',
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'category',
          // boundaryGap: false,
          inverse: true,
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#FFFFFF',
            fontSize: 12,
            // formatter: function(value, index) {
            //   return '{no|No.'+ ((index + 1) % 6) + '} ' + value
            // },
            // rich: {
            //   no: {
            //     fontSize: 10,
            //     fontWeight: 'bold'
            //   }
            // }
          },
          axisTick: {
            show: false
          },
          data: this.xAxisData
        },
        series: [
          {
            name: '水厂制水量统计',
            type: 'bar',
            barWidth: 12,
            label: {
              show: true,
              position: 'right',
              formatter: '{c}m³',
              color: '#fff',
              fontSize: 14,
              // fontWeight: 'bold'
            },
            data: this.rankData,
            itemStyle: {
              normal: {
                color: {
                  colorStops: [
                    {
                      offset: 0,
                      color: "#EBC965"
                    },
                    {
                      offset: 1,
                      color: "#FF9845"
                    }
                  ]
                },
                // barBorderRadius: 12,
              }
            }
          }
        ]
      })
    }
  }
}
</script>
