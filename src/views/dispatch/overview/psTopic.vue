<template>
  <div class="psTopic">
    <div class="topData">
      <div class="countCard">
        <img src="@/assets/img/dispatch/topicIcon1.png" alt="">
        <div>蓄水池数量(个)</div>
        <div class="count">{{ pageInfo.total }}</div>
      </div>
      <div class="countCard" style="margin-top: 5px;">
        <img src="@/assets/img/dispatch/topicIcon1.png" alt="">
        <div>总蓄水量(m³)</div>
        <div class="count">{{ totalWater }}</div>
      </div>
      <!-- <div class="item">
        <div>蓄水池数量</div>
        <div class="num">{{ pageInfo.total }} <span class="unit">个</span></div>
      </div>
      <div class="item">
        <div>总蓄水量</div>
        <div class="num">{{ totalWater }} <span class="unit">方</span></div>
      </div> -->
    </div>

    <!-- <div style="text-align: center;">蓄水量预警率</div> -->
    <!-- <SmallCircle :rateData="1.2" height="100px" /> -->
    <div class="chartBox">
      <div style="height: 136px;padding-left: 72.5px;">
        <GaugeChart :value="[3.8]" height="263px" width="263px" />
      </div>
      <div style="font-weight: bold;color: #66F5FE;text-align: center;">蓄水量预警率：3.8%</div>
    </div>

    <div class="listQuery">
      <div class="iconTitle">实时数据</div>
      <el-input v-model="name" class="searchInput" placeholder="请输入名称" size="mini" @keyup.enter.native="handleQuery"></el-input>
      <el-button type="primary" class="searchButton" size="mini" @click="handleQuery">搜索</el-button>
    </div>
    <div class="myList">
      <div class="myListHeader">
        <span class="myListHeaderItem">名称</span>
        <span class="myListHeaderItem">蓄水量(m³)</span>
        <span class="myListHeaderItem">液位(m)</span>
        <span>时间</span>
      </div>
      <div class="myListRow" v-for="item, index in tableData" :key="index">
        <span>{{ item.facilityName }}</span>
        <span>{{ item.currentWater }}</span>
        <span>{{ item.waterLevelValue }}</span>
        <span>{{ item.monitorTime }}</span>
      </div>
    </div>

    <el-pagination
      style="text-align: center;"
      small
      layout="prev, pager, next"
      :current-page.sync="pageInfo.page"
      :page-size="pageInfo.pageSize"
      :pager-count="5"
      @current-change="handlePageChange"
      :total="pageInfo.total">
    </el-pagination>
</div>
</template>

<script>
// 蓄水池主题
import SmallCircle from './charts/smallCircle'
import GaugeChart from '../../home/<USER>/gauge'

import { getPondLatestData } from '@/api/dispatch/topic'

import _ from 'loadsh'

export default {
  name: 'PsTopic',
  props: {
    pointList: {
      type: Array,
      default: () => []
    }
  },
  components: {
    SmallCircle,
    GaugeChart
  },
  data() {
    return {
      name: '',
      tableData: [],
      listTemp: [],
      isFilter: false,
      filterList: [],
      totalWaterInit: 0,
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    totalWater() {
      if (this.isFilter) {
        const total = this.filterList.reduce((total, item) => {
          return total + item.currentWater
        }, 0)
        return total.toFixed(2)
      } else {
        return this.totalWaterInit
      }
    }
  },
  mounted() {
    this.getList()
    // if(this.pointList.length) {
    //   console.log(this.pointList.length)
    //   this.getList()
    // }
  },
  methods: {
    handleQuery() {
      if(this.name) {
        this.isFilter = true
        this.filterList = this.listTemp.filter(point => point.facilityName.includes(this.name))
        this.pageInfo.total = this.filterList.length
        this.tableData = this.getOnePage(this.filterList, 1, this.pageInfo.pageSize)
      } else {
        this.isFilter = false
        this.pageInfo.total = this.listTemp.length
        this.tableData = this.getOnePage(this.listTemp, this.pageInfo.pageNo, this.pageInfo.pageSize)
      }
    },
    getList() {
      // getPondLatestData().then(res => {
      //   this.listTemp = res.data
      //   const totalWater = res.data.reduce((total, item) => {
      //     return total + item.currentWater
      //   }, 0)
      //   this.totalWater = totalWater.toFixed(2)
      //   this.pageInfo.total = res.data.length
      //   this.tableData = this.getOnePage(this.listTemp, this.pageInfo.pageNo, this.pageInfo.pageSize)
      // })
      this.listTemp = _.cloneDeep(this.pointList)
      const totalWater = this.pointList.reduce((total, item) => {
        return total + item.currentWater
      }, 0)
      this.totalWaterInit = totalWater.toFixed(2)
      this.pageInfo.total = this.pointList.length
      this.tableData = this.getOnePage(this.listTemp, this.pageInfo.pageNo, this.pageInfo.pageSize)
    },
    handlePageChange(curentPage) {
      if(this.isFilter) {
        this.tableData = this.getOnePage(this.filterList, curentPage, this.pageInfo.pageSize)
      } else {
        this.tableData = this.getOnePage(this.listTemp, curentPage, this.pageInfo.pageSize)
      }
    },
    getOnePage(list, pageNo, pageSize) {
      if (!Array.isArray(list) || pageNo < 1 || pageSize < 1) {
        return []
      }

      const startIndex = (pageNo - 1) * pageSize
      if (startIndex >= list.length) {
        return []
      }

      const endIndex = startIndex + pageSize
      return list.slice(startIndex, endIndex)
    }
  }
}
</script>

<style lang="scss">
.psTopic {
  .el-pagination {
    color: #fff;
    button, .el-pager li {
      background-color: transparent;
      height: 22px !important;
      min-width: 22px !important;
      line-height: 22px !important;
      margin: 0 !important;
    }
    .btn-quickprev, .btn-quicknext {
      color: #fff;
    }
    .btn-prev, .btn-next {
      color: #fff;
    }
  }
}
</style>

<style lang="scss" scoped>
.psTopic {
  height: calc(100% - 21px);
  .topData {
    // padding: 30px 0;
    // display: flex;
    // text-align: center;
    .countCard {
      height: 90px;
      background-image: url('~@/assets/img/dispatch/topicBg1.png');
      background-size: 100% 90px;
      display: flex;
      align-items: center;
      padding: 0 25px 0 32px;
      .count {
        margin-left: auto;
        font-family: DIN;
        color: #11E6FF;
        font-size: 26px;
      }
    }
    .item {
      flex: 1;
      .num {
        font-size: 30px;
      }
      .unit {
        font-size: 14px;
      }
    }
  }
  .chartBox {
    // height: 136px;
  }
  .listQuery {
    display: flex;
  }
  .myList {
    height: calc(100% - 292px);
  }
  .searchInput {
    width: 120px;
    margin-left: auto;
  }
  .searchButton {
    margin: 0 10px;
  }
}
</style>
