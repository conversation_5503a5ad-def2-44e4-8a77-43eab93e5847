<template>
  <div class="wfTopic">
    <div class="dateBox">
      <div class="iconTitle"><span>水厂制水量统计</span></div>
      <el-date-picker
        v-model="dateValue"
        type="date"
        value-format="yyyy-MM-dd"
        @change="dateChange"
        :clearable="false"
        size="mini"
        style="width: 150px;margin-left: auto;"
        placeholder="选择日期">
      </el-date-picker>
    </div>
    <RankBar :xAxisData="xAxisData" :rankData="rankData" height="210px" />

    <div class="iconTitle">出厂水质合格率分析</div>
    <div class="chartBox">
      <div>
        <SmallCircle :rateData="rateInfo.turbidityPassRate" width="100px" height="100px" />
        <div class="label">出水浊度</div>
      </div>
      <div>
        <SmallCircle :rateData="rateInfo.chlorinePassRate" color="#51d351" width="100px" height="100px" />
        <div class="label">出水余氯</div>
      </div>
      <div>
        <SmallCircle :rateData="rateInfo.phPassRate" color="#fec03d" width="100px" height="100px" />
        <div class="label">出水pH</div>
      </div>
    </div>

    <div class="iconTitle" style="padding-top: 20px;">实时数据</div>
    <el-table :data="tableData" style="margin-top: 10px;">
      <el-table-column fixed prop="name" label="水厂名称" width="120px"></el-table-column>
      <el-table-column prop="" label="上传时间"  width="100px"></el-table-column>
      <el-table-column prop="" label="出水压力" width="100px"></el-table-column>
      <el-table-column prop="" label="出水流量" width="100px"></el-table-column>
      <el-table-column prop="" label="出水pH"></el-table-column>
      <el-table-column prop="" label="出水温度" width="100px"></el-table-column>
      <el-table-column prop="" label="出水浊度" width="100px"></el-table-column>
      <el-table-column prop="" label="出水余氯" width="100px"></el-table-column>
      <el-table-column prop="" label="累计流量" width="100px"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import RankBar from './charts/rankBar'
import SmallCircle from './charts/smallCircle'

import { getWaterFactoryRate, getWaterFactoryStatistics } from '@/api/dispatch/topic'
import { parseTime } from '@/utils'

export default {
  name: 'WfTopic',
  components: {
    RankBar,
    SmallCircle
  },
  data() {
    return {
      dateValue: '',
      rateInfo: {
        turbidityPassRate: 0,
        chlorinePassRate: 0,
        phPassRate: 0
      },
      xAxisData: ['谭湾水厂', '双龙观水厂', '青华水厂', '大礼水厂', '吴家坪水厂'],
      rankData: [2809.6, 2635.4, 2479.7, 2427.6, 2310],
      tableData: [
        { name: '谭湾水厂' },
        { name: '双龙观水厂' },
        { name: '青华水厂' },
        { name: '大礼水厂' },
        { name: '吴家坪水厂' }
      ]
    }
  },
  mounted() {
    this.dateValue = parseTime(new Date(), '{y}-{m}-{d}')
    this.getData()
  },
  methods: {
    getData() {
      this._getWaterFactoryStatistics()
      getWaterFactoryRate().then(res => {
        if(res.status === 200) {

          this.rateInfo = res.data
        }
      })

    },
    dateChange() {
      this._getWaterFactoryStatistics()
    },
    _getWaterFactoryStatistics() {
      getWaterFactoryStatistics(this.dateValue).then(res => {
        if(res.status === 200) {
          let data = res.data.sort((a, b) => b.yield - a.yield)
          this.xAxisData = data.map(item => item.name)
          this.rankData = data.map(item => item.yield)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.wfTopic {
  .dateBox {
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
  }
  .chartBox {
    display: flex;
    justify-content: space-around;
    .label {
      font-size: 14px;
      text-align: center;
    }
  }
}
</style>
