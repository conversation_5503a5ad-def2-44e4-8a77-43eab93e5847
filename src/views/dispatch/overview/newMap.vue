<template>
  <div class="overviewMap">
    <div id="map"></div>

    <div class="wqLegend" v-if="topic === 'sw'">
      <el-radio-group v-model="SWRadio" @input="changeSWRadio">
        <el-radio label="hydrology">一体化水文站</el-radio>
        <el-radio label="pump">泵站</el-radio>
      </el-radio-group>
    </div>

    <div class="wqLegend" v-if="topic === 'wq'">
      <el-radio-group v-model="WQRadio" @input="changeWQRadio">
        <el-radio label="WATER_FACTORY">水厂水质</el-radio>
        <el-radio label="WATER_SOURCE">水源水质</el-radio>
        <el-radio label="END_USER">末端用户水质</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
import L, { icon } from 'leaflet'
import 'leaflet/dist/leaflet.css'
import axios from 'axios'

import { getWQLatest } from '@/api/wq/screen'
import { getHydrologyLatestData, getPumpLatestData } from '@/api/dispatch/topic'
import { getPondLatestData } from '@/api/dispatch/topic'
import LabelPopup from './labelPopup'
import Vue from 'vue'

export default {
  name: 'OverviewMap',
  props: {
    topic: {
      type: String,
      default: 'sw'
    },
    pointList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      geoUrl: '',
      map: null,
      WQRadio: 'WATER_FACTORY',
      SWRadio: 'hydrology',
      pointLayer: null,
      popupLayer: null,
      markerList: [], // 存储标记引用 
    }
  },
  watch: {
    topic(val) {
      this.pointLayer.clearLayers()
      this.popupLayer.clearLayers()
      this.markerList = []

      if(val === 'wq') {
        this.createWqTopicLayer(this.WQRadio)
      } else if(val === 'sw') {
        this.createSwTopicLayer()
      } else if(val === 'ps') {
        console.log('ps')
        this.createPsTopicLayer()
      }
    },
    pointList: {
      deep: true,
      handler() {
        this.createPsTopicLayer()
      }
    }
  },
  created() {
    this.geoUrl = process.env.VUE_APP_GEO_URL
  },
  mounted() {
    this.initMap()
  },
  methods: {
    initMap() {
      this.map = L.map('map', {
        attributionControl: false,
        closePopupOnClick: false,
        zoomControl: false,
        zoom: 10,
        // center: [31.25307163396126, 110.75210994390093],
        center: [31.329917907714844, 110.71060180664062],
        crs: L.CRS.EPSG4326
      })

      // 天地图影像标注
      let cia_c_url = 'http://t{s}.tianditu.gov.cn/cia_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6'
      let tdtImageNoteLayer = L.tileLayer(cia_c_url, {
        subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
        zoomOffset: 1
      })
      this.map.addLayer(tdtImageNoteLayer)

      this.pointLayer = L.featureGroup()
      this.popupLayer = L.layerGroup()
      this.map.addLayer(this.pointLayer)
      
      this.map.on('click', (event) => {
        console.log(this.map.getCenter())
      })
      this.map.on('zoomend', (event) => {
        const currentZoom = this.map.getZoom()
        // console.log(this.map.getZoom())

        if(currentZoom >= 15) {
          // this.map.addLayer(this.popupLayer)
          this.markerList.forEach(marker => {
            if (!marker.isPopupOpen()) {
              marker.openPopup()
            }
          })
        } else {
          // this.map.removeLayer(this.popupLayer)
          this.markerList.forEach(marker => {
            if (marker.isPopupOpen()) {
              marker.closePopup()
            }
          })
        }

      })
    },
    changeSWRadio(value) {
      this.pointLayer.clearLayers()
      this.popupLayer.clearLayers()
      this.createSwTopicLayer()
    },
    changeWQRadio(value) {
      // console.log(value)
      this.pointLayer.clearLayers()
      this.popupLayer.clearLayers()
      this.createWqTopicLayer(value)
    },
    // 蓄水池主题
    createPsTopicLayer() {
      console.log('createPsTopicLayer')
      let labelPopup = Vue.extend(LabelPopup)
      // 蓄水池
      let data = this.pointList
      let icon = L.icon({
        iconUrl: require('@/assets/img/home/<USER>'),
        iconSize: [20, 24]
      })
      data.forEach(point => {
        if(point.coordinate) {
          let coordinate = [point.coordinate.y, point.coordinate.x]
          let marker = L.marker(coordinate, { icon })

          let list = []
          list.push(`监测时间：${point.monitorTime.slice(5)}`)
          list.push(`蓄水量：${point.currentWater}m³`)
          list.push(`液位：${point.waterLevelValue}m`)

          let p = new labelPopup({
            propsData: {
              name: point.facilityName,
              list
            }
          })
          p.$mount()
          let content = p.$el
          let popup = L.popup({ closeOnClick: false, autoClose: false }).setLatLng(coordinate).setContent(content)
          // this.popupLayer.addLayer(popup)

          marker.bindPopup(popup)
          // 默认关闭所有弹窗
          marker.closePopup()
          this.markerList.push(marker)
          this.pointLayer.addLayer(marker)

        }
      })
      // getPondLatestData().then(res => {
      //   let data = res.data || []
      //   let icon = L.icon({
      //     iconUrl: require('@/assets/img/home/<USER>'),
      //     iconSize: [20, 24]
      //   })
      //   data.forEach(point => {
      //     if(point.coordinate) {
      //       let coordinate = [point.coordinate.y, point.coordinate.x]
      //       let marker = L.marker(coordinate, { icon })
      //       this.pointLayer.addLayer(marker)

      //       let list = []
      //       list.push(`蓄水量：${point.currentWater}m³`)
      //       list.push(`液位：${point.waterLevelValue}m`)

      //       let p = new labelPopup({
      //         propsData: {
      //           name: point.facilityName,
      //           list
      //         }
      //       })
      //       p.$mount()
      //       let content = p.$el
      //       let popup = L.popup({ closeOnClick: false }).setLatLng(coordinate).setContent(content)
      //       this.popupLayer.addLayer(popup)
      //     }
      //   })
      // })
    },
    // 原水主题
    createSwTopicLayer() {
      let labelPopup = Vue.extend(LabelPopup)
      if(this.SWRadio === 'hydrology') {
        // 水文站
        getHydrologyLatestData().then(res => {
          let data = res.data || []
          let icon = L.icon({
            iconUrl: require('@/assets/img/home/<USER>'),
            iconSize: [20, 24]
          })
          data.forEach(point => {
            if(point.insm.coordinate) {
              let coordinate = [point.insm.coordinate.y, point.insm.coordinate.x]
              let marker = L.marker(coordinate, { icon })
              this.pointLayer.addLayer(marker)

              let list = []
              list.push(`监测时间：${point.data.dataTime.slice(5)}`)
              list.push(`水深：${point.data.ss}m`)
              list.push(`瞬时流量：${point.data.ssll}m³/h`)
              list.push(`累计水量：${point.data.ljsl}m³`)

              let p = new labelPopup({
                propsData: {
                  name: point.insm.name,
                  list
                }
              })
              p.$mount()
              let content = p.$el
              let popup = L.popup({ closeOnClick: false, autoClose: false }).setLatLng(coordinate).setContent(content)
              // this.popupLayer.addLayer(popup)

              marker.bindPopup(popup)
              marker.closePopup()
              this.markerList.push(marker)
              this.pointLayer.addLayer(marker)
            }
          })
        })
      } else {
        // 泵站
        getPumpLatestData().then(res => {
          let data = res.data || []
          let icon = L.icon({
            iconUrl: require('@/assets/img/dispatch/pointIcon3.png'),
            iconSize: [20, 24]
          })
          data.forEach(point => {
            if(point.station.coordinate) {
              let coordinate = [point.station.coordinate.y, point.station.coordinate.x]
              let marker = L.marker(coordinate, { icon })
              // this.pointLayer.addLayer(marker)

              let list = []
              list.push(`监测时间：${point.data.createTime.slice(5)}`)
              list.push(`进水池液位：${(point.data.inWaterLevel).toFixed(2)}cm`)
              list.push(`高位池液位：${(point.data.poolLevel).toFixed(2)}cm`)
              list.push(`出水压力：${(point.data.outWaterPress).toFixed(2)}MPa`)
              list.push(`泵1运行状态：${point.data.pump1Run === '' ? '否' : '是'}`)
              list.push(`泵1电流/频率：${point.data.pump1Electric === '' ? '--' : point.data.pump1Electric}A/${point.data.pump1Hz === '' ? '--' : point.data.pump1Hz}hz`)
              list.push(`泵2运行状态：${point.data.pump2Run === '' ? '否' : '是'}`)
              list.push(`泵2电流/频率：${point.data.pump2Electric === '' ? '--' : point.data.pump2Electric}A/${point.data.pump2Hz === '' ? '--' : point.data.pump2Hz}hz`)
              
              let p = new labelPopup({
                propsData: {
                  name: point.station.name,
                  list
                }
              })
              p.$mount()
              let content = p.$el
              let popup = L.popup({ closeOnClick: false, autoClose: false }).setLatLng(coordinate).setContent(content)
              // this.popupLayer.addLayer(popup)

              marker.bindPopup(popup)
              marker.closePopup()
              this.markerList.push(marker)
              this.pointLayer.addLayer(marker)
            }
          })
        })
      }
    },
    // 水质主题
    createWqTopicLayer(radioValue) {
      getWQLatest().then(res => {
        let data = res.data || []
        let result = data.filter(point => point.sourceType === radioValue)

        
        let icon = L.icon({
          iconUrl: require('@/assets/img/home/<USER>'),
          iconSize: [20, 24]
        })
        
        let labelPopup = Vue.extend(LabelPopup)

        result.forEach(point => {
          if(point.sourceType === 'END_USER') {
            icon = L.icon({
              iconUrl: require('@/assets/img/home/<USER>'),
              iconSize: [20, 24]
            })
          }
          if(point.sourceType === 'WATER_SOURCE') {
            icon = L.icon({
              iconUrl: require('@/assets/img/home/<USER>'),
              iconSize: [20, 24]
            })
          }
          let marker = L.marker([point.coordinate.y, point.coordinate.x], { icon })
          // this.pointLayer.addLayer(marker)

          let list = []
          list.push(`监测时间：${point.dataTime.slice(5)}`)
          list.push(`pH：${point.ph === '' ? '--' : point.ph }`)
          list.push(`温度：${point.temp === '' ? '--' : point.temp }℃`)
          list.push(`浊度：${point.turbidity === '' ? '--' : point.turbidity}NTU`)
          list.push(`电导率：${point.powerRate === '' ? '--' : point.powerRate}μs/cm`)
          list.push(`余氯：${point.chlorine === '' ? '--' : point.chlorine }mg/L`)
          let p = new labelPopup({
            propsData: {
              name: point.name,
              list
            }
          })
          p.$mount()
          let content = p.$el
          let popup = L.popup({ closeOnClick: false, autoClose: false }).setLatLng([point.coordinate.y, point.coordinate.x]).setContent(content)
          // this.popupLayer.addLayer(popup)

          marker.bindPopup(popup)
          marker.closePopup()
          this.markerList.push(marker)
          this.pointLayer.addLayer(marker)
        })
        
      })
    },
    // 创建泵站弹窗内容
    createPumpPopupContent(list) {},
    openLayerPopup(layer) {
      layer.eachLayer(feature => {
        if(feature instanceof L.Marker && feature.getPopup()) {
          feature.openPopup()
        }
      })
    },
  }
}
</script>

<style lang="scss">
.overviewMap {
  .wqLegend {
    .el-radio {
      display: block;
      margin-top: 10px;
    }
    .el-radio__label {
      color: #fff;
    }
  }
  .leaflet-popup-content-wrapper {
    background: transparent;
  }
  .leaflet-popup-tip {
    background: #52D0DF;
  }
  .leaflet-popup-content {
    margin: 0;
  }
}
</style>

<style lang="scss" scoped>
.overviewMap {
  height: 100%;
  position: relative;
  #map {
    height: 100%;
    background-color: transparent;
  }
  .wqLegend {
    position: absolute;
    bottom: 20px;
    right: 450px;
    padding: 0 10px 10px 10px;
    background: rgba(5, 58, 127, 0.4);
    border-radius: 4px;
    border: 1px solid #0b0b88;
    z-index: 1001;
  }
}
</style>
