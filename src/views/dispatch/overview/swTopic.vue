<template>
  <div class="swTopic">
    <div class="dateBox">
      <div class="iconTitle"><span>原水取水排名</span></div>
      <el-date-picker
        v-model="dateValue"
        type="date"
        value-format="yyyy-MM-dd"
        size="mini"
        style="width: 150px;margin-left: auto;"
        placeholder="选择日期">
      </el-date-picker>
    </div>
    <RankBar :xAxisData="xAxisData" :rankData="rankData" height="210px" />

    <div class="dateBox">
      <div class="iconTitle">实时数据</div>
      <div style="font-size: 14px;color: #D8F0FF;">采样时间：2025-02-24 15:42</div>
    </div>
    <div style="margin-top: 10px;">
      <div class="card">
        <div class="title">
          <span>1#取水泵站</span>
          <span>今日取水量(m³) <span class="value">730</span><span style="color: #3DD283;margin: 0 10px;">16%<i class="el-icon-top"></i></span></span>
        </div>
        <div class="dataBox">
          <div>
            <span>氨氮：</span>
            <span>-- mg/L</span>
          </div>
          <div>
            <span>电导率：</span>
            <span>-- μs/cm</span>
          </div>
          <div>
            <span>浊度：</span>
            <span>-- NTU</span>
          </div>
          <div>
            <span>溶解氧：</span>
            <span>-- mg/L</span>
          </div>
          <div>
            <span>pH：</span>
            <span>7.24</span>
          </div>
          <div>
            <span>温度：</span>
            <span>-- ℃</span>
          </div>
          <div>
            <span>出水压力</span>
            <span>-- Mpa</span>
          </div>
          <div>
            <span>瞬时流量：</span>
            <span>-- m³/h</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RankBar from './charts/rankBar'

export default {
  name: 'SwTopic',
  components: {
    RankBar
  },
  data() {
    return {
      dateValue: '2025-03-07',
      xAxisData: ['1#取水泵站', '2#取水泵站', '3#取水泵站', '5#取水泵站', '4#取水泵站'],
      rankData: [2809.6, 2635.4, 2479.7, 2427.6, 2310]
    }
  },
}
</script>

<style lang="scss" scoped>
.swTopic {
  .dateBox {
    // padding-top: 20px;
    display: flex;
    justify-content: space-between;
  }
  .card {
    font-size: 14px;
    .title {
      background: rgba(19,119,208,0.8);
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      .value {
        font-family: DIN;
        font-size: 18px;
      }
    }
    .dataBox {
      padding: 10px;
      background-color: rgba(0, 170, 255, 0.06);
      display: flex;
      flex-wrap: wrap;
      div {
        width: 50%;
      }
    }
  }
}
</style>
