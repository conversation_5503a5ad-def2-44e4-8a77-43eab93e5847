<template>
  <div class="overviewPage">
    <!-- <Map /> -->
    <NewMap :topic="currentTopic" :pointList="pointList" />
    
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="dispatch" />

    <!-- 主题切换 -->
    <div class="control">
      <div
        class="item item1" style=""
        :class="{ itemaActive1: currentTopic === 'sw' }">
        <span class="label1"  @click="handleClick('sw')">原水主题</span>
      </div>
      <div class="item item2" :class="{ itemaActive2: currentTopic === 'wf' }">
        <span class="label2" style="writing-mode: vertical-rl;" @click="handleClick('wf')">水厂主题</span>
      </div>
      <div class="item item3" :class="{ itemaActive3: currentTopic === 'ps' }" style="">
        <span class="label3" style="writing-mode: vertical-rl;" @click="handleClick('ps')">蓄水池主题</span>
      </div>
      <div class="item item4" :class="{ itemaActive4: currentTopic === 'wq' }">
        <span class="label4" @click="handleClick('wq')">水质主题</span>
      </div>
      <div class="radar">
        <div class="inner"></div>
      </div>
    </div>

    <!-- 主题面板 -->
    <!-- <TopicCard :topic="currentTopic" /> -->
    <div class="topicCard">
      <div class="cardTitle">{{ currentTopic | topicFilter }}</div>
      <div class="cardContainer">
        <SwTopic v-if="currentTopic === 'sw'" />
        <WfTopic v-if="currentTopic === 'wf'" />
        <PsTopic v-if="currentTopic === 'ps' && pointList.length " :pointList="pointList" />
        <WqTopic v-if="currentTopic === 'wq'" />
      </div>
    </div>
  </div>
</template>

<script>
// 调度总览
// import Map from './map'
import NewMap from './newMap'
// import TopicCard from './topicCard'
import SwTopic from './swTopic'
import WfTopic from './wfTopic'
import PsTopic from './psTopic'
import WqTopic from './wqTopic'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

import { getPondLatestData } from '@/api/dispatch/topic'

export default {
  components: {
    // Map,
    NewMap,
    // TopicCard,
    SwTopic,
    WfTopic,
    PsTopic,
    WqTopic,
    FloatingSideMenu
  },
  data() {
    return {
      currentTopic: 'ps',
      pointList: []
    }
  },
  filters: {
    topicFilter(value) {
      const labelMap = {
        'sw': '原水主题',
        'wf': '水厂主题',
        'ps': '蓄水池主题',
        'wq': '水质主题'
      }
      return labelMap[value] || ''
    }
  },
  created() {
    this.getPointList()

  },
  methods: {
    handleClick(value) {
      this.currentTopic = value
    },
    getPointList() {
      if(this.currentTopic === 'ps') {
        getPondLatestData().then(res => {
          this.pointList = res.data || []
        })
      }
    }
  }
}
</script>

<style lang="scss">
.topicCard {
  .cardTitle {
    background: url("~@/assets/img/title.png") center no-repeat;
    background-size: 100%;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
  }
  .iconTitle::before {
    display: inline-block;
    content: '';
    width: 16px;
    height: 16px;
    margin-right: 5px;
    background-image: url('../../../assets/img/icon.png');
    background-size: 100%;
  }
  .myList {
    font-size: 14px;
    text-align: center;
    .myListHeader {
      height: 34px;
      font-weight: bold;
      background-image: url("~@/assets/img/table.png");
      background-size: 100%;
      display: flex;
      align-items: center;
      span {
        flex: 1;
      }
    }
    .myListRow {
      font-size: 12px;
      display: flex;
      align-items: center;
      height: 30px;
      background-color: rgba(0, 170, 255, 0.08);
      display: flex;
      align-items: center;
      margin-top: 5px;
      span {
        flex: 1;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.overviewPage {
  height: calc(100vh - 84px);
  position: relative;
  color: #fff;

  .control {
    transform: rotate(45deg);
    position: absolute;
    z-index: 1001;
    left: 58px;
    top: 20px;
    width: 236px;
    height: 236px;
    cursor: pointer;

    display: flex;
    flex-wrap: wrap;
    .radar {
      position: absolute;
      width: 116px;
      height: 116px;
      background-image: url('~@/assets/img/dispatch/radar.png');
      background-size: 100%;
      left: 60px;
      top: 60px;
      .inner {
        width: 100%;
        height: 100%;
        background-image: url('~@/assets/img/dispatch/inner.png');
        background-size: 100%;
        animation: rotate 3s infinite linear;
      }
    }
    .item {
      width: 116px;
      height: 116px;
      background-size: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      .label1 {
        transform: rotate(-45deg) translateY(-10px);
      }
      .label2 {
        transform: rotate(-45deg) translateX(10px);
      }
      .label3 {
        transform: rotate(-45deg) translateX(-10px);
      }
      .label4 {
        transform: rotate(-45deg) translateY(10px);
      }
      &:hover {
        // background-color: #136EC0;
      }
    }
    .item1 {
      background-image: url('~@/assets/img/dispatch/top.png');
      margin: 0 3px 3px 0; 
    }
    .itemaActive1 {
      background-image: url('~@/assets/img/dispatch/topActive.png');
    }
    .item2 {
      background-image: url('~@/assets/img/dispatch/right.png');
    }
    .itemaActive2 {
      background-image: url('~@/assets/img/dispatch/rightActive.png');
    }
    .item3 {
      background-image: url('~@/assets/img/dispatch/bottom.png');
      margin-right: 3px;
    }
    .itemaActive3 {
      background-image: url('~@/assets/img/dispatch/bottomActive.png');
    }
    .item4 {
      background-image: url('~@/assets/img/dispatch/left.png');
    }
    .itemaActive4 {
      background-image: url('~@/assets/img/dispatch/leftActive.png');
    }
    .text {
      width: 1em;
      font-size: 16px;
      letter-spacing: 16px;
    }
  }

  .topicCard {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 430px;
    z-index: 1001;
    background-color: rgba(4, 15, 45, 0.3);
    border: 1px solid rgba(23, 110, 217, 0.3);
    backdrop-filter: blur(16px);
    .cardContainer {
      padding: 10px;
    }
  }
}
</style>