<template>
  <div class="riskControl">
    
    <div class="mapContainer">
      <Layer class="map" :current="current"/>
    </div>

    <div class="control">
      <div
        class="item item1" style="margin: 0 3px 3px 0;"
        :class="{ itemaActive1: current === 1 }">
        <span class="label1"  @click="handleClick(1)">供水水源</span>
      </div>
      <div class="item item2" :class="{ itemaActive2: current === 2 }">
        <span class="label2" style="writing-mode: vertical-rl;" @click="handleClick(2)">用户端</span>
      </div>
      <div class="item item3" :class="{ itemaActive3: current === 3 }" style="margin-right: 3px;">
        <span class="label3" style="writing-mode: vertical-rl;" @click="handleClick(3)">供水管网</span>
      </div>
      <div class="item item4" :class="{ itemaActive4: current === 4 }">
        <span class="label4" @click="handleClick(4)">供水水厂</span>
      </div>
      <div class="radar">
        <div class="inner"></div>
      </div>
    </div>

    <div class="bottomCard">
      <div class="title title1">风险图</div>
      <div class="content">
        
        <swiper
          :options="swiperOption"
          class="mySwiper"
        >
          <swiper-slide class="listCell" v-for="item, index in riskImgList" :key="index">
            <!-- <div class="type">{{ item.type }}</div> -->
            <img :src="item.pic" alt="" style="width: 120px;height: 120px;">
            <div class="time">{{ item.time }}</div>
          </swiper-slide>
          <div class="swiper-button-prev" slot="button-prev"></div>
          <div class="swiper-button-next" slot="button-next"></div>
        </swiper>
        
      </div>
    </div>

    <div class="rightCard">
      <div class="title">风险数据</div>
      <div class="body">
        <div class="query">
          <el-select v-model="riskType" placeholder="风险类型" clearable style="width: 190px;" size="medium" @change="typeChange">
            <el-option v-for="item in riskTypeList" :key="item.code" :label="item.alarmName" :value="item.code"></el-option>
          </el-select>
          <div>共有数据：{{ pageInfo.total }}条</div>
        </div>
        <!-- 列表 -->
        <div class="list" >
          <div class="listHeader">
            <span style="width: 50px;text-align: center;">序号</span>
            <span style="width: 100px;">风险类型</span>
            <span style="width: 100px;">站点名称</span>
            <span style="width: 50px;">发生时间</span>
          </div>
          <div class="listItem" v-for="item, index in todayDataList" :key="item.id">
            <div style="width: 50px;text-align: center;">{{ index < 9 ? '0' + (index + 1) : index + 1 }}</div>
            <div style="width: 100px;">{{ item.alarm.alarmName }}</div>
            <div style="width: 100px;">{{ item.facilityName }}</div>
            <div style="flex: 1;">{{ item.createTime }}</div>
          </div>
        </div>
        <!-- 分页 -->
        <div class="pageBox">
          <el-pagination
            background
            layout="prev, pager, next"
            :current-page.sync="pageInfo.pageNo"
            :page-size.sync="pageInfo.pageSize"
            @current-change="handleCurrentChange"
            :total="pageInfo.total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import Layer from "./layer.vue";

import { getRiskProfileType, getRiskProfilePageList } from '@/api/dispatch/warn'

export default {
  name: 'RiskControl',
  components: {
    Swiper,
    SwiperSlide,
    Layer
  },
  data() {
    return {
      current: 1,
      riskImgList: [],
      swiperOption: {
        slidesPerView: 9,
        spaceBetween: 20,
         navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        } 
      },
      riskType: '',
      riskTypeList: [],
      pageInfo: {
        pageSize: 20,
        pageNo: 1,
        total: 0
      },
      todayDataList: []
    }
  },
  mounted() {
    this.getType()
    this.getData()
  },
  methods: {
    handleClick(value) {
      this.current = value
    },
    async getType() {
      const { status, data } = await getRiskProfileType()
      if(status === 200) {
        this.riskTypeList = data
      }
    },
    typeChange(currentType) {
      // if(currentType) {
        this.getData()
      // }
    },
    getData() {
      getRiskProfilePageList({
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        alarm: this.riskType
      }).then(res => {
        const { status, data, count } = res
        if(status === 200) {
          this.pageInfo.total = count
          this.todayDataList = data
          // 处理图片
          this.riskImgList = data.map(item => {
            return {
              pic: item.pic,
              time: item.createTime
            }
          })
        }
      })
    },
    handleCurrentChange() {
      this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
.riskControl {
  color: #fff;
  height: calc(100vh - 84px);
  position: relative;
  @keyframes totate {
    0% {
      transform: rotate(0deg); 
    }
    100% {
      transform: rotate(360deg); 
    }
  }
  .mapContainer {
    position: relative;
    height: 100%;
    width: 100%;
    // background-image: url('~@/assets/img/home/<USER>');
    // background-size: 908px 619px;
    // background-repeat: no-repeat;
    // background-position: center 100px;
  }
  .control {
    transform: rotate(45deg);
    position: absolute;
    left: 58px;
    top: 20px;
    width: 235px;
    height: 235px;
    cursor: pointer;

    display: flex;
    flex-wrap: wrap;
    .radar {
      position: absolute;
      width: 116px;
      height: 116px;
      background-image: url('~@/assets/img/dispatch/radar.png');
      background-size: 100%;
      left: 60px;
      top: 60px;
      .inner {
        width: 100%;
        height: 100%;
        background-image: url('~@/assets/img/dispatch/inner.png');
        background-size: 100%;
        animation: rotate 3s infinite linear;
      }
    }
    .item {
      width: 116px;
      height: 116px;
      background-size: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .label1 {
        transform: rotate(-45deg) translateY(-10px);
      }
      .label2 {
        transform: rotate(-45deg) translateX(10px);
      }
      .label3 {
        transform: rotate(-45deg) translateX(-10px);
      }
      .label4 {
        transform: rotate(-45deg) translateY(10px);
      }
      &:hover {
        // background-color: #136EC0;
      }
    }
    .item1 {
      background-image: url('~@/assets/img/dispatch/top.png');      
    }
    .itemaActive1 {
      background-image: url('~@/assets/img/dispatch/topActive.png'); 
    }
    .item2 {
      background-image: url('~@/assets/img/dispatch/right.png');
    }
    .itemaActive2 {
      background-image: url('~@/assets/img/dispatch/rightActive.png'); 
    }
    .item3 {
      background-image: url('~@/assets/img/dispatch/bottom.png');
    }
    .itemaActive3 {
      background-image: url('~@/assets/img/dispatch/bottomActive.png'); 
    }
    .item4 {
      background-image: url('~@/assets/img/dispatch/left.png');
    }
    .itemaActive4 {
      background-image: url('~@/assets/img/dispatch/leftActive.png'); 
    }
    .text {
      width: 1em;
      font-size: 16px;
      letter-spacing: 16px;
    }
  }
  .title {
    background: url('~@/assets/img/title.png') center no-repeat;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
  }
  .title1 {
    background: url('~@/assets/img/dispatch/longBg.png') center no-repeat;
    background-size: 100%;
    width: 100%;
  }
  .bottomCard {
    position: absolute;
    width: 1420px;
    left:20px;
    bottom: 20px;
    .content {
      height: 180px;
      padding: 20px;
      background-color: rgba(4,15,45,0.3);
      .mySwiper {
        height: 100%;
        padding: 0 50px;
        .time {
          font-size: 12px;
          color: #E0EAFF;
        }
        .type {
          position: absolute;
          padding: 0 5px;
          font-size: 13px;
          background: #0E9CFF;
        }
      }
      .listCell {
        
      }
    }
  }
  .rightCard {
    position: absolute;
    width: 440px;
    right: 20px;
    top: 20px;
    bottom: 20px;
    .body {
      height: calc(100% - 42px);
      padding: 20px;
      background-color: rgba(4,15,45,0.3);
      .query {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .list {
        margin-top: 10px;
        height: calc(100% - 92px);
        overflow: hidden;
      }
      .listHeader {
        font-size: 14px;
        background-image: url("../../../assets/img/table.png");
        background-size: 100%;
        height: 34px;
        display: flex;
        align-items: center;
        span:last-child {
          flex: 1;
        }
      }
      .listItem {
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 30px;
        background-color: rgba(2,50,128,0.23);
        border: 1px solid #023280;
        margin-top: 5px;
      }
      .pageBox {
        height: 46px;
        padding-top: 10px;
      }
    }
  }
}
</style>