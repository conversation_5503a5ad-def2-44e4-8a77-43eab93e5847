<template>
  <div>
    <div id="mars3dContainer" class="mars3d-container homeMapContainer"></div>
  </div>
</template>
      
<script>
export default {
  props: {
      current: {
          type: Number
      }
  },
  data() {
    return {
      map: null,
      mapOptions: {
        scene: {
          //默认视角参数
          center: {
            lat: 31.280435,
            lng: 110.858503,
            alt: 151000,
            heading: 0,
            pitch: -90,
          },
          cameraController: {
            // 滚轮缩小最大高度
            maximumZoomDistance: 200000,
          },
          backgroundColor: "#013453",
          sceneMode: 2,
          globe: {
            baseColor: " #363635", // 地球地面背景色
          },
        },
        control: {
          // homeButton: true, // 视角复位按钮
        },
        basemaps: [
          { id: 10, name: "地图底图", type: "group" },
          {
            pid: 10,
            name: "单张图片 (本地离线)",
            icon: "/img/basemaps/offline.png",
            type: "image",
            url: require("../../../assets/img/pipe_network/bjpic.png"),
            show: true,
          },
        ],
      },
      geourl: "",
      waterSourceLayer: null,
      userLayer: null,
      pipeLayer: null,
      waterFactoryLayer: null,
    };
  },
  watch: {
      current: {
          handler(val) {
              this.waterSourceLayer.show = false
              this.userLayer.show = false
              this.pipeLayer.show = false
              this.waterFactoryLayer.show = false
              if (val == 1) {
                  this.waterSourceLayer.show = true
              }
              if (val == 2) {
                  this.userLayer.show = true
              }
              if (val == 3) {
                  this.pipeLayer.show = true
              }
              if (val == 4) {
                  this.waterFactoryLayer.show = true
              }
          },
      }
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL;
    this.initMap();
  },
  beforeDestroy() {
    if(this.map) {
      this.map.destroy()
    }
  },
  methods: {
    initMap() {
      this.map = new mars3d.Map("mars3dContainer", this.mapOptions);
      this.map.container.style.backgroundColor = "#546a53"; // 背景色
      this.map.on('load', () => {     
        this.addImage();
        this.addWMS();   
        this.addRiskLayer();
      })
    },
    addRiskLayer() {
      // 水源地风险
      this.waterSourceLayer = new mars3d.layer.GeoJsonLayer({
          name: "水源风险",
          url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Arisk&maxFeatures=50&outputFormat=application%2Fjson&cql_filter=station_type=3",
          show: true,
          zIndex: 300,
          symbol: {
          styleOptions: {
              // label: {
              // font_size: 15,
              // text: "{description}",
              // color: "#FF162A",
              // outline: true,
              // outlineColor: "#000000",
              // distanceDisplayCondition: true,
              // distanceDisplayCondition_far: 10000000,
              // distanceDisplayCondition_near: 100,
              // setHeight: 10000,
              // },
              image: require("../../../assets/img/dispatch/risk.png"),
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
          },
          popup: [
              { field: "description", name: "详情" },
          ],
      })
      this.map.addLayer(this.waterSourceLayer)
      // 终端用户风险
      this.userLayer = new mars3d.layer.GeoJsonLayer({
          name: "用户端风险",
          url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Arisk&maxFeatures=50&outputFormat=application%2Fjson&cql_filter=station_type=5",
          show: false,
          zIndex: 300,
          symbol: {
          styleOptions: {
              // label: {
              // font_size: 15,
              // text: "{description}",
              // color: "#FF162A",
              // outline: true,
              // outlineColor: "#000000",
              // distanceDisplayCondition: true,
              // distanceDisplayCondition_far: 10000000,
              // distanceDisplayCondition_near: 100,
              // setHeight: 10000,
              // },
              image: require("../../../assets/img/dispatch/risk.png"),
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
          },
          popup: [
              { field: "description", name: "详情" },
          ],
      })
      this.map.addLayer(this.userLayer)
      // 管网风险
      this.pipeLayer = new mars3d.layer.GeoJsonLayer({
          name: "管网风险",
          url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Arisk&maxFeatures=50&outputFormat=application%2Fjson&cql_filter=station_type=4",
          show: false,
          zIndex: 300,
          symbol: {
          styleOptions: {
              // label: {
              // font_size: 15,
              // text: "{description}",
              // color: "#FF162A",
              // outline: true,
              // outlineColor: "#000000",
              // distanceDisplayCondition: true,
              // distanceDisplayCondition_far: 10000000,
              // distanceDisplayCondition_near: 100,
              // setHeight: 10000,
              // },
              image: require("../../../assets/img/dispatch/risk.png"),
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
          },
          popup: [
              { field: "description", name: "详情" },
          ],
      })
      this.map.addLayer(this.pipeLayer)
      // 水厂风险
      this.waterFactoryLayer = new mars3d.layer.GeoJsonLayer({
          name: "水厂风险",
          url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Arisk&maxFeatures=50&outputFormat=application%2Fjson&cql_filter=station_type=1",
          show: false,
          zIndex: 300,
          symbol: {
          styleOptions: {
              // label: {
              // font_size: 15,
              // text: "{description}",
              // color: "#FF162A",
              // outline: true,
              // outlineColor: "#000000",
              // distanceDisplayCondition: true,
              // distanceDisplayCondition_far: 10000000,
              // distanceDisplayCondition_near: 100,
              // setHeight: 10000,
              // },
              image: require("../../../assets/img/dispatch/risk.png"),
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
          },
          popup: [
              { field: "description", name: "详情" },
          ],
      })
      this.map.addLayer(this.waterFactoryLayer)
    },
    addImage() {
      const tileLayer = new mars3d.layer.ImageLayer({
        url: require("../../../assets/img/basemaps/indexBg.png"),
        rectangle: {
          xmin: 109.325,
          xmax: 111.916,
          ymin: 30.49,
          ymax: 32.06,
        },
        alpha: 0.7,
      });
      this.map.addLayer(tileLayer);
    },
    addWMS() {
      const tileLayer = new mars3d.layer.WmsLayer({
        url: this.geourl + "/geoserver/img/wms",
        layers: "img:png2",
        parameters: {
          service: "WMS",
          version: "1.1.0",
          request: "GetMap",
          srs: "EPSG:4326",
          transparent: true,
          format: "image/png",
        },
        getFeatureInfoParameters: {
          feature_count: 10,
          INFO_FORMAT: "text/plain",
        },
        zIndex: 20,
        flyTo: true,
      });
      this.map.addLayer(tileLayer);
    },
    simplifyGeoJSON(geojson) {
      try {
        geojson = turf.simplify(geojson, {
          tolerance: 0.0001,
          highQuality: true,
          mutate: true,
        });
      } catch (e) {
        //
      }
      return geojson;
    },
  },
};
</script>
  
<style lang="scss">
.homeMapContainer {
  background-color: #22df74;
  height: 800px;
  .wfpopup {
    height: 28px;
    padding: 0px;
    font-size: 15px;
    text-align: center;
    line-height: 26px;
    border: 1px solid #00EAFF;
    border-radius: 4px;
    padding: 0 10px;
  }
  .cesium-viewer-toolbar {
    left: 465px;
  }
  .mars3d-popup-background {
    background: RGBA(26, 97, 153, 0.9);
    .mars3d-popup-content {
      margin: 0;
    }
  }
  .cesium-baseLayerPicker-dropDown-visible {
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    left: 48px;
  }
}
</style>
  
<style scoped lang="scss">
</style>
        