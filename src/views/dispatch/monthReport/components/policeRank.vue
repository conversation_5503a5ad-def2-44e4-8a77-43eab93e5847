<template>
  <div class="policeRank">
    <div class="title">
      <span class="text">报警统计排名</span>
    </div>
    <div class="rankContent">
      <div class="rankTitle">
        <div class="title1">
          <span> 水源 </span>
        </div>
        <div class="title2">
          <span> 水厂 </span>
        </div>
      </div>
      <div class="rankChart">
        <RankBar :seriesData="rankChart" />
      </div>
    </div>
  </div>
</template>
<script>
import RankBar from "../components/chart/rankBar.vue";
export default {
  components: {
    RankBar,
  },
  props: {
    rankData: {
      type: Array,
      default: () => [[], []],
    },
  },
  computed: {
    rankChart() {
      return [
        {
          name: "水厂",
          type: "bar",
          barWidth: 10,
          stack: "总量",
          label: {
            show: true,
            position: "right",
            textStyle: {
              color: "#fff",
            },
            formatter: function (params) {
              // console.log(params.data.station);
              return params.data.station;
            },
          },
          data: this.rankData[1],
          itemStyle: {
            normal: {
              color: {
                colorStops: [
                  {
                    offset: 0,
                    color: "#1EE7E7",
                  },
                  {
                    offset: 1,
                    color: "rgba(30,231,231,0.35)",
                  },
                ],
              },
            },
          },
        },
        {
          name: "水源",
          type: "bar",
          stack: "总量",
          label: {
            textStyle: {
              color: "#fff",
            },
            show: true,
            position: "left",
            formatter: (value) => {
              return -value.data;
            },
            formatter: function (params) {
              // console.log(params.data.station);
              return params.data.station;
            },
          },
          data: this.rankData[0],

          itemStyle: {
            normal: {
              color: {
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(24,144,255,0.35)",
                  },
                  {
                    offset: 1,
                    color: "#1890FF",
                  },
                ],
              },
            },
          },
        },
      ];
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="scss" scoped>
.policeRank {
  // position: absolute;
  margin-top: 15px;
  width: 860px;
  height: 345px;
  // left: 520px;
  background-color: rgba(4, 15, 45, 0.3);

  .title {
    line-height: 44px;
    height: 44px;
    width: 100%;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;

    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }
  }

  .text {
    background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .rankContent {
    height: calc(100% - 44px);

    .rankTitle {
      height: 50px;
      position: relative;

      .title1 {
        margin-top: 10px;
        position: absolute;
        left: 200px;
        width: 190px;
        height: 30px;
        line-height: 30px;
        color: #fff;
        text-align: center;
        background-image: url('../../../../assets/img/dispatch/titlebackground.png');
        background-size: 100% 100%;
      }

      .title2 {
        margin-top: 10px;
        position: absolute;
        right: 150px;
        width: 190px;
        height: 30px;
        line-height: 30px;
        color: #fff;
        text-align: center;
        background-image: url('../../../../assets/img/dispatch/titlebackground.png');
        background-size: 100% 100%;
      }
    }

    .rankChart {
      height: calc(100% - 30px);
    }
  }
}
</style>