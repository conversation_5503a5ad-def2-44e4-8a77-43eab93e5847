<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            if (!params.length) return ''
            let s = params[0].axisValueLabel + '<br/>'
            for (const iterator of params) {
              let d = iterator.data.value < 0 ? -iterator.data.value : iterator.data.value
              s += iterator.marker + iterator.data.station + ':' + d + '<br/>'
            }
            return s
          }
        },
        legend: {
          show: false,
          data: ['水源', '水厂']
        },
        grid: {
          // top: 30,
          // left: 0,
          // right: 50,
          // bottom: 18,
          // containLabel: true
        },
        xAxis: {
          type: 'value',
          min: -100,
          max: 100,
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#fff',
            formatter: (value) => {
              if (value < 0) return -value
              else return value
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(222, 235, 255, 0.5)',
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'category',
          inverse: true,  // 反转 Y 轴
          // boundaryGap: false,
          // inverse: true,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#fff',
              margin: 15
            }
          },
          data: ['Top1', 'Top2', 'Top3', 'Top4', 'Top5']
        },
        series: this.seriesData,
      })
    }
  }
}
</script>
