<template>
  <div class="safeReport">
    <div class="title">
      <span class="text">
        安全报告
      </span>
    </div>
    <div class="reportContent">
      <div class="tableBox">
        <div class="listHeader" style="text-align: center;">
          <span>序号</span>
          <span style="width: 220px;">统计类型</span>
          <span>数据</span>
        </div>
        <div class="listItem" v-for="(item, index) in typeCount" :key="item.index">
          <div class="small">{{ index + 1 }}</div>
          <div class="small" style="width: 220px">{{ item.name }}</div>
          <div class="small">{{ item.value }}</div>
        </div>
      </div>
      <div class="chartBox" style="width: 100%;">
        <PieChart :pieData="pieData" />
      </div>

    </div>
  </div>
</template>

<script>
import PieChart from '../components/chart/pieChart.vue';

export default {
  name: 'safeReport',
  props: {
    typeCount: {
      type: Array,
      default: () => []
    }
  },
  components: {
    PieChart
  },
  data() {
    return {
      tableData: [
        { type: '风险总数', data: 156 },
        { type: '接触率', data: 80 + '%' },
        { type: '待处理', data: 20 + '%' },
        { type: '处理及时率', data: 70 + '%' },
      ]
    }
  },
  computed: {
    pieData() {
      let temp = []
      for (let i = 0; i < this.typeCount.length; i++) {
        if(this.typeCount[i].name !== '风险总数') {
          temp.push(this.typeCount[i])
        }
      }
      return temp
    }
  },
} 
</script>


<style lang="scss" scoped>
.safeReport {
  // position: absolute;
  width: 440px;
  height: 550px;
  margin-top: 15px;
  // left: 70px;
  background-color: rgba(4, 15, 45, 0.3);

  .title {
    line-height: 44px;
    height: 44px;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;

    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }
  }

  .text {
    background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .reportContent {
    height: calc(100% - 44px);

    .tableBox {
      margin: 20px;
      height: calc(100% - 300px);

      .listHeader {
        background: url("../../../../assets/img/table.png") center no-repeat;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 34px;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 34px;

        span {
          display: block;
          width: 80px;
        }
      }

      .listItem {
        text-align: center;
        border: 1px solid rgba(2, 50, 128, 0.6);
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: 12px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 30px;
        box-shadow: 0 0 10px #023280 inset;

        .small {
          width: 80px;
          // font-size: 14px;
        }
      }

    }

    .chartBox {
      height: calc(100% - 250px);

    }
  }
}
</style>