<template>
  <div class="policeAnalysis">
    <div class="title">
      <span class="text"> 报警分析 </span>
    </div>
    <div class="analysisContent">
      <div class="queryBox">
        <el-select
          v-model="selectForm.area"
          @change="areaChange"
          placeholder="选择区域"
          style="width: 190px"
        >
          <el-option
            v-for="item in areaList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="selectForm.type"
          @change="typeChange"
          placeholder="风险类型"
          style="width: 190px; margin-left: 20px"
        >
          <el-option
            v-for="item in typeList"
            :key="item.code"
            :label="item.alarmName"
            :value="item.code"
          >
          </el-option>
        </el-select>
      </div>
      <div class="tableBox">
        <div class="listHeader" style="text-align: center">
          <span>区域</span>
          <span style="width: 220px">统计类型</span>
          <span>风险个数</span>
        </div>
        <div class="listItem" v-for="item in tableData" :key="item.id">
          <div class="small">{{ item.areaName  }}</div>
          <div class="small" style="width: 220px">{{ item.alarm.alarmName }}</div>
          <div class="small">{{ item.count }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getRiskProfileType } from "@/api/dispatch/warn";
import { getAreaTree } from '@/api/leakageLoss/analysis'
import { getWarnAnalyse } from '@/api/dispatch/monthReport'

export default {
  name: "policAnalysis",
  props: {
    date: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      areaList: [],
      typeList: [],
      selectForm: {
        area: "",
        type: "",
      },
      tableData: [],
    };
  },
  mounted() {
    this.initData()
  },
  watch: {
    date: {
      handler(newVal, oldVal) {
        newVal && this.getList()
      },
      immediate: true
    }
  },
  methods: {
    initData() {
      getAreaTree().then(res => {
        this.areaList = res.data.map(item => {
          return {
            id: item.id,
            name: item.name
          }
        })
      })
      getRiskProfileType().then(res => {
        this.typeList = res.data
      })
    },
    getList() {
      if(this.date) {
        getWarnAnalyse({
          areaId: this.selectForm.area,
          type: this.selectForm.type,
          date: this.date + '-01'
        }).then(res => {
          console.log(res)
          this.tableData = res.data
        })
      }
    },
    areaChange(value) {
      value && this.getList()
    },
    typeChange(value) {
      value && this.getList()
    }
  }
};
</script>
<style lang="scss" scoped>
.policeAnalysis {
  // right: 70px;
  // position: absolute;
  margin-top: 15px;
  width: 440px;
  height: 345px;

  background-color: rgba(4, 15, 45, 0.3);

  .text {
    background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .title {
    line-height: 44px;
    height: 44px;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;

    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }
  }

  .analysisContent {
    height: calc(100% - 44px);

    .queryBox {
      height: 40px;
      margin: 20px;
    }

    .tableBox {
      margin: 20px;
      height: calc(100% - 80px);

      .listHeader {
        background: url("../../../../assets/img/table.png") center no-repeat;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 34px;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 34px;

        span {
          display: block;
          width: 80px;
        }
      }

      .listItem {
        text-align: center;
        border: 1px solid rgba(2, 50, 128, 0.6);
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: 12px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 30px;
        box-shadow: 0 0 10px #023280 inset;

        .small {
          width: 80px;
          // font-size: 14px;
        }
      }
    }
  }
}
</style>