<template>
  <div class="MonthReport app-container">
    <div class="content">
      <div class="queryBox">
        <span>选择时间：</span>
        <el-date-picker v-model="month" type="month" size="medium" value-format="yyyy-MM" :clearable="false"
          :editable="false" placeholder="请选择年月">
        </el-date-picker>
        <!-- <el-cascader
          style="margin: 0 10px;"
          v-model="code"
          :props="propsObject"
          :options="options"
          :clearable="false"
          size="medium"
          placeholder="请选择区域">
        </el-cascader> -->
        <el-button type="primary" size="medium" @click="handleQuery" style="margin-left: 20px;">
          <i class="el-icon-search"></i>
          查询
        </el-button>
      </div>
      <div class="title">
        <span class="text">{{ this.title }}</span>
      </div>
      <div class="reportcontent">
        <SafeReport :typeCount="typeCount" />
        <PoliceRank :rankData="rankData" />
        <PoliceAnalysis :date="month" />
      </div>


      <!-- <div class="reportBox">
        <div class="reportCard">
          <div class="reportTitle">{{report.villageName}}{{report.time}}供水安全报告</div>
          <div class="reportContent">
            <section>
              <div>
                <span class="key">本月总计供水量:</span>
                <span class="value">{{ report.waterSupply }}(万m³)</span>
              </div>
              <div>
                <span class="key">本月剩余总水量:</span>
                <span>{{ report.waterRest }}(万m³)</span>
              </div>
              <div>
                <span class="key">上月供水量:</span>
                <span>{{ report.waterSupplyLastMonth }}(万m³)</span>
              </div>
            </section>
            <section>
              <div>
                <span class="key">风险总数:</span>
                <span>{{ report.riskTotal }}</span>
              </div>
              <div>
                <span class="key">风险解除数:</span>
                <span>{{ report.riskDeal }}</span>
              </div>
              <div>
                <span class="key">风险处理及时率:</span>
                <span>{{ report.dealTimelyRate }}%</span>
              </div>
            </section>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import { getTownList } from '@/api/common'
import { getMonthReport, getRiskTypeCount, getWarnRank } from '@/api/dispatch/monthReport'
import PoliceAnalysis from './components/policeAnalysis.vue'
import PoliceRank from './components/policeRank.vue'
import SafeReport from './components/safeReport.vue'

import { parseTime } from '@/utils'

export default {
  // 月度报告
  name: 'MonthReport',
  components: {
    PoliceAnalysis,
    PoliceRank,
    SafeReport
  },
  data() {
    return {
      month: '',
      title: '',
      code: '',
      options: [],
      propsObject: {
        checkStrictly: true,
        children: 'childTown',
        label: 'name',
        value: 'code'
      },
      typeCount: [],
      rankData: [],
      // report: {
      //   waterSupply: '--',
      //   waterRest: '--',
      //   waterSupplyLastMonth: '--',
      //   riskTotal: '--',
      //   riskDeal: '--',
      //   dealTimelyRate: '--'
      // }
    }
  },
  mounted() {
    this.initData()
    this.handleQuery()
  },
  methods: {
    initData() {
      this.month = parseTime(new Date(), '{y}-{m}')
      const titleTemp = this.month.split('-')
      this.title = `${titleTemp[0]}年${titleTemp[1]}月月度报告`
      // getTownList().then(res => {
      //   this.options = [res.data]
      // })
    },
    // 查询按钮
    handleQuery() {
      const titleTemp = this.month.split('-')
      this.title = `${titleTemp[0]}年${titleTemp[1]}月月度报告`
      getRiskTypeCount({
        queryTime: this.month + '-01'
      }).then(res => {
        this.typeCount = res.data
      })
      // 排行
      getWarnRank({
        stationTypeList: 'WATER_SOURCE,WATER_FACTORY',
        date: this.month + '-01'
      }).then(res => {
        const { data } = res
        // 分组
        let source = []
        let factory = []
        for(let i = 0; i < data.length; i++ ) {
          data[i].stationType === 'WATER_SOURCE' ? source.push(data[i]) :  factory.push(data[i])
        }
        // 排序
        source = source.map(item => {
          return {
            station: item.name,
            value: item.riskCount
          }
        })
        factory = factory.map(item => {
          return {
            station: item.name,
            value: item.riskCount
          }
        })
        source.sort((pre, cur) => cur.value - pre.value)
        factory.sort((pre, cur) => cur.value - pre.value)
        console.log(source, factory)
        // 取前五项
        if(source.length > 5) {
          source = source.slice(0, 5)
        }
        if(factory.length > 5) {
          factory = factory.slice(0, 5)
        }
        this.rankData = [source, factory]
      })
    },
    getReport() {
      if (!this.month | !this.code) {
        this.$message.error('请选择年月及区域')
        return
      }
      getMonthReport({
        village: this.code[this.code.length - 1],
        time: this.month
      }).then(res => {
        if (res.data[0]) {
          this.report = res.data[0]
          if (typeof this.report.dealTimelyRate === 'number') {
            this.report.dealTimelyRate = (this.report.dealTimelyRate * 100).toFixed(2)
          }
        } else {
          this.$message.info('暂无供水安全报告')
          this.report = {
            waterSupply: '--',
            waterRest: '--',
            waterSupplyLastMonth: '--',
            riskTotal: '--',
            riskDeal: '--',
            dealTimelyRate: '--'
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.MonthReport {
  height: calc(100vh - 84px);
  padding: 30px;
  position: relative;

  .content {
    height: 100%;
    border: 1px solid #3084B5;

    .queryBox {
      padding: 20px;
      color: #ffffff;
    }

    .title {
      font-size: 28px;
      width: 100%;
      height: 17px;
      line-height: 18px;
      font-weight: 700;
      text-align: center;
      color: #ffffff;
    }

    .reportcontent {
      margin-top: 20px;
      height: calc(100% - 115px);
      border-top: 1px solid #245dd2;
      display: flex;
      justify-content: space-between;
    }

    .text {
      background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }


  }
}
</style>
