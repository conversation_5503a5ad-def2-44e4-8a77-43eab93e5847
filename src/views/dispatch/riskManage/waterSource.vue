<template>
  <!-- 风险管控 -->
  <div class="WaterSource">
    <div class="upPane">
      <!-- 水源地规模 -->
      <div class="waterChange">
        <div class="title">
          <span class="text"> 水源地规模 </span>
        </div>
        <div class="ChartBox">
          <PieChart :seriesData="seriesData" />
        </div>
      </div>
      <!-- 水源水质 -->
      <WaterQuality />
      <!-- 水质合格率 -->
      <WaterPass />
    </div>
    <div class="underPane">
      <WaterRisk/>
      <RiskPolicy/>
      <WaterAnalysis/>
    </div>
  </div>
</template>

<script>
import PieChart from "./components/chart/pieChart.vue";
import RiskPolicy from './components/waterSource/riskPolicy.vue';
import WaterPass from "./components/waterSource/waterPass.vue";
import WaterQuality from "./components/waterSource/waterQuality.vue";
import WaterRisk from "./components/waterSource/waterRisk.vue"
import WaterAnalysis from "./components/waterSource/waterAnalysis.vue"

export default {
  name: "WatrSource",
  components: {
    PieChart,
    WaterQuality,
    WaterPass,
    WaterRisk,
    WaterAnalysis,
    RiskPolicy
  },
  data() {
    return {
      seriesData: [
        {
          color: ["#3254DD", "#48E5E5", "#0098FF", "#FFF67F", "#FFA64C"],
          type: "pie",
          radius: "80%",
          center: ["30%", "40%"],
          labelLine: {
            show: false,
          },
          label: {
            show: false,
          },
          data: [
            { value: 1228, name: "地表水" },
            { value: 1115, name: "饮用水" },
            { value: 1046, name: "地下水" },
            { value: 898, name: "工业用水" },
            { value: 1692, name: " 其他" },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.WaterSource {
  width: 100%;
  height: 100%;
  .upPane {
    height: 400px;
    display: flex;
    // justify-content: space-between;
    .waterChange {
      width: 480px;
      height: 350px;
      background-color: rgba(4, 15, 45, 0.3);

      .title {
        line-height: 44px;
        height: 44px;
        background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
        font-size: 18px;

        span {
          font-family: Microsoft YaHei;
          margin-left: 30px;
          height: 17px;
          line-height: 18px;
        }
      }

      .ChartBox {
        width: 100%;
        height: calc(100% - 120px);
        margin-top: 50px;
      }

      .text {
        background-image: -webkit-linear-gradient(
          270deg,
          #fff 41%,
          #31b3ff 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
  .underPane{
    display: flex;
    margin-top: 20px;
    height: calc(100% - 425px);
    justify-content: center;
  }
}
</style>