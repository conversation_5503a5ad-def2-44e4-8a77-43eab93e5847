<template>
    <div class="RiskManage app-container">
      <!-- 浮动侧边栏 -->
      <FloatingSideMenu module-type="dispatch" />
      <div class="tab">
         <div class="first" :class="{ active: currentType === 'waterSource' }" @click="handleTabClick('waterSource')">水源地</div>
         <div class="other" :class="{ active: currentType === 'waterFactory' }" @click="handleTabClick('waterFactory')">水厂</div>
      </div>
      <div class="compenent-container">
         <WaterSource v-if="currentType === 'waterSource'" />
         <WaterFactory v-if="currentType === 'waterFactory'" />
      </div>

    </div>
</template>

<script>
import WaterFactory from './waterFactory.vue'
import WaterSource from './waterSource.vue'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
    name:'RiskManage',
    components:{
        WaterFactory,
        WaterSource,
        FloatingSideMenu
    },
    data(){
      return{
        currentType: 'waterSource'
      }
    },
    methods: {
      handleTabClick(key) {
        this.currentType = key
      }
    },


}
</script>


<style lang="scss" scoped>
.RiskManage{
    padding: 0 30px;
    height: calc(100vh - 84px);
    .compenent-container{
       color: #ffffff;
       padding: 20px;
       height: calc(100% - 76px);
       border: 1px solid #2A5DD2;
       border-top-color: #3084B5;
    }
}

</style>