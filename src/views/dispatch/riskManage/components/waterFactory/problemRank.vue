<template>
  <!-- 水质报警问题排名 -->
  <div class="ProblemRank">
    <div class="title">
      <span class="text"> 水质报警问题排名 </span>
    </div>
    <div class="RankContent">
      <div class="rankItem" v-for="item in rankData" :key="item.id">
        <div class="upItem">
          <img :src="item.icon" alt="" />
          <div style="flex-grow: 1">{{ item.name }}</div>
          <div>
            报警 <span  style="color: red;">{{ item.number }}</span>次
          </div>
        </div>
        <div class="underItem">
          <div class="inner" :style="{ width: item.rate + '%' }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProbelmRank",
  data() {
    return {
      rankData: [
        {
          icon:require("../../../../../assets/img/pipe_network/pipe5.png"),
          name: "供水站",
          number: 5,
          rate: 40,
          color: "#00B4FF",
        },
        {
          icon:require("../../../../../assets/img/pipe_network/pipe5.png"),
          name: "供水站",
          number: 4,
          rate: 40,
          color: "#00B4FF",
        },
        {
          icon:require("../../../../../assets/img/pipe_network/pipe5.png"),
          name: "供水站",
          number: 3,
          rate: 30,
          color: "#00B4FF",
        },
        {
          icon:require("../../../../../assets/img/pipe_network/pipe5.png"),
          name: "供水站",
          number: 2,
          rate: 20,
          color: "#00B4FF",
        },
        {
          icon:require("../../../../../assets/img/pipe_network/pipe5.png"),
          name: "供水站",
          number: 1,
          rate: 10,
          color: "#00B4FF",
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.ProblemRank {
  width: 400px;
  height: 95%;
  background-color: rgba(4, 15, 45, 0.3);

  .title {
    line-height: 44px;
    height: 44px;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;

    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }

    .text {
      background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .RankContent {
    height:85%;
    overflow-y: auto;
    .rankItem {
      margin: 8px;
      height: 50px;
      background: rgba(98, 106, 128, 0.3);

      .upItem {
        height: 27px;
        margin: 5px 10px 0 10px;
        display: flex;
        line-height: 27px;

        img {
          margin-top: 3px;
          width: 20px;
          height: 20px;
        }
      }

      .underItem {
        height: 13px;
        margin: 4px 10px 0 10px;
        background: #d3d5db;
        border-radius: 10px;

        .inner {
          border-radius: 10px;
          padding-top: 2px;
          position: relative;
          height: 12px;
          background: linear-gradient(90deg,
              rgba(45, 72, 173, 0.1) 0%,
              #00b4ff 100%);
        }
      }
    }
  }
}
</style>