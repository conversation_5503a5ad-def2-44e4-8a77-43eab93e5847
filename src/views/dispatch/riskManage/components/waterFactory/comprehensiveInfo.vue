<template>
  <!-- 综合信息 -->
  <div class="ComprehensiveInfo">
    <div class="title">
      <span class="text"> 综合信息 </span>
    </div>
    <div class="InfoContent">
      <div class="infoItem" v-for="item in infoData" :key="item.title">
        <img :src="item.img" alt="">
        <div>{{ item.title }}</div>
        <div class="number">
          {{ item.data }}
          <span>%</span>
        </div>

      </div>


    </div>
  </div>
</template>

<script>
export default {
  data(){
    return{
      infoData:[
        {title:'水质合格率',data:100,img:require("../../../../../assets/img/dispatch/test.png")},
        {title:'检查完好率',data:100,img:require("../../../../../assets/img/dispatch/test.png")},
        {title:'工业受损率',data:100,img:require("../../../../../assets/img/dispatch/test.png")},
        {title:'预警接触率',data:100,img:require("../../../../../assets/img/dispatch/test.png")},
      ],
    }
  }
};
</script>

<style lang="scss" scoped>
.ComprehensiveInfo {
  width: 400px;
  height: 95%;
  background-color: rgba(4, 15, 45, 0.3);
  .title {
    line-height: 44px;
    height: 44px;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;
    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }

    .text {
      background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .InfoContent{
    display: flex;
    flex-flow:row wrap;
    margin: 10px;
    height: calc(100% - 60px);
    .infoItem{
      width: 45%;
      height: 45%;
      margin-left: 10px;
      margin-top: 5px;
      border-radius: 10px;
      background: rgba(98, 106, 128, 0.3);
      text-align: center;
      img{
        width: 60px;
        margin-top: 20px;
        height: 60px;
      }
      div,span{
       font-size: 15px;
      }
      .number{
        font-size: 20px;
        margin-top: 5px;
      }
    }
  }
}
</style>