<template>
  <!-- 报警趋势 -->
  <div class="PolicyTrend">
    <div class="title">
      <span class="text"> 报警趋势 </span>
    </div>
    <div class="policyContent">
      <div class="policyType">
        <div class="typeItem" :style="{background: item.color }" v-for="item in typeData" :key="item.id">
          <div class="typeTitle">
            <div style="flex-grow: 1;">
              {{ item.type }} 
            </div>
            <img :src="item.icon" alt="">
          </div>
          <div class="number">{{ item.data }}
            <span>个</span>
          </div>
        </div>
      </div>
      <div class="chartContent">
        <TrendChart :xAxisData="xAxisData" :seriesData="seriesData" />
      </div>
    </div>
  </div>
</template>

<script>
import TrendChart from '../chart/trendChart.vue';

export default {
  name: "PolicyTrend",
  components:{
    TrendChart
  },
  data(){
    return{
      typeData:[
        {
          color:"#90bdd6",
          type:"水质报警",
          data:12,
          icon:require("../../../../../assets/img/pipe_network/pipe5.png")
        },
        {
          color:"#90bdd6",
          type:"水位报警",
          data:5,
          icon:require("../../../../../assets/img/pipe_network/pipe5.png")
        },
        {
          color:"#90bdd6",
          type:"流量报警",
          data:10,
          icon:require("../../../../../assets/img/pipe_network/pipe5.png")
        },
        {
          color:"#90bdd6",
          type:"设备报警",
          data:2,
          icon:require("../../../../../assets/img/pipe_network/pipe5.png")
        },
        {
          color:"#90bdd6",
          type:"压力报警",
          data:9,
          icon:require("../../../../../assets/img/pipe_network/pipe5.png")
        },
      ],
      xAxisData:  ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      seriesData: [
        [17, 22, 5, 16, 8, 19, 15, 8, 13, 20, 10, 20],
        [5, 13, 22, 10, 20, 12, 10, 15, 10, 14, 9, 20]
      ]
    }
  }
};
</script>

<style lang="scss" scoped>
.PolicyTrend {
  width: 800px;
  height: 95%;
  background-color: rgba(4, 15, 45, 0.3);
  margin-left: 20px;
  margin-right: 20px;
  .title {
    line-height: 44px;
    height: 44px;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;
    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }

    .text {
      background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .policyContent{
    height: calc(100% - 50px);
    .policyType{
      display: flex;
      margin: 10px;
      height: 70px;
      .typeItem{
        width: 150px;
        height: 100%;
        margin-left: 10px;
        .typeTitle{
          height: 30px;
          margin: 5px 0 0 5px;
          display: flex;
          img{
            width: 23px;
            height: 23px;
          }
        }
        .number{
          margin: 5px 10px;
          font-weight: 700;
          span{
            font-weight: normal;
          }
        }
      }
    }
    .chartContent{
      height: calc(100% - 86px);
    }
  }
}
</style>