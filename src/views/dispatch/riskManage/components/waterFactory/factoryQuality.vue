<template>
  <!-- 水厂水质 -->
  <div class="FactoryQuality">
    <div class="title">
      <span class="text"> 水厂水质 </span>
    </div>
    <div class="tableBox">
      <div class="listHeader" style="text-align: center">
        <span>水厂名称</span>
        <span>水厂检测</span>
        <span>温度</span>
        <span>PH</span>
        <span>浊度</span>
        <span>余氯</span>
      </div>
      <div class="listItem" v-for="item  in tableData" :key="item.id">
        <div class="small ellipsis">{{ item.name }}</div>
        <div class="small ellipsis">{{ item.test }}</div>
        <div class="small">{{ item.temp }}</div>
        <div class="small">{{ item.ph }}</div>
        <div class="small">{{ item.ntu }}</div>
        <div class="small">{{ item.chlorine }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "FactoryQuality",
  data() {
    return {
      tableData: [
        {
          name: "谭湾水厂",
          test: '泵站名称-1' ,
          temp: 124 + "°C",
          ph: 7.2,
          ntu: 0.21 + "NTU",
          chlorine: 1.1 + "mg/L",
        },
        {
          name: "双龙观水厂",
          test: '泵站名称-1' ,
          temp: 124 + "°C",
          ph: 7.2,
          ntu: 0.21 + "NTU",
          chlorine: 1.1 + "mg/L",
        },
        {
          name: "青华水厂",
          test: '泵站名称-1' ,
          temp: 124 + "°C",
          ph: 7.2,
          ntu: 0.21 + "NTU",
          chlorine: 1.1 + "mg/L",
        },
        {
          name: "大礼水厂",
          test: '泵站名称-1' ,
          temp: 124 + "°C",
          ph: 7.2,
          ntu: 0.21 + "NTU",
          chlorine: 1.1 + "mg/L",
        },
        {
          name: "吴家坪水厂",
          test: '泵站名称-1' ,
          temp: 124 + "°C",
          ph: 7.2,
          ntu: 0.21 + "NTU",
          chlorine: 1.1 + "mg/L",
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.FactoryQuality {
  width: 800px;
  height: 95%;
  margin-left: 20px;
  margin-right: 20px;
  background-color: rgba(4, 15, 45, 0.3);
  .title {
    line-height: 44px;
    height: 44px;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;
    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }

    .text {
      background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tableBox {
    margin:20px;
    height: calc(100% - 80px);
    overflow: hidden;
    .listHeader {
      background: url("../../../../../assets/img/table.png") center no-repeat;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 34px;
      background-size: 100% 100%;
      display: flex;
      height: 34px;
      span {
        display: block;
        width: 126px;
      }
    }

    .listItem {
      text-align: center;
      border: 1px solid rgba(2, 50, 128, 0.6);
      cursor: pointer;
      display: flex;
      font-size: 12px;
      font-family: Microsoft YaHei;
      padding-top: 3px;
      padding-bottom: 3px;
      font-weight: 400;
      color: #ffffff;
      line-height: 30px;
      box-shadow: 0 0 10px #023280 inset;

      .small {
        width: 126px;
      }
    }
  }
}
</style>