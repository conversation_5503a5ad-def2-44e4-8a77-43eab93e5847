<template>
  <!-- 报警推送 -->
  <div class="PolicyPush">
    <div class="title">
      <span class="text"> 报警推送 </span>
    </div>
    <div class="PushContent">
      <div class="info">
        <div class="item">
          <div class="itemTitle">今日报警</div>
          <div class="number">4<span>个</span></div>
        </div>
        <div class="item" style="margin-left: 40px">
          <div class="itemTitle">未处理</div>
          <div class="number" style="color: red">0<span>个</span></div>
        </div>
      </div>
      <div class="policyContent">
        <div class="policyItem" v-for="item in infoData" :key="item.id">
          <div class="upItem">
            <div :style="setColor(item.level)"> {{ item.level | levelFilter }}</div>
            <div style="flex-grow: 1; margin-left: 10px">{{ item.site }}</div>
            <div style="font-weight: normal">{{ item.time }}</div>
          </div>
          <div class="underItem">
            <img :src="item.icon" alt="" />
            <div>[{{ item.name }}]</div>
            <div style="flex-grow: 1">{{ item.reason }}</div>
            <div style="color: aqua">{{ item.state }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "PolicyPush",
  data() {
    return {
      infoData: [
        {
          level: 1,
          site: "青龙供水站",
          time: "2024-05-07 09:11",
          icon: require("../../../../../assets//img/dispatch/snow.png"),
          name:'水源水量',
          reason:'泵站漏水',
          state:'已处理'
        },
        {
          level: 2,
          site: "青龙供水站",
          time: "2024-05-03 09:11",
          icon: require("../../../../../assets//img/dispatch/snow.png"),
          name:'水源水量',
          reason:'泵站漏水',
          state:'处理中'
        },
        {
          level: 3,
          site: "青龙供水站",
          time: "2024-05-07 09:11",
          icon: require("../../../../../assets//img/dispatch/snow.png"),
          name:'水源水量',
          reason:'泵站漏水',
          state:'已处理'
        },
        {
          level: 2,
          site: "青龙供水站",
          time: "2024-05-07 09:11",
          icon: require("../../../../../assets//img/dispatch/snow.png"),
          name:'水源水量',
          reason:'泵站漏水',
          state:'未处理'
        },
        {
          level: 1,
          site: "青龙供水站",
          time: "2024-05-07 09:11",
          icon: require("../../../../../assets//img/dispatch/snow.png"),
          name:'水源水量',
          reason:'泵站漏水',
          state:'已处理'
        },
        {
          level: 3,
          site: "青龙供水站",
          time: "2024-05-07 09:11",
          icon: require("../../../../../assets//img/dispatch/snow.png"),
          name:'水源水量',
          reason:'泵站漏水',
          state:'已处理'
        },
      ],
    };
  },
  filters: {
    levelFilter(value) {
      const labelMap = {
        1: "【一级】",
        2: "【二级】",
        3: "【三级】",
      };
      return labelMap[value];
    },
  },
  methods:{
    setColor(level) {
      const colors = ["", "#FE230A", "#FF9900", "#FFEF00"];
      return { color: colors[level] };
    },
  }
};
</script>

<style lang="scss" scoped>
.PolicyPush {
  width: 520px;
  height: 95%;
  background-color: rgba(4, 15, 45, 0.3);
  .title {
    line-height: 44px;
    height: 44px;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;
    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }

    .text {
      background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .PushContent {
    height: calc(100% - 50px);
    .info {
      margin: 10px;
      height: 30px;
      display: flex;
      .item {
        background-color: rgba(98, 106, 128, 0.3);
        display: flex;
        width: 45%;
        .itemTitle {
          line-height: 30px;
          flex-grow: 1;
        }
        .number {
          font-weight: 700;
          line-height: 30px;
          span {
            font-weight: normal;
            color: #fff;
          }
        }
      }
    }
    .policyContent {
      overflow-y: scroll;
      height: 85%;
      .policyItem {
        margin: 8px;
        height: 55px;
        background: rgba(98, 106, 128, 0.3);
        .upItem {
          margin: 5px 10px 3px 10px;
          padding-top: 2px;
          display: flex;
          div {
            font-weight: 700;
          }
        }
        .underItem {
          display: flex;
          line-height: 30px;
          height: 30px;
          margin: 0 10px 3px 10px;
          img {
            width: 20px;
            height: 20px;
            margin-top: 5px;
          }
          div {
            margin: 0 5px 0 5px;
          }
        }
      }
    }
  }
}
</style>