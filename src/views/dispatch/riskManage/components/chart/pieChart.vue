<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'


export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '100%'
        },
        seriesData: {
            type: Array,
            default: () => []
        },
        xAxisData: {
            type: Array,
            default: () => []
        },
        legendData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            chart: null,
            data: [],
        }
    },
    watch: {
        seriesData: {
            deep: true,
            handler(val) {
                this.initChart()
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$el)
            this.chart.setOption({

                tooltip: {
                    trigger: 'item'
                },

                legend: {
                    top: 5,
                    
                    orient: 'vertical',
                    itemWidth: 10,
                    itemHeight: 10,

                    textStyle: {
                        padding: [8, 0, 0, 0],
                        fontSize: 12,
                        fontWeight: "normal",
                        color: "#fff",
                    },
                    right: 60
                },
                grid: {
                    top: 35,
                    left: 50,
                    right: 30,
                    bottom: 20
                },
                series: this.seriesData
            })
        }
    }
}

</script>