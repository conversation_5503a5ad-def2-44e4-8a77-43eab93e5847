<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart',
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    rateNumber: {
      type: Number,
      default: 0
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        series: [
          {
            type: 'gauge',
            radius: '100%',
            splitNumber: 5,
            axisLine: {
              lineStyle: {
                width: 10,
                color: [[1, '#2A92FB']]
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            pointer: {
              width: 3
            },
            detail: {
              fontSize: 24,
              fontWeight: 'bold',
              color: '#66F5FE',
              padding: [50, 0, 0, 0],
              formatter: function(value) {
                return value + '%'
              }
            },
            data: [
              {
                value: this.rateNumber
              }
            ]
          },
          // 刻度
          {
            type: 'gauge',
            radius: '80%',
            splitNumber: 5,
            axisLine: {
              show: false
            },
            axisTick: {
              length: 6
            },
            axisLabel: {
              distance: -20,
              color: '#ffffff'
            },
            splitLine: {
              show: false
            },
            pointer: {
              show: false
            },
            title: {
              show: false
            },
            detail: {
              show: false
            }
          }
        ]
      })
    }
  }
}
</script>
