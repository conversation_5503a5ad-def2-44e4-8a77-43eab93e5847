<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
      className: {
          type: String,
          default: 'chart'
      },
      width: {
          type: String,
          default: '100%'
      },
      height: {
          type: String,
          default: '100%'
      },
      seriesData: {
          type: Array,
          default: () => []
      },
      xAxisData: {
          type: Array,
          default: () => []
      },
      legendData: {
          type: Array,
          default: () => []
      }
  },
  data() {
      return {
          chart: null,

      }
  },
  watch: {
      seriesData: {
          deep: true,
          handler(val) {
              this.initChart()
          }
      }
  },
  mounted() {
      this.$nextTick(() => {
          this.initChart()
      })
  },
  beforeDestroy() {
      if (!this.chart) {
          return
      }
      this.chart.dispose()
      this.chart = null
  },
  methods: {
      initChart() {
          this.chart = echarts.init(this.$el)
          this.chart.setOption({
              tooltip: {
                  trigger: 'item'
              },
              title: {
                  text: "水厂规模统计",
                  top: "45%",
                //   subtext: "89个",
                  x: "center",
                  y: "center",
                  textStyle: {
                      fontSize: 20,
                      color: ["#00cfff"]
                  },
                  subtextStyle: {
                      color: "#fff",

                      fontSize: 24,
                  },
              },
              grid: {
                  bottom: 150,
                  left: 0,
                  right: "10%"
              },
              legend: {
                  show: true,
                //   orient: 'vertical',
                  left: 'center',
                  bottom:'bottom',
                  icon:'circle',
                  textStyle:{
                    color:'#fff',
                    fontSize:15,
                  },
                  bottom:-1,
                  data:this.legendData
              },
              series: [
                  //主要显示层
                  {
                      color: ['#496dff', '#ede497', '#00e4ff','#FF5733'],
                      type: 'pie',
                      radius: ['55%', '69%'],
                      center: ['50%', '50%'],
                      labelLine: {
                          length: 30,
                          length2: 60,
                          lineStyle: {
                              color: '#deebff'
                          }
                      },
                      label: {
                         show:false
                      },
                      data: this.seriesData
                  },
                  //边框设置
                  {
                      radius: ["41%", "41.3%"],
                      center: ["50%", "50%"],
                      type: "pie",
                      label: {
                          normal: {
                              show: false
                          },
                          emphasis: {
                              show: false
                          }
                      },
                      labelLine: {
                          normal: {
                              show: false
                          },
                          emphasis: {
                              show: false
                          }
                      },
                      animation: false,
                      tooltip: {
                          show: false
                      },
                      itemStyle: {
                          borderColor: "#295bce"

                      },
                      data: [
                          { value: 1 }
                      ]

                  }
              ]


          })
      }
  }
}

</script>