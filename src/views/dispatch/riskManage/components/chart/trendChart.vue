<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>
  
<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    yAxisName: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption(
        {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(2, 50, 128, 0.6)'
        },
        legend: {
          itemWidth: 16,
          itemHeight: 8,
          textStyle: {
            fontSize: 12,
            fontWeight: "normal",
            color: "#fff",
          },
        },
        grid: {
          top: 20,
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          // boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#B0D7FF'
            }
          },
          axisLabel: {
            color: '#DEEBFF',
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: '#6E9DC9',
            }
          },
          data: this.xAxisData
        },
        yAxis: {
          name: '单位：万元',
          // nameLocation: 'center',
          // nameGap: 5,
          type: 'value',
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#DEEBFF',
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#6E9DC9',
            }
          },
        },
        series: [
          {
            name: '2022年',
            type: 'line',
            symbol: 'circle',
            barWidth: 13,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  {offset: 0, color: '#3EA87D'},
                  {offset: 1, color: '#C9C84E'}
                ]
              )
            },
            data: this.seriesData[0]
          },
          {
            name: '2023年',
            type: 'line',
            color: '#11E6FF',
            symbol: 'circle',
            data: this.seriesData[1]
          }
        ]
        },
        {
          notMerge: true
        }
      )
    }
  }
}
</script>
  