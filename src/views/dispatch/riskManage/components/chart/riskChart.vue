<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
      className: {
          type: String,
          default: 'chart'
      },
      width: {
          type: String,
          default: '100%'
      },
      height: {
          type: String,
          default: '100%'
      },
      seriesData: {
          type: Array,
          default: () => []
      },
      xAxisData: {
          type: Array,
          default: () => []
      },
      legendData: {
          type: Array,
          default: () => []
      }
  },
  data() {
      return {
          chart: null,

      }
  },
  watch: {
      seriesData: {
          deep: true,
          handler(val) {
              this.initChart()
          }
      }
  },
  mounted() {
      this.$nextTick(() => {
          this.initChart()
      })
  },
  beforeDestroy() {
      if (!this.chart) {
          return
      }
      this.chart.dispose()
      this.chart = null
  },
  methods: {
      initChart() {
          this.chart = echarts.init(this.$el)
          this.chart.setOption({
              tooltip: {
                  trigger: 'item'
              },
              title: {
                  text: "386条",
                  top: "40%",
                  subtext: "风险总数",
                  x: "center",
                  y: "center",
                  textStyle: {
                      fontSize: 25,
                      color: ["#00cfff"]
                  },
                  subtextStyle: {
                      color: "#fff",

                      fontSize: 14,
                  },
              },
              grid: {
                  bottom: 150,
                  left: 0,
                  right: "10%"
              },
              legend: {
                  show: false
              },

              series: [
                  //主要显示层
                  {
                      color: ['#496dff', '#ede497', '#00e4ff'],
                      type: 'pie',
                      radius: ['55%', '69%'],
                      center: ['50%', '50%'],
                      labelLine: {
                          length: 30,
                          length2: 60,
                          lineStyle: {
                              color: '#deebff'
                          }
                      },
                      label: {
                          // '{top | {b}} \n {c}万m³({d}%)'
                          formatter: '{top|{b}} \n {bottom|{c}}({d}%)',
                          padding: -50,
                          // distanceToLabelLine: 5,
                          rich: {
                              top: {
                                  padding: 5
                              },
                              bottom: {
                                  padding: 5
                              }
                          }
                      },
                      data: [
                          {
                              name: '管道事故',
                              value: 174
                          },
                          {
                              name: '危害化学物',
                              value: 115
                          },
                          {
                              name: '生活垃圾',
                              value: 97
                          }
                      ]
                  },
                  //边框设置
                  {
                      radius: ["41%", "41.3%"],
                      center: ["50%", "50%"],
                      type: "pie",
                      label: {
                          normal: {
                              show: false
                          },
                          emphasis: {
                              show: false
                          }
                      },
                      labelLine: {
                          normal: {
                              show: false
                          },
                          emphasis: {
                              show: false
                          }
                      },
                      animation: false,
                      tooltip: {
                          show: false
                      },
                      itemStyle: {
                          borderColor: "#295bce"

                      },
                      data: [
                          { value: 1 }
                      ]

                  }
              ]


          })
      }
  }
}

</script>