<template>
    <div class="WqPass">
        <div class="title">
            <span class="text"> 水质合格率 </span>
        </div>
        <div class="PassContent">
            <!-- 仪表盘 -->
            <div class="gaugeContent">
                <GaugeChart :rateNumber="waterQualityRate" height="160px" />
            </div>
            <div class="PassInfo">
                <div class="infoItem">
                    <div class="infoItemleft">
                        总数量
                        <span>664</span>
                    </div>
                    <div class="infoItemright">
                        合格量
                        <span>664</span>
                    </div>
                </div>
                <div class="infoItem">
                    <div class="infoItemleft">
                        不合格量
                        <span>0</span>
                    </div>
                </div>


            </div>
        </div>
    </div>
</template>

<script>
import GaugeChart from '../chart/gaugeChart.vue'

export default {
    name: "WqPass",
    components: {
        GaugeChart
    },
    data() {
        return {
            waterQualityRate: 10,
        }
    }
};
</script>

<style lang="scss" scoped>
.WqPass {
    width: 450px;
    height: 370px;
    margin-left: 30px;
    background-color: rgba(4, 15, 45, 0.3);

    .title {
        line-height: 44px;
        height: 44px;
        background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
        font-size: 18px;

        span {
            font-family: Microsoft YaHei;
            margin-left: 30px;
            height: 17px;
            line-height: 18px;
        }

        .text {
            background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .PassContent {
        margin: 20px;
        height: calc(100% - 75px);

        .gaugeContent {
            margin-top: 30px;
            width: 100%;
            height: 170px;
        }

        .PassInfo {
            height: calc(100% - 180px);
            margin-top: 10px;

            .infoItem {
                background: url('~@/assets/img/leakageLoss/bottom.png') no-repeat;
                height: 60px;
                line-height: 44px;
                font-size: 12px;

                .infoItemleft {
                    float: left;
                    width: 225px;
                    height: 100%;
                    margin-left: 10px;
                    span {
                        float: right;
                    }
                }
                .infoItemright {
                    width: 120px;
                    height: 100%;
                    float: left;
                    margin-left: 25px;
                    span {
                        float: right;
                    }
                }
            }
        }
    }
}
</style>