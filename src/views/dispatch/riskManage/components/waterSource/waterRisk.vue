<template>
    <!-- 水源风险分析 -->
  <div class="WaterRisk">
    <div class="title">
      <span class="text"> 水源风险分析 </span>
    </div>
    <div class="ChartContent">
        <RiskChart/>
    </div>
  </div>
</template>

<script>
import RiskChart from "../chart/riskChart.vue"
export default {
  name: "WaterRisk",
  components:{
    RiskChart
  },
};
</script>

<style lang="scss" scoped>
.WaterRisk {
  width:480px;
  height: 95%;
  background-color: rgba(4, 15, 45, 0.3);
  .title {
    line-height: 44px;
    height: 44px;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;
    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }

    .text {
      background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .ChartContent{
    height:70%;
    margin-top: 10px;
 
  }
}
</style>