<template>
    <div class="WaterQuality">
        <div class="title">
            <span class="text">
                水源水质
            </span>
        </div>
        <div class="tableBox">
            <div class="listHeader" style="text-align: center;">
                <span>序号</span>
                <span>水质类型</span>
                <span>温度</span>
                <span>ph</span>
                <span>电导率</span>
                <span>浊度</span>
                <span>余氯</span>
            </div>
            <div class="listItem" v-for="(item, index) in tableData" :key="item.id">
                <div class="small">{{ index }}</div>
                <div class="small" >{{ item.type }}</div>
                <div class="small">{{ item.temp }}</div>
                <div class="small">{{ item.ph }}</div>
                <div class="small">{{ item.ec }}</div>
                <div class="small">{{ item.ntu }}</div>
                <div class="small">{{ item.chlorine }}</div>
            </div>
        </div>

    </div>
</template>

<script>
export default {
    name: 'WaterQuality',
    data() {
        return {
            tableData: [
                { type: '地表水', temp: 124+'°C',ph:7.2 ,ec:24.1+'μS/cm',ntu:0.21 + 'NTU',chlorine:1.1+'mg/L'},
                { type: '饮用水', temp: 124+'°C',ph:7.2 ,ec:24.1+'μS/cm',ntu:0.21 + 'NTU',chlorine:1.1+'mg/L'},
                { type: '地下水', temp: 124+'°C',ph:7.2 ,ec:24.1+'μS/cm',ntu:0.21 + 'NTU',chlorine:1.1+'mg/L'},
                { type: '工业用水', temp: 124+'°C',ph:7.2 ,ec:24.1+'μS/cm',ntu:0.21 + 'NTU',chlorine:1.1+'mg/L'},
                { type: '其他', temp: 124+'°C',ph:7.2 ,ec:24.1+'μS/cm',ntu:0.21 + 'NTU',chlorine:1.1+'mg/L'},
            ],
        }
    }
}
</script>

<style lang="scss" scoped>
.WaterQuality {
    width: 850px;
    height: 370px;
    margin-left: 20px;
    background-color: rgba(4, 15, 45, 0.3);

    .title {
        line-height: 44px;
        height: 44px;
        background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
        font-size: 18px;

        span {
            font-family: Microsoft YaHei;
            margin-left: 30px;
            height: 17px;
            line-height: 18px;
        }

        .text {
            background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .tableBox {
        margin: 40px 20px 20px 20px;
        height: calc(100% - 90px);
        overflow: hidden;
        .listHeader {
            background: url("../../../../../assets/img/table.png") center no-repeat;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #ffffff;
            line-height: 34px;
            background-size: 100% 100%;
            display: flex;
            height: 34px;

            span {
                display: block;
                width: 109px;
            }
        }

        .listItem {
            text-align: center;
            border: 1px solid rgba(2, 50, 128, 0.6);
            cursor: pointer;
            display: flex;
            padding-top: 3px;
            padding-bottom: 3px;
            font-size: 12px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #ffffff;
            line-height: 30px;
            box-shadow: 0 0 10px #023280 inset;

            .small {
                width: 109px;
            }
        }

    }

}
</style>