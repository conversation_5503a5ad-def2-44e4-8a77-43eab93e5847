<template>
  <!-- 取水量分析 -->
  <div class="WaterintakeAnalysis">
    <div class="title">
      <span class="text"> 取水量分析 </span>
    </div>
    <div class="chartContent">
      <LineChart
        :seriesData="lineChart"
        :xAxisData="time"
        yAxisName="单位：吨"
      />
    </div>
  </div>
</template>

<script>
import LineChart from "../chart/lineChart.vue";
export default {
  name: "WaterintakeAnalysis",
  components: {
    LineChart,
  },
  data() {
    return {
      lineChart: [
        {
          name: "取水量",
          type: "line",
          symbol: "none",
          smooth: true,
          lineStyle: {
            color: "#1B7EF2",
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(18, 156, 255, 0.31)",
                },
                {
                  offset: 1,
                  color: "rgba(18, 156, 255, 0)",
                },
              ],
            },
          },
          data: [],
        },
      ],
      time: [],
    };
  },
};
</script>

<style lang="scss" scoped>
.WaterintakeAnalysis {
  width: 820px;
  height: 95%;
  background-color: rgba(4, 15, 45, 0.3);
  margin-left: 10px;
  .title {
    line-height: 44px;
    height: 44px;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;
    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }

    .text {
      background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .chartContent{
    margin: 20px;
    height: 80%;
  }
}
</style>