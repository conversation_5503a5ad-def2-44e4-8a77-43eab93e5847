<template>
  <!-- 风险预警处理 -->
  <div class="RiskPolicy">
    <div class="title">
      <span class="text"> 风险预警分析 </span>
    </div>
    <div class="RiskContent">
      <div class="centerBox common-card">
        <!-- 列表 -->
        <div class="centenList">
          <div class="listItem" v-for="(item, index) in warnList" :key="index">
            <div :style="setColor(item.level)" style="width: 85px">
              {{ item.level | levelFilter }}
            </div>
            <div style="margin-right: 20px">{{ item.msg }}</div>
            <div style="width: 90px; flex-grow: 1">{{ item.time }}</div>
            <div :class="[`state${item.state}`]" style="margin-right: 8px;">{{ item.status }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "RiskPolicy",
  data() {
    return {
      warnList: [
        {
          msg: "泵站名称-1 出水压力产生把报警",
          level: 1,
          time: "08-30 13:07:04",
          state: 3,
          status: "已处理",
        },
        {
          msg: "泵站名称-1 出水压力产生把报警",
          level: 3,
          time: "08-30 13:07:04",
          state: 1,
          status: "待分配",
        },
        {
          msg: "泵站名称-1 出水压力产生把报警",
          level: 2,
          time: "08-30 13:07:04",
          state: 2,
          status: "处理中",
        },
        {
          msg: "泵站名称-1 出水压力产生把报警",
          level: 2,
          time: "08-30 13:07:04",
          state: 1,
          status: "待分配",
        },
        {
          msg: "泵站名称-1 出水压力产生把报警",
          level: 3,
          time: "08-30 13:07:04",
          state: 2,
          status: "处理中",
        },
      ],
    };
  },
  filters: {
    levelFilter(value) {
      const labelMap = {
        1: "【一级预警】",
        2: "【二级预警】",
        3: "【三级预警】",
      };
      return labelMap[value];
    },
  },
  methods: {
    setColor(level) {
      const colors = ["", "#FE230A", "#FF9900", "#FFEF00"];
      return { color: colors[level] };
    },
  },
};
</script>

<style lang="scss" scoped>
.RiskPolicy {
  width: 500px;
  height: 95%;
  margin-left: 10px;
  background-color: rgba(4, 15, 45, 0.3);
  .title {
    line-height: 44px;
    height: 44px;
    background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
    font-size: 18px;
    span {
      font-family: Microsoft YaHei;
      margin-left: 30px;
      height: 17px;
      line-height: 18px;
    }

    .text {
      background-image: -webkit-linear-gradient(270deg, #fff 41%, #31b3ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .RiskContent {
    .centerBox {
      height: 258px;
      padding: 10px;
      .listItem {
        font-size: 12px;
        display: flex;
        align-items: center;
        height: 30px;
        background-color: rgba(2, 50, 128, 0.23);
        border: 1px solid #023280;
        margin-bottom: 7px;
        padding-left: 9px;
        .state1 {
          color: #11e6ff;
        }
        .state2 {
          color: #f6bd16;
        }
        .state3 {
          color: #00fe00;
        }
      }
    }
  }
}
</style>