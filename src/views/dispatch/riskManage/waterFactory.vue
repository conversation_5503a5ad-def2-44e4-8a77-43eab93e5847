<template>
  <!-- 水厂 -->
  <div class="WaterFactory">
    <div class="upPane">
      <!-- 水厂统计 -->
      <div class="factoryStatistics">
        <div class="title">
          <span class="text"> 水厂统计 </span>
        </div>
        <div class="ChartContent">
          <FactoryChart :seriesData="seriesData" :legendData="legendData"/>
        </div>
      </div>
      <!-- 水厂水质 -->
      <FactoryQuality />
      <!-- 综合信息 -->
      <ComprehensiveInfo />
    </div>
    <div class="underPane">
      <PolicyPush />
      <PolicyTrend />
      <ProblemRank />
    </div>
  </div>
</template>

<script>
import ComprehensiveInfo from "./components/waterFactory/comprehensiveInfo.vue";
import FactoryQuality from "./components/waterFactory/factoryQuality.vue";
import PolicyPush from "./components/waterFactory/policyPush.vue";
import PolicyTrend from "./components/waterFactory/policyTrend.vue";
import ProblemRank from "./components/waterFactory/problemRank.vue";
import FactoryChart from './components/chart/factoryChart.vue'
import { getWaterFactoryStatic} from "@/api/dispatch/riskManage";

export default {
  name: "WaterFactory",
  components: {
    ComprehensiveInfo,
    FactoryQuality,
    PolicyPush,
    PolicyTrend,
    ProblemRank,
    FactoryChart
  },
  data(){
    return{
      seriesData:[],
      legendData:[]
    }
  },
  mounted(){
    this.getList()
  },
  methods:{
    getList(){
      const data ={}
      getWaterFactoryStatic(data).then(res => {
        this.seriesData = res.data.map((item) => {
          return {
            name: this.changeName(item.name),
            value: item.value,
          };
        });
        this.legendData= res.data.map((item) => this.changeName(item.name));
      })
    },
    changeName(name) {
      switch (name) {
        case "lessThan300":
          return "日处理水量小于300吨";
          break;
        case "between300To800":
          return "日处理水量在300-800吨";
          break;
        case "between800To1200":
          return "日处理水量在800-1200吨";
          break;
        case "greaterThan1200":
          return "日处理水量大于1200吨";
          break;
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.WaterFactory {
  width: 100%;
  height: 100%;
  .upPane {
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    .factoryStatistics {
      width: 520px;
      height: 95%;
      background-color: rgba(4, 15, 45, 0.3);
      .title {
        line-height: 44px;
        height: 44px;
        background: linear-gradient(270deg, #10243e 0%, #164f8f 100%);
        font-size: 18px;
        span {
          font-family: Microsoft YaHei;
          margin-left: 30px;
          height: 17px;
          line-height: 18px;
        }

        .text {
          background-image: -webkit-linear-gradient(
            270deg,
            #fff 41%,
            #31b3ff 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      .ChartContent{
        height: calc(100% - 50px);
      }
    }
  }
  .underPane {
    height: calc(100% - 400px);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
  }
}
</style>