<template>
  <div class="Experience">
    <div class="query">
      <span class="param">
        <span>站点：</span>
        <el-cascader
          v-model="sourceStation"
          :options="stationList"
          :props="propObject"
          size="medium"
          clearable>
        </el-cascader>
      </span>
      <span class="param">
        <!-- <span>时间：</span> -->
        <!-- <span style="width: 200px; display: inline-box;">
          <el-date-picker   
            :clearable="false"             
            v-model="time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-ddTHH:mm:ss"
            size="medium"
            >
          </el-date-picker>
        </span> -->
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="getList">查询</el-button>
      <el-button type="success" icon="el-icon-plus" size="medium" @click="openDialog">新增</el-button>
    </div>

    <el-table
      height="calc(100% - 106px)"
      :data="tableData"
      size="medium"
    >
      <el-table-column prop="name" label="经验名称"></el-table-column>
      <el-table-column prop="sourceStationName" label="源站点"></el-table-column>
      <el-table-column prop="destStationName" label="目标站点"></el-table-column>
      <el-table-column prop="actualAmount" label="实际调度水量(m³)"></el-table-column>
      <el-table-column label="实际调度时间">
        <template slot-scope="{ row }">
          <span>{{ row.actualStartTime + '~' + row.actualEndTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content="修改" placement="bottom">
            <i class="el-icon-edit actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;"
              @click="handleEdit(scope.row.id)" />
          </el-tooltip>
          <!-- 生成方案 -->
          <el-tooltip content="生成调度方案" placement="bottom">
            <i class="el-icon-document-add actionIcon mr-4" style="color: #e6a23c;font-size: 19px;margin-right: 15px;" @click="openCreateCaseDialog(scope.row.id)" />
          </el-tooltip>
          <el-tooltip content="删除" placement="bottom">
            <i class="el-icon-delete actionIcon" style="color: #f56c6c;font-size: 19px;margin-right: 15px;"
              @click="handleDelete(scope.row.id)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination        
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNum"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>

    <el-dialog 
      :title="isEdit ? '编辑调度经验' : '新增调度经验'"
      :visible.sync="visible"
      width="35%"
    >
      <el-form :model="baseForm" :rules="rules" ref="ruleForm" label-width="auto" size="medium">
        <el-form-item label="经验名称：" prop="name">
          <el-input v-model="baseForm.name" placeholder="请填写经验名称"></el-input>
        </el-form-item>
        <el-form-item label="源站点：">
          <el-cascader
            :disabled="isEdit"
            v-model="baseForm.source"
            :options="stationList"
            :props="propObject"
            clearable>
          </el-cascader>
        </el-form-item>
        <el-form-item label="目标站点：">
          <el-cascader
            :disabled="isEdit"
            v-model="baseForm.dest"
            :options="stationList"
            :props="propObject"
            clearable>
          </el-cascader>
        </el-form-item>

        <el-form-item label="实际调度水量(m³)：">
          <el-input v-model="baseForm.actualAmount"></el-input>
        </el-form-item>
        <el-form-item label="实际调度时间：">
          <el-date-picker
            v-model="baseForm.actualTime"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="方案描述：">
          <el-input type="textarea" v-model="baseForm.description"></el-input>
        </el-form-item>
        <div class="actionBox">
          <el-button type="primary" @click="cancel">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </el-form>
    </el-dialog>

    <el-dialog
      title="生成调度方案"
      :visible.sync="caseVisible"
      width="35%"
    >
      <el-form :model="caseForm" :rules="caseFormRules" ref="caseForm" label-width="95px" size="medium">
        <el-form-item label="方案名称：" prop="planName">
          <el-input v-model="caseForm.planName" placeholder="请填写方案名称"></el-input>
        </el-form-item>
        <el-form-item label="时间：" prop="timeRange">
          <el-date-picker
            v-model="caseForm.timeRange"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
        <div class="actionBox">
          <el-button type="primary" @click="caseVisible= false">取消</el-button>
          <el-button type="primary" @click="handleCreateCase">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getStationList } from '@/api/common'
import { getDispatchExperiencePageList, addDispatchExperience, getDispatchExperienceDetail,
  updateDispatchExperience,deleteDispatchExperienceById, dispatchExperienceToCase
} from '@/api/dispatch/stroeRegulate'

export default {
  name: 'Experience',
  data() {
    return {
      stationList: [],
      propObject: {
        children: 'list',
        label: 'name',
        value: 'id'
      },
      sourceStation: [],
      tableData: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      isEdit: false,
      visible: false,
      baseForm: {
        source: [],
        dest: [],
        time: [],
        actualTime: []
      },
      caseStatus: '',
      rules: {
        name: [{ required: true, message: '请输入经验名称', trigger: 'blur' }]
      },
      // 经验转方案
      caseVisible: false,
      caseForm: {
        timeRange: []
      },
      caseFormRules: {
        planName: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
        timeRange: [{ required: true, message: '请输入时间', trigger: 'blur' }]
      },
    }
  },
  watch: {
    visible(value) {
      if(!value) {
        this.$refs.ruleForm.clearValidate()
      }
    }
  },
  computed: {
    can() {
      return this.caseStatus === 'RUNNING' || this.caseStatus === 'FINISH'
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    this.getList()
  },
  methods: {
    initData() {
      // 站点列表
      getStationList().then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName:  item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
        }
      })
    },
    // 分页
    getList() {
      let data = {
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize
      }
      if(this.sourceStation.length > 0) {
        data.sourceStationType = this.sourceStation[0]
        data.sourceFacilityId = this.sourceStation[1]
      }
      getDispatchExperiencePageList(data).then(res => {
        const { status, count, data } = res
        if (status === 200) {
          this.pageInfo.total = count
          this.tableData = data
        }
      })
    },
    openDialog() {
      
      this.baseForm = {
        source: [],
        dest: [],
        time: [],
        actualTime: []
      }
      this.isEdit = false
      this.visible = true
    },
    handleEdit(id) {
      this.isEdit = true
      getDispatchExperienceDetail(id).then(res => {
        const { status, data } = res
        const typeMap = {
          'WATER_FACTORY': 1,
          'WATER_SOURCE': 2,
          'WATER_STORAGE_POND': 3,
          'PIPE_NETWORK': 4
        }
        if(status === 200) {
          this.baseForm = {
            id: data.id,
            name: data.name,
            source: [typeMap[data.sourceStationType], data.sourceFacilityId],
            dest: [typeMap[data.destStationType], data.destFacilityId],
            amount: data.amount,
            time: [data.startTime, data.endTime],
            actualAmount: data.actualAmount,
            actualTime: [data.actualStartTime, data.actualEndTime],
            description: data.description
          }
          this.caseStatus = data.status
          this.visible = true
        }
      })
    },
    // 打开方案转经验弹窗
    openCreateCaseDialog(experienceId) {
      this.caseVisible = true
      this.caseForm.experienceId = experienceId
    },
    // 由经验生成方案
    handleCreateCase() {
      this.$refs.caseForm.validate((vaild) => {
        if(vaild) {
          const payload = {
            experienceId:  this.caseForm.experienceId,
            planName:  this.caseForm.planName,
            start: this.caseForm.timeRange[0],
            end: this.caseForm.timeRange[1]
          }
          dispatchExperienceToCase(payload).then(res => {
            if(res.status === 200) {
              this.$message.success('生成成功')
              this.caseVisible = false
              this.getList()
              this.caseForm = {}
            } else {
              this.$message.warning('生成失败，请稍后重试')
            }
          })
        }
      })
      // this.$confirm('此操作将根据当前经验生成方案，是否继续？', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'info'
      // }).then(() => {
      //   let fd = new FormData()
      //   fd.append('experienceId', id)
      //   dispatchExperienceToCase(fd).then(res => {
      //     if(res.status === 200) {
      //       this.$message.success('生成成功')
      //     } else {
      //       this.$message.warning('生成失败，请稍后重试')
      //     }
      //   })
      // }).catch(() => {})
    },
    // 删除方案
    handleDelete(id) {
      this.$confirm('此操作将永久删除该数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        deleteDispatchExperienceById(id).then(res => {
          if(res.status === 200) {
            this.getList()
            this.$message.success('删除成功')
          } else {
            this.$message.warning('删除失败，请稍后重试')
          }
        })
      }).catch(() => {})      
    },
    cancel() {
      this.visible = false
    },
    handleSubmit() {
      this.$refs.ruleForm.validate((vaild) => {
        if (vaild) {
          // 校验通过
          let data = {
            name: this.baseForm.name,
            sourceStationType: this.baseForm.source[0],
            sourceFacilityId: this.baseForm.source[1],
            destStationType: this.baseForm.dest[0],
            destFacilityId: this.baseForm.dest[1],
            actualAmount: this.baseForm.actualAmount,
            actualStartTime: this.baseForm.actualTime[0],
            actualEndTime: this.baseForm.actualTime[1],
            description: this.baseForm.description
          }
          if(this.baseForm.id) {
            data.id = this.baseForm.id
          }
          if(this.isEdit) {
            updateDispatchExperience(data).then(res => {
              const { status, msg } = res
              if(status === 200) {
                this.$message.success('编辑调度经验成功')
                this.cancel()
                this.getList()
              } else {
                this.$message.error('编辑调度经验失败，请检查')
              }
            })
          } else {
            addDispatchExperience(data).then(res => {
              const { status, msg } = res
              if(status === 200) {
                this.$message.success('新增调度经验成功')
                this.cancel()
                this.getList()
              } else {
                this.$message.error('新增调度经验失败，请检查')
              }
            })
          }
        }
      })
      
    },
    handleSizeChange() {
      this.pageInfo.pageNum = 1
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    }
  }
}
</script>

<style lang="scss">
.Experience {
  .el-textarea__inner {
    color: #ffffff;
    background-color: #031E49 !important;
  }
}
</style>

<style lang="scss" scoped>
.Experience {
  height: 100%;
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .pagination-container {
    height: 50px;
    padding-top: 14px;
  }
  .actionBox {
    padding-bottom: 30px;
    display: flex;
    justify-content: center;
  }
}
</style>
