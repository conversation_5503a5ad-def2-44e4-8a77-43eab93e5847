<template>
  <div class="Plan">
    <div class="query">
      <span class="param">
        <span>预案编号：</span>
        <el-input v-model="queryForm.planCode" style="width: 200px;" size="medium"></el-input>
      </span>
      <span class="param">
        <span>预案名称：</span>
        <el-input v-model="queryForm.planName" style="width: 200px;" size="medium"></el-input>
      </span>
      <span class="param">
        <span>关联工单：</span>
        <el-select v-model="queryForm.relatedWork" style="width: 200px;" size="medium">
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="handleQuery">查询</el-button>
      <el-button type="success" icon="el-icon-plus" size="medium" @click="openDialog">新增</el-button>
    </div>

    <el-table
      height="calc(100% - 106px)"
      :data="tableData"
      size="medium"
    >
      <el-table-column prop="planCode" label="预案编号"></el-table-column>
      <el-table-column prop="planName" label="预案名称"></el-table-column>
      <el-table-column prop="suggestions" label="预案建议"></el-table-column>
      <el-table-column prop="" label="关联工单">
        <template slot-scope="{ row }">
          <span>{{ row.relatedWork ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="生效时间">
        <template slot-scope="{ row }">
          <span>{{ row.effectiveTimeStart + '~' + row.effectiveTimeEnd }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createName" label="创建人"></el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
      <el-table-column prop="" label="操作" width="190px">
          <template slot-scope="scope">
            <span class="textButton" @click="handleView(scope.row.id)"><i class="el-icon-view"></i><span>查看</span></span>
            <span class="textButton" style="padding: 0 10px;" @click="handleEdit(scope.row.id)"><i class="el-icon-edit"></i><span>编辑</span></span>
            <span class="textButton" style="color: #F56C6C;" @click="handleDelete(scope.row.id)"><i class="el-icon-delete"></i><span>删除</span></span>
          </template>
        </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination        
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNum"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>

    <el-dialog
      :title="isEdit ? '编辑预案' : '新增预案'"
      :visible.sync="visible"
      width="75%"
      @close="resetForm"
    >
      <div class="sectionTitle">调度预案信息</div>
      <el-form :model="baseForm" :disabled="isCheck" :rules="baseFormRules" ref="baseFormRef" label-width="auto" size="medium">
        <el-row>
          <el-col :span="8">
            <el-form-item label="预案名称：" prop="planName">
              <el-input v-model="baseForm.planName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="关联工单：" prop="relatedWork">
              <el-select v-model="baseForm.relatedWork">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="生效时间：" prop="time">
              <el-date-picker
                class="timePicker"
                v-model="baseForm.time"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                :clearable="false"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="处置建议：">
          <el-input type="textarea" v-model="baseForm.suggestions"></el-input>
        </el-form-item>
        <el-form-item label="厂站名称：" prop="stationArray">
          <el-cascader
            v-model="baseForm.stationArray"
            :options="stationList"
            :props="propObject"
          ></el-cascader>
        </el-form-item>
        <!-- <el-form-item label="厂站类型：" style="width: 50%">
          <el-select v-model="baseForm.stationType">
            <el-option v-for="t in stationTypeList" :key="t.value" :label="t.label" :value="t.value"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="比较关系：" prop="conditionAnd" style="width: 50%">
          <el-select v-model="baseForm.conditionAnd">
            <el-option label="并且(and)要求所有条件都满足" :value="true"></el-option>
            <el-option label="或者(or)则只要求至少有一个条件被满足" :value="false"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

    <div class="sectionTitle">预案规则</div>
    <el-button type="primary" @click="addRuleItem" size="mini" style="margin-bottom: 10px;">添加规则</el-button>
    <div class="ruleParamListBox">
      <!-- 规则列表 -->
      <div class="item" v-for="item, index in ruleList" :key="index">
        <el-row>
          <el-col :span="4">
            <el-select v-model="item.key">
              <el-option v-for="r in ruleParamList" :key="r.value" :label="r.label" :value="r.value"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="item.compare">
              <el-option v-for="c in compareList" :key="c.value" :label="c.label" :value="c.value"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input v-model="item.value"></el-input>
          </el-col>
          <!-- <el-col :span="4">
            <el-select v-model="item.c2">
              <el-option v-for="c in compareList" :key="c.value" :label="c.label" :value="c.value"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input v-model="item.value2"></el-input>
          </el-col> -->
          <el-col :span="4">
            <el-button type="text" @click="deleteRuleItem(index)">删除</el-button>
          </el-col>
        </el-row>
      </div>
      <div>
        <el-select class="field" v-model="baseForm.warnDuration">
          <el-option v-for="item in warnIntervalList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <div>
          <span style="color: #fff;">是否启用：</span>
          <el-switch
            v-model="baseForm.enable"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0">
          </el-switch>
        </div>
      </div>
    </div>

      <!-- <div class="sectionTitle">绑定动态阈值</div>
      <el-button type="primary" @click="openTriggerConditionDialog" style="margin-bottom: 10px;">新增触发条件</el-button>
      <el-table :data="conditionList">
        <el-table-column label="厂站名称" prop="stationName"></el-table-column>
        <el-table-column label="设备名称" prop=""></el-table-column>
        <el-table-column label="比较关系" prop="compare"></el-table-column>
        <el-table-column label="预案规则" prop=""></el-table-column>
        <el-table-column label="是否启用" prop="enable"></el-table-column>
        <el-table-column label="操作"></el-table-column>
      </el-table> -->
      
      <div class="actionBox">
        <el-button type="primary" v-if="!isCheck" @click="cancel">取消</el-button>
        <el-button type="primary" v-if="!isCheck" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    
    <!-- 暂时废弃，将规则提升，去掉条件 -->
    <el-dialog
      title="新增触发条件"
      :visible.sync="conditionVisible"
    >
      <div class="sectionTitle">新增条件间关系</div>
      <el-form :model="conditionItem" :rules="conditionFormRules" ref="conditionFormRef" label-width="auto" size="medium">
        <el-form-item label="厂站名称：" prop="facilityId" style="width: 50%">
          <!-- <el-select v-model="conditionItem.stationType" @change="stationTypeChange">
            <el-option v-for="t in stationTypeList" :key="t.value" :label="t.label" :value="t.value"></el-option>
          </el-select> -->
          <el-cascader
            v-model="conditionItem.facilityId"
            :options="stationList"
            :props="propObject"
          ></el-cascader>
        </el-form-item>
        <!-- <el-form-item label="厂站名称：" prop="facilityId" style="width: 50%">
          <el-select v-model="conditionItem.facilityId">
            <el-option v-for="t in facilityList" :key="t.value" :label="t.label" :value="t.value"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="比较关系：" prop="compare" style="width: 50%">
          <el-select v-model="conditionItem.compare">
            <el-option label="或者(or)则只要求至少有一个条件被满足" :value="1"></el-option>
            <el-option label="或者(or)则只要求至少有一个条件被满足" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div class="sectionTitle">预案规则</div>
      <el-button type="primary" @click="addRuleItem" size="mini" style="margin-bottom: 10px;">添加规则</el-button>
      <div class="ruleParamListBox">
        <!-- 规则列表 -->
        <div class="item" v-for="item, index in conditionItem.rules" :key="index">
          <el-row>
            <el-col :span="4">
              <el-select v-model="item.param">
                <el-option v-for="r in ruleParamList" :key="r.value" :label="r.label" :value="r.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="item.c1">
                <el-option v-for="c in compareList" :key="c.value" :label="c.label" :value="c.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-input v-model="item.value1"></el-input>
            </el-col>
            <el-col :span="4">
              <el-select v-model="item.c2">
                <el-option v-for="c in compareList" :key="c.value" :label="c.label" :value="c.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-input v-model="item.value2"></el-input>
            </el-col>
            <el-col :span="4">
              <el-button type="text" @click="deleteRuleItem(index)">删除</el-button>
            </el-col>
          </el-row>
        </div>
        <div>
          <el-select class="field" v-model="conditionItem.warnInterval">
            <el-option v-for="item in warnIntervalList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <div>
            <span style="color: #fff;">是否启用：</span>
            <el-switch
              v-model="conditionItem.enable"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0">
            </el-switch>
          </div>
        </div>
      </div>

      <div class="actionBox">
        <el-button type="primary" @click="cancelAddCondition">取消</el-button>
        <el-button type="primary" @click="submitCondition">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getEnum, getStationList, getFacilityTree } from '@/api/common'
import { getOrderPageList } from '@/api/order'
import { getDispatchPlanPageList, addDispatchPlan, updateDispatchPlan, getDispatchPlanDetail, deleteDispatchPlanById } from '@/api/dispatch/dispatch'

export default {
  name: 'Plan',
  data() {
    return {
      queryForm: {
        planCode: '',
        planName: '',
        relatedWork: null
      },

      tableData: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      isEdit: false,
      isCheck: false,
      visible: false,
      baseForm: {
        planName: '',
        suggestions: '',
        relatedWork: false,
        time: [],
        stationArray: [],
        stationType: null,
        facilityId: null,
        conditionAnd: true,
        warnDuration: 0,
        enable: false
      },
      stationList: [], // 站点列表
      propObject: {
        // checkStrictly: true,
        // multiple: true,
        children: 'list',
        label: 'name',
        value: 'id'
      },
      stationTypeList: [
        { label: '水厂', value: 'WATER_FACTORY' },
        { label: '水源地', value: 'WATER_SOURCE' },
        { label: '蓄水池', value: 'WATER_STORAGE_POND' },
        { label: '管网', value: 'PIPE_NETWORK' },
        { label: '终端用户', value: 'END_USER' },
        { label: '泵站', value: 'PUMP_STATION' },
        { label: '取水口', value: 'WATER_TAKE_DAM' },
      ],
      facilityList: [],
      baseFormRules: {
        planName: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
        suggestions: [{ required: true, message: '请输入处置建议', trigger: 'blur' }],
        time: [{ required: true, message: '请选择预案生效时间', trigger: ['change'] }],
        stationArray: [{ required: true, message: '请选择厂站', trigger: ['change'] }],
      },
      ruleList: [], // 预案规则列表
      conditionList: [], // 条件列表
      conditionVisible: false,
      conditionItem: {
        // stationArray: [],
        stationType: 'WATER_FACTORY',
        facilityId: 10,
        compare: 1,
        rules: [],
        warnInterval: 0,
        enable: false
      },
      conditionFormRules: {
        stationType: [{ required: true, message: '请选择厂站类型', trigger: 'change' }],
        facilityId: [{ required: true, message: '请选择厂站', trigger: 'change' }],
        // deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
        compare: [{ required: true, message: '请选择比较关系', trigger: 'change' }],
      },
      ruleParamList: [
        { label: '压力', value: 'pressure' },
        { label: '瞬时流量', value: 'flow' },
        { label: '液位', value: 'waterLevel' },
        { label: '余氯', value: 'chlorine' },
        { label: 'ph值', value: 'ph' },
        { label: '浊度', value: 'turbidity' }
      ],
      compareList: [
        { label: '<', value: 1 },
        { label: '>', value: 2 },
        { label: '≤', value: 3 },
        { label: '≥', value: 4 },
        { label: '=', value: 5 },
        { label: '≠', value: 6 },
        // { label: '小于', value: 'lt' },
        // { label: '小于等于', value: 'le' },
        // { label: '等于', value: 'eq' },
        // { label: '大于等于', value: 'ge' },
        // { label: '大于', value: 'gt' },
        // { label: '不等于', value: 'ne' }
      ],
      warnIntervalList: [
        { label: '立即告警', value: 0 },
        { label: '5分钟后告警', value: 300 },
        { label: '10分钟后告警', value: 600 },
        { label: '15分钟后告警', value: 900 },
        { label: '30分钟后告警', value: 1800 },
        { label: '1小时后告警', value: 3600 },
      ]
    }
  },
  created() {
    // this.initData()
  },
  mounted() {
    this.initData()
    this.getList()
  },
  methods: {
    initData() {
      getStationList().then(res => {
        let list = res.data || []
        this.stationList = list.map((item, index) => {
          return {
            name: item.sourceName,
            sourceName: item.sourceName,
            sourceType: item.sourceType,
            id: item.sourceType,
            list: item.list
          }
        })
      })
    },
    // 分页
    getList() {
      let data = {
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize,
        ...this.queryForm
      }
      getDispatchPlanPageList(data).then(res => {
        const { status, count, data } = res
        if (status === 200) {
          this.pageInfo.total = count
          this.tableData = data
        }
      })
    },
    // 查询按钮
    handleQuery() {
      this.pageInfo.pageNum = 1
      this.getList()
    },
    // 打开弹窗
    openDialog() {
      this.isCheck = false
      this.isEdit = false
      this.visible = true
    },
    // 查看按钮操作
    handleView(id) {
      this.isCheck = true
      // this.handleEdit(id)
      getDispatchPlanDetail(id).then(res => {
        const { status, data } = res
        if(status === 200) {
          this.baseForm = {
            id: data.id,
            planName: data.planName,
            suggestions: data.suggestions,
            time: [data.effectiveTimeStart, data.effectiveTimeEnd],
            relatedWork: data.relatedWork,
            stationType: data.stationType,
            conditionAnd: data.conditionAnd,
            warnDuration: data.warnDuration,
            enable: data.enable
          }
          // this.ruleList = data.rules
          this.visible = true
        }
      })
    },
    // 重置表单
    resetForm() {
      this.baseForm = {
        planName: '',
        suggestions: '',
        relatedWork: false,
        time: [],
        stationType: null,
        facilityId: null,
        conditionAnd: true,
        warnDuration: 0,
        enable: false
      }
      if(this.$refs.baseFormRef) {
        this.$refs.baseFormRef.resetFields()
      }
    },
    // 编辑按钮操作
    handleEdit(id) {
      this.isEdit = true
      this.isCheck = false
      getDispatchPlanDetail(id).then(res => {
        const { status, data } = res
        if(status === 200) {
          this.baseForm = {
            id: data.id,
            planName: data.planName,
            suggestions: data.suggestions,
            time: [data.effectiveTimeStart, data.effectiveTimeEnd],
            relatedWork: data.relatedWork,
            stationType: data.stationType,
            conditionAnd: data.conditionAnd,
            warnDuration: data.warnDuration,
            enable: data.enable
          }
          this.visible = true
        }
      })
    },
    // 删除按钮操作
    handleDelete(id) {
      this.$confirm('此操作将永久删除该数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        deleteDispatchPlanById(id).then(res => {
          if(res.status === 200) {
            this.getList()
            this.$message.success('删除成功')
          } else {
            this.$message.warning('删除失败，请稍后重试')
          }
        })
      }).catch(() => {})      
    },
    // 弹窗取消按钮
    cancel() {
      this.visible = false
    },
    // 弹窗确定按钮
    handleSubmit() {
      this.$refs.baseFormRef.validate((vaild) => {
        if (vaild) {
          // 校验通过
          let data = {
            planName: this.baseForm.planName,
            relatedWork: this.baseForm.relatedWork,
            suggestions: this.baseForm.suggestions,
            effectiveTimeStart: this.baseForm.time[0],
            effectiveTimeEnd: this.baseForm.time[1],
            // stationType: this.baseForm.stationType,
            stationType: this.stationTypeList[this.baseForm.stationArray[0] - 1].value,
            facilityId: this.baseForm.stationArray[1],
            rules: this.ruleList,
            warnDuration: this.baseForm.warnDuration,
            enable: this.baseForm.enable
          }
          console.log('form', data)
          if(this.baseForm.id) {
            data.id = this.baseForm.id
          }
          if(this.isEdit) {
            // 编辑
            updateDispatchPlan(data).then(res => {
              console.log('edit', res)
              const { status, msg } = res
              if(status === 200) {
                this.$message.success('编辑调度方案成功')
                this.cancel()
                this.getList()
              } else {
                this.$message.error('编辑调度方案失败，请检查')
              }
            })
          } else {
            // 新增
            addDispatchPlan(data).then(res => {
              console.log('add', res)
              const { status, msg } = res
              if(status === 200) {
                this.$message.success('新增调度方案成功')
                // 确认方案
                this.cancel()
                this.getList()
              } else {
                this.$message.error('新增调度方案失败，请检查')
              }
            })
          }
        }
      })
      
    },
    // 打开触发条件弹窗
    openTriggerConditionDialog() {
      this.conditionVisible = true
    },
    // 站点类型变化
    stationTypeChange(value) {
      console.log(value)
      getStationList(value).then(res => {
        this.facilityList = res.data || []
      })
    },
    // 增加一个规则
    addRuleItem() {
      this.ruleList.push({
        key: 'pressure',
        compare: 1,
        value: null
      })
      // this.conditionItem.rules.push({
      //   param: 'pressure',
      //   c1: null,
      //   value1: null,
      //   c2: null,
      //   value2: null
      // })
      // this.conditionItem.warnInterval = 0
      // this.conditionItem.enable = false
    },
    // 删除规则
    deleteRuleItem(index) {
      console.log(index)
      this.conditionItem.rules.splice(index, 1)
    },
    // 取消
    cancelAddCondition() {
      this.conditionVisible = false
    },
    // 
    submitCondition() {
      this.$refs.conditionFormRef.validate((vaild) => {
        if (vaild) {
          // 校验通过
          console.log('conditionItem', this.conditionItem)
          this.conditionList.push(this.conditionItem)
        }
      })
    },
    // 分页
    handleSizeChange() {
      this.pageInfo.pageNum = 1
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    }
  }
}
</script>

<style lang="scss">
.Plan {
  .el-textarea__inner {
    color: #ffffff;
    background-color: #031E49 !important;
  }
  .el-range-editor.is-disabled input {
    background-color: transparent;
  }
}
</style>

<style lang="scss" scoped>
.Plan {
  height: 100%;
  .textButton {
    color: #409eff;
    cursor: pointer;
    span {
      font-size: 13px;
      padding-left: 5px;
    }
  }
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .sectionTitle {
    font-size: 18px;
    color: #fff;
    font-weight: bold;
    padding-bottom: 10px;
  }
  .timePicker {
    width: 370px;
  }
  .ruleParamListBox {
    .item {
      margin-bottom: 5px;
    }
    .field {
      width: 200px;
    }
  }
  .pagination-container {
    height: 50px;
    padding-top: 14px;
  }
  .actionBox {
    padding-bottom: 30px;
    display: flex;
    justify-content: center;
  }
}
</style>
