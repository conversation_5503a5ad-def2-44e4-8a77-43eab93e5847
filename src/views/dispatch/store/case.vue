<template>
  <div class="Case">
    <div class="query">
      <span class="param">
        <span>源站点：</span>
        <el-cascader
          v-model="sourceStation"
          :options="stationList"
          :props="propObject"
          size="medium"
          clearable>
        </el-cascader>
      </span>
      <span class="param">
        <!-- <span>时间：</span> -->
        <!-- <span style="width: 200px; display: inline-box;">
          <el-date-picker   
            :clearable="false"             
            v-model="time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-ddTHH:mm:ss"
            size="medium"
            >
          </el-date-picker>
        </span> -->
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="getList">查询</el-button>
      <el-button type="success" icon="el-icon-plus" size="medium" @click="openDialog">新增</el-button>
    </div>

    <el-table
      height="calc(100% - 106px)"
      :data="tableData"
      size="medium"
    >
      <el-table-column prop="name" label="方案名称"></el-table-column>
      <el-table-column prop="sourceStationName" label="源站点"></el-table-column>
      <el-table-column prop="destStationName" label="目标站点"></el-table-column>
      <el-table-column prop="amount" label="计划调度水量(m³)"></el-table-column>
      <el-table-column label="计划调度时间">
        <template slot-scope="{ row }">
          <span>{{ row.startTime + '~' + row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content="修改" placement="bottom">
            <i class="el-icon-edit actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;"
              @click="handleEdit(scope.row.id)" />
          </el-tooltip>
          <!-- UN_CONFIRM 状态才能确认方案 -->
          <el-tooltip content="确认方案" placement="bottom">
            <i class="el-icon-check actionIcon mr-4" style="color: #67C23A;font-size: 19px;margin-right: 15px;"
              v-show="scope.row.status === 'UN_CONFIRM'"
              @click="handleConfirm(scope.row.id)" />
          </el-tooltip>
          <!-- CONFIRMED 状态才能生成工单 -->
          <el-tooltip content="生成工单" placement="bottom">
            <i class="el-icon-document-add actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;" v-show="scope.row.status === 'CONFIRMED'"  @click="handleCreateOrder(scope.row.id)" />
          </el-tooltip>
          <!-- FINISH 生成调度经验 -->
          <el-tooltip content="生成调度经验" placement="bottom">
            <i class="el-icon-document-add actionIcon mr-4" style="color: #e6a23c;font-size: 19px;margin-right: 15px;" v-show="scope.row.status === 'FINISH'"  @click="openCreateExperienceDialog(scope.row.id)" />
          </el-tooltip>
          <!-- RUNNING FINISH 不能删除-->
          <el-tooltip content="删除" placement="bottom">
            <i class="el-icon-delete actionIcon" style="color: #f56c6c;font-size: 19px;margin-right: 15px;"
              v-show="!(scope.row.status === 'RUNNING' || scope.row.status === 'FINISH')"
              @click="handleDelete(scope.row.id)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination        
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNum"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>

    <el-dialog 
      :title="isEdit ? '编辑' : '新增'"
      :visible.sync="visible"
      width="35%"
    >
      <el-form :model="baseForm" :rules="rules" ref="ruleForm" label-width="auto" size="medium">
        <el-form-item label="方案名称：" prop="name">
          <el-input v-model="baseForm.name"></el-input>
        </el-form-item>
        <el-form-item label="源站点：" prop="source">
          <el-cascader
            :disabled="isEdit"
            v-model="baseForm.source"
            :options="stationList"
            :props="propObject"
            clearable>
          </el-cascader>
        </el-form-item>
        <el-form-item label="目标站点：" prop="dest">
          <el-cascader
            :disabled="isEdit"
            v-model="baseForm.dest"
            :options="stationList"
            :props="propObject"
            clearable>
          </el-cascader>
        </el-form-item>
        <!-- RUNNING 不能编辑建议，能编辑实际 -->
        <!-- FINISH 所有都不能编辑 -->
        <template v-if="!can">
          <el-form-item label="建议调度水量(m³)：" prop="amount">
            <el-input v-model="baseForm.amount" ></el-input>
          </el-form-item>
          <el-form-item label="建议调度时间：" prop="time">
            <el-date-picker
              v-model="baseForm.time"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
        </template>

        <template v-if="caseStatus !== 'FINISH'">
          <el-form-item label="实际调度水量(m³)：" prop="actualAmount">
            <el-input v-model="baseForm.actualAmount"></el-input>
          </el-form-item>
          <el-form-item label="实际调度时间：" prop="actualTime">
            <el-date-picker
              v-model="baseForm.actualTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
        </template>

        <el-form-item label="方案描述：">
          <el-input type="textarea" v-model="baseForm.description"></el-input>
        </el-form-item>
        <div class="actionBox">
          <el-button type="primary" @click="cancel">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </el-form>
    </el-dialog>

    <el-dialog
      title="生成调度经验"
      :visible.sync="caseVisible"
      width="35%"
    >
      <el-form :model="caseForm" :rules="caseFormRules" ref="caseForm" label-width="95px" size="medium">
        <el-form-item label="经验名称：" prop="experienceName">
          <el-input v-model="caseForm.experienceName" placeholder="请填写经验名称"></el-input>
        </el-form-item>
        <el-form-item label="标签：">
          <el-select v-model="caseForm.tag" placeholder="请选择标签">
            <el-option
              v-for="item in tagList"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input type="textarea" v-model="caseForm.msg"></el-input>
        </el-form-item>
        <div class="actionBox">
          <el-button type="primary" @click="caseVisible= false">取消</el-button>
          <el-button type="primary" @click="handleCreateExperience">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getStationList } from '@/api/common'
import {
  getDispatchPlanPageList, addDispatchPlan, getDispatchPlanDetail,
  updateDispatchPlan, deleteDispatchPlanById, caseToExperience, caseConfirm, getTagList
} from '@/api/dispatch/stroeRegulate'
import { createOrderByCaseId } from '@/api/order'

export default {
  name: 'Case',
  data() {
    return {
      stationList: [],
      propObject: {
        children: 'list',
        label: 'name',
        value: 'id'
      },
      sourceStation: [],
      tableData: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      isEdit: false,
      visible: false,
      baseForm: {
        source: [],
        dest: [],
        time: [],
        actualTime: []
      },
      caseStatus: '',
      rules: {
        name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
        source: [{ required: true, message: '请选择源站点', trigger: 'change' }],
        dest: [{ required: true, message: '请选择目标站点', trigger: 'change' }],
        amount: [{ required: true, message: '请输入建议调度水量', trigger: 'blur' }],
        time: [{ required: true, message: '请选择建议调度时间', trigger: 'change' }],
        actualAmount: [{ required: true, message: '请输入实际调度水量', trigger: 'blur' }],
        actualTime: [{ required: true, message: '请选择实际调度时间', trigger: 'change' }]
      },
      // 方案转经验
      tagList: [],
      caseVisible: false,
      caseForm: {},
      caseFormRules: {
        experienceName: [{ required: true, message: '请输入经验名称', trigger: 'blur' }]
      },
    }
  },
  watch: {
    visible(value) {
      if(!value) {
        this.$refs.ruleForm.clearValidate()
      }
    }
  },
  computed: {
    can() {
      return this.caseStatus === 'RUNNING' || this.caseStatus === 'FINISH'
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    this.getList()
  },
  methods: {
    initData() {
      // 站点列表
      getStationList().then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName:  item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
        }
      })
      // tagList
      getTagList().then(res => {
        if(res.status === 200) {
          this.tagList = res.data
        }
      })
    },
    // 分页
    getList() {
      let data = {
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize
      }
      if(this.sourceStation.length > 0) {
        data.sourceStationType = this.sourceStation[0]
        data.sourceFacilityId = this.sourceStation[1]
      }
      getDispatchPlanPageList(data).then(res => {
        const { status, count, data } = res
        if (status === 200) {
          this.pageInfo.total = count
          this.tableData = data
        }
      })
    },
    openDialog() {
      
      this.baseForm = {
        source: [],
        dest: [],
        time: [],
        actualTime: []
      }
      this.isEdit = false
      this.visible = true
    },
    handleEdit(id) {
      this.isEdit = true
      getDispatchPlanDetail(id).then(res => {
        const { status, data } = res
        const typeMap = {
          'WATER_FACTORY': 1,
          'WATER_SOURCE': 2,
          'WATER_STORAGE_POND': 3,
          'PIPE_NETWORK': 4
        }
        if(status === 200) {
          this.baseForm = {
            id: data.id,
            name: data.name,
            source: [typeMap[data.sourceStationType], data.sourceFacilityId],
            dest: [typeMap[data.destStationType], data.destFacilityId],
            amount: data.amount,
            time: [data.startTime, data.endTime],
            actualAmount: data.actualAmount,
            actualTime: [data.actualStartTime, data.actualEndTime],
            description: data.description
          }
          this.caseStatus = data.status
          this.visible = true
        }
      })
    },
    // 确认方案
    handleConfirm(id) {
      this.$confirm('此操作将确认当前方案，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        caseConfirm(id).then(res => {
          if(res.status === 200) {
            this.$message.success('确认成功')
            this.getList()
          } else {
            this.$message.warning('确认失败，请稍后重试')
          }
        })
      }).catch(() => {})  
    },
    // 由方案生成工单
    handleCreateOrder(caseId) {
      this.$prompt('可填写工单名称', '生成工单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        console.log(value)
        let data = {
          recordId: caseId
        }
        if(value) {
          data.name = value
        }
        createOrderByCaseId(data).then(res => {
          console.log(res)
          if(res.status === 200) {
            this.$message.success('生成工单成功')
            this,this.getList()
          } else {
            this.$message.error('生成工单失败')
          }
        })
      }).catch(() => {
        console.log('cancle')
      })
    },
    // 打开方案转经验弹窗
    openCreateExperienceDialog(caseId) {
      this.caseVisible = true
      this.caseForm.planId = caseId
    },
    // 由方案生成经验
    handleCreateExperience() {
      this.$refs.caseForm.validate((vaild) => {
        if(vaild) {
          caseToExperience(this.caseForm).then(res => {
          if(res.status === 200) {
            this.$message.success('生成成功')
            this.caseVisible = false
            this.getList()
          } else {
            this.$message.warning('生成失败，请稍后重试')
          }
        })
        }
      })
      // this.$confirm('此操作将根据当前方案生成经验，是否继续？', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'info'
      // }).then(() => {
      //   let fd = new FormData()
      //   fd.append('planId', caseId)
      //   caseToExperience(fd).then(res => {
      //     if(res.status === 200) {
      //       this.$message.success('生成成功')
      //     } else {
      //       this.$message.warning('生成失败，请稍后重试')
      //     }
      //   })
      // }).catch(() => {})   
    },
    // 删除方案
    handleDelete(id) {
      this.$confirm('此操作将永久删除该数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        deleteDispatchPlanById(id).then(res => {
          if(res.status === 200) {
            this.getList()
            this.$message.success('删除成功')
          } else {
            this.$message.warning('删除失败，请稍后重试')
          }
        })
      }).catch(() => {})      
    },
    cancel() {
      this.visible = false
    },
    handleSubmit() {
      this.$refs.ruleForm.validate((vaild) => {
        if (vaild) {
          // 校验通过
          let data = {
            name: this.baseForm.name,
            sourceStationType: this.baseForm.source[0],
            sourceFacilityId: this.baseForm.source[1],
            destStationType: this.baseForm.dest[0],
            destFacilityId: this.baseForm.dest[1],
            amount: this.baseForm.amount,
            startTime: this.baseForm.time[0],
            endTime: this.baseForm.time[1],
            actualAmount: this.baseForm.actualAmount,
            actualStartTime: this.baseForm.actualTime[0],
            actualEndTime: this.baseForm.actualTime[1],
            description: this.baseForm.description
          }
          if(this.baseForm.id) {
            data.id = this.baseForm.id
          }
          if(this.isEdit) {
            updateDispatchPlan(data).then(res => {
              console.log('edit', res)
              const { status, msg } = res
              if(status === 200) {
                this.$message.success('编辑调度方案成功')
                this.cancel()
                this.getList()
              } else {
                this.$message.error('编辑调度方案失败，请检查')
              }
            })
          } else {
            addDispatchPlan(data).then(res => {
              console.log('add', res)
              const { status, msg } = res
              if(status === 200) {
                this.$message.success('新增调度方案成功')
                // 确认方案
                this.cancel()
                this.getList()
              } else {
                this.$message.error('新增调度方案失败，请检查')
              }
            })
          }
        }
      })
      
    },
    handleSizeChange() {
      this.pageInfo.pageNum = 1
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    }
  }
}
</script>

<style lang="scss">
.Case {
  .el-textarea__inner {
    color: #ffffff;
    background-color: #031E49 !important;
  }
}
</style>

<style lang="scss" scoped>
.Case {
  height: 100%;
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .pagination-container {
    height: 50px;
    padding-top: 14px;
  }
  .actionBox {
    padding-bottom: 30px;
    display: flex;
    justify-content: center;
  }
}
</style>
