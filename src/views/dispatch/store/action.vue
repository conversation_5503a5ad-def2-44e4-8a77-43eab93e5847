<template>
  <div class="action">
    <section class="typeBox">
      <el-row :gutter="20">
        <el-col :span="6" v-for="t in typeList" :key="t.type">
          <div
            class="typeCard"
            @click="clickType(t.type)"
          >
            <img :src="t.icon" alt="">
            <div class="label">{{ t.label }}</div>
            <div class="count">{{ t.doneCount + '/' + t.totalCount }}</div>
          </div>
        </el-col>
      </el-row>
    </section>

    <section class="dataBox">
      <div class="query">
        <div>
          <span>调度类型：</span>
          <el-select v-model="dispatchQueryForm.dispatchType" placeholder="请选择调度类型" size="medium" style="width: 200px;">
            <el-option
              v-for="item in dispatchTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <span class="fieldName">指令编码：</span>
          <el-input v-model="dispatchQueryForm.code" placeholder="请输入指令编码" size="medium" style="width: 200px;"></el-input>
          <span class="fieldName">指令状态：</span>
          <el-select v-model="dispatchQueryForm.status" placeholder="请选择指令状态" size="medium" style="width: 200px;">
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <span class="fieldName">指令类型：</span>
          <el-select v-model="dispatchQueryForm.type" placeholder="请选择指令类型" size="medium" style="width: 200px;">
            <el-option
              v-for="item in dispatchTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-button type="primary" icon="el-icon-search" size="medium" style="margin-left: 20px;" @click="handleQuery">查询</el-button>
          <el-button type="success" icon="el-icon-plus" size="medium" style="margin-left: 20px;" @click="handleAddCmd">新建指令</el-button>
        </div>
        <div class="paramBox">
          <div
            v-for="p in paramList"
            :key="p.key"
            class="tabButton"
            :class="{ tabButtonActive: currentParam == p.key }" 
            @click="toggleParam(p.key)"
          >
            {{ p.name }}
          </div>
        </div>
      </div>

      <!-- 表格 -->
      <!-- 调度指令 -->
      <el-table
        v-show="currentParam === 5"
        style="height: calc(100% - 106px);"
        :data="cmdTableData"
        size="medium"
      >
        <el-table-column prop="dispatchType.name" label="调度类型" width="190px"></el-table-column>
        <el-table-column prop="code" label="指令编码" width="190px"></el-table-column>
        <el-table-column prop="status.name" label="指令状态"></el-table-column>
        <el-table-column prop="type.name" label="指令类型" width="200px"></el-table-column>
        <el-table-column prop="content" label="指令内容"></el-table-column>
        <el-table-column prop="urgencyLevel.name" label="紧急程度"></el-table-column>
        <el-table-column prop="siteName" label="调度站点"></el-table-column>
        <el-table-column prop="processorName" label="处理部门/人"></el-table-column>
        <el-table-column prop="processTime" label="处理时间"></el-table-column>
        <el-table-column prop="processDescription" label="处理过程"></el-table-column>
        <el-table-column prop="" label="操作" width="190px">
          <template slot-scope="scope">
            <span class="textButton" @click="handleCmdView(scope.row)"><i class="el-icon-view"></i><span>查看</span></span>
            <span class="textButton" style="padding: 0 10px;" @click="handleCmdEdit(scope.row)"><i class="el-icon-edit"></i><span>编辑</span></span>
            <span class="textButton" style="color: #F56C6C;" @click="handleCmdDelete(scope.row)"><i class="el-icon-delete"></i><span>删除</span></span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 我的待办 已办 -->
      <el-table
        v-show="currentParam === 6 || currentParam === 7"
        style="height: calc(100% - 106px);"
        :data="myTableData"
        size="medium"
      >
        <el-table-column prop="code" label="指令编码" width="190px"></el-table-column>
        <el-table-column prop="status.name" label="指令状态"></el-table-column>
        <el-table-column prop="type.name" label="指令类型" width="200px"></el-table-column>
        <el-table-column prop="content" label="指令内容"></el-table-column>
        <el-table-column prop="urgencyLevel.name" label="紧急程度"></el-table-column>
        <el-table-column prop="siteName" label="调度站点"></el-table-column>
        <el-table-column prop="processorName" label="处理部门/人"></el-table-column>
        <el-table-column prop="createId" label="创建人"></el-table-column>
        <el-table-column prop="createTime" label="创建时间"></el-table-column>
        <el-table-column prop="" label="操作" width="190px">
          <template slot-scope="scope">
            <span class="textButton" @click="handleViewTodo(scope.row)"><i class="el-icon-view"></i><span>查看</span></span>
            <span class="textButton"
              style="padding-left: 10px;" v-if="currentParam === 6" @click="handleToDo(scope.row)"
            >
              <i class="el-icon-check"></i>
              <span>处理</span>
            </span>
          </template>
        </el-table-column>
      </el-table>

      <el-table
        v-show="currentParam === 1"
        height="calc(100% - 106px)"
        :data="tableData"
        size="medium"
      >
        <el-table-column prop="facilityName" label="站点名称"></el-table-column>
        <el-table-column prop="sourceTypeName" label="站点类别"></el-table-column>
        <el-table-column prop="calculationTime" label="计算时间" width="200px"></el-table-column>
        <el-table-column prop="restWater" label="剩余水量"></el-table-column>
        <el-table-column prop="restRunDay" label="剩余正常运转天数"></el-table-column>
        <el-table-column prop="population" label="影响人口"></el-table-column>
      </el-table>

      <el-table
        v-show="currentParam === 2"
        height="calc(100% - 106px)"
        :data="waterLevelTableData"
        size="medium"
      >
        <el-table-column prop="facilityName" label="站点"></el-table-column>
        <el-table-column prop="monitorTime" label="监测时间"></el-table-column>
        <el-table-column prop="insmName" label="关联仪器"></el-table-column>
        <el-table-column prop="waterLevelValue" label="水位(m)"></el-table-column>
      </el-table>

      <el-table
        v-show="currentParam === 3"
        height="calc(100% - 106px)"
        :data="waterQualityTableData"
        size="medium"
      >
        <el-table-column prop="name" label="站点名称"></el-table-column>
          <el-table-column prop="sourceTypeName" label="站点类别"></el-table-column>
          <el-table-column label="监测时间" width="200px">
            <template slot-scope="{ row }">
              {{ row.dataTime | filterDateTime }}
            </template>
          </el-table-column>
          <!-- <el-table-column prop="wqLevel" label="水质类别"></el-table-column> -->
          <el-table-column label="是否健康">
            <template slot-scope="{ row }">{{ row.pass ? '是' : '否' }}</template>
          </el-table-column>
          <el-table-column prop="ph" label="PH值"></el-table-column>
          <el-table-column prop="turbidity" label="浊度(NTU)"></el-table-column>
          <el-table-column prop="temp" label="水温(℃)"></el-table-column>
          <el-table-column prop="powerRate" label="电导率(μs/cm)"></el-table-column>
          <el-table-column prop="chlorine" label="余氯(mg/L)"></el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination        
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageInfo.pageNum"
          :page-sizes="[10, 20, 30, 40]"
          :page-size.sync="pageInfo.pageSize"
          layout="total, sizes, ->, prev, pager, next, jumper"
          :total="pageInfo.total">
        </el-pagination>
      </div>

    </section>

    <!-- 新增调度指令 -->
    <el-dialog
      :title="formMode === 0 ? '查看调度指令' : formMode === 1 ? '新增调度指令' : '编辑调度指令'"
      :visible.sync="cmdVisible"
      width="35%"
      @close="resetCmdForm"
    >
      <el-form :model="cmdForm" :rules="cmdFormRules" :disabled="formMode === 0" ref="cmdForm" label-width="100px" size="medium">
        <el-row>
          <el-col :span="12">
            <el-form-item label="调度类型：" prop="dispatchType">
              <el-select v-model="cmdForm.dispatchType">
                <el-option
                  v-for="item in dispatchTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指令类型：" prop="type">
              <el-select v-model="cmdForm.type">
                <el-option
                  v-for="item in cmdTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="紧急程度：" prop="urgencyLevel">
              <el-select v-model="cmdForm.urgencyLevel">
                <el-option
                  v-for="item in levelList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调度站点：" prop="station">
              <el-cascader
                v-model="cmdForm.station"
                :options="stationList"
                :props="propObject"
                size="medium"
                clearable>
              </el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="处理部门：">
              <el-cascader
                v-model="deptId"
                :options="deptTree"
                :props="{ value: 'id', label: 'label', children: 'children', checkStrictly: true }"
                @change="handleDeptChange">
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理人：" prop="processorId">
              <el-select v-model="cmdForm.processorId">
                <el-option v-for="u in userList" :key="u.userId" :value="u.userId" :label="u.nickName"></el-option>
              </el-select>
              <!-- <el-input v-model="cmdForm.processorId"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联预案：" prop="relatedPlanId">
              <el-select v-model="cmdForm.relatedPlanId">
                <el-option
                  v-for="item in planList"
                  :key="item.id"
                  :label="item.planName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="附件信息：">
          <div>{{ fileInfo.annexName }}</div>
          <el-button type="success" @click="handleTrigger('myFileInput')">上传文件</el-button>
          <div style="font-size: 14px;">支持格式：.doc .docx .pdf，文件不能超过10MB</div>
          <input type="file" @change="fileChange($event, 'myFileInput')" accept=".doc, .docx, .pdf" ref="myFileInput" class="myFileInput" />
        </el-form-item>

        <el-row>
          <el-col :span="24">
            <el-form-item label="指令内容：" prop="content">
              <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" v-model="cmdForm.content"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="actionBox">
          <el-button type="primary" v-if="formMode" @click="cancelCmd">取消</el-button>
          <el-button type="primary" v-if="formMode" @click="handleSubmitCmd">确定</el-button>
        </div>
      </el-form>
      
    </el-dialog>

    <!-- 处理待办 -->
    <el-dialog title="处理待办" :visible.sync="todoVisible"  @close="resetTodoForm">
      <section class="sectionBox">
        <div class="sectionTitle">调度内容</div>
        <div class="fieldBox">
          <div><span class="label">指令编码：</span><span>{{ dispathCmdInfo.code }}</span></div>
          <div><span class="label">指令类型：</span><span>{{ dispathCmdInfo.type.name || '' }}</span></div>
          <div><span class="label">紧急程度：</span><span>{{ dispathCmdInfo.urgencyLevel.name || '' }}</span></div>
          <div><span class="label">调度站点：</span><span>{{ dispathCmdInfo.siteName }}</span></div>
          <div><span class="label">处理部门/人：</span><span>{{ dispathCmdInfo.processorName }}</span></div>
          <div><span class="label">关联预案：</span><span>{{ dispathCmdInfo.relatedPlanName }}</span></div>
          <div><span class="label">创建人：</span><span>{{ dispathCmdInfo.createId }}</span></div>
          <div style="width: 66.6%;"><span class="label">创建时间：</span><span>{{ dispathCmdInfo.createTime }}</span></div>
          <div style="flex: 1;"><span class="label">指令内容：</span><span>{{ dispathCmdInfo.content }}</span></div>
        </div>
      </section>
      <section class="sectionBox">
        <div class="sectionTitle">附件信息</div>
        <div style="padding-left: 54px;padding-bottom: 20px;">{{ dispathCmdInfo.fileName }}</div>
      </section>
      <section class="sectionBox">
        <div class="sectionTitle">处理操作</div>
        <el-form ref="todoFormRef" :model="todoForm" label-width="auto">
            <el-form-item label="处理过程：" prop="processDescription"
              :rules="[{ required: true, message: '请输入处理过程', trigger: 'blur' }]"
            >
              <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" v-model="todoForm.processDescription"></el-input>
            </el-form-item>
        </el-form>
      </section>
      <section style="padding-left: 92px;">
        <div>
          <img v-if="fileInfo.annexPath" :src="fileInfo.annexPath" alt="" style="width: 100px;height: 100px;">
        </div>
        <el-button type="success" @click="handleTrigger('myPicInput')">上传图片</el-button>
        <input type="file" @change="fileChange($event, 'myPicInput')" accept=".jpg, .jpeg, .png" ref="myPicInput" class="myFileInput" />
      </section>
      <div class="actionBox">
        <el-button type="primary" @click="cancelTodo">取消</el-button>
        <el-button type="primary" @click="handleSubmitTodo">确定</el-button>
      </div>
    </el-dialog>

    <!-- 代办/已办 详情 -->
    <el-dialog title="详情" :visible.sync="detailVisible">
      <section class="sectionBox">
        <div class="sectionTitle">调度内容</div>
        <div class="fieldBox">
          <div><span class="label">指令编码：</span><span>{{ dispathCmdInfo.code }}</span></div>
          <div><span class="label">指令类型：</span><span>{{ dispathCmdInfo.type.name || '' }}</span></div>
          <div><span class="label">紧急程度：</span><span>{{ dispathCmdInfo.urgencyLevel.name || '' }}</span></div>
          <div><span class="label">调度站点：</span><span>{{ dispathCmdInfo.siteName }}</span></div>
          <div><span class="label">处理部门/人：</span><span>{{ dispathCmdInfo.processorName }}</span></div>
          <div><span class="label">关联预案：</span><span>{{ dispathCmdInfo.relatedPlanName }}</span></div>
          <div><span class="label">创建人：</span><span>{{ dispathCmdInfo.createId }}</span></div>
          <div style="width: 66.6%;"><span class="label">创建时间：</span><span>{{ dispathCmdInfo.createTime }}</span></div>
          <div style="flex: 1;"><span class="label">指令内容：</span><span>{{ dispathCmdInfo.content }}</span></div>
        </div>
      </section>
      <section class="sectionBox">
        <div class="sectionTitle">附件信息</div>
        <div style="padding-left: 54px;padding-bottom: 20px;">{{ dispathCmdInfo.fileName }}</div>
      </section>
      <template v-if="dispathCmdInfo.status.code === 'FINISHED'">
        <section class="sectionBox">
          <div class="sectionTitle">处理操作</div>
          <div style="padding: 0 20px;">
            <div>处理过程：{{ dispathCmdInfo.processDescription }}</div>
            <img :src="doneFile.annexPath" v-if="doneFile.annexPath" alt="" style="width: 100px;margin-top: 20px;">
          </div>
        </section>
        <section class="sectionBox">
          <div class="sectionTitle">操作记录</div>
        </section>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { getStationList } from '@/api/common'
import { getDeptTree, getUserByDeptId } from '@/api/system'

import {
  getDispatchCmdPageList, addDispatchCmd, getDispatchCmdDetail, updateDispatchCmd, deleteDispatchCmdById,
  getDispatchCmdTodoPageList, getDispatchCmdDonePageList, handleDispatchCmdTodo,
  getDispatchPlanPageList
} from '@/api/dispatch/dispatch'
import { getSupplyTypeCount } from '@/api/home/<USER>'

import { uploadFile } from '@/api/common'

import { parseTime } from '@/utils'
import _ from 'loadsh'

export default {
  name: 'Action',
  data() {
    return {
      currentType: 'type1',
      typeList: [
        {
          type: 'type1',
          label: '原水调度',
          doneCount: 0,
          totalCount: 0,
          icon: require('@/assets/img/dispatch/type1Icon.png'),
          // normalPic: require('@/assets/img/dispatch/type1.png'),
          // activePic:  require('@/assets/img/dispatch/type1_active.png')
        },
        {
          type: 'type2',
          label: '生产调度',
          doneCount: 0,
          totalCount: 0,
          icon: require('@/assets/img/dispatch/type2Icon.png'),
          // normalPic: require('@/assets/img/dispatch/type2.png'),
          // activePic:  require('@/assets/img/dispatch/type2_active.png')
        },
        {
          type: 'type3',
          label: '供水调度',
          doneCount: 0,
          totalCount: 0,
          icon: require('@/assets/img/dispatch/type3Icon.png'),
          // normalPic: require('@/assets/img/dispatch/type3.png'),
          // activePic:  require('@/assets/img/dispatch/type3_active.png')
        },
        {
          type: 'type4',
          label: '综合调度',
          doneCount: 0,
          totalCount: 0,
          icon: require('@/assets/img/dispatch/type4Icon.png'),
          // normalPic: require('@/assets/img/dispatch/type4.png'),
          // activePic:  require('@/assets/img/dispatch/type4_active.png')
        }
      ],
      sourceTypeMap: {
        1: 'WATER_FACTORY',
        2: 'WATER_SOURCE',
        3: 'WATER_STORAGE_POND',
        4: 'PIPE_NETWORK',
        5: 'END_USER',
        'WATER_FACTORY': 1,
        'WATER_SOURCE': 2,
        'WATER_STORAGE_POND': 3,
        'PIPE_NETWORK': 4,
        'END_USER': 5
      },
      dispatchTypeList: [
        { value: 'RAW_WATER', label: '原水调度' },
        { value: 'PRODUCTION', label: '生产调度' },
        { value: 'WATER_SUPPLY', label: '供水调度' },
        { value: 'COMPREHENSIVE', label: '综合调度' }
      ],
      deptId: [],
      deptTree: [], // 部门树
      userList: [], // 用户列表
      // 调度指令查询
      dispatchQueryForm: {
        dispatchType: '',
        code: '',
        status: '',
        type: ''
      },
      statusList: [
        { value: 'PROCESSING', label: '处理中' },
        { value: 'FINISHED', label: '已完结' },
      ],
      // 指令类型
      cmdTypeList: [
        { value: 'ADJUST_PROCESSING_PARAMETERS', label: '调整处理参数' },
        { value: 'START_STOP_DEVICE', label: '启动/停止设备' },
        { value: 'CHECK_DEVICE', label: '检查设备' },
        { value: 'MANUAL_SAMPLING', label: '人工取样' }
      ],
      // 紧急程度
      levelList: [
        { value: 'GENERAL', label: '一般' },
        { value: 'URGENT', label: '紧急' },
        { value: 'VERY_URGENT', label: '特急' }
      ],
      planList: [],
      cmdVisible: false,
      currentParam: 5,
      paramList: [
        // {
        //   name: '保供预测',
        //   key: 1
        // },
        // {
        //   name: '水位',
        //   key: 2
        // },
        // {
        //   name: '水质',
        //   key: 3
        // },
        // {
        //   name: '生产',
        //   key: 4
        // },
        {
          name: '调度记录',
          key: 5
        },
        {
          name: '我的待办',
          key: 6
        },
        {
          name: '我的已办',
          key: 7
        }
      ],
      // 调度指令
      cmdTableData: [],
      isEdit: false,
      formMode: 0, // 0 查看 1 新增 2 编辑
      cmdForm: {
        // code: '',
        dispatchType: null,
        type: null,
        urgencyLevel: null,
        station: [],
        processorId: null,
        relatedPlanId: null,
        content: ''
      },
      cmdFormRules: {
        dispatchType: [ { required: true, message: '请选择调度类型', trigger: 'change' } ],
        type: [ { required: true, message: '请选择指令类型', trigger: 'change' } ],
        urgencyLevel: [ { required: true, message: '请选择紧急程度', trigger: 'change' }],
        station: [ { required: true, message: '请选择调度站点', trigger: 'change' }],
        processorId: [ { required: true, message: '请选择处理人', trigger: 'change' }],
        // relatedPlanId: [ { required: true, message: '请选择关预案', trigger: 'change' }],
        content: [ { required: true, message: '请输入指令内容', trigger: 'blur' }]
      },
      fileInfo: {}, // 暂存上传成功的文件信息
      // 处理待办
      todoVisible: false,
      dispathCmdInfo: {
        type: {},
        urgencyLevel: {},
        status: {}
      }, // 待办弹窗调度指令信息
      todoForm: {
        processDescription: '',
        processPicture: ''
      },
      // 待办已办
      myTableData: [],
      detailVisible: false,
      doneFile: {},

      stationList: [],
      propObject: {
        children: 'list',
        label: 'name',
        value: 'id'
      },
      tableData: [],
      waterLevelTableData: [],
      waterQualityTableData: [],

      pageInfo: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      }
    }
  },
  filters: {
    filterDateTime(val) {
      return parseTime(val)
    }
  },
  mounted() {
    this.getCmd()
    this.initData()
  },
  methods: {
    clickType(value) {
      this.currentType = value
      if(value === 'type3') {
        
      }
    },
    // 切换
    toggleParam(value) {
      this.currentParam = value
      this.pageInfo.pageSize = 10
      this.getPageListBy()
    },
    // 初始化数据获取
    initData() {
      // 调度数量
      const year = new Date().getFullYear()
      getSupplyTypeCount({ year }).then(res => {
        let list = res.data
        list.forEach((item, index) => {
          this.typeList[index].totalCount = item.totalCount
          this.typeList[index].doneCount = item.doneCount
        })
      })
      // 部门树
      getDeptTree().then(res => {
        this.deptTree = res.data || []
      })
      this.getUserList()
      // 站点列表
      getStationList().then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName:  item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
        }
      })
      // 预案列表
      getDispatchPlanPageList({ pageNo: 1, pageSize: 999 }).then(res => {
        this.planList = res.data
      })
    },
    // 查询用户
    getUserList() {
      const params = {
        pageNo: 1,
        pageSize: 999,
        deptId: this.deptId[this.deptId.length - 1]
      }
      getUserByDeptId(params).then(res => {
        this.userList = res.rows
      })
    },
    handleDeptChange() {
      this.getUserList()
    },
    // 查询按钮
    handleQuery() {
      console.log(this.currentParam)
      this.pageInfo.pageNum = 1
      this.getPageListBy()
    },

    /* 调度指令相关 */
    handleCmdView(row) {
      this.formMode = 0
      this.cmdForm = row
      this.cmdForm.dispatchType = row.dispatchType
      this.cmdForm.status = row.status
      this.cmdForm.urgencyLevel = row.urgencyLevel
      this.cmdForm.type = row.type
      this.cmdForm.station = [this.sourceTypeMap[row.siteType], row.siteId]

      this.cmdVisible = true
    },
    handleCmdEdit(row) {
      this.isEdit = true
      this.formMode = 2
      getDispatchCmdDetail(row.id).then(res => {
        const { status, data } = res
        if(status === 200) {
          this.cmdForm = res.data
          this.cmdForm.dispatchType = data.dispatchType.code
          this.cmdForm.status = data.status.code
          this.cmdForm.urgencyLevel = data.urgencyLevel.code
          this.cmdForm.type = data.type.code
          this.cmdForm.station = [this.sourceTypeMap[data.siteType], data.siteId]

          // 处理附件
          this.fileInfo = JSON.parse(data.attachmentInfo)
          
          this.cmdVisible = true
        }
      })
    },
    handleCmdDelete(row) {
      this.$confirm('此操作将永久删除该数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        deleteDispatchCmdById(row.id).then(res => {
          if(res.status === 200) {
            this.getCmd()
            this.$message.success('删除成功')
          } else {
            this.$message.warning('删除失败，请稍后重试')
          }
        })
      }).catch(() => {})  
    },
    fileChange(event, refName) {
      const file = event.target.files[0]
      if (!file) return

      // const allowedTypes = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/pdf']
      // if(!allowedTypes.includes(file.type)) {
      //   this.$message.error('请选择正确的文件类型')
      // }

      // 上传文件
      let fd = new FormData()
      fd.append('uploadFile', file)
      uploadFile(fd).then(res => {
        if(res.status === 200) {
          this.$message.success('上传成功')
          // this.uploadForm.reportName = res.data.annexName
          this.fileInfo = res.data
          this.$refs[refName].value = null
        }
      })

    },
    handleTrigger(refName) {
      this.$refs[refName].click()
    },
    handleAddCmd() {
      this.fileInfo = {}
      this.isEdit = false
      this.formMode = 1
      this.cmdVisible = true
    },
    cancelCmd() {
      this.fileInfo = {}
      this.cmdVisible = false
    },
    handleSubmitCmd() {
      this.$refs.cmdForm.validate((vaild) => {
        if(vaild) {
          let payload = {
            ...this.cmdForm,
            siteId: this.cmdForm.station[1],
            siteType: this.sourceTypeMap[this.cmdForm.station[0]]
          }
          if(this.cmdForm.id) {
            payload.id = this.cmdForm.id
          }
          if(this.formMode === 2) {
            updateDispatchCmd(payload).then(res => {
              const { status, msg } = res
              if(status === 200) {
                this.$message.success('编辑调度指令成功')
                this.cancelCmd()
                this.getCmd()
              } else {
                this.$message.error('编辑调度指令失败，请检查')
              }
            })
          } else if(this.formMode === 1) {
            payload.attachmentInfo = JSON.stringify(this.fileInfo)
            addDispatchCmd(payload).then(res => {
                const { status, msg } = res
                if(status === 200) {
                  this.$message.success('新增调度指令成功')
                  this.cancelCmd()
                  this.getCmd()
                } else {
                  this.$message.error('新增调度指令失败，请检查')
                }
            })
          }
        }
      })
    },
    resetCmdForm() {
      this.cmdForm = {
        // code: '',
        dispatchType: null,
        type: null,
        urgencyLevel: null,
        station: [],
        processorId: null,
        relatedPlanId: null,
        content: ''
      }
      if(this.$refs.cmdForm) {
        this.$refs.cmdForm.resetFields()
      }
    },
    
    /* 待办相关方法 */
    handleViewTodo(row) {
      this.dispathCmdInfo = row
      this.dispathCmdInfo.fileName = (JSON.parse(row.attachmentInfo)).annexName || '暂无附件信息'
      this.doneFile = row.processPicture ? JSON.parse(row.processPicture) : {}
      this.detailVisible = true
    },
    handleToDo(row) {
      this.fileInfo = {}
      this.todoForm = {
        processDescription: '',
        processPicture: ''
      }
      this.dispathCmdInfo = row
      this.dispathCmdInfo.fileName = (JSON.parse(row.attachmentInfo)).annexName || '暂无附件信息'
      this.todoVisible = true
    },
    cancelTodo() {
      this.fileInfo = {}
      this.todoVisible = false
    },
    handleSubmitTodo() {
      this.$refs.todoFormRef.validate((vaild) => {
        if(vaild) {
          let payload = {
            id: this.dispathCmdInfo.id,
            processDescription: this.todoForm.processDescription
          }
          if(this.fileInfo.annexPath) {
            payload.processPicture = JSON.stringify(this.fileInfo)
          } else {
            payload.processPicture = ''
          }
          handleDispatchCmdTodo(payload).then(res => {
            const { status, msg } = res
              if(status === 200) {
                this.$message.success('处理成功')
                this.cancelCmd()
                this.getTodo()
              } else {
                this.$message.error('处理失败，请检查')
              }
          })
        }
      })
    },
    resetTodoForm() {
      this.todoForm = {
        processDescription: '',
        processPicture: ''
      }
      if(this.$refs.todoFormRef) {
        this.$refs.todoFormRef.resetFields()
      }
    },

    // 分页请求
    getPageListBy() {
      // 根据currentParam判断
      if(this.currentParam === 5) {
        this.getCmd()
      } else if(this.currentParam === 6) {
        this.getTodo()
      } else if(this.currentParam === 7) {
        this.getDone()
      }
    },
    // 查询调度指令分页数据
    getCmd() {
      let payload = {
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize,
        ...this.dispatchQueryForm
      }
      getDispatchCmdPageList(payload).then(res => {
        
        if(res.status === 200) {
          this.cmdTableData = res.data
          this.pageInfo.total = res.count
        }
      })
    },
    // 查询我的待办分页
    getTodo() {
      let payload = {
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize,
        ...this.dispatchQueryForm
      }
      getDispatchCmdTodoPageList(payload).then(res => {
        if(res.status === 200) {
          this.myTableData = res.data
          this.pageInfo.total = res.count
        }
      })
    },
    // 查询我的已办分页
    getDone() {
      let payload = {
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize,
        ...this.dispatchQueryForm
      }
      getDispatchCmdDonePageList(payload).then(res => {
        if(res.status === 200) {
          this.myTableData = res.data
          this.pageInfo.total = res.count
        }
      })
    },
    handleSizeChange(currentSize) {
      this.pageInfo.pageNum = 1
      this.getPageListBy()
    },
    handleCurrentChange(currentPage) {      
      this.getPageListBy()
    },
  }
}
</script>

<style lang="scss">
.action {
  .el-textarea__inner {
    color: #ffffff;
    background-color: #031E49 !important;
  }
}
</style>

<style lang="scss" scoped>
.action {
  height: 100%;
  .typeBox {
    .typeCard {
      width: 100%;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      cursor: pointer;
      background-image: url('~@/assets/img/dispatch/blueCardBg.png');
      background-size: 100% 120px;
      .label {
        font-size: 32px;
        background: linear-gradient(#FFFFFF 35%, #31B3FF 72%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }
      .count {
        font-family: DIN;
        font-size: 30px;
      }
    }
  }
  .textButton {
    color: #409eff;
    cursor: pointer;
    span {
      font-size: 13px;
      padding-left: 5px;
    }
  }
  .dataBox {
    height: calc(100% - 120px);
    .query {
      padding: 10px 0;
      display: flex;
      justify-content: space-between;
      .fieldName {
        padding-left: 10px;
      }
      .paramBox {
        display: flex;
        align-items: center;
      }
      .tabButton {
        background: rgba(2, 50, 128, 0.80);
        width: 90px;
        height: 30px;
        cursor: pointer;
        color: #ACC6EA;
        text-align: center;
        line-height: 30px;
        margin-left: 5px;
      }
      .tabButtonActive {
        line-height: 26px;
        color: #ffffff;
        background: linear-gradient(180deg, #2A5DD2 0%, #06245A 100%);
        border: 2px solid;
        border-image: linear-gradient(180deg, rgba(82.00000271201134, 208.0000028014183, 223.00000190734863, 1), rgba(54.00000058114529, 124.00000020861626, 174.00000482797623, 0)) 2 2;
      }
    }
    .pagination-container {
      height: 50px;
      padding-top: 14px;
    }
  }
  .actionBox {
    padding-bottom: 30px;
    display: flex;
    justify-content: center;
  }
  .sectionBox {
    color: #fff;
  }
  .sectionTitle {
    font-weight: bold;
    margin-bottom: 10px;
  }
  .fieldBox {
    display: flex;
    flex-wrap: wrap;
    div {
      width: 33.3%;
      margin-bottom: 5px;
      .label {
        color: #999;
        display: inline-block;
        width: 100px;
        text-align: right;
      }

    }
  }
  .myFileInput {
    display: none;
  }
}
</style>
