<template>
  <div class="StroeRegulate">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="dispatch" />
    <div class="tab">
      <div class="first" :class="{ active: currentType === 'tab1' }" @click="handleTabClick('tab1')">调度指令</div>
      <div class="other" :class="{ active: currentType === 'tab2' }" @click="handleTabClick('tab2')">调度预案</div>
      <!-- <div class="other" :class="{ active: currentType === 'tab3' }" @click="handleTabClick('tab3')">调度经验</div> -->
    </div>

    <div class="container">
      <Action v-if="currentType === 'tab1'" />
      <Plan v-if="currentType === 'tab2'" />
      <!-- <Case v-if="currentType === 'tab2'" /> -->
      <!-- <Experience v-if="currentType === 'tab3'" /> -->
    </div>
  </div>
</template>

<script>
// 错峰调度
import Action from './action'
import Plan from './plan'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'
// import Case from './case'
// import Experience from './experience'

export default {
  name: 'StroeRegulate',
  components: {
    // Case,
    Action,
    Plan,
    FloatingSideMenu,
    // Experience
  },
  data() {
    return {
      currentType: 'tab1',
    }
  },
  methods: {
    handleTabClick(type) {
      this.currentType = type
    }
  }
}
</script>

<style lang="scss">
.StroeRegulate {
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
  .el-switch__label--left.is-active {
    color: #13ce66;
  }
  .el-switch__label--right.is-active {
    color: #ff4949;
  }
}
</style>

<style lang="scss" scoped>
.StroeRegulate {
  padding: 0 30px;
  height: calc(100vh - 84px);
  .tab {
    color: #ffffff;    
    display: flex;
    div {
      width: 136px;
      height: 56px;
      opacity: 0.6;
      text-align: center;
      padding-top: 26px;
      cursor: pointer;
    }
    .active {
      opacity: 1;
    }
    .first {
      background-image: url('~@/assets/img/wq/first-tab.png');
      background-size: 100%;
    }
    .other {
      background-image: url('~@/assets/img/wq/other-tab.png');
      background-size: 100%;
    }
  }
  .container {
    height: calc(100% - 76px);
    color: #ffffff;
    padding: 20px;
    border: 1px solid #3084B5;
  }
}
</style>
