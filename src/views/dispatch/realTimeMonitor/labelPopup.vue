<template>
  <div class="labelPopup">
    <div class="name" :style="{ 'background-color': labelType === 1 ? '#4487FF' : '#00917F' }">{{ name }}</div>
    <div class="dataItem" v-if="this.labelType === 1">{{ speed }} m³/h</div>
    <div class="" v-else>
      <div class="dataItem" v-for="item, index in list" :key="index">{{ item }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LabelPopup',
  props: {
    name: {
      type: String,
      default: ''
    },
    labelType: {
      type: Number
    },
    speed: {
      type: [Number, String]
    },
    list: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang="scss" scoped>
.labelPopup {
  .name {
    border-radius: 4px 4px 0 0;
    padding: 5px 10px;
    // width: 80px;
    height: 30px;
    line-height: 20px;
    text-align: center;
    color: #ffffff;
  }
  .dataItem {
    text-align: center;
  }
}
</style>