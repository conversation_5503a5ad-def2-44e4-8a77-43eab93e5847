<template>
  <div class="flowRate">
    <div class="query">
      <span class="param">
        <span>站点：</span>
        <el-cascader
          style="width: 300px;"
          v-model="stationName"
          :options="stationList"
          :props="propObject"
          collapse-tags
          size="medium"
          clearable>
        </el-cascader>
      </span>
      <span class="param">
        <span>时间：</span>
        <span style="width: 200px; display: inline-box;">
          <el-date-picker   
            :clearable="false"             
            v-model="time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            size="medium"
            >
          </el-date-picker>
        </span>
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="getChartData">查询</el-button>
    </div>

    <div class="chart-container">
      <LineChart :seriesData="seriesData" :legendData="legendData" />
    </div>

    <!-- <div class="table-container"> -->
      <el-table
        height="calc(100% - 386px)"
        :data="tableData"
        size="medium"
      >
        <el-table-column prop="name" label="站点"></el-table-column>
        <el-table-column prop="insmName" label="关联仪器"></el-table-column>
        <el-table-column prop="dataTime" label="监测时间"></el-table-column>        
        <el-table-column prop="speed" label="流速(m³/h)"></el-table-column>
        <el-table-column prop="flow" label="累计流量(m³)"></el-table-column>
      </el-table>
    <!-- </div> -->

    <div class="pagination-container">
      <el-pagination        
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import LineChart from './chart/lineChart'
import { getWeekTs } from '@/utils/time'

import { getStationList } from '@/api/common'
import { getFlowRateForChart, getFlowRatePageList } from '@/api/dispatch/realTimeMonitor'

export default {
  components: { LineChart },
  data() {
    return {
      stationName: [],
      stationList: [],
      propObject: {
        multiple: true,
        children: 'list',
        label: 'name',
        value: 'id'
      },
      time: [],
      tableData: [],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      },
      seriesData: [],
      legendData: []
    }
  },
  created() {
    // this.init()
  },
  mounted() {
    this.init()
    // this.getPageList()
  },
  methods: {
    async init() {
      this.time = getWeekTs()
      await getStationList('WATER_FLOW').then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName:  item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
          // 设置默认值
          const first = this.stationList[0]
          this.stationName = [[first.id, first.list[0].id]]
        }
      })
      this.getChartData()
      this.getPageList()
    },
    getChartData() {
      let sourceNameVoList = this.stationName.map(s => {
        return {
          sourceType: s[0],
          facilityId: s[1]
        }
      })
      let data = {
        sourceNameVoList,
        beginTime: this.time[0],
        endTime: this.time[1]
      }
      getFlowRateForChart(data).then(res => {
        this.seriesData = res.data.map(item => {
          return {
            name: item.insmName,
            type: 'line',
            data: item.datas
          }
        })
        this.legendData = res.data.map(item => {
          return item.insmName
        })
      })
    },
    getPageList() {
      let sourceNameVoList = this.stationName.map(s => {
        return {
          sourceType: s[0],
          facilityId: s[1]
        }
      })
      let data = {
        sourceNameVoList,
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        beginTime: this.time[0],
        endTime: this.time[1]
      }
      getFlowRatePageList(data).then(res => {
        const { status, data, count } = res
        if(status === 200) {
          this.tableData = data
          this.pageInfo.total= count
        }
      })
    },
    handleSizeChange(size) {
      this.pageInfo.pageNum = 1
      this.getPageList()
    },
    handleCurrentChange() {
      this.getPageList()
    }
  }
}
</script>

<style lang="scss" scoped>
.flowRate {
  height: 100%;
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .chart-container {
    height: 300px;
  }
}
</style>
