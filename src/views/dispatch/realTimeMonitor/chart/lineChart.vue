<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'
import { parseTime } from  '@/utils'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        color: ['#3392FF', '#FF8D1A', '#1CFF27', '#FF5733'],
        tooltip: {
          trigger: 'axis',
          // formatter: function(params) {
          //   console.log(params)
          //   return '1'
          // }
        },
        legend: {
          textStyle: {
            color: '#fff'
          },
          data: this.legendData
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'time',
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#ACC6EA',
              width: 2
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: function(params) {
              return parseTime(params)
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#6E9DC9',
              opacity: 0.2
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#ACC6EA',
              width: 2
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#6E9DC9',
              opacity: 0.2
            }
          },
        },
        series: this.seriesData
      })
    }
  }
}
</script>