<template>
  <div class="disptch-monitor app-container">
    <div class="tab">
      <div class="first" :class="{ active: currentType === 'wl' }" @click="handleTabClick('wl')">水位</div>
      <div class="other" :class="{ active: currentType === 'flow' }" @click="handleTabClick('flow')">流量</div>
      <!-- <div class="other" :class="{ active: currentType === 'insm' }" @click="handleTabClick('insm')">设备</div> -->
    </div>
    <div class="compenent-container">
      <WaterLevel v-if="currentType === 'wl'" />
      <FlowRate v-if="currentType === 'flow'" />
      <!-- <InsmStatus v-if="currentType === 'insm'" /> -->
    </div>
  </div>
</template>

<script>
import WaterLevel from './waterLevel'
import FlowRate from './flowRate'
import InsmStatus from './insmStatus'

export default {
  components: { 
    WaterLevel,
    FlowRate,
    InsmStatus
  },
  data() {
    return {
      currentType: 'wl'
    }
  },
  methods: {
    handleTabClick(type) {
      this.currentType = type
    }
  }
}
</script>

<style lang="scss">
.disptch-monitor { 
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.disptch-monitor {
  padding: 0 30px;
  height: calc(100vh - 84px);
  .compenent-container {
    color: #ffffff;
    padding: 20px;
    height: calc(100% - 76px);
    border: 1px solid #2A5DD2;
    border-top-color: #3084B5;
  }
}
</style>
