<template>
  <div class="insmStatus">
    <div class="query">
      <span class="param">
        <span>设备类型：</span>
        
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="getData">查询</el-button>
    </div>

    <el-table
      height="calc(100% - 106px)"
      :data="tableData"
      size="medium"
    >
      <el-table-column prop="name" label="设备名称"></el-table-column>
      <el-table-column prop="name" label="设备名称"></el-table-column>
      <el-table-column prop="name" label="设备名称"></el-table-column>
      <el-table-column prop="name" label="设备名称"></el-table-column>
      <el-table-column prop="name" label="设备名称"></el-table-column>
      <el-table-column prop="name" label="设备名称"></el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination        
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getInsmPageList } from '@/api/dispatch/instrument'

export default {
  data() {
    return {
      tableData: [],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  methods: {
    getData() {
      let data = {
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize
      }
      getInsmPageList(data).then(res => {
        console.log(res)
        const { status, data, count } = res
        if(status === 200) {
          this.tableData = data
          this.pageInfo.total = count
        }
      })
    },
    // 分页
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.getData()
    },
    handleCurrentChange() {
      this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
.insmStatus {
  height: 100%;
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .pagination-container {
    height: 50px;
  }
}
</style>
