<template>
  <div class="realTimeScreen">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="dispatch" />
    <!-- <RealMap class="map" ref="realTimeMonitorMap" /> -->
    <!-- <div class="leftList setIndex">
      <div class="title">实时数据<div class="customButtom" @click="toDataPage">数据查询</div></div>
      <div class="query">
        <el-date-picker
          v-model="time"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :clearable="false"   
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 360px;"
          size="medium">
        </el-date-picker>
        <el-select style="width: 180px;" v-model="station" value-key="facilityId" @change="getList" placeholder="选择站点" size="medium">
          <el-option v-for="item in stationListTemp" :key="item.facilityId" :value="item" :label="item.name"></el-option>
        </el-select>
      </div>

      <div class="lineBox">
        <div
          class="line ellipsis"
          :class="{ active: item.value === currentSelectedLine }"
          v-for="item in lines"
          :key="item.id"
          @click="changeLine(item)">{{ item.label }}</div>
      </div>

      <div class="list">
        <div class="listHeader">
          <span style="width: 50px;text-align: center;">序号</span>
          <span style="width: 100px;">监测站点</span>
          <span style="width: 110px;">点位</span>
          <span style="width: 120px;">监测时间</span>
          <span style="width: 70px;">流量(m³/h)</span>
          <span style="width: 60px;">压力(pa)</span>
          <span style="width: 50px;">液位(m)</span>
        </div>
        <el-scrollbar class="scrollBox">
          <div class="listItem" v-for="item, index in list" :key="item.stationId" @click="handleClickItem(item.pointWrapper)">
            <div style="width: 50px;text-align: center;">{{ index < 9 ? '0' + (index + 1) : index + 1 }}</div>
            <div style="width: 110px;">{{ item.pointWrapper | filterPoint }}</div>
            <div style="width: 120px;">{{ item. dataTime | filterTime }}</div>
            <div style="width: 70px;">{{ item.speed | filterValue }}</div>
            <div style="width: 60px;">{{ item.press | filterValue }}</div>
            <div style="width: 50px;">{{ item.level | filterValue}}</div>
          </div>
        </el-scrollbar>
      </div>
      <div class="pageBox">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page.sync="pageInfo.pageNo"
          :page-size.sync="pageInfo.pageSize"
          @current-change="handleCurrentChange"
          :total="pageInfo.total">
        </el-pagination>
      </div>
    </div> -->

    <ArcMap />

    <!-- <div class="rightTree">
      <div id="treeChart"></div>
    </div> -->
  </div>
</template>

<script>
import {
  getLineList,
  getStationAllData,
  getLastDataOrderByStation,
  getVillagesByTown
} from '@/api/dispatch/realTimeMonitor'

import { getShortcutTs } from '@/utils/time'
import { parseTime, listToTree } from '@/utils'

import RealMap from './layer'
import ArcMap from './map'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'
// import Vue2OrgTree from 'vue2-org-tree'
// import 'vue2-org-tree/dist/style.css'

import echarts from 'echarts'


export default {
  name: 'RealTimeMonitor',
  components: {
    RealMap,
    ArcMap,
    FloatingSideMenu
    // Vue2OrgTree
  },
  data() {
    return {
      time: [],
      station: null,
      lines: [],
      currentSelectedLine: '',
      stationListTemp: [],
      list: [],
      source: [],
      chart: null,
      pageInfo: {
        pageSize: 20,
        pageNo: 1,
        total: 0
      }
    }
  },
  filters: {
    filterPoint(point) {
      if(point) {
        return `${(point.x).toFixed(3)},${(point.y).toFixed(3)}`
      }
      return ''
    },
    filterTime(value) {
      if(value) {
        return parseTime(value, '{y}-{m}-{d} {h}:{i}')
      }
      return ''
    },
    filterValue(value) {
      if(value != undefined && value !== '') {
        return value.toFixed(2)
      }
      return '-'
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.time = getShortcutTs()
      // await this.getLine()
      // const first = this.lines[0]
      // this.stationListTemp = first.stationList
      // this.currentSelectedLine = first.id
      // await this.getList()
      // this.drawTree()
    },
    // 获取最新数据
    async getList() {
      // const payload = {
      //   pageNo: this.pageInfo.pageNo,
      //   pageSize: this.pageInfo.pageSize,
      //   startMonitorTime: this.time[0],
      //   endMonitorTime: this.time[1]
      // }
      // if(this.station) {
      //   payload.sourceNameVoList = [
      //     {
      //       facilityId: this.station.facilityId,
      //       sourceType: this.station.sourceType
      //     }
      //   ]
      // } else {
      //   payload.sourceNameVoList = this.stationListTemp.map(item => {
      //     return {
      //       facilityId: item.facilityId,
      //       sourceType: item.sourceType
      //     }
      //   })
      // }
      // const { data, count } = await getStationAllData(payload)
      // this.list = data
      // this.pageInfo.total = count
      
      // 查询一条管线下的最新数据
      const { data } = await getLastDataOrderByStation(this.currentSelectedLine)
      this.list = data

      this.source = data.map(item => {
        return {
          name: item.stationName,
          id: item.stationId,
          parentId: item.parentId,
          monitorData: {
            type: '2',
            speed: this.fixedValue(item.speed),
            level: this.fixedValue(item.level),
            press: this.fixedValue(item.press)
          }
        }
      })
      this.source[0].parentId = null


      const tree = listToTree(this.source)
      // if(this.chart) {
      //   this.chart.dispose()
      //   this.chart = null
      //   this.drawTree(tree)
      // } else {
      //   this.drawTree(tree)
      // }
    },
    fixedValue(data) {
      if(data != undefined && data !== '') {
        return data.toFixed(2)
      }
      return '-'
    },
    async getLine() {
      const { data } = await getLineList({ orderBy: 'id', asc: true })
      console.log(data)
      this.lines = data.map(item => {
        return {
          id: item.id,
          label: item.name,
          value: item.id,
          stationList: item.stationList
        }
      })
    },
    // 绘制 treeChart
    drawTree(treeData = []) {
      this.chart = echarts.init(document.getElementById('treeChart'))
      this.chart.setOption({
        series: [
          {
            type: 'tree',
            left: 200,
            orient: "horizontal",
            name: "管网线",
            edgeShape: "polyline",
            // roam: 'move',
            roam: true,
            // symbol: "image://http://localhost:9528/nodePic.png",
            // symbol: 'image://http://localhost:9528/nodePic.png',
            // symbolSize: [160, 104],
            
            initialTreeDepth: 4,
            data: treeData,
            itemStyle: {
              normal: {
                color: '#1382E6'
              },
              emphasis: {
                borderColor: 'red'
              }
            },
            label: {
              // position: [0, 0],
              // offset: [0, 30],
              width: 180,
              height: 104,
              // backgroundColor: {
              //   color: '#333'
              // },
              // backgroundColor: {
              //   image: 'image://http://localhost:9528/nodePic.png'
              // },
              position: 'left',
              backgroundColor: {
                image: '/nodePic.png'
              },
              formatter: function(params) {
                const arr = [`{name|${params.name}}`]
                const time = params.data.monitorData.time
                const level = params.data.monitorData.level
                const speed = params.data.monitorData.speed
                const press = params.data.monitorData.press
                if(time) {
                  arr.push(`{time|监测时间：${time}}`)
                }
                if(speed) {
                  arr.push(`{label|流量：}{value|${speed}m³/h}`)
                }
                
                if(level) {
                  if(params.data.monitorData.type == 2) {
                    arr.push(`{label|液位：}{value|${level}m}`)
                  } else {

                    arr.push(`{label|水池液位：}{value|${level}m}`)
                  }
                }
                if(press) {
                  arr.push(`{label|压力：}{value|${press}Pa}`)
                }
                // console.log(arr)
                return arr.join('\n')
              },
              rich: {
                name: {
                  color: '#fff',
                  fontSize: 16,
                  fontWeight: 700,
                  width: 160,
                  padding: 10,
                  align: 'center'
                },
                time: {
                  fontSize: 12,
                  color: '#D8F0FF',
                  padding: [0, 0, 0, 10]
                  // align: 'center'
                },
                label: {
                  fontSize: 14,
                  color: '#fff',
                  // borderWidth: 1,
                  // borderColor: '#fff',
                  padding: [0, 0, 5, 10]
                },
                value: {
                  fontSize: 14,
                  color: '#00EAFF',
                  padding: [0, 0, 5, 10]
                },
              }
            },
            lineStyle: {
              color: '#1382E6'
            }
          }
        ]
      })
    },
    toDataPage() {
      this.$router.push({ name: 'Data' })
    },
    // 切换管线
    changeLine(item) {
      this.currentSelectedLine = item.value
      this.station = ''
      this.stationListTemp = item.stationList
      this.getList()
    },
    // 列表项点击
    handleClickItem(point) {
      console.log(point)
      this.$refs.realTimeMonitorMap.flyToPoint(point)
    },
    // 分页
    handleCurrentChange() {
      this.getList()
    }
  }
}
</script>

<style lang="scss">
.realTimeScreen {
  .el-scrollbar__wrap {
    overflow-x: auto;
  }
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
  .org-tree-container {
    background-color: transparent;
  }
}
</style>

<style lang="scss" scoped>
.realTimeScreen {
  height: calc(100vh - 84px);
  color: #ffffff;
  padding: 0 30px;
  position: relative;
}
.map {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.setIndex {
  z-index: 1;
}
.leftList {
  position: absolute;
  width: 600px;
  // height: 100%;
  left: 20px;
  top: 20px;
  bottom: 20px;
  background-color: rgba(4, 15, 45, 0.3);
}
.rightTree {
  position: absolute;
  width: calc(100% - 640px);
  top: 0;
  bottom: 0;
  right: 0;
}
.title {
  height: 43px;
  width: 600px;
  font-family: PangMenZhengDao;
  font-size: 20px;
  padding-left: 44px;
  padding-right: 20px;
  background-image: url("~@/assets/img/dispatch/titleBg.png");
  line-height: 35px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.customButtom {
  width: 88px;
  height: 26px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  font-family: Microsoft YaHei;
  background-color: #1E49A7;
}

.query {
  padding: 20px;
  display: flex;
  justify-content: space-between;
}
.lineBox {
  padding: 0 20px;
  display: flex;
  flex-wrap: wrap;
  .line {
    width: 125px; 
    height: 36px;
    margin-bottom: 20px;
    margin-right: 20px;
    padding: 0 5px;
    font-size: 14px;
    text-align: center;
    line-height: 36px;
    background-image: url("~@/assets/img/dispatch/defaultLine.png");
    background-size: 100%;
    cursor: pointer;
  }
  .line:nth-child(4n) {
    margin-right: 0;
  }
  .active {
    background-image: url("~@/assets/img/dispatch/activeLine.png");
  }
}
.list {
  height: calc(100% - 297px);
  overflow-y: hidden;
  padding: 0 20px;
}
.listHeader {
  font-size: 14px;
  background-image: url("~@/assets/img/table.png");
  background-size: 100%;
  height: 34px;
  display: flex;
  align-items: center;
  span:last-child {
    // flex: 1;
  }
}
.scrollBox {
  height: calc(100% - 34px);
}
.listItem {
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  background-color: rgba(2,50,128,0.23);
  border: 1px solid #023280;
  margin-top: 5px;
  cursor: pointer;
}
.pageBox {
  height: 46px;
  padding-top: 10px;
}
#treeChart {
  width: 100%;
  height: 100%;
}
</style>
