<template>
  <div class="pointPopup">
    <i class="closeIcon el-icon-close" @click="handleClose"></i>
    <div class="title">{{ detailData.attributes.station_name || '' }}</div>
    <div class="tabs">
      <div class="tab" :class="{ activeTab: currentTab === 1 }" @click="changeTab(1)">基本信息</div>
      <div class="tab" v-if="hasMonitor" :class="{ activeTab: currentTab === 2 }" @click="changeTab(2)">实时监测</div>
      <div class="tab" v-if="hasHistory" :class="{ activeTab: currentTab === 3 }" @click="changeTab(3)">历史数据</div>
    </div>

    <div class="dataBox" v-if="currentTab === 1 || currentTab === 2">
      <div v-for="item, index in infoList" :key="index" class="infoItem">
        <div>{{ item.label }}</div>
        <div>{{ item.value }}</div>
      </div>
      <!-- <div class="infoItem" v-if="isAdmin && detailData.pointType === 'line'">
        <div>操作</div>
        <div>
          <el-button type="danger" icon="el-icon-delete" size="mini"></el-button>
        </div>
      </div> -->
    </div>

    <!-- 点位照片 -->
    <div style="padding: 0 20px;" v-if="currentTab === 1 && (detailData.pointType === 'point2' || detailData.pointType === 'point1')">
      <div style="font-size: 12px;color: #d8f0ff;margin-bottom: 5px;">点位照片</div>
      <el-image
        class="picBox"
        v-if="detailData.attributes.pic"
        :src="detailData.attributes.pic"
        fit="contain"
        :preview-src-list="[detailData.attributes.pic]"
      ></el-image>
      <div v-else class="picBox" style="line-height: 200px;text-align: center; color: #d8f0ff;">暂无点位照片</div>
    </div>

    <!-- 历史数据 -->
    <div class="historyDataBox" v-if="currentTab === 3">
      <div class="query" style="padding: 10px 0;">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="getHistoryData"
          :clearable="false"   
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 360px;"
          size="mini">
        </el-date-picker>
      </div>
      <div class="list" v-if="this.detailData.pointType === 'point1'">
        <div class="listHeader">
          <!-- <span style="width: 50px;text-align: center;">序号</span> -->
          <span style="padding-left: 5px;width: 75px;">监测时间</span>
          <span style="width: 60px;">液位(m)</span>
          <span style="width: 85px;">储水量(m³)</span>
          <span style="width: 100px;">出水流量(m³/h)</span>
          <span style="width: 100px;">出水压力(MPa)</span>
        </div>
        <div class="listItem" v-for="item, index in pageData" :key="index">
          <!-- <div style="width: 50px;text-align: center;">{{ index < 9 ? '0' + (index + 1) : index + 1 }}</div> -->
          <div style="padding-left: 5px;width: 75px;">{{ item.monitorTime | filterMonitorTime }}</div>
          <div style="width: 60px;">{{ item.waterLevelValue | filterValue }}</div>
          <div style="width: 85px;">{{ item.currentWater | filterValue }}</div>
          <div style="width: 100px;">{{ item.speed | filterValue}}</div>
          <div style="width: 100px;">{{ item.press | filterValue}}</div>
        </div>
      </div>
      <!--  -->
      <div class="list" v-if="this.detailData.pointType === 'point3'">
        <div class="listHeader">
          <span style="width: 70px;">液位(cm)</span>
          <span style="width: 80px;">压力(MPa)</span>
          <!-- <span style="width: 80px;">供水量(m³)</span> -->
          <span style="width: 80px;">泵1状态</span>
          <span style="width: 80px;">泵2状态</span>
          <span style="width: 80px;">监测时间</span>
        </div>
        <div class="listItem" v-for="item, index in pageData" :key="index">
          <div style="width: 70px;">{{ (item.poolLevel).toFixed(2) }}</div>
          <div style="width: 80px;">{{ (item.outWaterPress).toFixed(2) }}</div>
          <!-- <div style="width: 80px;">{{ '--' }}</div> -->
          <div style="width: 80px;">{{ item.pump1Run ? '是' : '否' }}</div>
          <div style="width: 80px;">{{ item.pump2Run ? '是' : '否' }}</div>
          <div style="width: 80px;">{{ item.createTime }}</div>
        </div>
      </div>

      <div class="pageBox">
        <el-pagination
          layout="prev, pager, next"
          :current-page.sync="pageInfo.pageNo"
          :page-size.sync="pageInfo.pageSize"
          @current-change="handleCurrentChange"
          :total="pageInfo.total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getAllMonitorData, getPointBaseInfo } from '@/api/home/<USER>'
import { getWaterLevelPageList, getPumpDataPageList } from '@/api/dispatch/realTimeMonitor'

import { getTodayTs } from '@/utils/time'
import { parseTime } from '@/utils'

export default {
  name: 'PointPopup',
  props: {
    detailData: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      currentTab: 1,
      infoList: [],
      pageData: [],
      timeRange: [],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  computed: {
    hasMonitor() {
      return this.detailData.pointType === 'point1' || this.detailData.pointType === 'point2'
    },
    hasHistory() {
      return this.detailData.pointType === 'point1' || this.detailData.pointType === 'point3'
    },
    isAdmin() {
      return localStorage.getItem('name') === 'admin'
    }
  },
  watch: {
    detailData: {
      handler(val) {
        this.currentTab = 1
        this.setInfoList()
      }
    }
  },
  filters: {
    filterMonitorTime(time) {
      return time ? parseTime(time, '{m}-{d} {h}:{i}') : '--'
    },
    filterValue(value) {
      return (value === null || value === '') ? '--' : value
    }
  },
  mounted() {
    this.setInfoList()
    // this.getMonitorData()
  },
  methods: {
    handleClose() {
      this.$emit('closePopup')
    },
    changeTab(tab) {
      this.currentTab = tab
      if(tab === 1) {
        this.setInfoList()
      }
      if(tab === 2) {
        // 实时监测
        this.getMonitorData()
      }
      if(tab === 3) {
        // 历史数据
        this.timeRange = getTodayTs()
        this.getHistoryData()
      }
    },
    // 查询监测数据
    getMonitorData() {
      this.infoList = []
      console.log('getMonitorData')

      const insm = this.detailData.attributes.insm
      const type = this.detailData.pointType
      const typeMap = {
        'point1': 'WATER_STORAGE_POND',
        'point2': 'WATER_FACTORY',
        'point3': 'PUMP_STATION',
        'point4': 'WATER_TAKE_DAM',
        'line': 'PIPE_NETWORK',
      }
      // 监测数据查询参数
      const query = {
        facilityId: this.detailData.attributes.id,
        stationType: typeMap[this.detailData.pointType]
      }
      // 是否有监测设备在站点上
      // if(insm) {
        
        getAllMonitorData(query).then(res => {
          let info = this.detailData.attributes
          if(type === 'point1' || type === 'point2') {
            // 蓄水池 水厂
            let baseInfo = res.data.baseInfo
            let waterLevelResp = res.data.waterLevelResp // 液位数据
            let currentV = res.data.currentWater // 当前水量

            // 根据监测液位计算当前储水量
            let height = baseInfo.depth
            let level = waterLevelResp.waterLevelValue || 0
            let storeage = baseInfo.maxStorage

            if(!height || !storeage) {
              height = '--'
            } else {
              height = height.toFixed(2)
            }
            // 水质数据
            let wq = res.data.waterQualityHistoryResp

            // let wqData = `PH${wq.ph || '--'}，浊度${wq.turbidity || '--'}NTU，余氯${wq.chlorine || '--'}mg/L，温度${wq.temp || '--'}℃`
            let wqData = `温度${wq.temp || '--'}℃，pH${wq.ph || '--'}，电导率${wq.powerRate || '--'}us/cm，浊度${wq.turbidity || '--'}NTU`

            let day = ''
            if(this.dayNeed !== '--' && currentV) {
              day = Math.ceil((currentV / this.dayNeed))
            } else {
              day = '--'
            }

            this.infoList.push({ label: '监测时间', value: waterLevelResp.monitorTime || '--' })
            this.infoList.push({ label: '蓄水池液位', value: (waterLevelResp.waterLevelValue || '--') + 'm' })
            this.infoList.push({ label: '蓄水池储水量', value: (currentV || '--') + 'm³' })
            // this.infoList.push({ label: '出水压力', value: (waterLevelResp.press ? waterLevelResp.press.toFixed(2) : '--') +'MPa' })

            // 处理出水流量
            let flows = res.data.flowResps
            flows.forEach((item, index) => {
              this.infoList.push({ label: `出水管${index + 1}`, value: `${item.speed !== '' ? item.speed.toFixed(2) : '--' }m³/h(${item.coverageDirection})` })
            })
            // this.infoList.push({ label: '出水流量', value: (waterLevelResp.speed ? waterLevelResp.speed.toFixed(2) : '--') +'m³/h' })

            this.infoList.push({ label: '水质数据', value: wqData})

            if(type === 'point2') {
              this.infoList.push({ label: '受益户数', value: (info.family || '--') + '户' })
              this.infoList.push({ label: '受益人口', value: (info.people || '--') + '人' })
            } else {
              this.infoList.push({ label: '受益户数', value: (info.home || '--') + '户' })
              this.infoList.push({ label: '受益人口', value: (info.population || '--') + '人' })
            }

            this.infoList.push({ label: '日需水量', value: (this.dayNeed) + '吨' })
            this.infoList.push({ label: 'AI预测保供天数', value: `${day}天` })
          }  else if(type === 'point3') {
            let pumpData = res.data.pumpStationDataResp
            let p1State = pumpData.pump1Run ? `运行中 ${pumpData.pump1Hz}Hz` : '未运行'
            let p2State = pumpData.pump2Run ? `运行中 ${pumpData.pump2Hz}Hz` : '未运行'

            // this.infoList.push({ label: '监测时间', value: pumpData.monitorTime || '--' })
            this.infoList.push({ label: '泵站液位', value: (pumpData.poolLevel || '--') + 'cm' })
            this.infoList.push({ label: '泵站压力', value: (pumpData.outWaterPress || '--') + 'MPa' })
            this.infoList.push({ label: '1号机运行状态', value: p1State })
            this.infoList.push({ label: '2号机运行状态', value: p2State })
          }
        })
      // }
    },
    // 设置字段 基本信息
    async setInfoList() {
      let info = this.detailData.attributes
      this.infoList = []

      // promise
      let data =  await this.getBaseInfo()

      // 基本信息
      if(this.currentTab === 1) {
        // 水厂
        if(this.detailData.pointType === 'point2') {
          let ret = this.calcHAndV(data.depth, data.maxStorage)
          // 水厂使用 people 字段
          this.dayNeed = info.people ? (info.people * 0.13).toFixed(2) : '--' // 日用水量
          this.infoList.push({ label: '站点名称', value: info.station_name })
          this.infoList.push({ label: '站点地址', value: data.address || '--' })
          this.infoList.push({ label: '蓄水池类型', value: data.waterStoragePondType || '--' })
          // this.infoList.push({ label: '所属镇村组', value: data.townGroup || '--'  })
          this.infoList.push({ label: '蓄水总方量', value: (ret[1]) + 'm³' })
          this.infoList.push({ label: '蓄水总高度', value: (ret[0]) + 'm' })
          this.infoList.push({ label: '净化设施', value: data.purificationFacilities || '--' })
          // this.infoList.push({ label: '水厂水箱', value: `尺寸(${data.waterTank || '--'})` })
          this.infoList.push({ label: '净水箱', value: '24m³' })
          // this.infoList.push({ label: '受益户数', value: (info.family || '--') + '户' })
          // this.infoList.push({ label: '受益人口', value: (info.people || '--') + '人' })
          // this.infoList.push({ label: '日需水量', value: (this.dayNeed) + '吨' })
        }
        // 蓄水池
        if(this.detailData.pointType === 'point1') {
          let ret = this.calcHAndV(data.depth, data.maxStorage)
          // 蓄水池使用 population 字段
          this.dayNeed = info.population ? (info.population * 0.13).toFixed(2) : '--' // 日用水量
          this.infoList.push({ label: '站点名称', value: info.station_name })
          this.infoList.push({ label: '站点地址', value: data.address || '--' })
          this.infoList.push({ label: '蓄水池类型', value: data.waterStoragePondType || '--' })
          // this.infoList.push({ label: '所属镇村组', value: data.townGroup || '--' })
          this.infoList.push({ label: '蓄水总方量', value: (ret[1]) + 'm³' })
          this.infoList.push({ label: '蓄水总高度', value: (ret[0]) + 'm' })
          // this.infoList.push({ label: '受益户数', value: (info.home || '--') + '户' })
          // this.infoList.push({ label: '受益人口', value: (info.population || '--') + '人' })
          // this.infoList.push({ label: '日需水量', value: (this.dayNeed) + '吨' })
        }
        // 泵站
        if(this.detailData.pointType === 'point3') {
          this.infoList.push({ label: '站点名称', value: info.station_name })
          this.infoList.push({ label: '站点地址', value: info.identity || '--' })
          // this.infoList.push({ label: '所属镇村组', value: `${info.villageName || ''}${info.village_village_group || ''}` || '--' })
          this.infoList.push({ label: '泵站总供水量', value: '--' })
        }
        // 取水口
        if(this.detailData.pointType === 'point4') {
          let typeMap = {
            0: '石河堰',
            1: '泉水堰',
            2: '取水口'
          }
          this.infoList.push({ label: '站点名称', value: info.station_name })
          this.infoList.push({ label: '站点地址', value: info.identity || '--' })
          // this.infoList.push({ label: '所属镇村组', value: `${info.villageName || ''}${info.village_village_group || ''}` || '--' })
          this.infoList.push({ label: '类型', value: typeMap[info.type] || '--' })
        }
        // 管网
        if(this.detailData.pointType === 'line') {
          let typeMap = {
            0: '引水管',
            1: '配水管',
            2: '提水管' 
          }
          this.infoList.push({ label: '管线名称', value: info.station_name })
          this.infoList.push({ label: '类型', value: typeMap[info.type] || '--' })
          this.infoList.push({ label: '材质', value: info.material || '--' })
          this.infoList.push({ label: '长度：', value: (info.length || '--') + 'm' })
          this.infoList.push({ label: '管径', value: `${info.diameter ? 'DN' + info.diameter : '--'}` })
          this.infoList.push({ label: '编码', value: info.code || '--'})
        }
      } 
    },
    // 根据高度和方量计算蓄水总高度和蓄水总方量
    calcHAndV(depth, maxStorage) {
      if (depth && maxStorage) {
        let height = depth - 0.2
        let h = height.toFixed(2)
        let v = (height / depth * maxStorage).toFixed(2)
        return [h, v]
      } else {
        return ['--', '--']
      }
    },
    // 获取基本信息
    getBaseInfo() {
      console.log('getBaseInfo')
      const type = this.detailData.pointType
      const typeMap = {
        'point1': 'WATER_STORAGE_POND',
        'point2': 'WATER_FACTORY',
        'point3': 'PUMP_STATION',
        'point4': 'WATER_TAKE_DAM',
        'line': 'PIPE_NETWORK',
      }
      const query = {
        facilityId: this.detailData.attributes.id,
        stationType: typeMap[this.detailData.pointType]
      }
      return new Promise((resolve, reject) => {
        getPointBaseInfo(query).then(res => {
          return resolve(res.data || {})
        })
      })
    },
    // 查询历史数据
    getHistoryData() {
      let info = this.detailData.attributes
      let height = info.depth - 0.2
      let storage = info.max_storage * height
      if(!this.timeRange) {
        this.timeRange = ['', '']
      }
      

      let id = this.detailData.attributes.id
      if(this.detailData.pointType === 'point1') {
        getWaterLevelPageList({
          pageNo: this.pageInfo.pageNo,
          pageSize: this.pageInfo.pageSize,
          sourceType: 'WATER_STORAGE_POND',
          facilityId: id,
          startMonitorTime: this.timeRange[0],
          endMonitorTime: this.timeRange[1]
        }).then(res => {
          this.pageData = res.data
          // this.pageData = res.data.map(item => {
          //   let v1 = (item.waterLevelValue / height * storage).toFixed(2)
          //   return {
          //     ...item,
          //     v: v1 || '--'
          //   }
          // })
          this.pageInfo.total = res.count
        })
      } else if(this.detailData.pointType === 'point3') {
        getPumpDataPageList({
          pageNo: this.pageInfo.pageNo,
          pageSize: this.pageInfo.pageSize,
          pumpStationId: id,
          beginTime: this.timeRange[0],
          endTime: this.timeRange[1]
        }).then(res => {
          this.pageData = res.data
          this.pageInfo.total = res.count
        })
      }
    },
    handleCurrentChange() {
      this.getHistoryData()
    }
  }
}
</script>

<style lang="scss">
.pointPopup {
  .el-pagination {
    color: #ffffff;
    .btn-next {
      color: #ffffff;
    }
    .btn-prev {
      color: #ffffff;
    }
  }
  .el-pagination button {
    height: 28px !important;
    margin: 0 !important;
    background-color: transparent;
  }
  .el-pager li {
    height: 28px !important;
    margin: 0 !important;
    background-color: transparent;
  }
  .el-pager li.btn-quicknext, .el-pager li.btn-quickprev {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.pointPopup {
  width: 460px;
  height: 640px;
  background-image: url('~@/assets/img/home/<USER>');
  position: absolute;
  left: 30px;
  top: 120px;
  .closeIcon {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
  }
  .title {
    height: 51px;
    padding: 13px 0 12px 19px;
    font-size: 22px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #d8f0ff;
    line-height: 26px;
    letter-spacing: 1px;
    text-shadow: 0px 0px 10px rgba(0, 145, 255, 0.5011), 0px 0px 4px #0091ff;
  }
  .tabs {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .tab {
      color: #d8f0ff;
      line-height: 34px;
      cursor: pointer;
    }
    .activeTab {
      border-bottom: 1px solid #83D1FF ;
    }
  }
  .dataBox {
    padding: 10px 20px;
    min-height: 244px;
    .infoItem {
      display: flex;
      div {
        font-size: 12px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 32px;
      }
      div:first-child {
        background: rgba(0, 57, 112, 0.5);
        border-radius: 0px 0px 0px 0px;
        width: 110px;
        padding-right: 10px;
        text-align: right;
      }
      div:last-child {
        background: rgba(4, 15, 45, 0.2);
        border-radius: 0px 0px 0px 0px;
        padding-left: 10px;
        text-align: left;
      }
    }
  }
  .picBox {
    width: 100%;
    height: 200px;
    background-color: black;
  }
  .historyDataBox {
    padding: 0 20px;
    .list {
      height: 384px;
      .listHeader {
        font-size: 14px;
        background-image: url("~@/assets/img/table.png");
        background-size: 100%;
        height: 34px;
        display: flex;
        align-items: center;
        span:last-child {
          // flex: 1;
        }
      }
      .listItem {
        font-size: 12px;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        height: 30px;
        text-align: center;
        background-color: rgba(2,50,128,0.23);
        border-bottom: 1px solid #023280;
        margin-top: 5px;
        // cursor: pointer;
      }
    }
  }
}
</style>