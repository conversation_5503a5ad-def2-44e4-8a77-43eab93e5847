<template>
  <div class="defaultPopup">
    <div class="title">
      <span>{{ name }}</span>
      <i class="el-icon-close close" @click="closePopup" />
    </div>
    <div class="line"></div>
    <div class="dataBox">
      <!-- 基本字段 -->
      <div class="content">
        <div style="color: #61f9f8;">基本信息</div>
        <div v-for="item, index in list" :key="index">{{ item }}</div>
      </div>
      <!-- 实时监测数据 -->
      <div class="monitor" v-if="hasInsm">
        <div style="color: #61f9f8;">实时数据</div>
        <div v-for="item, index in dataList" :key="index">
          <span>{{ item.name }}：</span>
          <span>{{ item.value }}</span>
        </div>
        <div class="cButton" @click="handleClick">历史数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAllMonitorData } from '@/api/home/<USER>'

export default {
  name: 'DefaultPopup',
  props: {
    name: {
      type: String,
      default: ''
    },
    id: {
      type: Number
    },
    list: {
      type: Array,
      default: () => []
    },
    monitorDataQuery: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dataList: [],
      params: []
    }
  },
  computed: {
    hasInsm() {
      return this.monitorDataQuery.insm
    }
  },
  
  created() {
    // console.log('111')
    const insm = this.monitorDataQuery.insm
    if(insm) {
      getAllMonitorData({
        facilityId: this.id,
        stationType: this.monitorDataQuery.stationType,
      }).then(res => {
        const data = res.data

        if(data.flow3in1) {
          // 计算实时方量 当前液位 / 高度 * 方量
          let level = data.flow3in1.waterLevelValue || 0
          let depth = this.monitorDataQuery.depth
          let storeage = this.monitorDataQuery.storeage
          console.log(this.monitorDataQuery)
          let v = '--'
          if(!depth || !storeage) {
            v = '--'
          } else {
            v = (level / depth * storeage).toFixed(2)
          }

          this.dataList.push({
            name: '流量',
            value: data.flow3in1.speed  + 'm³/h'
          })
          this.dataList.push({
            name: '压力',
            value: (data.flow3in1.press || '--') + 'MPa'
          })
          this.dataList.push({
            name: '液位',
            // value: data.flow3in1.waterLevelValue + 'm',
            value: `${level}m(${v}m³)`
          })
          this.params.push({
            label: '流量',
            type: 'flow',
            sourceType: this.monitorDataQuery.stationType
          })
        }

        if(data.pumpStationDataResp) {

          this.dataList.push({
            name: '水池水位',
            value: data.pumpStationDataResp.poolLevel + 'cm'
          })
          this.dataList.push({
            name: '出水压力',
            value: data.pumpStationDataResp.outWaterPress + 'MPa'
          })
          this.dataList.push({
            name: '1号泵状态',
            value: data.pumpStationDataResp.pump1Run ? `运行中 ${data.pumpStationDataResp.pump1Hz}Hz` : '未运行'
          })
          this.dataList.push({
            name: '2号泵状态',
            value: data.pumpStationDataResp.pump2Run ? `运行中 ${data.pumpStationDataResp.pump2Hz}Hz` : '未运行'
          })

          this.params.push({
            label: '泵站',
            type: 'pump',
            sourceType: '泵站机井'
          })
        }

        if(data.waterQualityHistoryResp) {
          this.dataList.push({
            name: 'ph',
            value: data.waterQualityHistoryResp.ph
          })
          this.dataList.push({
            name: '浊度',
            value: data.waterQualityHistoryResp.turbidity + 'NTU'
          })
          this.dataList.push({
            name: '电导率',
            value: data.waterQualityHistoryResp.powerRate + 'μs/cm'
          })
          this.dataList.push({
            name: '余氯',
            value: data.waterQualityHistoryResp.chlorine + 'mg/L'
          })

          this.params.push({
            label: '水质',
            type: 'waterQuality',
            sourceType: this.monitorDataQuery.stationType
          })
        }

      })
    }
  },
  methods: {
    closePopup() {
      this.$emit('close')
    },
    handleClick() {
      this.$emit('emitHistory', this.params)
    }
  }
}
</script>

<style lang="scss" scoped>
.defaultPopup {
  // width: 233px;
  padding: 20px;
  background-color: rgba(5, 58, 127, 0.4);
  border-radius: 4px;
  border: 1px solid rgb(11, 11, 136);
  .title {
    display: flex;
    justify-content: space-between;
    span {
      width: 150px;
      // height: 26px;
      font-size: 16px;
      font-weight: 400;
      color: #61f9f8;
      padding-bottom: 5px;
    }
    .close {
      cursor: pointer;
      line-height: 16px;
      color: #ffffff;
      font-size: 16px;
      pointer-events:visible;
    }
  }
  .line {
    width: calc(100% - 20px);
    height: 1px;
    background-image: linear-gradient(
      to bottom left,
      rgba(97, 249, 248, 0),
      rgba(97, 249, 248, 1)
    );
  }
  .dataBox {
    display: flex;
    font-size: 14px;
    padding-top: 10px;
  }
  .content {
    width: 140px;
    margin-right: 10px;
  }
  .monitor {
  }
  .cButton {
    width: 68px;
    height: 28px;
    background-color: #61f9f8;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
    line-height: 28px;
    margin-right: 10px;
    cursor: pointer;
  }
}
</style>