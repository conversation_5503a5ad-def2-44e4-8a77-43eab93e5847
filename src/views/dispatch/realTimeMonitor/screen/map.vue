<template>
  <div class="RealTimeMap">
    <div id="mapBox"></div>

    <div class="topBox">
      <div class="item" v-for="item in categoryList" :key="item.name" 
        :class="{ active: currentCategory === item.id }" @click="chooseCategory(item.id)">
        <span>{{ item.name }}</span>
        <div class="pointList" v-show="item.hasPoint && currentCategory === item.id">
          <div class="pointItem" v-for="p in item.pointList" :key="p">{{ p }}</div>
        </div>
      </div>
    </div>

    <!-- 底图切换 -->
    <div class="baseBox" @click="changeBase">
      <img src="@/assets/img/home/<USER>/earth.png" alt="">
    </div>

    <div class="legendBox">
      <div class="item" v-for="l in legendList" :key="l.name">
        <el-checkbox v-model="l.checked" @change="checkBoxChange($event, l.layerId)"></el-checkbox>
        <img :src="l.icon" alt="">
        <span>{{ l.name }}</span>
      </div>
    </div>


    <!-- 历史数据列表 -->
    <div class="leftList" v-if="showList">
      <div class="title">历史数据</div>

      <div class="query" style="padding: 10px 20px;">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="getPage"
          :clearable="false"   
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 360px;"
          size="medium">
        </el-date-picker>
      </div>

      <!-- 参数选择 流量 泵站 水质 -->
      <div class="paramsBox" v-if="paramList.length > 1">
        <div
          v-for="p, index in paramList" :key="index"
          class="paramItem" :class="{ selected: currentParam === p }" @click="toggleParam(p)">{{ p.label }}</div>
      </div>

      <div class="listBox">
        <div class="list" v-if="currentParam.type === 'flow'">
          <div class="listHeader">
            <span style="width: 50px;text-align: center;">序号</span>
            <span style="width: 120px;">监测时间</span>
            <span style="width: 70px;">流量(m³/h)</span>
            <span style="width: 60px;">压力(pa)</span>
            <span style="width: 50px;">液位(m)</span>
          </div>
          <el-scrollbar class="scrollBox">
            <div class="listItem" v-for="item, index in tableData" :key="index">
              <div style="width: 50px;text-align: center;">{{ index < 9 ? '0' + (index + 1) : index + 1 }}</div>
              <div style="width: 120px;">{{ item.monitorTime }}</div>
              <div style="width: 70px;">{{ item.speed }}</div>
              <div style="width: 60px;">{{ item.press }}</div>
              <div style="width: 50px;">{{ item.waterLevelValue }}</div>
            </div>
          </el-scrollbar>
        </div>
        <div class="list" v-if="currentParam.type === 'pump'">
          <div class="listHeader">
            <span style="width: 50px;text-align: center;">序号</span>
            <span style="width: 120px;">监测时间</span>
            <span style="width: 70px;">水池水位</span>
            <span style="width: 60px;">出水压力</span>
            <span style="width: 50px;">1号泵状态</span>
            <span style="width: 50px;">2号泵状态</span>
          </div>
          <el-scrollbar class="scrollBox">
            <div class="listItem" v-for="item, index in tableData" :key="index">
              <div style="width: 50px;text-align: center;">{{ index < 9 ? '0' + (index + 1) : index + 1 }}</div>
              <div style="width: 120px;">{{ item.createTime }}</div>
              <div style="width: 70px;">{{ item.poolLevel }}</div>
              <div style="width: 60px;">{{ item.outWaterPress }}</div>
              <div style="width: 50px;">{{ item.pump1Run }}</div>
              <div style="width: 50px;">{{ item.pump2Run }}</div>
            </div>
          </el-scrollbar>
        </div>
        <div class="list" v-if="currentParam.type === 'waterQuality'">
          <div class="listHeader">
            <span style="width: 50px;text-align: center;">序号</span>
            <span style="width: 120px;">监测时间</span>
            <span style="width: 70px;">ph</span>
            <span style="width: 60px;">浊度(NTU)</span>
            <span style="width: 50px;">电导率(μs/cm)</span>
            <span style="width: 50px;">余氯(mg/L)</span>
          </div>
          <el-scrollbar class="scrollBox">
            <div class="listItem" v-for="item, index in tableData" :key="index">
              <div style="width: 50px;text-align: center;">{{ index < 9 ? '0' + (index + 1) : index + 1 }}</div>
              <div style="width: 120px;">{{ item.dataTime }}</div>
              <div style="width: 70px;">{{ item.ph }}</div>
              <div style="width: 60px;">{{ item.turbidity }}</div>
              <div style="width: 50px;">{{ item.powerRate }}</div>
              <div style="width: 50px;">{{ item.chlorine }}</div>
            </div>
          </el-scrollbar>
        </div>
      </div>

      <div class="pageBox">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page.sync="pageInfo.pageNo"
          :page-size.sync="pageInfo.pageSize"
          @current-change="handleCurrentChange"
          :total="pageInfo.total">
        </el-pagination>
      </div>
    </div>

    <!-- 点位左侧固定弹窗 -->
    <PointPopup v-if="showLeftPopup" :detailData="detailData" @closePopup="showLeftPopup = false" />

    <div class="queryBox" v-show="currentChecked === 'point1' && legendList[0].checked">
      <el-input v-model="nameKey" placeholder="查询蓄水池" size="small" style="width: 200px;" @keyup.enter.native="handleQuery"></el-input>
      <div class="featureListBox">
        <el-scrollbar style="height: 100%;">
          <div
            class="featureItem"
            v-for="feature in queryResults" 
            :key="feature.uid"
            @click="handleClickFeature(feature)"
          >{{ feature.attributes.station_name }}</div>
        </el-scrollbar>
      </div>
    </div>

    <!-- 5个水厂 -->
    <div class="bottomBox" v-show="currentCategory === 5">
      <div class="item" v-for="item in factoryList" :key="item.name" 
        :class="{ active: currentFactory === item.name }" @click="chooseFactory(item)">{{ item.name }}</div>
    </div>
  </div>
</template>

<script>
import Map from '@arcgis/core/Map'
import MapView from '@arcgis/core/views/MapView'
import Ground from '@arcgis/core/Ground'
import TileInfo from '@arcgis/core/layers/support/TileInfo'
import WebTileLayer from '@arcgis/core/layers/WebTileLayer'
import WMSLayer from '@arcgis/core/layers/WMSLayer'
import Point from '@arcgis/core/geometry/Point'
import Polygon from '@arcgis/core/geometry/Polygon'
import WFSLayer from '@arcgis/core/layers/WFSLayer'
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer'
import Graphic from '@arcgis/core/Graphic'
import GroupLayer from '@arcgis/core/layers/GroupLayer'
import FeatureLayer from "@arcgis/core/layers/FeatureLayer"
import Query from "@arcgis/core/rest/support/Query"

import { lods, townCode } from './constant'
import { getTodayTs } from '@/utils/time'

import { getVillagesByTown, getWaterLevelPageList, getPumpDataPageList } from '@/api/dispatch/realTimeMonitor'
import { getWqHistoryDataPage } from '@/api/wq/monitor'

import Vue from 'vue'
import DefaultPopup from './popup.vue'
import PointPopup from './pointPopup.vue'
import axios from 'axios'


export default {
  name: 'RealTimeMap',
  data() {
    return {
      geoUrl: null,
      map: null,
      view: null,
      isSatellite: true,
      JL1ImageLayer: null,
      imageNoteLayer: null,
      baseLayer: null,
      groupLayer: null,
      lineOneLayer: null, // 高亮点击的管线，在networkLayer上 groupLayer下
      networkLayer: null, // 管线图层
      legendList: [
        {
          checked: false,
          layerId: 'point1',
          name: '蓄水池',
          icon: require('../../../../assets/img/dispatch/pointIcon1.png')
        },
        {
          checked: true,
          layerId: 'point2',
          name: '水厂',
          icon: require('../../../../assets/img/dispatch/pointIcon2.png')
        },
        {
          checked: false,
          layerId: 'point3',
          name: '泵站/机井',
          icon: require('../../../../assets/img/dispatch/pointIcon3.png')
        },
        {
          checked: false,
          layerId: 'point4',
          name: '取水口',
          icon: require('../../../../assets/img/dispatch/pointIcon4.png')
        },
        {
          checked: false,
          layerId: 'wq',
          name: '水质',
          icon: require('../../../../assets/img/dispatch/pointIcon5.png')
        }
      ],
      // 历史数据列表
      showList: false,
      timeRange: [],
      paramList: [],
      currentParam: '',
      currentId: null,
      tableData: [],
      pageInfo: {
        pageSize: 20,
        pageNo: 1,
        total: 0
      },


      // 临时静态数据
      categoryList: [
        { id: 1, name: '规模化水厂',
          hasPoint: true,
          pointList: [
            '兴山县水月寺水厂',
            '兴山县高岚水厂',
            '琚坪水厂',
            '兴山县黄家河水厂',
            '乡村振兴示范区水厂',
            '仙侣水厂',
            '兴山县南阳水厂',
            '黄粮集镇水厂'
          ]
        },
        { id: 2, name: '千人工程',
          hasPoint: true,
          pointList: [
            '麦仓中心水厂',
            '大礼水厂',
            '青华水厂',
            '青山泵站'
          ]
        },
        { id: 3, name: '百人工程',
          hasPoint: true,
          pointList: [
            '吴家湾水厂',
            '沙坝水厂',
            '上孙家湾集中供水',
            '梁家湾集中供水',
            '马道集中供水',
            '小河潭水厂',
            '垭上坪集中供水',
            '古井坪榛子青山管网延伸',
            '院子湾集中供水',
            '金子山水厂',
            '簸萁山集中供水',
            '郑院王家沟集中供水',
            '咸水安置小区集中供水',
            '楠树坪水厂',
            '大块坡水厂',
            '咸水河集中供水',
            '高坪集中供水',
            '黄家堑集中供水',
            '油杉树集中供水',
            '黑岩集中供水',
            '严家坡小区水厂'
          ]
        },
        // { id: 4, name: '' },
        { id: 5, name: '左岸重点水厂' }
      ],
      currentCategory: 5,

      // 5个水厂
      factoryList: [
        {
          name: '谭湾水厂',
          village: '麦仓村',
          town: '古夫镇',
          longitude: 110.7227346666861,
          latitude: 31.316562565772447
        },
        {
          name: '双龙观水厂',
          village: '麦仓村',
          town: '古夫镇',
          longitude: 110.71362353984955,
          latitude: 31.3115926927495
        },
        {
          name: '青华水厂',
          village: '青华村',
          town: '昭君镇',
          longitude: 110.80396551305375,
          latitude: 31.247688012190306
        },
        {
          name: '大礼水厂',
          village: '大礼村',
          town: '昭君镇',
          longitude: 110.768188,
          latitude: 31.237423
        },
        {
          name: '吴家坪水厂',
          village: '白鹤村',
          town: '峡口镇',
          longitude: 110.79361795570478,
          latitude: 31.16420279335171
        }
      ],
      currentFactory: '',

      // 弹窗相关
      showLeftPopup: false,
      detailData: {},

      nameKey: '',
      currentChecked: '',
      queryResults: []
    }
  },
  components: {
    PointPopup
  },
  mounted() {
    this.geoUrl = process.env.VUE_APP_GEO_URL
    this.createMap()
  },
  methods: {
    createMap() {
      let titeInfo = new TileInfo({
        dpi: 90.71428571427429,
        size: 256,
        origin: {
          type: 'point',
          x: -180,
          y: 90,
          spatialReference: { wkid: 4326 }
        },
        spatialReference: { wkid: 4326 },
        lods: lods
      })
      // 天地图影像注记
      this.imageNoteLayer = new WebTileLayer({
        id: 'imageNoteMap',
        title: 'imageNoteMap',
        urlTemplate: 'http://{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
        subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
        visible: false
        // tileInfo: titeInfo,
        // spatialReference: { wkid: 4326 }
      })
      // 吉林一号影像瓦片
      this.JL1ImageLayer = new WebTileLayer({
        id: 'JL1ImageLayer',
        urlTemplate: 'https://api.jl1mall.com/getMap/{z}/{x}/{y}?mk=73ad26c4aa6957eef051ecc5a15308b4&tk=24277043ba78c68ff6ed3b50ce486283&pro=ca3a3754837f49d7ac3068edce0e65f7&sch=wmts',
      })
      // 基础图层
      // this.baseLayer = new WMSLayer({
      //   id: 'baseInfoLayer',
      //   url: this.geoUrl + '/geoserver/xingshan/wms',
      //   sublayers: [
      //     {
      //       name: 'river_track'
      //     },
      //     {
      //       name: 'xingshan:town'
      //     },
      //     {
      //       name: 'main_pipe_network'
      //     }
      //   ]
      // })

      // 主线图层
      let mainFactoryPipeLayer = new WFSLayer({
        url: this.geoUrl + '/geoserver/xingshan/wfs',
        name: 'xingshan:main_factory_pipe',
        id: 'mainFactoryPipeLayer',
        renderer: {
          type: 'simple',
          symbol: {
            type: 'simple-line',
            // color: '#2CECEA',
            color: 'red',
            width: 2
          }
        }
      })
      // 水厂
      let graphics = []
      this.factoryList.forEach(item => {
        let point = {
          type: 'point',
          longitude: item.longitude,
          latitude: item.latitude
        }
        let attributes = {
          name: item.name
        }
        let graphic = new Graphic({
          geometry: point,
          attributes
        })
        graphics.push(graphic)
      })
      let waterFactoryLayer = new FeatureLayer({
        id: 'waterFactoryLayer',
        source: graphics,
        objectIdField: 'OBJECTID',
        fields: [
          { name: 'name', type: 'string' }
        ],
        outFields: ['*'],
        labelingInfo: [
          {
            labelExpressionInfo: {
              expression: '$feature.name'
            },
            labelPlacement: 'above-center',
            symbol: {
              type: 'text',
              color: 'white',
              font: { size: 12 }
            }
          }
        ],
        renderer: {
          type: 'simple',
          symbol: {
            type: 'picture-marker',
            url: require('@/assets/img/home/<USER>'),
            width: '43px',
            height: '106px',
            yoffset: '53px'
          }
        }
      })
      this.mainLayer = new GroupLayer({
        id: 'mainLayer',
        layers: [mainFactoryPipeLayer, waterFactoryLayer]
      })
    

      // 区域图层
      this.areaLayer = new WFSLayer({
        url: this.geoUrl + '/geoserver/xingshan/ows',
        name: 'xingshan:town',
        id: 'area',
        outFields: ['*'],
        renderer: {
          type: 'simple',
          symbol: {
            type: 'simple-fill',
            color: [255, 255, 255, 0],
            outline: {
              color: 'white',
              width: 1
            }
          }
        }
      })

      // 放置高亮图形的图层
      this.areaHighlightLayer = new GraphicsLayer()
      this.lineOneLayer = new GraphicsLayer()

      // 图层组 管理点位图层
      this.groupLayer = new GroupLayer({
        id: 'pointGroupLayer'
      })

      // 单独的管线图层组
      this.networkLayer = new GroupLayer({
        id: 'networkLayer'
      })
      

      // map view
      this.map = new Map({
        basemap: {
          baseLayers:[
            this.JL1ImageLayer,
            this.imageNoteLayer
          ]
        }
        // ground: new Ground({
        //   layers: [],
        //   surfaceColor: 'transparent',
        //   opacity: 0
        // })
      })
      this.view = new MapView({
        container: 'mapBox',
        map: this.map,
        extent: {
          xmax: 110.82556434056983,
          xmin: 110.74325912092598,
          ymax: 31.176874262674232,
          ymin: 31.1399655157402,
        },
        // spatialReference: {
        //   wkid: 4326,
        // },
        constraints: {
          minZoom: 11,
          maxZoom: 18
        },
        ui: {
          components: []
        }
      })
      console.log(this.view)
      // 去除地图组件
      this.view.ui.remove('attribution')
      this.view.ui.empty('top-left')
      this.view.popup.autoOpenEnabled = false
      // this.view.popup.alignment = 'bottom-right'
      
      const targetPoint = new Point({
        latitude: 31.32521963464773,
        longitude: 110.69993986039871
      })
      this.view.goTo({
        target: targetPoint,
        zoom: 11
      })

      this.view.when((e) => {
        // this.view.map.add(this.JL1ImageLayer)
        // this.view.map.add(this.imageNoteLayer)
        
        this.view.map.add(this.areaLayer)
        this.view.map.add(this.mainLayer)
        // this.view.map.add(waterFactoryLayer)
        this.view.map.add(this.areaHighlightLayer )

        this.view.map.add(this.networkLayer)
        this.view.map.add(this.lineOneLayer)
        this.view.map.add(this.groupLayer)
        
      })

      // 监听地图点击
      this.view.on('click', (event) => {
        console.log('拾取点位', event)

        this.view.hitTest(event).then(response => {
          // console.log('map click', response)
          const results = response.results

          // console.log('layers', this.map.layers)

          if(results.length > 0) {
            const ret = results[0]
            
            // 处理高亮 面
            this.handleHighlight(ret.graphic, this.areaHighlightLayer)


            if((ret.graphic.layer.id).includes('point') || ret.graphic.layer.id === 'line') {

              // 点击点位
              this.showPopup(ret)
              // 处理管线高亮
              if(ret.graphic.layer.id === 'line') {
                this.lineHighlight(ret.graphic, this.lineOneLayer)
                // this.showLineOne(ret.graphic)
              }

            } else if(ret.graphic.layer.id === 'area') {

              // 点击镇区域面，查询这个镇的点位
              // 清空 groupLayer
              this.groupLayer.removeAll()
              this.networkLayer.removeAll()
              this.createPointLayer(ret.graphic)

            } else {

            }
          }
        })

      })

    },
    // 切换底图
    changeBase() {
      this.isSatellite = !this.isSatellite
      if(this.isSatellite) {
        this.JL1ImageLayer.visible = true
        this.imageNoteLayer.visible = false
      } else {
        this.JL1ImageLayer.visible = false
        this.imageNoteLayer.visible = true
      }
    },
    // wfs 蓄水池图层查询
    handleQuery() {
      // let query = new Query()
      let query = this.pointLayer1.createQuery()
      query.where = `station_name LIKE '%${this.nameKey}%'`
      query.outFields = ['*']
      this.pointLayer1.queryFeatures(query).then(result => {
        console.log(result)
        this.queryResults = result.features
      })
    },
    handleClickFeature(feature) {
      console.log(feature)
      this.view.goTo(feature.geometry)

      this.view.popup.open({
        location: feature.geometry,
        content: feature.attributes.station_name
      })
    },
    // 高亮要素
    highlightFeature(feature) {
      if(this.view.graphics.length > 0) {
        this.view.graphics.removeAll()
      }

      const highlightSymbol = {
        color: [255, 0, 0],
        size: 12,
        outline: {
          color: [255, 255, 0],
          width: 3
        }
      }
      const highlightGraphic = new Graphic({
        geometry: feature.geometry,
        symbol: highlightSymbol
      })
      this.view.graphics.add(highlightGraphic)
    },
    // 设置镇面高亮
    handleHighlight(graphic, layer) {
      if(graphic.layer.id === 'area') {
        this.view.popup.close()
        this.view.goTo(graphic.geometry)
        layer.removeAll()
        const highlightGraphic = new Graphic({
          geometry: graphic.geometry,
          symbol: {
            type: 'simple-fill',
            color: [19, 119, 208,0.3],
            outline: {
              color: '#CCECFC',
              width: 1
            }
          }
        })
        layer.add(highlightGraphic)
      }
    },
    // 实现管线点击高亮
    lineHighlight(graphic, layer) {
      console.log(graphic)
      layer.removeAll()
      const line1 = new Graphic({
        geometry: graphic.geometry,
        symbol: {
          type: 'simple-line',
          color: '#00E4FF',
          width: 2
        }
      })
      layer.add(line1)
    },
    // 实现管线点击高亮 利用WMS
    showLineOne(data) {
      let lineId = data.attributes.id
      if(this.lineOneLayer) {
        this.view.map.remove(this.lineOneLayer)
        this.lineOneLayer = null
        return
      }
      this.lineOneLayer = new WMSLayer({
        url: this.geoUrl + '/geoserver/xingshan/wms',
        id: 'lineOne',
        sublayers: [
          { name: 'total_pipe_network' }
        ],
        customParameters: {
          'CQL_FILTER': `id = ${lineId}`
        }
      })
      this.view.map.add(this.lineOneLayer)
    },

    chooseCategory(id) {
      this.currentCategory = id
    },
    // 选择水厂
    chooseFactory(data) {
      this.currentFactory = data.name
      let mainLayer = this.view.map.findLayerById('mainLayer')
      if(mainLayer) {
        mainLayer.visible = false
      }

      // 使用 CQL_FILTER 查询村边界
      let cql_filter = `ZLDWMC='${data.village}'`
      axios.get(this.geoUrl + '/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Avillage&maxFeatures=50&outputFormat=application%2Fjson&CQL_FILTER=' + cql_filter)
        .then(res => {
          this.areaHighlightLayer.removeAll()
          this.createPolygon(res.data.features[0].geometry.coordinates[0][0])
        })

      this.groupLayer.removeAll()
      this.networkLayer.removeAll()
      this.createPointLayer(null, data)
    },
    // 创建下方水厂所在村的面
    createPolygon(paths) {
      const polygonGraphic = new Graphic({
        geometry: new Polygon({
          rings: paths
        }),
        symbol: {
          type: 'simple-fill',
          color: [19, 119, 208,0.3],
          outline: {
            color: '#CCECFC',
            width: 1
          }
        }
      })
      this.view.goTo(polygonGraphic.geometry)
      this.areaHighlightLayer.add(polygonGraphic)
    },

    /* 
      创建点位图层，添加进去图层组，对图层组进行操作
      如果 oneParam 有值，则为点击下方5个水厂
    */
    async createPointLayer(graphic, oneParam) {

      // 获取镇下面村，组织cql
      let cql = ''
      let codeList = []
      let { data } = await getVillagesByTown(oneParam ? oneParam.town : graphic.attributes.XZQMC)
      if(data && data.length > 0) {
        codeList = data.map(item => item.code)
      }
      cql = `village in (${codeList.join(',')})`
      console.log('村', cql)

      // 获取镇对应的code，组织获取管网cql
      let lineCql = ''
      let isExist = oneParam ? townCode[oneParam.town] : townCode[graphic.attributes.XZQMC]
      if(isExist) {
        lineCql = `village = ${isExist}`
      }
      console.log('镇', lineCql)

      const shows = this.legendList.map(item => item.checked)

      // 管线
      const lineLayer = new WFSLayer({
        url: this.geoUrl + '/geoserver/xingshan/ows',
        name: 'xingshan:total_pipe_network',
        id: 'line',
        customParameters: {
          'CQL_FILTER': lineCql
        },
        outFields: ['*'],
        // 值渲染器
        renderer: {
          type: 'unique-value',
          field: 'type',
          defaultSymbol: { type: 'simple-line' },
          uniqueValueInfos: [
            {
              value: 0,
              symbol: {
                type: 'simple-line',
                // color: '#14B784',
                // color: '#2CECEA',
                // color: '#20A162',
                color: 'red',
                width: 2
              }
            },
            {
              value: 1,
              symbol: {
                type: 'simple-line',
                // color: 'Magenta',
                color: 'orange',
                width: 2
              }
            },
            {
              value: 2,
              symbol: {
                type: 'simple-line',
                color: 'green',
                // color: '#2486b9',
                width: 2
              }
            },
          ]
        },
        // renderer: {
        //   type: 'simple',
        //   symbol: {
        //     type: 'simple-line',
        //     color: '#14B784',
        //     width: 2
        //   }
        // }
      })

      // 蓄水池
      this.pointLayer1 = new WFSLayer({
        url: this.geoUrl + '/geoserver/xingshan/ows',
        name: 'xingshan:water_storage_pond',
        id: 'point1',
        // definitionExpression: 'village = 17',
        customParameters: {
          'CQL_FILTER': cql
        },
        visible: shows[0],
        outFields: ['*'],
        renderer: {
          type: 'simple',
          symbol: {
            type: 'picture-marker',
            url: require('../../../../assets/img/dispatch/pointIcon1.png'),
            width: 12,
            height: 16.5,
          },
        }
      })
      // 水厂
      const pointLayer2 = new WFSLayer({
        url: this.geoUrl + '/geoserver/xingshan/ows',
        name: 'xingshan:water_factory',
        id: 'point2',
        customParameters: {
          'CQL_FILTER': cql
        },
        outFields: ['*'],
        visible:  shows[1],
        renderer: {
          type: 'simple',
          symbol: {
            type: 'picture-marker',
            url: require('../../../../assets/img/dispatch/pointIcon2.png'),
            width: 12,
            height: 16.5,
          },
        }
      })
      // 泵站机井
      const pointLayer3 = new WFSLayer({
        url: this.geoUrl + '/geoserver/xingshan/ows',
        name: 'xingshan:water_supply_pumping_station',
        id: 'point3',
        customParameters: {
          'CQL_FILTER': cql
        },
        visible: shows[2],
        outFields: ['*'],
        renderer: {
          type: 'simple',
          symbol: {
            type: 'picture-marker',
            url: require('../../../../assets/img/dispatch/pointIcon3.png'),
            width: 12,
            height: 16.5,
          },
        }
      })
      // 取水口
      const pointLayer4 = new WFSLayer({
        url: this.geoUrl + '/geoserver/xingshan/ows',
        name: 'xingshan:tbl_water_take_dam',
        id: 'point4',
        customParameters: {
          'CQL_FILTER': cql
        },
        visible: shows[3],
        outFields: ['*'],
        renderer: {
          type: 'simple',
          symbol: {
            type: 'picture-marker',
            url: require('../../../../assets/img/dispatch/pointIcon4.png'),
            width: 12,
            height: 16.5,
          },
        }
      })

      this.networkLayer.add(lineLayer)

      // this.groupLayer.add(lineLayer)
      this.groupLayer.add(this.pointLayer1)
      this.groupLayer.add(pointLayer2)
      this.groupLayer.add(pointLayer3)
      this.groupLayer.add(pointLayer4)
    },

    // 点位图层显隐
    checkBoxChange(value, layerId) {
      this.view.popup.close()
      this.currentChecked = layerId
      let targrtLayer = this.groupLayer.findLayerById(layerId)
      if(targrtLayer) {

        targrtLayer.visible = value
      }
    },
    // 点位弹窗
    showPopup(ret) {
      console.log(ret)
      this.detailData = {
        pointType: ret.graphic.layer.id,
        attributes: ret.graphic.attributes
      }
      this.showLeftPopup = true

      // this.view.popup.open({
      //   // 位置
      //   location: ret.mapPoint,
      //   content: '加载中...',
      // })
      // console.log('popup', ret)

      this.currentId = ret.graphic.attributes.id
      let insm = ret.graphic.attributes.insm

      // 组织展示字段
      // let list = []
      // let monitorDataQuery = {}
      // const layerId = ret.graphic.layer.id
      // list.push('标识符：' + (ret.graphic.attributes.identity || '-'))

      /* 点位 
        point1 蓄水池
        point2 水厂
        point3 泵站/机井
        point4 取水口
      */
      // switch (layerId) {
      //   case 'point1':
      //     let shape = ret.graphic.attributes.shape || 0
      //     let shapeLabel = shape ? (shape == 1 ? '圆形' : '方形') : '--'
      //     list.push(`类型：蓄水池${shapeLabel}`)
      //     list.push('组：' + (ret.graphic.attributes.village_group || '-' ))
      //     list.push('方量：' + (ret.graphic.attributes.max_storage || '-' ) + 'm³')
      //     list.push('高度：' + (ret.graphic.attributes.depth || '-' ) + 'm')
      //     list.push('受益户数：' + (ret.graphic.attributes.home || '-') + '户')
      //     list.push('受益人口：' + (ret.graphic.attributes.population || '-') + '人')
      //     monitorDataQuery = {
      //       insm,
      //       storeage: ret.graphic.attributes.max_storage,
      //       depth: ret.graphic.attributes.depth,
      //       stationType: 'WATER_STORAGE_POND'
      //     }
      //     break
      //   case 'point2':
      //     list.push('类型：水厂' )
      //     list.push('组：' + (ret.graphic.attributes.village_group || '-' ))
      //     // list.push('方量：' + (ret.graphic.attributes.max_storage || '-' ))
      //     list.push('受益户数：' + (ret.graphic.attributes.home || '-' ) + '户')
      //     list.push('受益人口：' + (ret.graphic.attributes.population || '-' ) + '人')
      //     monitorDataQuery = {
      //       insm,
      //       stationType: 'WATER_FACTORY'
      //     }
      //     break
      //   case 'point3':
      //     if(ret.graphic.attributes.type == 1) {
      //       list.push('类型：泵站' )
      //     } else {
      //       list.push('类型：机井' )
      //     }
      //     list.push('组：' + (ret.graphic.attributes.village_group || '-' ))
      //     // list.push('受益户数：' + (ret.graphic.attributes.home || '-' ))
      //     // list.push('受益人口：' + (ret.graphic.attributes.population || '-' ))
      //     monitorDataQuery = {
      //       insm,
      //       stationType: 'PUMP_STATION'
      //     }
      //     break
      //   case 'point4':
      //     if(ret.graphic.attributes.type == 1) {
      //       list.push('类型：石河堰' )
      //     } else if(ret.graphic.attributes.type == 2) {
      //       list.push('类型：泉水堰' )
      //     } else if(ret.graphic.attributes.type == 3) {
      //       list.push('类型：取水口' )
      //     }
      //     list.push('组：' + (ret.graphic.attributes.village_group || '-' ))
      //     break
      //   case 'line':
      //     list = []
      //     if(ret.graphic.attributes.type == 0) {
      //       list.push('类型：引水管' )
      //     } else if(ret.graphic.attributes.type == 1) {
      //       list.push('类型：配水管' )
      //     } else if(ret.graphic.attributes.type == 2) {
      //       list.push('类型：提水管' )
      //     }
      //     list.push('材质：' + (ret.graphic.attributes.material || '-' ))
      //     list.push('长度：' + (ret.graphic.attributes.length || '-' ) + ' m')
      //     list.push('管径：DN' + (ret.graphic.attributes.diameter || '-' ))
      //     list.push('编码：' + (ret.graphic.attributes.code|| '-' ))
      //     monitorDataQuery = {
      //       insm,
      //       stationType: 'PIPE_NETWORK'
      //     }
      //     break
      //   default:
      //     console.log('无命中要素')
      // }

      // let p = Vue.extend(DefaultPopup)
      // let popup = new p({
      //   propsData: {
      //     id: ret.graphic.attributes.id,
      //     monitorDataQuery,
      //     name: ret.graphic.attributes.station_name,
      //     list
      //   }
      // })

      // let p = Vue.extend(PointPopup)
      // let popup = new p({
      //   propsData: {
      //     name: 'test'
      //   }
      // })
      // popup.$mount()
      // popup.$on('close', () => {
      //   this.view.popup.close()
      //   // this.lineOneLayer.removeAll()

      //   // const resultLayer = this.view.map.findLayerById('lineOne')
      //   // if(resultLayer) {
      //   //   this.view.map.remove(this.resultLayer)
      //   //   this.lineOneLayer = null
      //   // }
      //   this.showList = false
      // })
      // popup.$on('emitHistory', (data) => {
      //   this.timeRange = getTodayTs()
      //   this.paramList = data
      //   if(data.length > 0) {

      //     this.currentParam = data[0]
      //   }
      //   this.tableData = []
      //   this.pageInfo.pageNo = 1
      //   this.getPage(this.paramList)
      //   this.showList = true
      //   console.log(data)
      // })
      // popup.$nextTick(() => {
      //   this.view.popup.content = popup.$el;
      // })
    },

    // 分页处理函数
    getPage(paramList) {
      if(paramList.length > 0) {
        
        if(this.currentParam.type === 'flow') {
          // 流量历史数据
          getWaterLevelPageList({
            pageNo: this.pageInfo.pageNo,
            pageSize: this.pageInfo.pageSize,
            sourceType: this.currentParam.sourceType,
            facilityId: this.currentId,
            startMonitorTime: this.timeRange[0],
            endMonitorTime: this.timeRange[1]
          }).then(res => {
            this.tableData = res.data
            this.pageInfo.total = res.count
          })
        } else if(this.currentParam.type === 'waterQuality') {
          // 水质历史数据
          getWqHistoryDataPage({
            pageNo: this.pageInfo.pageNo,
            pageSize: this.pageInfo.pageSize,
            sourceType: this.currentParam.sourceType,
            facilityId: this.currentId,
            startMonitorTime: this.timeRange[0],
            endMonitorTime: this.timeRange[1]
          }).then(res => {
            this.tableData = res.data
            this.pageInfo.total = res.count
          })
        } else if(this.currentParam.type === 'pump') {
          // 泵站历史数据
          getPumpDataPageList({
            pageNo: this.pageInfo.pageNo,
            pageSize: this.pageInfo.pageSize,
            pumpStationId: this.currentId,
            beginTime: this.timeRange[0],
            endTime: this.timeRange[1]
          }).then(res => {
            this.tableData = res.data
            this.pageInfo.total = res.count || 0
          })
        }
      }
    },
    // 切换参数
    toggleParam(p) {
      this.currentParam = p
      this.getPage(this.paramList)
    },
    // 历史数据分页
    handleCurrentChange() {
      this.getPage(this.paramList)
    },

    async _getVillagesByTown(townName) {
      let codeList = []
      let { data } = await getVillagesByTown(townName)
      if(data && data.length > 0) {
        codeList = data.map(item => item.code)
      }
      return codeList
    }
  }
}
</script>


<style lang="scss" scoped>
.RealTimeMap {
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  right: 0;
  overflow: hidden;
  #mapBox {
    width: 100%;
    height: 100%;
  }
  .baseBox {
    position: absolute;
    top: 60px;
    right: 30px;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .legendBox {
    position: absolute;
    right: 20px;
    bottom: 20px;
    width: 131px;
    padding: 10px 0 10px 10px;
    background: rgba(5, 58, 127, 0.4);
    border-radius: 4px;
    border: 1px solid rgba(11, 11, 136, 1);
    .item {
      display: flex;
      align-items: center;
      img {
        width: 18px;
        margin: 0 5px;
      }
      span {
        color: #fff;
        font-size: 12px;
      }
    }
  }
  .leftList {
    position: absolute;
    width: 600px;
    left: 20px;
    top: 20px;
    bottom: 20px;
    background-color: rgba(4, 15, 45, 0.3);
    .title {
      height: 43px;
      width: 600px;
      font-family: PangMenZhengDao;
      font-size: 20px;
      padding-left: 44px;
      padding-right: 20px;
      background-image: url("~@/assets/img/dispatch/titleBg.png");
      line-height: 35px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  .listBox {
    height: calc(100% - 163px);
  }
  .list {
    // height: calc(100% - 155px);
    height: 100%;
    overflow-y: hidden;
    padding: 0 20px;
  }
  .listHeader {
    font-size: 14px;
    background-image: url("~@/assets/img/table.png");
    background-size: 100%;
    height: 34px;
    display: flex;
    align-items: center;
    span:last-child {
      // flex: 1;
    }
  }
  .scrollBox {
    height: calc(100% - 34px);
  }
  .listHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .listItem {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    background-color: rgba(2,50,128,0.23);
    border: 1px solid #023280;
    margin-top: 5px;
    // cursor: pointer;
  }
  .paramsBox {
    display: flex;
    padding-left: 20px;
    .paramItem {
      width: 68px;
      height: 28px;
      border: 2px solid transparent;
      background: rgba(2, 50, 128, 0.8);
      font-size: 12px;
      text-align: center;
      line-height: 24px;
      margin-right: 10px;
      cursor: pointer;
    }
    .selected {
      border-color: #3ad2e7;
    }
  }
  .queryBox {
    position: absolute;
    top: 100px;
    right: 20px;
    padding: 10px;
    background: rgba(5, 58, 127, 0.4);
    border-radius: 4px;
    border: 1px solid rgba(11, 11, 136, 1);
    .featureListBox {
      height: 300px;
      .featureItem {
        height: 24px;
        margin-top: 10px;
        background: linear-gradient(180deg, rgba(17, 62, 102, 0.5) 0%, rgba(2, 50, 128, 0.5) 100%);
        border: 1px solid #1377d0;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }
  .topBox {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    .item {
      width: 160px;
      height: 44px;
      margin-right: 20px;
      color: #FFFFFF;
      font-size: 18px;
      line-height: 44px;
      text-align: center;
      background-image: url('~@/assets/img/dispatch/default.png');
      background-size: 100%;
      cursor: pointer;
      position: relative;
      .pointList {
        width: 100%;
        position: absolute;
        top: 44px;
        background: rgba(5, 58, 127, 0.4);
        border-radius: 4px;
        border: 1px solid #0b0b88;
        .pointItem {
          height: 22px;
          line-height: 22px;
          font-size: 14px;
        }
      }

    }
    .active {
      background-image: url('~@/assets/img/dispatch/active.png');
    }
  }
  .bottomBox {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    .item {
      width: 160px;
      height: 44px;
      margin-right: 20px;
      color: #FFFFFF;
      font-size: 18px;
      line-height: 44px;
      text-align: center;
      background-image: url('~@/assets/img/dispatch/default.png');
      background-size: 100%;
      cursor: pointer;
    }
    .active {
      background-image: url('~@/assets/img/dispatch/active.png');
    }
  }
}
</style>

<style lang="scss">
.RealTimeMap {
  .el-checkbox__inner {
    background: rgba(0, 57, 112, 0.5);
    border: 1px solid rgba(19, 130, 230, 1);
  }
  .el-scrollbar__wrap {
    overflow-x: auto;
  }
}
// .esri-popup__footer {
//   display: none;
// }
// .esri-popup__feature-menu {
//   display: none;
// }
// .esri-view .esri-view-surface--inset-outline:focus::after {
//   outline: none !important;
// }
// .esri-view-surface {
//   outline: none !important;
// }
// .esri-view .esri-view-surface--inset-outline:focus::after {
//   outline: auto 0px Highlight !important;
//   outline: auto 0px -webkit-focus-ring-color !important;
// }
// [class*="esri-popup--is-docked-top-"] .esri-popup__footer,
// [class*="esri-popup--aligned-bottom-"] .esri-popup__footer {
//   border-bottom: solid 1px #6e6e6e4d;
// }
// .esri-popup__header {
//   border-top-left-radius: 5px !important;
//   border-top-right-radius: 5px;
//   background-color: #fff;
//   color: #fff;
// }
// .esri-popup--shadow {
//   box-shadow: 0 0 0 0 rgb(155, 155, 155) !important;
// }
// .esri-popup__header-title {
//   background-color: #009688 !important;
// }
// .esri-popup__header-container,
// .esri-popup__header-container--button {
//   outline: none !important;
// }
// .esri-popup__icon,
// .esri-icon-close {
//   color: #000000 !important;
// }
// .esri-ui .esri-popup {
//   border-radius: 5px !important;
// }
// .esri-popup {
//   position: absolute !important;
// }
// .esri-popup__button {
//   background-color: transparent !important;
//   outline: none;
// }
</style>