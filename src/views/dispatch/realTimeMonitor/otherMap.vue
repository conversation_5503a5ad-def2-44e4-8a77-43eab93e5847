<template>
  <div class="otherMap">
    <div class="map" id="map"></div>
    <div class="leftList">
      <div class="title">实时数据</div>

      <div class="lineBox">
        <div
          class="line ellipsis"
          :class="{ active: item.value === currentSelectedLine }"
          v-for="item in lines"
          :key="item.id"
          @click="changeLine(item)">{{ item.label }}</div>
      </div>

      <div class="query">
        <el-date-picker
          v-model="time"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :clearable="false"   
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 360px;"
          size="medium">
        </el-date-picker>
        <el-select style="width: 180px;margin-left: 20px;" v-model="station" value-key="facilityId" @change="getList" placeholder="选择站点" size="medium">
          <el-option v-for="item in stationListTemp" :key="item.facilityId" :value="item" :label="item.name"></el-option>
        </el-select>
      </div>

      <div class="list">
        <div class="listHeader">
          <span style="width: 50px;text-align: center;">序号</span>
          <span style="width: 100px;">站点名称</span>
          <!-- <span style="width: 110px;">点位</span> -->
          <span style="width: 120px;">监测时间</span>
          <span style="width: 100px;">流量(m³/h)</span>
          <span style="width: 100px;">压力(pa)</span>
          <span style="width: 100px;">液位(m)</span>
          <span style="width: 100px;">储水量(m³)</span>
        </div>
        <el-scrollbar class="scrollBox">
          <div class="listItem" v-for="item, index in list" :key="item.stationId" @click="handleClickItem(item.pointWrapper)">
            <div style="width: 50px;text-align: center;">{{ index + 1 }}</div>
            <span style="width: 100px;">{{ item.stationName }}</span>
            <!-- <div style="width: 110px;">{{ item.pointWrapper | filterPoint }}</div> -->
            <div style="width: 120px;">{{ item.dataTime | filterTime }}</div>
            <div style="width: 100px;">{{ item.speed | filterValue }}</div>
            <div style="width: 100px;">{{ item.press | filterValue }}</div>
            <div style="width: 100px;">{{ item.level | filterValue }}</div>
            <div style="width: 100px;">{{ item.currentWater | filterValue }}</div>
          </div>
        </el-scrollbar>
      </div>
      <div class="pageBox">
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page.sync="pageInfo.pageNo"
          :page-size.sync="pageInfo.pageSize"
          @current-change="handleCurrentChange"
          :total="pageInfo.total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

import LabelPopup from './labelPopup'
import Vue from 'vue'

import { getLineList, getLastDataOrderByStation, getStationLastData, getPointSpeedForMap, getPondPointDataForMap } from '@/api/dispatch/realTimeMonitor'

import { getShortcutTs } from '@/utils/time'
import { parseTime } from '@/utils'

export default {
  data() {
    return {
      map: null,
      geoUrl: '',
      waterFactoryPoints: [
        {
          name: '谭湾水厂',
          longitude: 110.7227346666861,
          latitude: 31.316562565772447
        },
        {
          name: '双龙观水厂',
          longitude: 110.71362353984955,
          latitude: 31.3115926927495
        },
        {
          name: '青华水厂',
          longitude: 110.80396551305375,
          latitude: 31.247688012190306
        },
        {
          name: '大礼水厂',
          longitude: 110.71533482870338,
          latitude: 31.223431626828795
        },
        {
          name: '吴家坪水厂',
          longitude: 110.79361795570478,
          latitude: 31.16420279335171
        }
      ],
      time: [],
      lines: [],
      currentSelectedLine: '',
      station: '',
      stationListTemp: [],
      list: [],
      pageInfo: {
        pageSize: 20,
        pageNo: 1,
        total: 120
      }
    }
  },
  filters: {
    filterTime(value) {
      if(value) {
        return parseTime(value, '{m}-{d} {h}:{i}')
      }
      return '--'
    },
    filterValue(value) {
      if(value != undefined && value !== '') {
        return value.toFixed(2)
      }
      return '--'
    }
  },
  mounted() {
    this.geoUrl = process.env.VUE_APP_GEO_URL
    this.initMap()
    this.init()
  },
  methods: {
    initMap() {
      let map = L.map('map', {
        attributionControl: false,
        zoomControl: false,
        zoom: 12,
        // maxZoom: 17,
        center: [31.25307163396126, 110.75210994390093],
        crs: L.CRS.EPSG4326
      })
      this.map = map

      // 天地图影像标注
      let cia_c_url = 'http://t{s}.tianditu.gov.cn/cia_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6'
      let tdtImageNoteLayer = L.tileLayer(cia_c_url, {
        subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
        zoomOffset: 1
      })
      map.addLayer(tdtImageNoteLayer)

      // 官网 wms
      let pipeLayer = L.tileLayer.wms(
        this.geoUrl + '/geoserver/xingshan/wms',
        {
          layers: 'xingshan:main_factory_pipe',
          format: 'image/png',
          transparent: true
        }
      )
      map.addLayer(pipeLayer)



      let pondLayer = L.layerGroup()
      let icon = L.icon({
        iconUrl: require('@/assets/img/dispatch/pondIcon.png'),
        iconSize: [30, 30]
      })
      let labelPopup = Vue.extend(LabelPopup)
      let list = []
      getPondPointDataForMap().then(res => {
        let pointList = res.data || []
        console.log(pointList)
        pointList.forEach(p => {
          let point = [p.pointWrapper.y, p.pointWrapper.x]
          let marker = L.marker(point, { icon }).addTo(this.map)
          
          list = []
          list.push(`${ p.speed !== '' ? (p.speed).toFixed(2)  : '--'}m³/h`)
          list.push(`${ p.press !== '' ? (p.press).toFixed(2) : '--'}MPa`)
          list.push(`${ p.level !== '' ? (p.level).toFixed(2)  : '--'}m`)
          list.push(`${ p.currentWater !== '' ? (p.currentWater).toFixed(2)  : '--'}m³`)
          console.log(list)

          let pondLabel = new labelPopup({
            propsData: {
              name: p.stationName,
              labelType: 2,
              list: list
            }
          })

          pondLabel.$mount()
          let content = pondLabel.$el
          let popup = L.popup({ closeOnClick: false, closeButton: false }).setLatLng(point).setContent(content)
          this.map.addLayer(popup)

        })
      })

      getPointSpeedForMap().then(res => {
        let factoryMonitorList = res.data.factoryMonitorList || []
        if(factoryMonitorList.length) {
          // 塞数据
          this.waterFactoryPoints.forEach(item => {
            let matchPoint = factoryMonitorList.find(d => d.name === item.name)
            // let speed = (matchPoint.waterLevel && matchPoint.waterLevel.speed !== '') ? matchPoint.waterLevel.speed : ''
            let speed = ''
            if(matchPoint && matchPoint.waterLevel && matchPoint.waterLevel.speed !== '') {
              speed = matchPoint.waterLevel.speed
            }
            matchPoint && (item.speed = speed)
          })
        }
        this.setPoints()
      })
    },
    setPoints() {
      // 水厂
      let factoryLayer = L.layerGroup()
      let icon = L.icon({
        iconUrl: require('@/assets/img/dispatch/factoryIcon.png'),
        iconSize: [30, 30]
      })
      // label 弹窗
      let labelPopup = Vue.extend(LabelPopup)
      this.waterFactoryPoints.forEach(item => {
        let marker= L.marker([item.latitude, item.longitude], { icon })
          .addTo(this.map)
        
        let p = new labelPopup({
          propsData: {
            name: item.name,
            labelType: 1,
            speed: (item.speed && item.speed !== '') ? (item.speed).toFixed(2) : '--'
          }
        })
        p.$mount()
        let content = p.$el
        let popup = L.popup({ closeOnClick: false, closeButton: false }).setLatLng([item.latitude, item.longitude]).setContent(content)
        this.map.addLayer(popup)
      })
    },
    async init() {
      this.time =  getShortcutTs()
      await this.getLine()
      const first = this.lines[0]
      this.currentSelectedLine = first.id
      await this.getList()
    },
    async getLine() {
      const { data } = await getLineList({ orderBy: 'id', asc: true })
      this.lines = data.map(item => {
        return {
          id: item.id,
          label: item.name.slice(0, -3),
          value: item.id,
          stationList: item.stationList
        }
      })
    },
    // 切换水厂线
    changeLine(item) {
      this.currentSelectedLine = item.value
      // this.station = ''
      // this.stationListTemp = item.stationList
      this.getList()
    },
    // 获取最新数据
    async getList() {
      // 查询一条管线下的最新数据
      // const { data } = await getLastDataOrderByStation(this.currentSelectedLine)
      // this.list = data
      const { data }  = await getStationLastData({ pageNo: this.pageInfo.pageNo, id: this.currentSelectedLine })
      this.list = data || []
    },
    // 分页
    handleCurrentChange() {
      this.getList()
    },
    handleClickItem(point) {
      this.map.flyTo([point.y, point.x])
    }
  }
}
</script>

<style lang="scss">
.otherMap {
  .el-scrollbar__wrap {
    overflow-x: auto;
  }
  .leaflet-popup-content-wrapper {
    border-radius: 4px;
    padding: 0;
  }
  .leaflet-popup-content {
    margin: 0;
  }
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.otherMap {
  height: calc(100vh - 84px);
  position: relative;
  .map {
    height: 100%;
    background-color: transparent;
  }
  .leftList {
    z-index: 999;
    color: #ffffff;
    position: absolute;
    left: 20px;
    top: 20px;
    bottom: 20px;
    width: 700px;
    background-color: rgba(4, 15, 45, 0.5);
    border: 1px solid rgba(23, 110, 217, 0.3);
    backdrop-filter: blur(16px);
    .title {
      height: 42px;
      // width: 600px;
      font-family: PangMenZhengDao;
      font-size: 20px;
      padding-left: 44px;
      padding-right: 20px;
      background-image: url("~@/assets/img/dispatch/titleLongBg.png");
      background-size: 100% 100%;
      line-height: 42px;
    }
    .lineBox {
      padding: 10px 20px;
      display: flex;
      justify-content: space-between;
      .line {
        width: 120px; 
        height: 36px;
        // margin-bottom: 20px;
        margin-right: 20px;
        padding: 0 5px;
        font-size: 14px;
        opacity: 0.75;
        text-align: center;
        line-height: 36px;
        // background-image: url("~@/assets/img/dispatch/defaultLine.png");
        // background-size: 100%;
        background: linear-gradient(270deg, rgba(3, 251, 255, 0.01) 0%, rgba(5, 252, 255, 0.58) 39%, rgba(6, 252, 255, 0.6) 43%, rgba(6, 253, 255, 0.61) 47%, rgba(7, 253, 255, 0.01) 100%);
        cursor: pointer;
      }
      .active {
        // background-image: url("~@/assets/img/dispatch/activeLine.png");
        opacity: 1;
      }
    }
    .query {
      padding: 0 20px 10px;
      // display: flex;
      // justify-content: space-between;
    }
    .list {
      // height: calc(100% - 297px);
      height: calc(100% - 200px);
      overflow-y: hidden;
      padding: 0 20px;
    }
    .listHeader {
      font-size: 14px;
      background-color: #012650;
      height: 34px;
      display: flex;
      align-items: center;
    }
    .scrollBox {
      height: calc(100% - 34px);
    }
    .listItem {
      font-size: 12px;
      display: flex;
      align-items: center;
      // justify-content: space-between;
      height: 30px;
      // background-color: rgba(2,50,128,0.23);
      border-bottom: 1px solid #023280;
      margin-top: 5px;
      cursor: pointer;
    }
    .pageBox {
      height: 46px;
      padding-top: 10px;
      padding-left: 15px;
    }
  }
}
</style>