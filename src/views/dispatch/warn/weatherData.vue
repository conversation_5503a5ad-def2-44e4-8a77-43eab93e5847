<template>
  <div class="WeatherData">
    <div class="query">
      <span class="param">
        <span>日期：</span>
        <span style="width: 200px;">
          <el-date-picker
            v-model="time"
            value-format="yyyy-MM-dd"
            type="date"
            :clearable="false"
            :picker-options="pickerOptions"
            @change="endTimeChange"
            size="medium">
          </el-date-picker>
        </span>
      </span>
      <span class="param">
        <span>参数：</span>
        <el-select v-model="param" size="medium" style="width: 200px;" @change="paramChange">
          <el-option
            v-for="item in paramOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="handleQuery">确定</el-button>
    </div>

    <div style="height: 300px;">
      <LineChart :xAxisData="xData" :yAxisName="yAxisName" :seriesData="seriesData" />
    </div>

    <div class="query">
      <span class="param">
        <span>日期：</span>
        <span style="width: 200px;">
          <el-date-picker
            v-model="pageDataTime"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            size="medium">
          </el-date-picker>
        </span>
      </span>
      <span class="param">
        <span>来源：</span>
        <el-select v-model="source" size="medium" style="width: 200px;">
          <el-option
            v-for="item in sourceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="getPageData">确定</el-button>
    </div>

    <el-table
      height="calc(100% - 448px)"
      :data="tableData"
      size="medium"
    >
      <el-table-column label="日期" prop="time"></el-table-column>
      <el-table-column label="来源">
        <template slot-scope="{ row }">{{ row.source | sourceFilter }}</template>
      </el-table-column>
      <el-table-column label="天气" prop="weatherStr"></el-table-column>
      <el-table-column label="温度(°C)">
        <template slot-scope="{ row }">{{ row.tempMin + ' ~ ' + row.tempMax }}</template>
      </el-table-column>
      <el-table-column label="降雨量(mm)" prop="precip"></el-table-column>
      <el-table-column label="风向" prop="wd"></el-table-column>
      <el-table-column label="风速(km/h)">
        <template slot-scope="{ row }">{{ row.wseMin + ' ~ ' + row.wseMax }}</template>
      </el-table-column>
      <el-table-column label="风力">
        <template slot-scope="{ row }">{{ row.ws }}级</template>
      </el-table-column>
      <el-table-column label="气压(100Pa)">
        <template slot-scope="{ row }">{{ row.wseMin + ' ~ ' + row.wseMax }}</template>
      </el-table-column>
      <el-table-column label="湿度" prop="sd"></el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination        
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>

  </div>
</template>

<script>
import { getWeatherDataPageList, getWeatherDataBy } from '@/api/dispatch/warn'

import { parseTime } from '@/utils'

import LineChart from './lineChart'

export default {
  name: 'WeatherData',
  components: { LineChart },
  data() {
    return {
      pickerOptions: {
        // 7-15天
        disabledDate(time) {
          return time.getTime() < Date.now() + 3600 * 24 * 6 * 1000 || time.getTime() >  Date.now() + 3600 * 24 * 15 * 1000
        }
      },
      startTime: '',
      time: '',
      param: 'temp',
      paramOptions: [
        {
          label: '温度',
          value: 'temp'
        },
        {
          label: '风速',
          value: 'wse'
        },
        {
          label: '气压',
          value: 'qy'
        },
        {
          label: '降雨量',
          value: 'precip'
        }
      ],
      weatherPic: null,
      xData: [],
      yAxisName: '温度(°C)',
      seriesData: [],
      // 表格
      sourceOptions: [
        {
          label: '中国天气网',
          value: 'ChinaWeather'
        },
        {
          label: '和风天气',
          value: 'QWeather'
        },
        {
          label: '彩云天气',
          value: 'CaiYunWeather'
        }
      ],
      pageDataTime:[],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      },
      source: '',
      tableData: []
    }
  },
  filters: {
    sourceFilter(value) {
      const labelMap = {
        'ChinaWeather': '中国天气网',
        'QWeather': '和风天气',
        'CaiYunWeather': '彩云天气'
      }
      return labelMap[value]
    }
  },
  created() {
    this.init()
  },
  mounted() {
    this.getChartData()
    this.getPageData()
  },
  methods: {
    init() {
      this.startTime = parseTime(new Date(), '{y}-{m}-{d}')
      this.time = parseTime((new Date().getTime()) + 3600 * 24 * 6 * 1000, '{y}-{m}-{d}')
      // 设置x轴数据
      this.xData = this.getXData(this.time)
    },
    getXData(endPoint) {
      let end = (new Date(endPoint).getTime())
      const interval = 24 * 3600 * 1000 // 1d
      let temp = []
      end = end + interval
      while(end > (new Date(this.startTime)).getTime()) {
        end = end - interval
        temp.push(parseTime(end, '{y}-{m}-{d}'))
      }
      return temp.reverse()
    },
    endTimeChange() {
      this.xData = this.getXData(this.time)
    },
    paramChange(param) {
      console.log(param)
      if(param === 'wse') {
        this.yAxisName = '风速(km/h)'
        this.seriesData = [
          {
            name: '最低风速',
            type: 'line',
            data: this.weatherPic.wseMin
          },
          {
            name: '最高风速',
            type: 'line',
            data: this.weatherPic.wseMax
          }
        ]
      } else if(param === 'qy') {
        this.yAxisName = '气压(100Pa)'
        this.seriesData = [
          {
            name: '最低气压',
            type: 'line',
            data: this.weatherPic.qyMin
          },
          {
            name: '最高气压',
            type: 'line',
            data: this.weatherPic.qyMax
          }
        ]
      } else if(param === 'precip') {
        this.yAxisName = '降水量(mm)'
        this.seriesData = [
          {
            name: '降水量',
            type: 'line',
            data: this.weatherPic.precip
          }
        ]
      } else {
        this.yAxisName = '温度'
        this.seriesData = [
            {
              name: '最低温度',
              type: 'line',
              data: this.weatherPic.tempMin
            },
            {
              name: '最高温度',
              type: 'line',
              data: this.weatherPic.tempMax
            }
          ]
      }
    },
    handleQuery() {
      this.getChartData()
    },
    // 天气画图数据
    getChartData() {
      let data = {
        beginTime: this.startTime,
        endTime: this.time,
        source: 'QWeather'
      }
      getWeatherDataBy(data).then(res => {
        const { status,data } = res
        if(status === 200) {
          this.weatherPic = data
          // 设置数据
          this.seriesData = [
            {
              name: '最低温度',
              type: 'line',
              data: this.weatherPic.tempMin
            },
            {
              name: '最高温度',
              type: 'line',
              data: this.weatherPic.tempMax
            }
          ]
        }
      })
    },
    // 天气数据表格
    getPageData() {
      let requestParams = {
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        beginTime: this.pageDataTime[0] ? this.pageDataTime[0] : null,
        endTime: this.pageDataTime[1] ? this.pageDataTime[1] : null,
        source: this.source ? this.source : null
      }
      getWeatherDataPageList(requestParams).then(res => {
        const { status, count, data } = res
        if(status === 200) {
          this.tableData = data
          this.pageInfo.total = count
        }
      })
    },
    handleSizeChange() {
      this.pageInfo.pageNum = 1
      this.getPageData()
    },
    handleCurrentChange() {
      this.getPageData()
    }
  }
}
</script>

<style lang="scss" scoped>
.WeatherData {
  height: 100%;
  .query {
    padding-bottom: 20px;
  }
  .param {
    margin-right: 20px;
  }
}
</style>
