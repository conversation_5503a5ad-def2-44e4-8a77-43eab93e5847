<template>
  <div class="disptch-warn app-container">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="dispatch" />
    <div class="tab">
      <div class="first" :class="{ active: currentType === 'warning' }" @click="handleTabClick('warning')">预警</div>
      <!-- <div class="other" :class="{ active: currentType === 'weather' }" @click="handleTabClick('weather')">天气预报</div> -->
      <div class="other" :class="{ active:currentType === 'risk'}" @click="handleTabClick('risk')">风险</div>
      <div class="other" :class="{ active:currentType === 'meter'}" @click="handleTabClick('meter')">用户用水报警</div>
      <div class="other" :class="{ active:currentType === 'report'}" @click="handleTabClick('report')">月度报告</div>
    </div>
    <div class="compenent-container">
      <WaterLevelWarn v-if="currentType === 'warning'" />
      <WeatherData v-if="currentType === 'weather'" />
      <RiskProfile v-if="currentType === 'risk'"/>
      <UserMeter v-if="currentType === 'meter'" />
      <MonthReort v-if="currentType === 'report'" />
    </div>
  </div>
</template>

<script>
import RiskProfile from './riskProfile.vue'
import WaterLevelWarn from './waterLevelWarn'
import WeatherData from './weatherData'
import UserMeter from './UserMeter'
import MonthReort from '../monthReport/forWarn'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  components: { 
    WaterLevelWarn,
    WeatherData,
    FloatingSideMenu,
    RiskProfile,
    UserMeter,
    MonthReort
  },
  data() {
    return {
      currentType: 'warning'
    }
  },
  methods: {
    handleTabClick(type) {
      this.currentType = type
    }
  }
}
</script>

<style lang="scss">
.disptch-monitor { 
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style lang="scss">
.disptch-warn { 
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.disptch-warn {
  padding: 0 30px;
  height: calc(100vh - 84px);
  .compenent-container {
    color: #ffffff;
    padding: 20px;
    height: calc(100% - 76px);
    border: 1px solid #2A5DD2;
    border-top-color: #3084B5;
  }
}
</style>
