<template>
  <div :class='className' :style='{ height: height, width: width }' />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    chartTitle: {
      type: String,
      default: ''
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        console.log('seriesData', val)
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      console.log('initChart')
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        title: {
          text: this.chartTitle,
          textStyle: {
            color: '#fff',
            fontSize: 16
          }
        },
        legend: {
          icon: 'circle',
          orient: 'vertical',
          right: 10,
          // bottom: 10,
          top: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          // formatter: (name) => {
          //   const target = this.seriesData.find((item) => item.name === name)
          //   return `${name}: ${target.length}km`
          // },
          itemWidth: 10,
          itemHeight: 10
        },
        tooltip: {
          trigger: 'item',
          // formatter: (params) => {
          //   // return `${params.name}: ${params.data.length}km (${params.percent}%)`;
          //   return '1'
          // },
        },
        series: [
          {
            color: [
              '#FB7B1E',
              '#FFC056',
              '#00D360',
              '#03A4FF',
              '#00E4FF',
              '#C6ECDF',
            ],
            type: 'pie',
            roseType: 'area',
            radius: ['55%', '70%'],
            center: ['40%', '45%'],
            avoidLabelOverlap: true, // 避免标签重叠
            data: this.seriesData,
            label: {
              formatter: '{d}%'
            }
          }
        ]
      })
    }
  }
}
</script>
