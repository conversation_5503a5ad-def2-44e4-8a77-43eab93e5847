<template>
  <div class="eqWarn">
    <el-table height="calc(100% - 106px)" :data="tableData" size="medium">
        <el-table-column prop="name" label="站点名称"></el-table-column>
        <el-table-column prop="alarmGrade" label="预警等级">
          <template slot-scope="scope">
            <span :style="setColor(scope.row.alarmGrade)" >
              {{ scope.row.alarmGrade  | levelFilter}}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="msg" label="预警信息"></el-table-column>
        <el-table-column prop="dataTime" label="预警时间"></el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <!-- <el-tooltip content="关闭预警" placement="bottom">
              <i class="el-icon-edit actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;" @click="closeWarn(scope.row)" v-if="scope.row.status === 1" />
            </el-tooltip> -->
            <el-tooltip content="生成工单" placement="bottom">
              <i
                class="el-icon-document-add actionIcon mr-4"
                style="color: #409eff; font-size: 19px; margin-right: 15px"
                @click="handleCreateOrder(scope.row.id)"
              />
            </el-tooltip>
            <!-- <el-tooltip content="删除" placement="bottom">
              <i class="el-icon-delete actionIcon" style="color: #f56c6c;font-size: 19px;margin-right: 15px;" @click="handleDelete(scope.row.id)" />
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageInfo.pageNo"
          :page-sizes="[10, 20, 30, 40]"
          :page-size.sync="pageInfo.pageSize"
          layout="total, sizes, ->, prev, pager, next, jumper"
          :total="pageInfo.total"
        >
        </el-pagination>
      </div>

   
  </div>
</template>

<script>
import { getWarnListByType } from '@/api/dispatch/warn'
import { createOrderByWarnId } from '@/api/order'

import { getWeekTs } from '@/utils/time'

export default {
  data() {
    return {
      time: [],
      tableData: [],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  created() {
    this.init()
  },
  mounted() {
    this.getData()
  },
  filters: {
    levelFilter(value) {
      const labelMap = {
        1: "【一级预警】",
        2: "【二级预警】",
        3: "【三级预警】",
      };
      return labelMap[value];
    },
  },
  methods: {
    init() {
      this.time = getWeekTs()
    },
    setColor(level) {
      const colors = ["", "#FE230A", "#FF9900", "#FFEF00"];
      return { color: colors[level] };
    },
    getData() {
      let data = {
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        warnType: 4,
        beginTime: this.time[0],
        endTime: this.time[1]
      }
      getWarnListByType(data).then(res => {
        console.log(res)
        const { status, data, count } = res
        if(status === 200) {
          this.tableData = data
          this.pageInfo.total = count
        }
      })
    },
    handleCreateOrder(warnId) {
      this.$prompt('可填写工单名称', '生成工单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        console.log(value)
        let data = {
          recordId: warnId
        }
        if(value) {
          data.name = value
        }
        createOrderByWarnId(data).then(res => {
          console.log(res)
          if(res.status === 200) {
            this.$message.success('生成工单成功')
          } else {
            this.$message.error('生成工单失败')
          }
        })
      }).catch(() => {
        console.log('cancle')
      })
    },
    // 分页
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.getData()
    },
    handleCurrentChange() {
      this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
.eqWarn {
  height: 100%;
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .pagination-container {
    height: 50px;
  }
}
</style>
