<template>
  <div class="waterLevelWarn">
    <div class="query">
      <div>
        <span class="param">
          <span>时间：</span>
          <span style="width: 200px; display: inline-box">
            <el-date-picker
              :clearable="false"
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="medium"
            >
            </el-date-picker>
          </span>
          <span style="margin-left: 20px;">预警类型:</span>
          <span style="display: inline-box">
            <el-select
              clearable
              placeholder="请选择"
              v-model="selectType"
              size="medium"
              style="width: 190px; margin-left: 10px"
            >
              <el-option v-for="item in typeList" :key="item.value" :label="item.type" :value="item.value"> </el-option>
            </el-select>
          </span>
        </span>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="medium"
          @click="handleQuery"
          >查询</el-button
        >
      </div>
    </div>
    <div class="top">
      <CountPie class="pieItem" :seriesData="statusCount" chartTitle="预警状态统计" />
      <CountPie class="pieItem" :seriesData="sourceCount" chartTitle="预警来源统计" />
    </div>
    <div class="bottom">
      <el-table height="100%" :data="tableData" size="medium">
        <el-table-column prop="name" label="站点名称"></el-table-column>
        <el-table-column prop="warnType" label="预警类型">
          <template slot-scope="scope">
            <span :style="setColor(scope.row.warnType)">
              {{ scope.row.warnType| typeFilter }}
            </span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="alarmGrade" label="预警等级">
          <template slot-scope="scope">
            <span :style="setColor(scope.row.alarmGrade)">
              {{ scope.row.alarmGrade | levelFilter }}
            </span>
          </template>
        </el-table-column> -->
        <el-table-column prop="value" label="监测数据"></el-table-column>
        <el-table-column prop="msg" label="预警信息"></el-table-column>
        <!-- <el-table-column prop="duration" label="持续时长">
          <template slot-scope="scope">
            <span>
              {{ scope.row.duration | durationFilter }}
            </span>
          </template>
        </el-table-column> -->
        <el-table-column prop="suggestion" label="解决方案"></el-table-column>
        <el-table-column prop="createTime" label="预警时间"></el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <!-- <el-tooltip content="关闭预警" placement="bottom">
              <i class="el-icon-edit actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;" @click="closeWarn(scope.row)" v-if="scope.row.status === 1" />
            </el-tooltip> -->
            <el-tooltip content="生成工单" placement="bottom">
              <i
                v-if="!scope.row.ticketId"
                class="el-icon-document-add actionIcon mr-4"
                style="color: #409eff; font-size: 19px; margin-right: 15px"
                @click="handleCreateOrder(scope.row.id, scope.row.ticketId)"
              />
            </el-tooltip>
            <!-- <el-tooltip content="删除" placement="bottom">
              <i class="el-icon-delete actionIcon" style="color: #f56c6c;font-size: 19px;margin-right: 15px;" @click="handleDelete(scope.row.id)" />
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getWarnListByType } from "@/api/dispatch/warn";
import { getWqWarnCountWithStatus, getWqWarnCountWithSource } from '@/api/wq/warn'
import { createOrderByWarnId } from "@/api/order";
import { getWeekTs } from "@/utils/time";

import CountPie from './countPie'

export default {
  name: 'WarnList',
  components: { CountPie },
  data() {
    return {
      statusCount: [
        { name: '待指派', value: null, key: 'WAIT_DISTRIBUTED' },
        { name: '处理中', value: null, key: 'PROCESSING' },
        { name: '已处理', value: null, key: 'DONE' }
      ],
      sourceCount: [
        { name: '水源', value: null, key: 'WATER_SOURCE' },
        { name: '水厂', value: null, key: 'WATER_FACTORY' },
        { name: '末梢用户', value: null, key: 'END_USER' },
        { name: '官网', value: null, key: 'PIPE_NETWORK' },
        { name: '蓄水池', value: null, key: 'WATER_STORAGE_POND' }
      ],
      typeList:[
        { value: 'WATER_QUALITY', type: '水质预警'},
        { value: 'WATER_LEVEL', type: '水位预警'},
        { value: 'WATER_TRAFFIC', type: '流量预警'},
        { value: 'EQUIPMENT', type: '设备预警'},
        { value: 'WATER_PRESSURE', type: '压力预警'},
        { value: 'WEATHERWARN', type: '天气预警'},
        { value: 'NATIONALWARNING', type: '国家地质灾害气象风险预警'},
      ],
      selectType: null,
      time: [],
      tableData: [],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
      },
    };
  },
  computed: {
    warnType() {
      return this.$route.query.warnType
    }
  },
  created() {
    this.init();
  },
  mounted() {
    if (this.warnType) {
      this.selectType = this.warnType
    }
    this.handleQuery()
  },
  filters: {
    levelFilter(value) {
      const labelMap = {
        1: "【一级预警】",
        2: "【二级预警】",
        3: "【三级预警】",
      };
      return labelMap[value];
    },
    typeFilter(value){
      const typeMap ={
        'WATER_QUALITY':'水质预警',
        'WATER_LEVEL':'水位预警',
        'WATER_TRAFFIC':'流量预警',
        'EQUIPMENT':'设备预警',
        'WATER_PRESSURE':'压力预警',
        'WEATHERWARN':'天气预警',
        'NATIONALWARNING':'国家地质灾害气象风险预警',
      }
      return typeMap[value];
    },
    durationFilter(durationString) {
      const reg = /PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+(?:\.\d+)?)S)?/
      let temp = ''
      if(durationString) {
        const match = durationString.match(reg)

        if(match) {
          let hours = parseInt(match[1]) || 0
          const minutes = parseInt(match[2]) || 0
          const seconds = parseInt(match[3]) || 0

          let days = 0
          let H = 0
          if(hours) {
            if(hours >= 24) {
              days = Math.floor(hours / 24)
              H = hours % 24
              temp = minutes ? `${days}天${H}时${minutes}分` : `${days}天${H}时`
            } else {
              temp = minutes ? `${H}时${minutes}分` : `${H}时`
            }
          } else {
            temp = '不足一分钟'
          }
          
        }
      }
      return temp
    }
  },
  methods: {
    init() {
      this.time = getWeekTs();
    },
    setColor(level) {
      const colors = ["", "#FE230A", "#FF9900", "#FFEF00"];
      return { color: colors[level] };
    },
    handleQuery() {
      this.getPieData()
      this.getData()
    },
    getData() {
      let temp =  this.time || []
      let data = {
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        orderBy: 'createTime',
        beginTime: temp.length ? this.time[0] : '',
        endTime: temp.length ? this.time[1] : '',
        warnType: this.selectType
      };
      getWarnListByType(data).then((res) => {
        const { status, data, count } = res;
        if (status === 200) {
          this.tableData = data;
          this.pageInfo.total = count;
          // data.map(item => {
          //   console.log(item.value, '-->', item.name)
          // })
        }
      });
    },
    getPieData() {
      const params = {
        begin: this.time[0],
        end: this.time[1]
      }
      getWqWarnCountWithStatus(params).then(res => {
        const list = res.data || []
        this.statusCount.forEach(item => {
          const matchedItem = list.find(innerItem => innerItem.key === item.key)
          if (matchedItem) item.value = matchedItem.count
        })
      })
      getWqWarnCountWithSource(params).then(res => {
        const list = res.data || []
        this.sourceCount.forEach(item => {
          const matchedItem = list.find(innerItem => innerItem.key === item.key)
          if (matchedItem) item.value = matchedItem.count
        })
      })
    },
    handleCreateOrder(warnId, ticketId) {
      this.$prompt("可填写工单名称", "生成工单", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /\S/,
        inputErrorMessage: '工单名称不能为空'
      })
        .then(({ value }) => {
          let data = {
            recordId: warnId,
          };
          if (value) {
            data.name = value.trim();
          }
          if(!ticketId) {
            createOrderByWarnId(data).then((res) => {
              if (res.status === 200) {
                this.$message.success("生成工单成功");
              } else {
                this.$message.error("生成工单失败");
              }
            });
          } else {
            this.$message.warning('当前预警已生成工单')
          }
          this.getData()

        })
        .catch(() => {
          // console.log("cancle");
        });
    },
    // 分页
    handleSizeChange() {
      this.pageInfo.pageNo = 1;
      this.getData();
    },
    handleCurrentChange() {
      this.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.waterLevelWarn {
  height: 100%;
  .top {
    height: 240px;
    display: flex;
    justify-content: space-around;
    .pieItem {
      width: 25% !important;
    }
  }
  .bottom {
    height: calc(100% - 326px);
    .tableHeight {
      height: calc(100% - 50px);
    }
  }
  .query {
    padding-bottom: 20px;
    display: flex;
    justify-content: space-between;
    .param {
      margin-right: 20px;
    }
  }
  .pagination-container {
    height: 50px;
  }
}
</style>
