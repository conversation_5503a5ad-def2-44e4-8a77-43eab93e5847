<template>
  <!-- 用户用水报警 -->
  <div class="userMeter">
    <!-- 查询 -->
    <div class="query">
      <div>
        <span class="param">
          <span>时间：</span>
          <span style="width: 200px; display: inline-box">
            <el-date-picker :clearable="false" v-model="time" type="datetimerange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" size="medium">
            </el-date-picker>
          </span>
        </span>
        <!-- <span style="margin-left: 20px;">预警类型:</span>
        <span style="display: inline-box">
          <el-select
            clearable
            placeholder="请选择"
            v-model="alarm"
            style="width: 190px; margin: 0 10px;"
            size="medium"
          >
            <el-option v-for="item in typeList" :key="item.code" :label="item.alarmName" :value="item.code"> </el-option>
          </el-select>
        </span> -->
        <el-button type="primary" icon="el-icon-search" size="medium" @click="getData">查询</el-button>
        <!-- <el-button type="success" icon="el-icon-plus" size="medium" @click="openDialog">新增</el-button> -->
      </div>
    </div>
    <el-table height="calc(100% - 106px)" :data="tableData" size="medium">
      <el-table-column prop="name" label="用户名"></el-table-column>
      <el-table-column prop="meterAddress" label="水表编号"></el-table-column>
      <el-table-column prop="actualWater" label="用水量(m³)"></el-table-column>
      <el-table-column prop="description" label="描述"></el-table-column>
      <!-- <el-table-column prop="duration" label="持续时长">
        <template slot-scope="scope">
          <span>
            {{ scope.row.duration | durationFilter }}
          </span>
        </template>
      </el-table-column> -->
      <el-table-column prop="address" label="用户地址"></el-table-column>
      <el-table-column prop="time" label="日期"></el-table-column>
      
      <el-table-column label="操作">
        <template slot-scope="scope">
          <!-- <el-tooltip content="查看" placement="bottom">
            <i class="el-icon-view actionIcon mr-4" style="color: #409eff; font-size: 19px; margin-right: 15px"
              @click="handleView(scope.row.id)" />
          </el-tooltip>
          <el-tooltip content="修改" placement="bottom">
            <i class="el-icon-edit actionIcon mr-4" style="color: #409eff; font-size: 19px; margin-right: 15px"
              @click="handleEdit(scope.row.id)" />
          </el-tooltip>
          <el-tooltip content="启动应急预案" placement="bottom">
            <i class="el-icon-collection actionIcon mr-4" style="color: #409eff; font-size: 19px; margin-right: 15px"
              @click="handleStartCase(scope.row)" />
          </el-tooltip> -->
          <el-tooltip content="删除" placement="bottom">
            <i class="el-icon-delete actionIcon" style="color: #f56c6c; font-size: 19px; margin-right: 15px"
              @click="handleDelete(scope.row.id)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNo" :page-sizes="[10, 20, 30, 40]" :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper" :total="pageInfo.total">
      </el-pagination>
    </div>
    <!-- 新增弹窗 -->
    <el-dialog title="新增" :visible.sync="addVisible" width="35%" top="10vh">
      <el-form :model="baseForm" :rules="rules" ref="rules" label-width="80px" size="medium">
        <el-form-item label="设施：" prop="facilityName" style="width: 310px">
          <el-cascader v-model="stationTemp" :options="stationList" :props="propObject" clearable>
          </el-cascader>
        </el-form-item>
        <el-form-item label="预警类型：">
          <el-select v-model="baseForm.alarm" style="width: 200px">
            <el-option v-for="item in typeList" :key="item.code" :label="item.alarmName" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="图片：">
          <div class="fileItem" @click="fileTrigger">
            <div v-if="!previewUrl" class="fileTrigger">
              <i class="el-icon-plus" style="color: #fff; font-size: 24px"></i>
            </div>
            <div v-else>
              <img :src="previewUrl" alt="" class="fileTrigger" />
            </div>
          </div>
          <input type="file" accept=".png, .jpg, .jpeg" @change="fileChange" ref="fileRef1" id="fileRef" />
        </el-form-item>
        <el-form-item label="视频：">
          <el-button type="success" @click="chooseFile">选择文件</el-button>
          <div v-if="videoFileName">{{ videoFileName }}</div>
          <input type="file" accept=".mp4, " @change="videoFileChange" ref="fileRef" id="fileRef" />
        </el-form-item>
        <el-form-item label="地址：">
          <el-input v-model="baseForm.address"></el-input>
        </el-form-item>
        <el-form-item label="预测描述：">
          <el-input type="textarea" v-model="baseForm.description"></el-input>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input v-model="baseForm.remark"></el-input>
        </el-form-item>
        <el-form-item label="状态：">
          <el-select v-model="baseForm.status" placeholder="请选择" style="width: 200px">
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <div class="actionBox">
          <el-button type="primary" @click="canceladd()">取消</el-button>
          <el-button type="primary" @click="handleSubmitAdd()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>

    <!-- 编辑弹窗 -->
    <el-dialog title="编辑" :visible.sync="editVisible" width="35%">
      <el-form :model="baseForm" :rules="rules" ref="rules" label-width="80px" size="medium">
        <el-form-item label="设施名称：" style="width: 310px">
          <el-input v-model="baseForm.facilityName" disabled></el-input>
        </el-form-item>
        <el-form-item label="地址：">
          <el-input v-model="baseForm.address"></el-input>
        </el-form-item>
        <el-form-item label="预测描述：">
          <el-input type="textarea" v-model="baseForm.description"></el-input>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input v-model="baseForm.remark"></el-input>
        </el-form-item>
        <el-form-item label="状态：">
          <el-select v-model="baseForm.status" placeholder="请选择" style="width: 200px">
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="图片：">
          <div class="fileItem" @click="fileTrigger">
            <div v-if="!previewUrl" class="fileTrigger">
              <i class="el-icon-plus" style="color: #fff; font-size: 24px"></i>
            </div>
            <div v-else>
              <img :src="previewUrl" alt="" class="fileTrigger" />
            </div>
          </div>
          <input type="file" accept=".png, .jpg, .jpeg" @change="fileChange" ref="fileRef1" id="fileRef" />
        </el-form-item>
        <el-form-item label="视频：">
          <el-button type="success" @click="chooseFile">选择文件</el-button>
          <div v-if="videoFileName">{{ videoFileName }}</div>
          <input type="file" accept=".mp4, " @change="videoFileChange" ref="fileRef" id="fileRef" />
        </el-form-item>
        <div class="actionBox">
          <el-button type="primary" @click="cancelEdit()">取消</el-button>
          <el-button type="primary" @click="handleSubmitEdit()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>

    <!-- 详情 -->
    <el-dialog title="详情" :visible.sync="infoVisible" width="40%">
      <el-form :model="baseForm" ref="rules" label-width="auto" size="medium" disabled>
        <div class="infoContent">
          <div class="infoLeft">
            <el-form-item label="设施名称：" prop="facilityName">
              <el-input v-model="baseForm.facilityName"></el-input>
            </el-form-item>
            <el-form-item label="设施类型：">
              <el-select v-model="baseForm.stationTypeName"> </el-select>
            </el-form-item>
            <el-form-item label="预警类型：">
              <el-select v-model="baseForm.alarm">
                <el-option v-for="item in typeList" :key="item.code" :label="item.alarmName" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item label="处理解决时间：">
              <el-input v-model="baseForm.doneTime"></el-input>
            </el-form-item>
            <el-form-item label="地址：">
              <el-input v-model="baseForm.address"></el-input>
            </el-form-item>
            <div class="imgBox">
              <el-form-item label="点位照片：">
                <img v-if="baseForm.pic" :src="baseForm.pic" />
                <div v-else class="emptyImg">暂无照片</div>
              </el-form-item>
            </div>
          </div>
          <div class="infoRight">
            <el-form-item label="经纬度坐标：">
              <el-input v-model="baseForm.coordinate"></el-input>
            </el-form-item>
            <el-form-item label="预测描述：">
              <el-input type="textarea" v-model="baseForm.description"></el-input>
            </el-form-item>
            <el-form-item label="备注：">
              <el-input v-model="baseForm.remark"></el-input>
            </el-form-item>
            <el-form-item label="状态：">
              <el-input v-model="baseForm.status"></el-input>
            </el-form-item>
            <div class="videoBox">
              <el-form-item label="点位视频：">
                <div class="video">
                  <video v-if="baseForm.video" :src="baseForm.video" muted autoplay width="200px"
                    height="200px"></video>
                </div>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </el-dialog>

    <!-- 下发应急预案 -->
    <el-dialog title="启动应急预案" :visible.sync="caseVisible">
      <el-table 
        ref="multipleTable"
        height="350px"
        :data="caseList"
        row-key="id"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection"></el-table-column>
        <el-table-column label="预案名称" prop="name"></el-table-column>
        <el-table-column label="预案类型" prop="type.name"></el-table-column>
        <el-table-column label="预案等级" prop="level" :formatter="levelFormat"></el-table-column>
      </el-table>
      <div class="actionBox">
        <el-button type="primary" @click="cancelStartCase">取消</el-button>
        <el-button type="primary" @click="handleSubmitStartCase">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWeekTs } from "@/utils/time";
import {
  getRiskProfilePageList,
  getRiskProfileDetail,
  createRiskProfile,
  updateRiskProfile,
  uploadFile,
  startRiskPlansById,
  getUserMeterWarnPageList,
  deleteUserMeterWarn
} from "@/api/dispatch/warn";


export default {
  name: "riskProfile",
  data() {
    return {
      stationList: [],
      addVisible: false,
      editVisible: false,
      infoVisible: false,
      caseVisible: false,
      startPlansRiskId: null,
      caseList: [], // 应急预案列表
      multipleSelection: [], // 选中的应急预案
      propObject: {
        children: "list",
        label: "name",
        value: "id",
      },
      typeList: [],
      stationTemp: null,
      baseForm: {},
      rules: {
        facilityName: [
          { required: true, message: "请输入设施名称", trigger: "blur" },
        ],
      },
      imgFile: "", // 图片文件对象
      previewUrl: "", // 图片预览url
      videoFile: "",
      videoFileName: "",
      time: [],
      alarm: '',
      tableData: [],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
      },
      statusList: [],
    };
  },
  created() {
    this.init();
  },
  mounted() {
    this.getData();
  },
  filters: {
    durationFilter(durationString) {
      const reg = /PT(?:-(\d+)H)?(?:-(\d+)M)?(?:-(\d+(?:\.\d+)?)S)?/
      // const reg = /PT(?:-(\d+\.?\d*)H)?(?:-(\d+\.?\d*)M)?(?:-(\d+\.?\d*)S)?/
      let temp = ''
      if(durationString) {
        const match = durationString.match(reg)

        if(match) {
          console.log(match)
          let hours = parseInt(match[1]) || 0
          const minutes = parseInt(match[2]) || 0
          const seconds = parseInt(match[3]) || 0

          let days = 0
          let H = 0

          if(hours >= 24) {
            days = Math.floor(hours / 24)
            H = hours % 24
            temp = `${days}天${H}时${minutes}分`
          } else {
            temp = `${hours}时${minutes}分`
          }         
        }
      }
      return temp
    }
  },
  methods: {
    init() {
      this.time = getWeekTs();
    },
    getData() {
      let temp =  this.time || []
      let data = {
        orderBy: 'actualWater',
        asc: true,
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        beginTime: temp.length ? this.time[0] : '',
        endTime: temp.length ? this.time[1] : '',
      };
      // 获取分页信息
      getUserMeterWarnPageList(data).then((res) => {
        const { status, data, count } = res;
        if (status === 200) {
          this.tableData = data;
          this.pageInfo.total = count;
        }
      });
    },
    //获取预测详细信息
    getDetail(id) {
      getRiskProfileDetail(id).then((res) => {
        const { status, data } = res;
        if (status === 200) {
          const statusMap = {
            0: "新增",
            1: "核实",
            2: "督办",
            3: "已解除",
            4: "已处理",
          };
          if (status === 200) {
            this.baseForm = {
              id: data.id,
              facilityName: data.facilityName,
              stationTypeName: data.stationTypeName,
              stationType: data.stationType,
              status: data.status.code,
              alarm: data.alarm.code,
              address: data.address,
              remark: data.remark,
              coordinate:
                data.coordinate.x +
                "," +
                data.coordinate.y +
                "," +
                data.coordinate.z +
                ";",
              description: data.description,
              doneTime: data.doneTime,
              pic: data.pic,
              video: data.video,
            };
            this.previewUrl = data.pic
          }
        }
      });
    },
    //选择视频文件
    chooseFile() {
      this.$refs.fileRef.click();
    },
    //选择图片文件
    fileTrigger() {
      this.$refs.fileRef1.click();
    },
    //文件变化
    fileChange(e) {
      this.imgFile = e.target.files[0];
      this.previewUrl = URL.createObjectURL(this.imgFile);
      // 上传
      const fd = new FormData()
      fd.append('uploadFile', this.imgFile)
      uploadFile(fd).then(res => {
        if (res.status === 200) {
          this.$message.success('上传成功')
          this.baseForm.pic = res.data.annexPath
          this.$refs.fileRef1.value = null
        }
      })
    },

    // 视频文件变化
    videoFileChange(e) {
      this.videoFile = e.target.files[0];
      this.videoFileName = this.videoFile.name;
      // 上传
      const fd = new FormData()
      fd.append('uploadFile', this.videoFile)
      uploadFile(fd).then(res => {
        if (res.status === 200) {
          this.$message.success('上传成功')
          this.baseForm.video = res.data.annexPath
          this.$refs.fileRef.value = null
        }
      })
    },
    //打开详情弹窗
    handleView(id) {
      this.infoVisible = true;
      this.getDetail(id);
    },
    // 打开新增弹窗
    openDialog() {
      this.baseForm = {};
      this.previewUrl = ''
      this.addVisible = true;
    },
    // 取消新增
    canceladd() {
      this.previewUrl = null
      this.stationTemp = null
      this.baseForm = {}
      this.addVisible = false;
    },
    //提交新增信息
    handleSubmitAdd() {
      // 校验文件类型
      this.baseForm.stationType = this.stationTemp[0];
      this.baseForm.facilityId = this.stationTemp[1];
      createRiskProfile(this.baseForm).then((res) => {
        const { status, msg } = res;
        if (status === 200) {
          this.$message.success("新增风险预测成功");
          this.getData();
          this.canceladd()
        } else {
          this.$message.error("新增风险预测失败，请检查");
        }
      });
    },
    levelFormat(row) {
      if (row.level === 1) {
        return "一级"
      } else if (row.level === 2) {
        return "二级"
      } else if (row.level === 3) {
        return "三级"
      }
    },
    // 点击编辑
    handleEdit(id) {
      this.editVisible = true;
      this.getDetail(id);
    },
    // 打开应急预案弹窗
    handleStartCase(row) {
      this.caseVisible = true
      this.startPlansRiskId = row.id
      this.getList(row.emergencyPlans)
    },
    handleSelectionChange(value) {
      this.multipleSelection = value
    },
    cancelStartCase() {
      this.caseVisible = false
    },
    // 启动应急方案
    handleSubmitStartCase() {
      const plans = this.multipleSelection.map(item => item.id)
      startRiskPlansById(this.startPlansRiskId, plans).then(res => {
        if(res.status === 200) {
          this.$message.success('启动应急方案成功')
          this.getData()
          this.caseVisible = false
        } else {
          this.$message.error('启动应急方案失败')
        }
      })
    },
    // 取消编辑
    cancelEdit() {
      this.editVisible = false;
    },
    //提交编辑信息
    handleSubmitEdit(id) {
      const statusMap = {
        '新增': 0,
        '核实': 1,
        '督办': 2,
        '已解除': 3,
        '已处理': 4,
      };
      let data = {
        id: this.baseForm.id,
        status: statusMap[this.baseForm.status],
        address: this.baseForm.address,
        pic: this.baseForm.pic,
        video: this.baseForm.video,
        remark: this.baseForm.remark,
        description: this.baseForm.description,
      }
      updateRiskProfile(data).then((res) => {
        const { status, msg } = res;
        if (status === 200) {
          this.$message.success("编辑风险预测成功");
          this.cancelEdit();
          this.getData();
        } else {
          this.$message.error("编辑风险预测失败，请检查");
        }
      });
    },
    //删除信息
    handleDelete(id) {
      this.$confirm("此操作将永久删除该数据，是否继续？", "删除", {
        type: "error",
      })
        .then(() => {
          deleteUserMeterWarn(id).then((res) => {
            if (res.status === 200) {
              this.$message.success("删除成功");
              this.getData();
            } else {
              this.$message.error("删除失败");
            }
          });
        })
        .catch(() => { });
    },
    // 分页
    handleSizeChange() {
      this.pageInfo.pageNo = 1;
      this.getData();
    },
    handleCurrentChange() {
      this.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.userMeter {
  height: 100%;

  .query {
    padding-bottom: 20px;
    display: flex;
    justify-content: space-between;

    .param {
      margin-right: 20px;
    }
  }

  .actionBox {
    padding-bottom: 30px;
    display: flex;
    justify-content: center;
  }
}

.infoContent {
  // height: 500px;
  display: flex;

  .infoLeft {
    width: 500px;

    .imgBox {
      img {
        width: 250px;
        height: 125px;
      }
      .emptyImg {
        color: #fff;
      }
    }
  }

  .infoRight {
    width: 600px;
    margin-left: 20px;

    .videoBox {
      .video {
        width: 200px;
        height: 200px;
        background-color: black;
      }
    }
  }
}

.fileItem {
  display: flex;
  align-items: flex-end;
}

.fileTrigger {
  width: 80px;
  height: 80px;
  border: 1px solid #1382e6;
  text-align: center;
  line-height: 80px;
  cursor: pointer;
}

#fileRef,
#fileRef1 {
  display: none;
}
</style>