<template>
  <div ref="chart" :style="{ width: width, height: height }"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    // 图表数据
    chartData: {
      type: Array,
      required: true,
      default: () => []
    },
    // 图表标题
    title: {
      type: String,
      default: ''
    },
    // 宽度
    width: {
      type: String,
      default: '100%'
    },
    // 高度
    height: {
      type: String,
      default: '400px'
    },
    // 是否显示图例
    showLegend: {
      type: Boolean,
      default: true
    },
    // 自定义颜色
    color: {
      type: Array,
      default: () => [
        '#c23531', '#2f4554', '#61a0a8', '#d48265', '#91c7ae',
        '#749f83', '#ca8622', '#bda29a', '#6e7074', '#546570'
      ]
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    // 监听数据变化，重新渲染图表
    chartData: {
      handler(newVal) {
        if (this.chart) {
          this.updateChart(newVal)
        } else {
          this.initChart()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 初始化图表
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart(this.chartData)
    },
    // 更新图表数据
    updateChart(data) {
      const option = this.getOption(data)
      this.chart.setOption(option)
    },
    // 获取图表配置
    getOption(data) {
      return {
        title: {
          text: this.title,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          show: this.showLegend,
          orient: 'horizontal',
          bottom: 0,
          data: data.map(item => item.name)
        },
        series: [
          {
            name: this.title || '数据',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              color: params => {
                return this.color[params.dataIndex % this.color.length]
              }
            },
            label: {
              show: true,
              formatter: '{b}: {c} ({d}%)'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: data
          }
        ]
      }
    },
    // 处理窗口大小变化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>