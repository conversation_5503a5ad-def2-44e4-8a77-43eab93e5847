<template>
  <div class="simplePopup">
    <div class="title">
      <span>{{ name }}</span>
      <i class="el-icon-close close" @click="closePopup" />
    </div>
    <div class="line"></div>
    <div class="content">
      <div v-for="item, index in list" :key="index">{{ item }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimplePopup',
  props: {
    name: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  created() {
    console.log(1111)
  },
  methods: {
    closePopup() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.simplePopup {
  width: 233px;
  padding: 20px;
  background-color: rgba(5, 58, 127, 0.4);
  border-radius: 4px;
  border: 1px solid rgb(11, 11, 136);
  .title {
    display: flex;
    justify-content: space-between;
    span {
      width: 150px;
      // height: 26px;
      font-size: 16px;
      font-weight: 400;
      color: #61f9f8;
      padding-bottom: 5px;
    }
    .close {
      cursor: pointer;
      line-height: 16px;
      color: #ffffff;
      font-size: 16px;
      pointer-events:visible;
    }
  }
  .line {
    width: 178px;
    height: 1px;
    background-image: linear-gradient(
      to bottom left,
      rgba(97, 249, 248, 0),
      rgba(97, 249, 248, 1)
    );
  }
  .content {
    margin-top: 10px;
    font-size: 12px;
  }
}
</style>