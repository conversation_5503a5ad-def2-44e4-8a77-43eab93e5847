<template>
  <div class="rightPane app-container">
    <!-- 实时数据 -->
    <div class="realTime screen-common">
      <div class="title"><span>实时数据</span></div>
      <div class="outerBox common-card">
        <div class="listHeader" style="text-align: center;">
          <span>水厂名称</span>
          <span>水池液位</span>
          <span>出水流量</span>
          <span>更新时间</span>
        </div>
        <!-- 滚动 -->
        <VueSeamlessScroll
          :data="list"
          :class-option="classOption"
          style="overflow: hidden;height: calc(100% - 34px);"
        >
        <div class="listItem" v-for="item in list" :key="item.id" style="text-align: center;">
          <div>{{ item.name }}</div>
          <div>{{ item.waterLevel.waterLevelValue || '--' }}</div>
          <div>{{ item.waterLevel.speed || '--' }}</div>
          <div>{{ item.time }}</div>
        </div>
        </VueSeamlessScroll>
      </div>
    </div>

    <!-- 调度指令 -->
    <div class="cmd screen-common">
      <div class="title"><span>调度指令</span></div>
      <div class="listBox common-card">
        <div class="listHeader" style="text-align: center;">
          <span>执行厂站</span>
          <span>调度类型</span>
          <span>发送时间</span>
          <span>状态</span>
        </div>
        <!-- 滚动 -->
        <VueSeamlessScroll
          :data="cmdList"
          :class-option="classOption"
          style="overflow: hidden;height: calc(100% - 34px);"
        >
        <div class="listItem" v-for="item, index in cmdList" :key="index" style="text-align: center;">
          <div>{{ item.siteName }}</div>
          <div>{{ item.dispatchType.name }}</div>
          <div>{{ item.createTime ? item.createTime.slice(5, -3) : '--' }}</div>
          <div>
            <span class="base yes" v-if="item.status.code === 'FINISHED'">{{ item.status.name }}</span>
            <span class="base no" v-else>{{ item.status.name }}</span>
          </div>
        </div>
        </VueSeamlessScroll>
      </div>
    </div>

    <!-- 预警预报 -->
    <div class="center screen-common">
      <div class="title"><span>预警预报</span></div>
      <div class="centerBox common-card">
        <!-- 列表 -->
        <!-- <div class="centenList">
          <div class="listItem" v-for="item, index in warnList" :key="index">
            <div :style="setColor(item.level)" style="width: 85px;">{{item.level | levelFilter}}</div>
            <div style="flex-grow: 1;">{{item.msg}}</div>
            <div style="width: 90px;">{{item.time}}</div>
          </div>
        </div> -->

        <VueSeamlessScroll
          :data="warnList1"
          :class-option="classOption1"
          style="overflow: hidden;height: 100%;"
        >
          <div 
            v-for="item in warnList1" :key="item.id"
            class="warnCard" :class="getClass(item.alarmGrade)"
          >
            <div class="name" @click="jumpToList(item.warnType)">{{ item.name }}--{{ item.warnType | typeFilter }}</div>
            <div class="paramBox">
              <div class="item">{{ item.dataTime }}</div>
              <div class="item">{{ item.duration | durationFilter }}</div>
              <div class="item1 ellipsis" style="width: 70%;padding-top: 20px;">{{ item.msg }}</div>
              <div class="item" style="width: 30%;padding-left: 20px;">{{ item.value }}</div>
            </div>
          </div>
        </VueSeamlessScroll>

        <!-- 卡片 -->
        <!-- <div class="warnCard red">
          <div class="name">青华水厂</div>
          <div class="paramBox">
            <div class="item">03-21 18:01</div>
            <div class="item">持续时间68分钟</div>
            <div class="item">出水压力高限</div>
            <div class="item">0.36 MPa</div>
          </div>
        </div>
        <div class="warnCard yellow">
          <div class="name">谭湾水厂</div>
          <div class="paramBox">
            <div class="item">03-21 18:01</div>
            <div class="item">持续时间68分钟</div>
            <div class="item">出水压力低限</div>
            <div class="item">0.15 MPa</div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import VueSeamlessScroll from 'vue-seamless-scroll'

import { getPointSpeedForMap } from '@/api/dispatch/realTimeMonitor'
import { getWarnListByType } from '@/api/dispatch/warn'
import { getDispatchCmdPageList } from '@/api/dispatch/dispatch'

export default {
  components: { VueSeamlessScroll },
  data() {
    return {
      // 实时数据
      list: [],
      classOption: {
        direction: 1,
        singleHeight: 35,
      },
      // 调度指令
      cmdList: [],
      // 预警预报
      warnList1: [],
      warnList2: [],
      classOption1: {
        direction: 1,
        singleHeight: 130
      }
    }
  },
  filters: {
    levelFilter(value) {
      const labelMap = {
        1: '【一级预警】',
        2: '【一级预警】',
        3: '【一级预警】'
      }
      return labelMap[value]
    },
    typeFilter(value){
      const typeMap ={
        'WATER_QUALITY':'水质预警',
        'WATER_LEVEL':'水位预警',
        'WATER_TRAFFIC':'流量预警',
        'EQUIPMENT':'设备预警',
        'WATER_PRESSURE':'压力预警',
        'WEATHERWARN':'天气预警',
        'NATIONALWARNING':'国家地质灾害气象风险预警',
      }
      return typeMap[value];
    },
    durationFilter(durationString) {
      const reg = /PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+(?:\.\d+)?)S)?/
      let temp = ''
      if(durationString) {
        const match = durationString.match(reg)

        if(match) {
          let hours = parseInt(match[1]) || 0
          const minutes = parseInt(match[2]) || 0
          const seconds = parseInt(match[3]) || 0

          let days = 0
          let H = 0
          if(hours) {
            if(hours >= 24) {
              days = Math.floor(hours / 24)
              H = hours % 24
              temp = minutes ? `${days}天${H}时${minutes}分` : `${days}天${H}时`
            } else {
              temp = minutes ? `${H}时${minutes}分` : `${H}时`
            }
          } else {
            temp = '不足一分钟'
          }
          
        }
      }
      return temp
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    setColor(level) {
      const colors = ['', '#FE230A', '#FF9900', '#FFEF00']
      return { 'color': colors[level]}
    },
    getClass(alarmGrade) {
      const classMap = {
        1: 'red',
        2: 'yellow',
        3: 'blue'
      }
      return alarmGrade ? classMap[alarmGrade] : ''
    },
    getData() {
      getPointSpeedForMap().then(res => {
        let data = res.data.factoryMonitorList || []
        this.list =  data.map(item => {
          return {
            id: item.id,
            name: item.name,
            waterLevel: item.waterLevel ? item.waterLevel : {},
            time: item.waterLevel.monitorTime ? item.waterLevel.monitorTime.slice(5, -3) : '--'
          }
        })
      })
      getDispatchCmdPageList({
        pageNo: 1,
        pageSize: 10,
      }).then(res => {
        this.cmdList = res.data || []
      })
      getWarnListByType({
        pageNo: 1,
        pageSize: 10,
      }).then(res => {
        this.warnList1 = res.data || []
      })
    },
    jumpToList(type) {
      console.log(type)
      this.$router.push({
        name: 'Warn',
        query: { warnType: type }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.rightPane {
  position: absolute;
  width: 430px;
  height: 100%;
  right: 30px;
  top: 0;
  .outerBox {
    // height: 290px;
    height: calc(100% - 35px);
    padding: 5px;
  }
  .realTime {
    height: 37%;
  }
  .cmd {
    // height: calc(100% - 650px);
    height: 37%;
    .listBox {
      height: calc(100% - 35px);
      padding: 5px;
      overflow: hidden;
    }
  }
  .center {
    // height: 325px;
    height: 26%;
    .centerBox {
      height: 280px;
      padding: 10px;
      .listItem {
        font-size: 12px;
        display: flex;
        align-items: center;
        height: 30px;
        background-color: rgba(2,50,128,0.23);
        border: 1px solid #023280;
        margin-bottom: 7px;
        padding-left: 9px;
      }
    }
    .warnCard {
      width: 410px;
      height: 130px;
      background-size: 100%;
      .name {
        font-size: 18px;
        padding-left: 30px;
        padding-top: 3px;
        cursor: pointer;
      }
      .paramBox {
        padding: 20px 20px 0;
        display: flex;
        flex-wrap: wrap;
        .item {
          width: 50%;
          margin-top: 5px;
        }
      }
    }
    .red {
      background-image: url('~@/assets/img/dispatch/redCard.png');
    }
    .yellow {
      margin-top: 10px;
      background-image: url('~@/assets/img/dispatch/yellowCard.png');
    }
    .blue {
      margin-top: 10px;
      background-image: url('~@/assets/img/dispatch/blueCard.png');
    }
  }
  .base {
    padding: 4px 8px;
  }
  .no {
    color: #FF5557;
    background: rgba(255,85,87,0.3);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #FF5557;
  }
  .yes {
    color: #00EEFD;
    background: rgba(0,238,253,0.3);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #00EEFD;
  }
  .listHeader {
    font-size: 14px;
    font-weight: bold;
    background-image: url("~@/assets/img/table.png");
    background-size: 100%;
    height: 34px;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    span {
      flex: 1;
    }
    span:first-child {
      // width: 93px;
      // padding-left: 30px;
    }
    span:last-child {
      // flex-grow: 1;
      // padding-right: 30px;
    }
  }
  .listItem {
    font-size: 12px;
    display: flex;
    align-items: center;
    height: 30px;
    background-color: rgba(0, 170, 255, 0.08);
    // border: 1px solid #023280;
    margin-top: 5px;
    div {
      width: 25%;
    }
  }
}
</style>
