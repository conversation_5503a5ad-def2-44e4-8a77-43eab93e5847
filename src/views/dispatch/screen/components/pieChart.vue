<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        color: ['#52D0DF', '#2A5DD2'],
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {d}%'
        },
        grid: {
          left: 0,
          right: 15,
          bottom: '3%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '90%'],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            itemStyle: {
              // shadowColor: 'rgba(4, 15, 45, 0.2)',
              // borderColor: 'rgba(4, 15, 45, 0.2)',
              // borderWidth: 5,
              // shadowBlur: 20,
            },
            data: this.seriesData
          }
        ]
      })
    }
  }
}
</script>
