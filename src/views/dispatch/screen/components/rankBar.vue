<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        grid: {
          top: 30,
          left: 0,
          right: 50,
          bottom: 30,
          containLabel: true
        },
        xAxis: {
          type: 'value',
          min: 0,
          axisLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'category',
          boundaryGap: false,
          inverse: true,
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#FFFFFF',
            fontSize: 12,
            formatter: function(value, index) {
              return '{no|No.'+ ((index + 1) % 6) + '} ' + value
            },
            rich: {
              no: {
                fontSize: 10,
                fontWeight: 'bold'
              }
            }
          },
          axisTick: {
            show: false
          },
          data: this.seriesData.map(item => item.label)
        },
        series: [
          {
            name: '年产销差率排名',
            type: 'bar',
            barWidth: 17,
            label: {
              show: true,
              position: 'right',
              formatter: '{c}m³',
              color: '#fff',
              fontWeight: 'bold'
            },
            data: this.seriesData,
            itemStyle: {
              normal: {
                color: function (params) {
                  var colorList = [
                    {
                      colorStops: [
                        {
                          offset: 0,
                          color: "#FF8A00"
                        },
                        {
                          offset: 1,
                          color: "#FFC809"
                        }
                      ]
                    },
                    {
                      colorStops: [
                        {
                          offset: 0,
                          color: "#2A5DD2"
                        },
                        {
                          offset: 1,
                          color: "#52D0DF"
                        }
                      ]
                    },
                  ]
                  if (params.dataIndex == 0) {
                    return colorList[0]
                  } else {
                    return colorList[1]
                  }
                },
                barBorderRadius: 15,
              }
            }
          }
        ]
      })
    }
  }
}
</script>
