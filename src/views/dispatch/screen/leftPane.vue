<template>
  <div class="leftPane app-container">
    <!-- 气象信息 -->
    <div class="weatherInfo screen-common">
      <div class="title"><span>气象信息</span></div>
      <div class="weatherBox common-card">
        <!-- <div class="top">
          <i :class="'qi-' + weatherInfo.icon" style="font-size: 60px;"></i>
          <div class="temp">
            <div style="font-size: 36px;">{{ weatherInfo.temp }}℃</div>
            <div style="font-size: 20px;">{{ weatherInfo.text }}</div>
          </div>
        </div> -->
        <div class="topPane">
          <section>
            <div class="dateMessage">
              <div>{{ date }}</div>
              <div style="font-size: 14px;color: #ACC6EA;">数据来源于和风天气</div>
            </div>

            <div class="innerPane">
              <div class="topPaneLeft">
                <!-- 图标 -->
                <i :class="'qi-' + weatherInfo.icon" style="font-size: 60px;"></i>
                <div class="topPaneLeftMessage">
                  <div class="temp">
                    <div style="font-size: 36px;">{{ weatherInfo.temp }}<span style="font-size: 16px;">℃</span></div>
                    <div class="level">51良</div>
                  </div>
                  <div>兴山县 | {{ weatherInfo.text  }}</div>
                </div>       
              </div>
              <div class="topPaneRight">
                <div>
                  <span class="label">风量：</span>
                  <span>{{ weatherInfo.windDir }}{{ weatherInfo.windScale }}</span>
                </div>
                <div>
                  <span class="label">温度：</span>
                  <span>3-16</span>
                </div>
                <div>
                  <span class="label">湿度：</span>
                  <span>{{ weatherInfo.humidity }}%</span>
                </div>
              </div>
            </div>
          </section>
        </div>
        <div>
          <LineChart :xAxisData="xData" :yAxisName="yAxisName" :seriesData="seriesData" height="180px" />
        </div>
        <!-- <div class="param">
          <div class="item">
            <div class="value">{{ weatherInfo.windScale }}级</div>
            <div class="label">{{ weatherInfo.windDir }}</div>
          </div>
          <div class="item">
            <div class="value">{{ weatherInfo.humidity }}%</div>
            <div class="label">相对湿度</div>
          </div>
          <div class="item">
            <div class="value">{{ weatherInfo.feelsLike }}℃</div>
            <div class="label">体感温度</div>
          </div>
          <div class="item">
            <div class="value">{{ weatherInfo.vis }}km</div>
            <div class="label">能见度</div>
          </div>
          <div class="item">
            <div class="value">{{ weatherInfo.precip }}mm</div>
            <div class="label">降水量</div>
          </div>
          <div class="item">
            <div class="value">{{ weatherInfo.pressure }}hPa</div>
            <div class="label">大气压</div>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 组织结构 -->
    <div class="structure screen-common">
      <div class="title"><span>组织结构</span></div>
      <!-- 卡片 -->
      <div class="structureBox common-card">
        <div class="typeBox">
          <div class="item" v-for="item, index in structureList" :key="index">
            <img :src="item.icon" alt="">
            <div class="itemRight">
              <div>{{item.value}}</div>
              <div style="font-size: 14px;font-weight: 400;">{{item.name}}(个)</div>
            </div>
          </div>
        </div>
        <!-- 列表 -->
        <div class="stationList">
          <div class="listHeader">
            <span>站点名称</span>
            <span>类型</span>
            <span>告警</span>
          </div>
          <!-- 滚动区域 -->
          <!-- <VueSeamlessScroll :data="stationList" class="warp">
            <div class="item">
              <div class="listItem" v-for="item, index in stationList" :key="index">
                <div>{{item.sourceName}}</div>
                <div>{{item.sourceTypeName}}</div>
                <div style="color:#FF9900;font-weight: 400;">{{item.count}}</div>
              </div>
            </div>
          </VueSeamlessScroll> -->
          <el-scrollbar class="scrollBox" ref="scrollDiv">
            <div style="height: 100%;min-height: 100%;">
              <div class="listItem" v-for="item, index in stationList" :key="index">
                <div>{{item.sourceName}}</div>
                <div>{{item.sourceTypeName}}</div>
                <div style="color:#FF9900;font-weight: 400;">{{item.count}}</div>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { getWeatherInfo } from '@/api/common'
import { getWeatherDataPageList } from '@/api/dispatch/warn'
import {  parseTime } from '@/utils'
import { getWeekday } from '@/utils/time'

import { getStationCount, getStationWarnCount } from '@/api/dispatch/screen'
import VueSeamlessScroll from 'vue-seamless-scroll'
import LineChart from '../warn/lineChart.vue'

export default {
  components: { VueSeamlessScroll, LineChart },
  data() {
    return {
      date: '',
      weatherInfo: {},
      xData: [],
      yAxisName: '温度(°C)',
      seriesData: [],
      structureList: [
        {
          name: '水厂',
          value: 9,
          icon: require('@/assets/img/ws/sc.png')
        },
        {
          name: '水源地',
          value: 6,
          icon: require('@/assets/img/ws/syd.png')
        },
        {
          name: '泵站',
          value: 24,
          icon: require('@/assets/img/ws/bz.png')
        },
        {
          name: '蓄水池',
          value: 20,
          icon: require('@/assets/img/ws/xsc.png')
        }
      ],
      stationList: []
    }
  },
  mounted() {
    this.date = parseTime(new Date(), '{y}-{m}-{d}') + ' ' + getWeekday()
    this.getData()
    window.addEventListener('resize', () => {
      this.$refs.scrollDiv.update()
    })
  },
  methods: {
    getData() {
      getWeatherInfo().then(res => {
        if(res.status === 200) {
          this.weatherInfo = res.data
        }
      })
      let now = new Date().getTime()
      let end = now + 60 * 60 * 1000 * 24 * 7
      let requestBody = {
        beginTime: parseTime(now, '{y}-{m}-{d}'),
        endTime: parseTime(end, '{y}-{m}-{d}'),
        source: "QWeather"
      }
      console.log(now)
      getWeatherDataPageList(requestBody).then(res => {
        if(res.status === 200) {
          const data = res.data.reverse()
          this.xData = data.map(item => item.time)
          this.seriesData = [
            {
              name: '最低温度',
              type: 'line',
              data: data.map(item => item.tempMin)
            },
            {
              name: '最高温度',
              type: 'line',
              data: data.map(item => item.tempMax)
            }
          ]
        }
      })
      getStationCount().then(res => {
        if(res.status === 200) {
          this.structureList[0].value = res.data.waterFactory
          this.structureList[1].value = res.data.waterSource
          this.structureList[2].value = res.data.waterPump
          this.structureList[3].value = res.data.waterStoragePond
        }
      })
      getStationWarnCount().then(res => {
        console.log(res)
        if(res.status === 200) {
          this.stationList = res.data
        }
      })
    }
  }
}
</script>

<style lang="scss">
.leftPane {
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
</style>

<style lang="scss" scoped>
.leftPane {
  position: absolute;
  width: 430px;
  height: 100%;
  top: 0;
  left: 30px;
  .weatherInfo {
    .flex {
      display: flex;
    }
    .dateMessage {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .innerPane {
      padding-top: 10px;
      display: flex;
      justify-content: space-between;
    }
    .topPaneLeft {
      display: flex;
      .topPaneLeftMessage {
        margin-left: 20px;
        .temp {
          display: flex;
          align-items: baseline;
        }
        .level {
          width: 40px;
          height: 18px;
          margin-left: 10px;
          background-color: #52D0DF;
          border-radius: 4px;
          font-size: 14px;
          color: #FFFFFF;
          text-align: center;
        }
      }
    }
    .topPaneRight {
      padding-right: 20px;
      .label {
        color: #ACC6EA;
      }
    }
    .weatherBox {
      height: 300px;
      padding: 10px;
      .top {
        display: flex;
        justify-content: center;
        .temp {
          padding-left: 20px;
        }
      }
      .param {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .item {
          width: 120px;
          text-align: center;
          background-color: #023280;
          border-radius: 5px;
          margin-top: 10px;
          padding: 10px 0;
          .value {
            font-weight: bold;
          }
        }
      }
    }
  }
  .structure {
    height: calc(100% - 335px);
    .structureBox {
      height: calc(100% - 35px);
    }
    .typeBox {
      padding: 10px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .item {
        width: 200px;
        height: 90px;
        background-image: url("~@/assets/img/ws/card.png");
        background-repeat: no-repeat;
        background-size: 100%;
        display: flex;
        align-items: center;
        padding-left: 20px;
        img {
          width: 59px;
          height: 64px;
        }
        .itemRight {
          padding-left: 14px;
          font-size: 24px;
          font-weight: bold;
        }
      }
    }
    .stationList {
      padding: 0 10px;
      height: calc(100% - 200px);
      .listHeader {
        font-size: 14px;
        background-image: url("../../../assets/img/table.png");
        background-size: 100%;
        height: 34px;
        display: flex;
        align-items: center;
        padding-left: 4%;
        span {
          width: 33%;
        }
      }
      .warp {
        height: 200px;
        overflow: hidden;
      }
      .scrollBox {
        height: calc(100% - 34px);
      }
      .listItem {
        font-size: 12px;
        display: flex;
        align-items: center;
        height: 28px;
        background-color: rgba(2,50,128,0.23);
        border: 1px solid #023280;
        margin-top: 5px;
        padding-left: 4%;
        div {
          width: 33%;
        }
      }
    }
  }
}
</style>
