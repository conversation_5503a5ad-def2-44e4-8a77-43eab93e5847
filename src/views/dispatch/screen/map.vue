<template>
  <div class="mapContainer">
    <div id="wsmap"></div>

    <div class="baseBox" @click="changeBase" :class="{ mapModeFull: mapMode }">
      <img src="@/assets/img/home/<USER>/earth.png" alt="">
    </div>

    <div class="legend">
      <div>
        <div class="check" @click="changelayer('pond')">
          <div class="checked" v-if="pondstate"></div>
        </div>
        <img src="../../../assets/img/pondIcon.png" alt="" />
        <div class="name">蓄水池</div>
      </div>
      <div>
        <div class="check" @click="changelayer('factory')">
          <div class="checked" v-if="factorystate"></div>
        </div>
        <img src="../../../assets/img/factoryIcon.png" alt="" />
        <div class="name">水厂</div>
      </div>
      <div>
        <div class="check" @click="changelayer('source')">
          <div class="checked" v-if="sourcestate"></div>
        </div>
        <img src="../../../assets/img/syd.png" alt="" />
        <div class="name">取水口</div>
      </div>
    </div>
  </div>
</template>

<script>
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import Ground from "@arcgis/core/Ground";
import WMSLayer from "@arcgis/core/layers/WMSLayer";
import WFSLayer from "@arcgis/core/layers/WFSLayer";
import GeoJSONLayer from "@arcgis/core/layers/GeoJSONLayer"
import WebTileLayer from "@arcgis/core/layers/WebTileLayer"
import Polygon from "@arcgis/core/geometry/Polygon"
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine"
import Graphic from "@arcgis/core/Graphic"

import axios from 'axios'
import Vue from 'vue'
import SimplePopup from './popup.vue'

export default {
  props: {
    mapMode: {
      type: Boolean
    }
  },
  data() {
    return {
      isSatellite: false,
      map: null,
      view: null,
      // imglayer: null,
      sourcelayer: null,
      factorylayer: null,
      pondlayer: null,
      pondstate: true,
      factorystate: true,
      sourcestate: true,
      geourl: "",
      borderLayer: null
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL;
    this.createmap();
  },
  methods: {
    createmap() {
      // this.imglayer = new WMSLayer({
      //   url: this.geourl + "/geoserver/img/wms",
      // });

      // 边界
      this.borderLayer = new GeoJSONLayer({
        id: 'borderLayer',
        name: 'borderLayer',
        url: '/border.json',
        renderer: {
          type: 'simple',
          symbol: {
            type: 'simple-fill',
            color: [255, 255, 255, 0],
            outline: {
              width: 2,
              color: '#00B3FF'
            }
          }
        }
      })
      // 天地图影像图-标注
      this.tdtImageNoteLayer = new WebTileLayer({
        id: 'tdtImageNoteLayer',
        name: 'tdtImageNoteLayer',
        urlTemplate: 'http://{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
        subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
        visible: true
      })
      // mapbox 卫星瓦片
      // this.mapBoxTileLayer = new WebTileLayer({
      //   id: 'mapBoxTileLayer',
      //   name: 'mapBoxTileLayer',
      //   urlTemplate: 'https://api.mapbox.com/v4/mapbox.satellite/{level}/{col}/{row}.webp?access_token=pk.eyJ1IjoidGFuZ2d1b2NoYW5nIiwiYSI6ImNtNzE4dGYxdTA0aHUya3B4dWViM3l3cWsifQ.awtc46Zfl0VCpgNd5JCFXQ',
      //   visible: false
      // })
      // 吉林一号
      this.JL1ImageLayer = new WebTileLayer({
        id: 'JLTileLayer',
        name: 'JLTileLayer',
        urlTemplate: 'https://api.jl1mall.com/getMap/{z}/{x}/{y}?mk=73ad26c4aa6957eef051ecc5a15308b4&tk=24277043ba78c68ff6ed3b50ce486283&pro=ca3a3754837f49d7ac3068edce0e65f7&sch=wmts',
        visible: false
      })

      this.map = new Map({
        basemap: {
          baseLayers: [
            // this.mapBoxTileLayer,
            this.tdtImageNoteLayer,
            this.JL1ImageLayer,
            this.borderLayer
          ]
        },
        // ground: new Ground({
        //   layers: [],
        //   surfaceColor: "transparent",
        //   opacity: 0,
        // }),
        // layers: [],
      });
      this.view = new MapView({
        container: "wsmap",
        map: this.map,
        // background: {
        //   type: "color",
        //   color: [255, 252, 244, 0],
        // },
        extent: {
          xmax: 111.15191207080001,
          xmin: 110.39483293,
          ymax: 31.591080100800003,
          ymin: 30.9630324254,
        },
        constraints: {
          minZoom: 11,
          maxZoom: 18
        }
        // spatialReference: {
        //   wkid: 4326,
        // },
      });
      this.view.ui.remove("attribution");
      this.view.ui.empty("top-left");
      this.view.popup.autoOpenEnabled = false

      // this.view.map.add(this.imglayer);


      // 取水口
      this.sourcelayer = new WFSLayer({
        url: this.geourl + "/geoserver/xingshan/wms",
        name: 'xingshan:tbl_water_take_dam',
        customParameters: {
          'CQL_FILTER': 'population > 200'
        },
        outFields: ['*'],
        renderer: {
          type: "simple", // autocasts as new SimpleRenderer()
          symbol: {
            type: "picture-marker",
            url: require("../../../assets/img/syd.png"),
            width: "20px",
            height: "27.5px",
          },
        },
      });
      // 水厂
      this.factorylayer = new WFSLayer({
        url: this.geourl + "/geoserver/xingshan/wms",
        name: 'xingshan:water_factory',
        customParameters: {
          'CQL_FILTER': 'people > 500'
        },
        outFields: ['*'],
        renderer: {
          type: "simple", // autocasts as new SimpleRenderer()
          symbol: {
            type: "picture-marker",
            url: require('../../../assets/img/factoryIcon.png'),
            width: "20px",
            height: "27.5px",
          },
        },
      });
      // 蓄水池
      this.pondlayer = new WFSLayer({
        url: this.geourl + "/geoserver/xingshan/wms",
        name: 'xingshan:water_storage_pond',
        customParameters: {
          'CQL_FILTER': 'max_storage > 200'
        },
        outFields: ['*'],
        renderer: {
          type: "simple", // autocasts as new SimpleRenderer()
          symbol: {
            type: "picture-marker",
            url: require('../../../assets/img/pondIcon.png'),
            width: "20px",
            height: "27.5px",
          },
        },
      });

      this.view.when(() => {
        this.view.zoom = 12
        this.view.goTo({
          center: [110.75210994390093, 31.25307163396126]
        })

        this.view.map.add(this.sourcelayer);
        this.view.map.add(this.factorylayer);
        this.view.map.add(this.pondlayer);
      })

      // 获取兴山县边界geojson
      let borderPolygon = null
      async function getBorder () {
        await axios.get('/border.json').then(res => {
        let ret = res.data.features[0].geometry.coordinates
          borderPolygon = new Polygon({
            rings: ret.map(item => item[0]),
            spatialReference: { wkid: 4326 }
          })
        })
      }
      this.view.whenLayerView(this.tdtImageNoteLayer).then(async layerView => {
        // 创建一个大的多边形
        const fullPolygon = new Polygon({
          rings: [
            [-180, 90],  // 左上角
            [-180, -90], // 左下角
            [180, -90],  // 右下角
            [180, 90],   // 右上角
            [-180, 90]   // 闭合
          ],
          spatialReference: { wkid: 4326 }
        })

        await getBorder()
    
        // 创建两个几何图形的差异
        const maskPolygon = geometryEngine.difference(fullPolygon, borderPolygon)
        const maskSymbol = {
          type: 'simple-fill',
          color: [0, 0, 0, 0.7],
          outline: null
        }
        const maskGraphic = new Graphic({
          geometry: maskPolygon,
          symbol: maskSymbol
        })
        this.view.graphics.add(maskGraphic)
     
      })


      this.view.on('click', (event) => {
        // console.log(this.view.popup)

        this.view.hitTest(event).then(response => {
          const results = response.results
          if(results.length > 0) {
            const ret = results[0]
            this.showPopup(ret)
          }
        })

      })
    },

    // 点位弹窗
    showPopup(ret) {
      this.view.popup.open({
        // 位置
        location: ret.mapPoint,
        content: '加载中...',
      })

      console.log(ret)
      let p = Vue.extend(SimplePopup)
      let popup = new p({
        propsData: {
          name: ret.graphic.attributes.station_name,
          list: [
            '标识符：' + (ret.graphic.attributes.identity || '-')
          ]
        }
      })
      popup.$mount()
      // 关闭弹窗
      popup.$on('close', () => {
        this.view.popup.close()
      })

      this.view.popup.content = popup.$el
    },
    changelayer(type) {
      switch (type) {
        case "pond":
          this.pondstate = !this.pondstate;
          this.pondlayer.visible = this.pondstate;
          break;
        case "factory":
          this.factorystate = !this.factorystate;
          this.factorylayer.visible = this.factorystate;
          break;
        case "source":
          this.sourcestate = !this.sourcestate;
          this.sourcelayer.visible = this.sourcestate;
          break;
      }
    },
    // 切换底图
    changeBase() {
      this.isSatellite = !this.isSatellite
      if(this.isSatellite) {
        // this.mapBoxTileLayer.visible = true
        this.JL1ImageLayer.visible = true
        this.tdtImageNoteLayer.visible = false
      } else {
        // this.mapBoxTileLayer.visible = false
        this.JL1ImageLayer.visible = false
        this.tdtImageNoteLayer.visible = true
      }
    },
  },
};
</script>
<!-- <style lang="scss">
.esri-view-width-medium .esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0.3);
}
.esri-popup__header {
  background-color: rgba(4, 15, 45, 0.3) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  background-color: rgba(4, 15, 45, 0) !important;
}
.esri-popup__icon,
.esri-icon-close {
  color: #fff !important;
}
.esri-popup__content {
  margin: 0;
}
.esri-feature {
  background-color: rgba(4, 15, 45, 0.3);
  padding: 0 15px 12px;
}
.esri-widget__table {
  color: #fff;
}
.esri-popup__header-title {
  background-color: rgba(4, 15, 45, 0) !important;
  color: #fff;
}
.esri-popup__pointer-direction {
  background-color: rgba(4, 15, 45, 0.3);
}

.esri-popup__footer {
  display: none;
}
.esri-popup__feature-menu {
  display: none;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
.esri-view-surface {
  outline: none !important;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: auto 0px Highlight !important;
  outline: auto 0px -webkit-focus-ring-color !important;
}
[class*="esri-popup--is-docked-top-"] .esri-popup__footer,
[class*="esri-popup--aligned-bottom-"] .esri-popup__footer {
  border-bottom: solid 1px #6e6e6e4d;
}
.esri-popup__header {
  border-top-left-radius: 5px !important;
  border-top-right-radius: 5px;
  background-color: #fff;
  color: #fff;
}
.esri-popup--shadow {
  box-shadow: 0 0 0 0 rgb(155, 155, 155) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  outline: none !important;
}
.esri-ui .esri-popup {
  border-radius: 5px !important;
}
.esri-popup {
  position: absolute !important;
}
.esri-popup__button {
  background-color: transparent !important;
  outline: none;
}
</style> -->
<style scoped lang="scss">
.mapContainer {
  height: 100%;
  position: relative;
  overflow: hidden;
  .baseBox {
    position: absolute;
    top: 20px;
    right: 480px;
    cursor: pointer;
    img {
      width: 20px;
      // height: 35px;
    }
  }
  .mapModeFull {
    right: 30px;
  }
}
#wsmap {
  width: 100%;
  height: 100%;
}

.legend {
  width: 140px;
  height: 120px;
  background: rgba(4, 15, 45, 0.5);
  position: absolute;
  right: 450px;
  bottom: 0;
  padding: 18px 14px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  .check {
    width: 16px;
    height: 16px;
    border: 1px solid #91d5ff;
    margin-right: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .legendimg {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: solid 1px #fff;
  }
  .checked {
    width: 8px;
    height: 8px;
    background: linear-gradient(180deg, #60dad1 0%, #1e9afc 100%);
  }
  div {
    display: flex;
    align-items: center;
    .name {
      font-size: 12px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      margin-left: 8px;
    }
  }
}
</style>