<template>
  <div class="rightPane app-container">
    <!-- 保供预测 -->
    <div class="top screen-common">
      <div class="title"><span>保供预测</span></div>
      <div class="topBox common-card">
        <!-- 数字 -->
        <section class="pic">
          <div style="flex-grow: 1;">
            <div class="infoBox">
              <span>日供水量</span>
              <div class="cellBox">
                <div v-for="b, index in info1" :key="index" class="numberCell">{{b}}</div>
              </div>
              <span style="color: #ACC6EA;font-size: 12px;">万m³</span>
            </div>
            <div class="infoBox" style="margin-top: 10px;">
              <span>可供水量</span>
              <div class="cellBox">
                <div v-for="b, index in info2" :key="index" class="numberCell">{{b}}</div>
              </div>
              <span style="color: #ACC6EA;font-size: 12px;">亿m³</span>
            </div>
          </div>
          <div class="day">
            <div class="value">120</div>
            <div class="label">保供天数</div>
          </div>
        </section>
        <!-- 预警 -->
        <section class="warnBox">
          <div 
            class="longCard"
            v-for="item, index in warnCard"
            :key="index"
            :style="{ backgroundImage: 'url(' + item.type + ')', color: item.color }"
          >
            <div class="">{{item.label}}</div>
            <div class="">{{item.value1}} <span style="color: #ACC6EA;font-weight: 400;">个镇</span></div>
            <div class="">{{item.value2}} <span style="color: #ACC6EA;font-weight: 400;">千人</span></div>
          </div>
        </section>
      </div>
    </div>

    <!-- 预警预报 -->
    <div class="center screen-common">
      <div class="title"><span>预警预报</span></div>
      <div class="centerBox common-card">
        <!-- 列表 -->
        <div class="centenList">
          <div class="listItem" v-for="item, index in warnList" :key="index">
            <div :style="setColor(item.level)" style="width: 85px;">{{item.level | levelFilter}}</div>
            <div style="flex-grow: 1;">{{item.msg}}</div>
            <div style="width: 90px;">{{item.time}}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 本月预测调度比 -->
    <div class="bottom screen-common">
      <div class="title"><span>本月预测调度比</span></div>
      <div class="bottomBox common-card">
        <div class="chartBox">
          <PieChart :seriesData="pieData" />
          <div class="pieTitle">本月预测调度比</div>
          <!-- <div class="sRate">{{pieData[0].rate}}</div> -->
        </div>
        <div class="desc">
          <div class="item">
            <div style="color: #ACC6EA;font-size: 14px;">本月人工调度方案（例）</div>
            <div class="value">
              <span>{{pieData[0].rate}}</span>
              <span style="color: #52D0DF;">{{pieData[0].value}}</span>
            </div>
          </div>
          <div class="item">
            <div style="color: #ACC6EA;font-size: 14px;">本月自动调度方案（次）</div>
            <div class="value">
              <span>{{pieData[1].rate}}</span>
              <span style="color: #2A5DD2;">{{pieData[1].value}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import PieChart from './components/pieChart'

import { getDispatchPlanStatistic } from '@/api/dispatch/screen'

export default {
  components: { PieChart },
  data() {
    return {
      info1: ['2', '0', '6', '4'],
      info2: ['0', '1', '5', '3'],
      warnCard: [
        {
          label: '黄色预警',
          value1: '0',
          value2: '0.00',
          type: require('@/assets/img/ws/yellow.png'),
          color: '#F5BD16'
        },
        {
          label: '橙色预警',
          value1: '0',
          value2: '0.00',
          type: require('@/assets/img/ws/orange.png'),
          color: '#F76300'
        },
        {
          label: '红色预警',
          value1: '0',
          value2: '0.00',
          type: require('@/assets/img/ws/red.png'),
          color: '#E8202B'
        }
      ],
      warnList: [
        {
          msg: '泵站名称-1 出水压力产生把报警',
          level: 1,
          time: '08-30 13:07:04'
        },
        {
          msg: '泵站名称-1 出水压力产生把报警',
          level: 3,
          time: '08-30 13:07:04'
        },
        {
          msg: '泵站名称-1 出水压力产生把报警',
          level: 2,
          time: '08-30 13:07:04'
        },
        {
          msg: '泵站名称-1 出水压力产生把报警',
          level: 2,
          time: '08-30 13:07:04'
        },
        {
          msg: '泵站名称-1 出水压力产生把报警',
          level: 3,
          time: '08-30 13:07:04'
        }
      ],
      pieData: [
        {
          name: '本月人工调度方案（例）',
          value: 0,
          rate: 0
        },
        {
          name: '本月自动调度方案（次）',
          value: 0,
          rate: 0
        }
      ],
    }
  },
  filters: {
    levelFilter(value) {
      const labelMap = {
        1: '【一级预警】',
        2: '【一级预警】',
        3: '【一级预警】'
      }
      return labelMap[value]
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    setColor(level) {
      const colors = ['', '#FE230A', '#FF9900', '#FFEF00']
      return { 'color': colors[level]}
    },
    getData() {
      getDispatchPlanStatistic().then(res => {
        if(res.status === 200) {
          const total = res.data.manualDispatchPlan + res.data.autoDispatchPlan
          let rate1 = (res.data.manualDispatchPlan / total * 100).toFixed(1)
          if(total === 0) {
            rate1 = 0
          }
          this.pieData = [
            {
              name: '本月人工调度方案（例）',
              value: res.data.manualDispatchPlan,
              rate: rate1 + '%'
            },
            {
              name: '本月自动调度方案（次）',
              value: res.data.autoDispatchPlan,
              rate: (100 - rate1) + '%'
            }
          ]
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.rightPane {
  position: absolute;
  width: 430px;
  height: 100%;
  right: 30px;
  top: 0;
  .top {
    .topBox {
      height: 300px;
      padding: 10px;
    }
    .pic {
      display: flex;
      .infoBox {
        display: flex;
        align-items: center;
      }
      .cellBox {
        display: flex;
        padding-left: 5px;
        .numberCell {
          width: 36px;
          height: 52px;
          background-image: url("~@/assets/img/ws/number_cell.png");
          background-size: 100%;
          color: #09FDFB;
          text-align: center;
          line-height: 52px;
          font-size: 30px;
          font-weight: bold;
          margin-right: 5px;
        }
      }
      
      .day {
        width: 118px;
        background-image: url("~@/assets/img/ws/day_bg.png");
        background-size: 100%;
        position: relative;
        .value {
          position: absolute;
          left: 50px;
          top: 33px;
          font-size: 30px;
          font-weight: bold;
        }
        .label {
          position: absolute;
          left: 41px;
          bottom: -6px;
          font-size: 16px;
        }
      }
    }
    .warnBox {
      padding-top: 14px;
      .longCard {
        height: 44px;
        background-size: 100%;
        display: flex;
        font-size: 14px;
        padding-left: 48px;
        font-weight: bold;
        margin-top: 6px;
        div {
          flex-grow: 1;
          text-align: center;
          line-height: 44px;
        }
      }
    }
  }
  .center {
    
    .centerBox {
      height: 198px;
      padding: 10px;
      .listItem {
        font-size: 12px;
        display: flex;
        align-items: center;
        height: 30px;
        background-color: rgba(2,50,128,0.23);
        border: 1px solid #023280;
        margin-bottom: 7px;
        padding-left: 9px;
      }
    }
  }
  .bottom {
    height: calc(100% - 568px);
    .bottomBox {
      height: calc(100% - 35px);
      display: flex;
      align-items: center;
    }
    .chartBox {
      height: 180px;
      width: 240px;
      position: relative;
      .pieTitle {
        font-size: 16px;
        color: #ACC6EA;
        text-align: center;
      }
      .sRate {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-weight: bold;
        font-size: 30px;
      }
    }
    .desc {
      .item {
        .value {
          display: flex;
          justify-content: space-around;
          font-size: 24px;
          font-weight: bold;
          padding-top: 10px;
        }
      }
    }
  }
}
</style>
