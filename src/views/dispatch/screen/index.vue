<template>
  <div class="WsScreen">
    <!-- <div id="map"></div> -->
    
    <WsMap :mapMode="isFull" />

    <!-- 面板控制 -->
    <div class="mapMode" @click="changeMapMode" :class="{ mapModeFull: isFull }">
      <img v-if="isFull" src="@/assets/img/home/<USER>/full-active.png" alt="">
      <img v-else src="@/assets/img/home/<USER>/full.png" alt="">
    </div>

    <LeftPane v-if="!isFull" class="setIndex" />
    <RightPane v-if="!isFull"  class="setIndex" />
    
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="dispatch" />
  </div>
</template>

<script>
import LeftPane from './leftPaneNew'
import RightPane from './rightPaneNew'
import WsMap from "./map";
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'WsScreen',
  components: { LeftPane, RightPane, WsMap, FloatingSideMenu },
  data() {
    return {
      isFull: false   
    }
  },
  mounted() {
    
  },
  methods: {
    changeMapMode() {
      this.isFull = !this.isFull
    }
  }
}
</script>

<style lang="scss" scoped>
.WsScreen {
  height: calc(100vh - 84px);
  color: #ffffff;
  // padding: 0 30px;
  position: relative;
  #map {
    width: 100%;
    height: 100%;
    position: absolute;
  }
  .mapMode {
    position: absolute;
    top: 20px;
    left: 490px;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .mapModeFull {
    left: 30px;
  }
  .setIndex {
    z-index: 1;
  }
}
</style>
