<template>
  <div class="leftPane app-container">
    <!-- 运行监测 -->
    <div class="top screen-common">
      <div class="title"><span>运行监测</span></div>
      <div class="cardBox common-card">
        <div class="card" v-for="item in monitorData" :key="item.label">
          <img :src="item.icon" alt="">
          <div>
            <div class="value">{{ item.value }}</div>
            <div class="label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 保供预测 -->
    <div class="center screen-common">
      <div class="title"><span>保供预测</span></div>
      <div class="topBox common-card">
        <!-- 数字 -->
        <section class="pic">
          <div style="flex-grow: 1;">
            <div class="infoBox">
              <span>日用水量</span>
              <div class="cellBox">
                <div v-for="b, index in info1" :key="index" class="numberCell">{{b}}</div>
              </div>
              <span style="color: #ACC6EA;font-size: 12px;">m³</span>
            </div>
            <div class="infoBox" style="margin-top: 10px;">
              <span>现存水量</span>
              <div class="cellBox">
                <div v-for="b, index in info2" :key="index" class="numberCell">{{b}}</div>
              </div>
              <span style="color: #ACC6EA;font-size: 12px;">m³</span>
            </div>
          </div>
          <div class="day">
            <div class="value">{{day}}</div>
            <div class="label">保供天数</div>
          </div>
        </section>
        <!-- 预警 -->
        <section class="warnBox">
          <div 
            class="longCard"
            v-for="item, index in warnCard"
            :key="index"
            :style="{ backgroundImage: 'url(' + item.type + ')', color: item.color }"
          >
            <div class="">{{item.label}}</div>
            <div class="">{{item.value1}} <span style="color: #ACC6EA;font-weight: 400;">个镇</span></div>
            <div class="">{{item.value2}} <span style="color: #ACC6EA;font-weight: 400;">千人</span></div>
          </div>
        </section>
      </div>
    </div>

    <!-- 供水排名 -->
    <div class="rank screen-common">
      <div class="title"><span>今日制水</span></div>
      <div class="chartBox common-card">
        <RankBar :seriesData="seriesData" height="220px" />
      </div>
    </div>
  </div>
</template>

<script>
import RankBar from './components/rankBar'

import { factorySupplyData } from '@/api/screenData'
import { getRunMonitor } from '@/api/dispatch/screen'

import { formatterNumber } from '@/utils/numberHelper'

export default {
  components: { RankBar },
  data() {
    return {
      monitorData: [
        {
          label: '水泵运行(台)',
          value: '',
          icon: require('@/assets/img/dispatch/pumpIcon.png')
        },
        {
          label: '管网平均压力',
          value: '',
          icon: require('@/assets/img/dispatch/pressureIcon.png')
        },
        {
          label: '泵站取水量(m³)',
          value: '',
          icon: require('@/assets/img/dispatch/take.png')
        },
        {
          label: '管网最高压力',
          value: '',
          icon: require('@/assets/img/dispatch/pressureIcon.png')
        },
        {
          label: '今日用水量(m³)',
          value: '',
          icon: require('@/assets/img/dispatch/support.png')
        },
        {
          label: '管网最低压力',
          value: '',
          icon: require('@/assets/img/dispatch/pressureIcon.png')
        }
      ],
      info1: ['0', '0', '0', '0', '0'],
      info2: ['0', '0', '0', '0', '0'],
      day: 0,
      warnCard: [
        {
          label: '黄色预警',
          value1: '0',
          value2: '0.00',
          type: require('@/assets/img/ws/yellow.png'),
          color: '#F5BD16'
        },
        {
          label: '橙色预警',
          value1: '0',
          value2: '0.00',
          type: require('@/assets/img/ws/orange.png'),
          color: '#F76300'
        },
        {
          label: '红色预警',
          value1: '0',
          value2: '0.00',
          type: require('@/assets/img/ws/red.png'),
          color: '#E8202B'
        }
      ],
      xAxisData: ['大礼水厂', '谭湾水厂', '双龙观水厂', '吴家坪水厂', '青华水厂'],
      seriesData: []
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      // 获取供水概况
      getRunMonitor().then(res => {
        console.log(res.data)
        let temp = res.data
        this.monitorData[0].value = `${temp.pumpRunning}/${temp.pumpTotal}`
        this.monitorData[1].value = temp.pressureAverage
        this.monitorData[2].value = temp.todayWaterGet
        this.monitorData[3].value = temp.pressureMax
        this.monitorData[4].value = temp.todayWaterSupply
        this.monitorData[5].value = temp.pressureMin
        // this.monitorData = res.data
        let str1 = formatterNumber(Number((temp.yesterdayWater).toFixed(0)), 5)
        this.info1 = str1.split('')
        let str2 =formatterNumber(Number((temp.restWater).toFixed(0)), 5)
        this.info2 = str2.split('')

        this.day = Math.floor(temp.restWater / temp.yesterdayWater)
      })
      // 获取排名数据
      factorySupplyData().then(res => {
        const target2 = res.data.find(item => item.type === 2)

        if(target2) {
          let list = [
            { code: 'daLi', label: '大礼水厂', value: target2.daLi },
            { code: 'tanWan', label: '谭湾水厂', value: target2.tanWan },
            { code: 'shuangLongGuan', label: '双龙观水厂', value: target2.shuangLongGuan },
            { code: 'wuJiaPing', label: '吴家坪水厂', value: target2.wuJiaPing },
            { code: 'qingHua', label: '青华水厂', value: target2.qingHua }
          ]
          list.sort((a, b) => b.value - a.value)
          this.seriesData = list
        }
        
      })
    }
  }
}
</script>

<style lang="scss">
.leftPane {
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
</style>

<style lang="scss" scoped>
.leftPane {
  position: absolute;
  width: 430px;
  height: 100%;
  top: 0;
  left: 30px;
  .cardBox {
    padding: 0 10px 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    .card {
      width: 190px;
      height: 72px;
      background-image: url('~@/assets/img/dispatch/paramBg.png');
      background-size: 100%;
      margin-top: 10px;
      display: flex;
      align-items: center;
      padding-left: 13px;
      .value {
        font-family: DIN;
        font-size: 24px;
        color: #11E6FF;
      }
      .label {
        font-size: 14px;
      }
    }
  }
  .top {
    height: 33%;
  }
  .center {
    height: 38%;
    .topBox {
      height: 300px;
      padding: 10px;
    }
    .pic {
      display: flex;
      .infoBox {
        display: flex;
        align-items: center;
      }
      .cellBox {
        display: flex;
        padding-left: 5px;
        .numberCell {
          width: 36px;
          height: 52px;
          background-image: url("~@/assets/img/ws/number_cell.png");
          background-size: 100%;
          color: #09FDFB;
          text-align: center;
          line-height: 52px;
          font-size: 30px;
          font-weight: bold;
          margin-right: 5px;
        }
      }
      
      .day {
        width: 118px;
        background-image: url("~@/assets/img/ws/day_bg.png");
        background-size: 100%;
        position: relative;
        .value {
          position: absolute;
          left: 50px;
          top: 33px;
          font-size: 30px;
          font-weight: bold;
        }
        .label {
          position: absolute;
          left: 41px;
          bottom: -6px;
          font-size: 16px;
        }
      }
    }
    .warnBox {
      padding-top: 14px;
      .longCard {
        height: 44px;
        background-size: 100%;
        display: flex;
        font-size: 14px;
        padding-left: 48px;
        font-weight: bold;
        margin-top: 6px;
        div {
          flex-grow: 1;
          text-align: center;
          line-height: 44px;
        }
      }
    }
  }
  .rank {
    // height: calc(100% - 626px);
    height: 29%;
    .chartBox {
      height: calc(100% - 35px);
      display: flex;
      align-items: center;
    }
  }
}
</style>
