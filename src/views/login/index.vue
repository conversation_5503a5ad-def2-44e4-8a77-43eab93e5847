<template>
  <div class="login-container">
    <!-- <div class="login-container-title">
      <img src="../../assets/login/loginTitle.png" alt="">
    </div> -->
    <div class="login-container-center">
      <div class="title">农村智慧供水平台</div>
      <div class="formBox">
        <div class="innerTitle">欢迎登录</div>
        <el-form
          v-show="isForget"
          ref="pwdForm"
          :model="pwdForm"
          :rules="pwdRules"
          class="login-form"
          auto-complete="on"
          style="top: 18%"
        >
          <div class="login-form-title">找回密码</div>
          <div class="interval" />
          <el-form-item prop="account">
            <el-input
              v-model="pwdForm.account"
              type="text"
              auto-complete="off"
              placeholder="请输入账号名、手机、邮箱"
            />
          </el-form-item>
          <el-form-item prop="code">
            <el-input
              v-model="pwdForm.code"
              auto-complete="off"
              placeholder="请输入验证码"
              style="width: 63%"
            />
            <div class="login-code">
              <img :src="codeUrl" class="login-code-img" @click="getCode" />
            </div>
          </el-form-item>
          <el-form-item prop="mail_code">
            <el-input
              v-model="pwdForm.mail_code"
              auto-complete="off"
              placeholder="请输入邮件验证码"
              style="width: 63%"
              :disabled="!pwdForm.account || !pwdForm.code"
              @keyup.enter.native="handleRestPwd"
            />
            <div class="login-code">
              <el-button
                size="medium"
                type="primary"
                style="width: 100%"
                :disabled="!pwdForm.account || !pwdForm.code"
                @click.native.prevent="handleGetCode"
              >
                <span>获取验证码</span>
              </el-button>
            </div>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="pwdForm.password"
              type="password"
              auto-complete="off"
              placeholder="请输入新密码"
              @keyup.enter.native="handleRestPwd"
            />
          </el-form-item>
          <el-form-item prop="checkPass">
            <el-input
              v-model="pwdForm.checkPass"
              type="password"
              auto-complete="off"
              placeholder="请再次输入新密码"
              @keyup.enter.native="handleRestPwd"
            />
          </el-form-item>
          <el-form-item style="width: 100%">
            <el-button
              :loading="loading"
              size="medium"
              type="primary"
              style="width: 100%"
              @click.native.prevent="handleRestPwd"
            >
              <span v-if="!loading">确 认</span>
              <span v-else>确 认 中...</span>
            </el-button>
          </el-form-item>
          <div class="pwd"><span @click="handleForgetPwd">去登录</span></div>
        </el-form>

        <el-form
          v-show="!isForget"
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          auto-complete="on"
        >
          <el-form-item prop="userName">
            <el-input
              v-model="loginForm.userName"
              type="text"
              auto-complete="off"
              placeholder="请输入用户名"
            >
              <i slot="prefix" class="el-input__icon el-icon-user"></i>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              show-password
              auto-complete="off"
              placeholder="请输入密码"
              @keyup.enter.native="handleLogin"
            >
              <i slot="prefix" class="el-input__icon el-icon-unlock"></i>
            </el-input>
          </el-form-item>
          <el-form-item prop="code">
            <div class="codeBox">
              <el-input
                v-model="loginForm.code"
                auto-complete="off"
                placeholder="请输入验证码"
                style="width: 55%;"
                @keyup.enter.native="handleLogin"
              >
                <img slot="prefix" src="@/assets/login/codeIcon.png" alt="" style="width: 22px;margin-top: 9px;">
              </el-input>
              <div class="login-code">
                <img :src="codeUrl" class="login-code-img" />
              </div>
              <div><i class="el-input__icon el-icon-refresh" @click="getCode" style="cursor: pointer;"></i></div>
            </div>
          </el-form-item>
          <!-- <div class="pwd"><span @click="handleForgetPwd">忘记密码?</span></div> -->
          <el-form-item style="width: 100%">
            <el-button
              :loading="loading"
              size="medium"
              type="primary"
              style="width: 100%;background-color: #00B8FF !important;"
              @click.native.prevent="handleLogin"
            >
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken, setToken, removeToken } from '@/utils/auth'
import { getCodeImg, sendMailCode, forgotPassword } from "@/api/user";
import { loginNew, getXKey, logoutNew, getInfo } from '@/api/user'
import JSEncrypt from 'jsencrypt'

export default {
  name: "Login",
  data() {
    var validateCheckPass = (rule, value, callback) => {
      if (value !== this.pwdForm.password) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      loginForm: {
        userName: "",
        password: "",
        codeId: "",
        code: "",
      },
      loginRules: {
        userName: [
          {
            required: true,
            trigger: "blur",
            message: "账号不能为空",
          },
        ],
        password: [
          {
            required: true,
            trigger: "blur",
            message: "密码不能为空",
          },
        ],
        code: [
          {
            required: true,
            // trigger: "change",
            message: "验证码不能为空",
          },
        ],
      },
      loading: false,
      codeUrl: "",
      redirect: undefined,
      isForget: false,
      pwdForm: {
        account: "",
        password: "",
        codeId: "",
        code: "",
        checkPass: "",
        mail_code: "",
      },
      pwdRules: {
        account: [
          {
            required: true,
            trigger: "blur",
            message: "账号不能为空",
          },
        ],
        password: [
          {
            required: true,
            trigger: "blur",
            message: "新密码不能为空",
          },
          {
            pattern:
              /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,16}$/,
            message: "密码不少于8位，包含大小写字母、数字、特殊字符至少三种",
          },
        ],
        code: [
          {
            required: true,
            trigger: "change",
            message: "验证码不能为空",
          },
        ],
        mail_code: [
          {
            required: true,
            trigger: "change",
            message: "邮件验证码不能为空",
          },
        ],
        checkPass: [
          {
            required: true,
            trigger: "blur",
            message: "请再次输入新密码",
          },
          { validator: validateCheckPass, trigger: "blur" },
        ],
        redirect: undefined
      },
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  mounted() {
    this.getCode();
    const timer = setInterval(() => {
      this.getCode()
    }, 1000 * 60 * 3)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer)
    })
  },
  methods: {
    getCode() {
      getCodeImg().then((res) => {
        this.codeUrl = "data:image/gif;base64," + res.data.imgBase64;
        if (this.isForget) {
          this.pwdForm.code = ''
          this.pwdForm.codeId = res.data.codeId;
        } else {
          this.loginForm.code = ''
          this.loginForm.codeId = res.data.codeId;
        }
      });
    },
    verifyUser(token) {
      const str = token.split('.')[1] || ''
      const decodedStr = atob(str)
      let info = decodedStr ? JSON.parse(decodedStr) : {}
      if(info && info.userName === this.loginForm.userName) {
        return true
      } else {
        return false
      }
    },
    encrpyPassword(publicKey, password) {
      const encryptor = new JSEncrypt()
      encryptor.setPublicKey(publicKey)
      return encryptor.encrypt(password)
    },
    handleLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const now = Date.now()
          let payload = {
            userName: this.loginForm.userName,
            // password: sha256(this.loginForm.password + ' ' + now),
            codeId: this.loginForm.codeId,
            code: this.loginForm.code,
            timestamp: now.toString()
          }

          const { data } = await getXKey()
          if(!data) {
            this.$message.error('登录失败，请稍后重试')
            return
          }
          payload.password = this.encrpyPassword(data, this.loginForm.password)

          loginNew(payload).then((res) => {
              if (res.status == 200) {
                const token = res.data.token
                if (this.verifyUser(token)) {
                  setToken(res.data.token)
                  localStorage.setItem('name', res.data.accountNo)
                  this.$router.push({ path: "/project" });
                }
                
                // this.$router.push({ path: this.redirect || "/project" });
              } else {
                this.$message.error({
                  message: res.msg,
                  duration: 5000,
                });
                this.getCode();
              }
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
              this.getCode();
            });
        } else {
          this.getCode();
          console.log("error submit!!");
          return false;
        }
      });
    },
    handleForgetPwd() {
      this.isForget = !this.isForget;
      if (this.$refs.pwdForm) {
        this.$refs.pwdForm.resetFields();
      }
      if (this.$refs.loginForm) {
        this.$refs.loginForm.resetFields();
      }
      this.getCode();
      this.loading = false;
    },
    handleGetCode() {
      const { account, codeId, code } = this.pwdForm;
      sendMailCode(account, "mail", codeId, code).then(() => {
        this.$message.success("邮件验证码发送成功");
      });
    },
    handleRestPwd() {
      this.$refs.pwdForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          const { code, mail_code, checkPass, ...data } = this.pwdForm;
          data.code = mail_code;
          forgotPassword(data)
            .then((res) => {
              this.$message.success(res.msg);
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.login-container {
  .el-input__inner {
    background-color: #FFFFFF !important;
    color: #333 !important;
  }
}
</style>

<style lang="scss" scoped>
.login-container {
  height: 100%;
  width: 100%;
  min-height: 720px;
  overflow: hidden;
  background: url("../../assets/login/bg.png") center no-repeat;
  background-size: 100% 100%;
  display: flex;
  padding-top: 10%;
  justify-content: center;

  .login-container-center {
    .title {
      font-size: 40px;
      font-weight: bold;
      color: #FFFFFF;
      padding-bottom: 60px;
      text-align: center;
    }
  }
  .formBox {
    padding: 50px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    .innerTitle {
      font-size: 30px;
      color: #0073FF;
      padding-bottom: 30px;
      text-align: center;
    }
    .el-input__icon {
      color: #333;
      font-weight: 600;
      font-size: 20px;
    }
  }
  .login-form {
    overflow: hidden;
    

    .interval {
      background-color: #effaff;
      width: 100%;
      height: 4px;
      margin-top: 5px;
      margin-bottom: 22px;
    }
    .codeBox {
      display: flex;
      justify-content: space-between;
    }
    .login-code {
      width: 115px;
      height: 40px;

      img {
        vertical-align: middle;
        height: 40px;
        width: 115px;
      }
    }
    .el-button--primary {
      // background-color: #3c63d7;
      // border-color: #3c63d7;
    }
    .pwd {
      width: 100%;
      text-align: right;
      font-size: 15px;
      color: #409eff;
      margin-top: -8px;
      margin-bottom: 10px;
      span{
        cursor: pointer;
      }
    }
  }
}
</style>
