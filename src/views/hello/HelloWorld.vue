<!--
 * @Author: your name
 * @Date: 2020-10-12 16:59:58
 * @LastEditTime: 2020-10-13 14:26:36
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \vue-demo\src\pages\hello\HelloWorld.vue
-->
<template>
  <div>
    <hawk-basic-table
      v-bind="mainList"
      @handlePageChange="changePagination"
      @handleSelectionChange="changeSelection"
      @handleSwitchStatus="switchStatus"
      @handleUpdate="modifyData"
      @handleResetPwd="resetPwd"
      @handleDelete="deleteData"
    ></hawk-basic-table>
  </div>
</template>

<script>
export default {
  name: "HelloWorld",
  data() {
    return {
      mainList: {
        listName: "list_demo",
        loading: false,
        data: [],
        staticColumns: [],
        columns: [
          { label: "用户名称", minWidth: 120, field: "name", fixed: "left" },
          { label: "登录名", minWidth: 120, field: "loginName" },
          { label: "所属机构", minWidth: 150, field: "deptName" },
          { label: "角色", minWidth: 100, field: "roleName" },
          { label: "手机号", minWidth: 120, field: "mobile" },
          {
            label: "创建时间",
            minWidth: 140,
            field: "createTime",
            prop: "timeFilter",
            format: "yyyy-MM-dd HH:mm",
          },
          {
            label: "状态",
            width: 100,
            type: "switch",
            field: "unlocked",
            power: "userSwitch",
            prop: "unlockStatus",
            fnType: "SwitchStatus",
          },
          { label: "操作", width: 180, type: "operate" },
        ],
        handleColumn: [
          { label: "修改", icon: "", type: "Update", power: "userUpdate" },
          {
            label: "重置密码",
            icon: "",
            type: "ResetPwd",
            power: "userResetPwd",
          },
          { label: "删除", icon: "", type: "Delete", power: "userDelete" },
        ],
        showSelection: true,
        showIndex: true,
        showPagination: true,
        pageNo: 1,
        pageSize: 10,
        totalCount: 0,
        selectedRow: [],
        showColConfig: false,
      },
    };
  },
  methods: {
    //获取列表数据
    getDataList() {},
    // 改变列表选中项
    changeSelection(val) {
      this.mainList.selectedRow = val;
    },
    // 分页改变
    changePagination(pageInfo) {
      this.mainList["pageNo"] = pageInfo.pageNo;
      this.mainList["pageSize"] = pageInfo.pageSize;
      this.getDataList();
    },
    switchStatus() {},
    modifyData() {},
    resetPwd() {},
    deleteData() {},
  },
};
</script>

<style lang="scss" scoped>
h3 {
  margin: 40px 0 0;
}

.m-b {
  margin-bottom: 20px;
}
</style>
