<template>
  <div class="userCenter">
    <div class="queryBox">
      <div class="queryItem">
        <div class="fieldName">镇</div>
        <el-select style="width: 200px;" v-model="queryForm.businessArea" @change="handleBusinessAreaChange" size="medium" placeholder="">
          <el-option v-for="item in businessAreaList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </div>
      <div class="queryItem">
        <div class="fieldName">村(居委会)</div>
        <el-select style="width: 200px;" v-model="queryForm.residentialQuarters" size="medium" placeholder="">
          <el-option v-for="item in residentialQuartersList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </div>
      <div class="queryItem">
        <div class="fieldName">用户名</div>
        <el-input style="width: 200px;" v-model="queryForm.userName" prefix-icon="el-icon-search" size="medium" placeholder=""></el-input>
      </div>
      <div class="queryItem">
        <div class="fieldName">用户编号</div>
        <el-input style="width: 200px;" v-model="queryForm.userCode" prefix-icon="el-icon-search" size="medium" placeholder=""></el-input>
      </div>
      <!-- <div class="queryItem">
        <div class="fieldName">水表编号</div>
        <el-input style="width: 300px;" v-model="queryForm.waterMeterCode" prefix-icon="el-icon-search" placeholder=""></el-input>
      </div> -->
      <!-- <div class="queryItem">
        <div class="fieldName">水厂</div>
        <el-input style="width: 300px;" v-model="queryForm.factory" prefix-icon="el-icon-search" placeholder=""></el-input>
      </div> -->
      <div class="queryItem">
        <div class="fieldName">账单月份</div>
        <el-date-picker style="width: 200px;" v-model="queryForm.billMonth" type="month" value-format="yyyyMM" size="medium" placeholder="">
        </el-date-picker>
      </div>
      <!-- <div class="queryItem">
        <div class="fieldName">关键字</div>
        <el-input style="width: 300px;" v-model="queryForm.keyword" prefix-icon="el-icon-search" placeholder=""></el-input>
      </div> -->
      <div class="queryItem">
        <div class="myButton" style="margin-left: 20px;" @click="handleQuery">查询</div>
        <div class="myButton" style="margin-left: 20px;" @click="resetQuery">重置</div>
        <!-- <div class="myButton" style="margin-left: 20px;" @click="handleExport">表单导出</div> -->
      </div>
    </div>

    <div class="cardBox" v-loading="cardLoading">
      <RevenueCard  v-for="item, index in cardList" :key="index" :obj="item" />
    </div>

    <!-- 账单 -->
    <div class="userTable" v-loading="isLoading">
      <div class="header">
        <div>序号</div>
        <div>用户名</div>
        <div>用户编号</div>
        <div>所属村镇</div>
        <div>缴存结余(元)</div>
        <div>应缴金额(元)</div>
        <div>账单用水量</div>
        <div>缴费状态</div>
        <div>缴费时间</div>
        <div>账单月份</div>
        <div>用户信息</div>
      </div>

      <div class="row" v-for="item, index in tableData" :key="index">
        <div>{{ index + 1 }}</div>
        <div>{{ item.userName }}</div>
        <div>{{ item.userCode }}</div>
        <div>{{ getTown(item.businessArea) }}{{ item.townName }}</div>
        <div>{{ item.balance }}</div>
        <div>{{ item.yjAmount }}</div>
        <div>{{ item.waterAmount }}</div>
        <div>{{ item.billState }}</div>
        <div>{{ item.paymentTime | formatTime }}</div>
        <div>{{ item.billMonth }}</div>
        <div>
          <img class="userAction" src="@/assets/img/revenue/icon/userAction.png" alt="" @click="showInfo(item.id, item.billMonth)">
        </div>
      </div>
    </div>

    <el-pagination
      style="margin-top: 10px;"
      background
      @current-change="handlePageNoChange"
      :current-page.sync="pageNo"
      :page-size="10"
      layout="total, prev, pager, next"
      :total="total">
    </el-pagination>

    <!-- 弹窗 -->
    <el-dialog
      :visible.sync="visible"
      width="1500px"
      :close-on-click-modal="false"
      style="detailDialog"
    >
      <!-- 上方数据 -->
      <span slot="title" class="dialogTitle">{{ userInfo.userName }}-用户信息</span>
      <div class="top">
        <div class="currentRead">
          <div>
            <span>户名：{{ userInfo.userName }}</span>
            <span>户号：{{ userInfo.userCode }}</span>
            <!-- <span>抄表序号：--</span> -->
            <span>当前读数：{{ userInfo.currentReadNum }}({{ userInfo.dateTime }})</span>
          </div>
          <div>
            <span>预存结余：{{ userInfo.balance }}</span>
          </div>
        </div>
        <div class="card">
          <div class="item label">表号</div>
          <div class="item value">{{ userInfo.waterMeterCode }}</div>
          <div class="item label">水表类型</div>
          <div class="item value">{{ userInfo.waterMeterTypeName }}</div>
          <div class="item label">水表出厂编号</div>
          <div class="item value">{{ userInfo.factoryNo }}</div>
          <div class="item label">磁卡号</div>
          <div class="item value">{{ userInfo.cikaCode }}</div>
          <div class="item label">电话</div>
          <div class="item value">{{ userInfo.mobilePhone }}</div>
          <div class="item label">身份证号</div>
          <div class="item value">{{ userInfo.idCard }}</div>
          <div class="item label">所属分公司</div>
          <div class="item value">{{ userInfo.belongCompny }}</div>
          <!-- <div class="item label">所属水厂</div>
          <div class="item value">{{ userInfo.belongWaterFactory }}</div> -->
          <div class="item label">所属乡镇</div>
          <div class="item value">{{ userInfo.residentialQuarters }}</div>
          <!-- <div class="item label">抄表手册</div>
          <div class="item value">{{ userInfo.readBook }}</div> -->
          <div class="item label">抄表人员</div>
          <div class="item value">{{ userInfo.readStaff }}</div>
          <div class="item label">用水类型</div>
          <div class="item value">{{ userInfo.waterUseType }}</div>
          <div class="item label">家庭人口（人）</div>
          <div class="item value">{{ userInfo.peopleNum }}</div>
          <div class="item label">初始密码</div>
          <div class="item value">{{ userInfo.password }}</div>
          <div class="item label">建档时间</div>
          <div class="item value">{{ userInfo.enableDate | formatTime }}</div>
          <div class="item label">诚信度评价</div>
          <div class="item value">{{ userInfo.integrityEvaluation }}</div>
          <div class="item label">地址</div>
          <div class="item value">{{ userInfo.userAddre }}</div>
          <div class="item label">是否特例用户</div>
          <div class="item value">{{ userInfo.special }}</div>
          <div class="item label">水价类别</div>
          <div class="item value">{{ userInfo.expenseTypeName }}</div>
          <div class="item label">附加费</div>
          <div class="item value">{{ userInfo.otherBill }}</div>
          <div class="item label">违约金（%）</div>
          <div class="item value">{{ userInfo.wyj }}</div>
        </div>
      </div>

      <!-- 下方数据 -->
      <div class="bottom">
        <div class="toggleBox">
          <div class="left item">
            <div class="toggleItem" :class="{ active: currentValue === 1 }" @click="toggleValue(1)">{{ currentBillMonth || '本期账单' }}</div>
            <div class="toggleItem" :class="{ active: currentValue === 2 }" @click="toggleValue(2)">用水分析</div>
            <div class="toggleItem" :class="{ active: currentValue === 3 }" @click="toggleValue(3)">历史账单</div>
          </div>
          <!-- <div class="item" style="display: flex;align-items: center;" v-show="currentValue === 1">
            <span>月水费</span>
            <el-date-picker style="width: 150px;margin-left: 20px;" size="mini" v-model="currentMonth" type="month" placeholder="选择月"></el-date-picker>
          </div> -->
          <div class="item" style="display: flex;align-items: center;" v-show="currentValue === 2">
            <span>选择日期</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              value-format="yyyy-MM-dd"
              size="mini"
              :clearable="false"
              style="margin-left: 20px;"
            ></el-date-picker>
            <el-button type="primary" size="mini" @click="queryDay" style="margin-left: 20px;">查询</el-button>
          </div>
        </div>

        <!-- 用水账单 -->
        <div v-if="currentValue === 1" class="tabContent">
          <!-- <div style="display: flex;"> -->
            <div class="cardContainer">
              <div v-for="item, index in list" :key="index" class="smallCard">
                <div style="font-size: 18px;padding-top: 10px;">{{ item.label }}</div>
                <div style="font-size: 22px;padding-top: 16px;">{{ item.value }}</div>
              </div>
            </div>
            <!-- <div class="half">
              <div class="tab1Row headerRow">
                <div></div>
                <div>最小用水量（m³）</div>
                <div>用水量（m³）</div>
                <div>最大用水量（m³）</div>
                <div>单价（元）</div>
                <div>已计费（元）</div>
              </div>
              <div class="tab1Row bodyRow">
                <div>第一档</div>
                <div>--</div>
                <div>--</div>
                <div>--</div>
                <div>--</div>
                <div>--</div>
              </div>
              <div class="tab1Row bodyRow">
                <div>总计</div>
                <div>用水量：--方</div>
                <div>水费：--元</div>
              </div>
            </div> -->
          <!-- </div> -->
          <!-- <div class="tab1LongRow longHeaderRow">
            <div>抄表时间</div>
            <div>上期读数（m³）</div>
            <div>本期读数（m³）</div>
            <div>增加量（m³）</div>
            <div>基本方（m³）</div>
            <div>用水量（m³）</div>
            <div>水费（元）</div>
            <div>污水处理费（元）</div>
            <div>垃圾处理费（元）</div>
            <div>违约金（元）</div>
            <div>减免金额（元）</div>
            <div>账单金额（元）</div>
          </div>
          <div class="tab1LongRow oneRow">
            <div style="width: 130px;flex: 0 0 130px;">--</div>
            <div>--</div>
            <div>--</div>
            <div>--</div>
            <div>--</div>
            <div>--</div>
            <div>--</div>
            <div>--</div>
            <div>--</div>
            <div>--</div>
            <div>--</div>
            <div>--</div>
          </div> -->
        </div>

        <!-- 用水分析 -->
        <div v-if="currentValue === 2" class="tabContent">
          <div style="display: flex;">
            <div class="half" style="padding-right: 10px;">
              <div class="lineBox">
                <LineChart :xAxisData="xAxisData" :seriesData="seriesData" />
                <span class="rangeUse">期间用水量(m3)：{{ rangeUse }}</span>
              </div>
            </div>

            <div class="half" style="overflow: auto;">
              <div class="tab1Row headerRow">
                <div>时间</div>
                <div>日常水量(m³)</div>
                <div>总用水量(m³)</div>
              </div>
              <div class="body">
                <div class="tab1Row bodyRow" v-for="item in useWaterInfo" :key="item.time">
                  <div>{{ item.time }}</div>
                  <div>{{ item.current }}</div>
                  <div>{{ item.all }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="currentValue === 3" class="historyData">
          <div class="row headerRow">
            <div>账单月份</div>
            <div>上期读数(m³)</div>
            <div>本期读数(m³)</div>
            <div>本期用量(m³)</div>
            <div>账单金额(元)</div>
            <div>抄表时间</div>
            <div>缴费状态</div>
            <div>缴费时间</div>
          </div>
          <div class="row bodyRow" v-for="item in historyList" :key="item.billMonth">
            <div>{{ item.billMonth }}</div>
            <div>{{ item.lastWaterRead }}</div>
            <div>{{ item.currentWaterRead }}</div>
            <div>{{ item.waterUse }}</div>
            <div>{{ item.billAmount }}</div>
            <div>{{ item.readTime }}</div>
            <div>{{ item.billState }}</div>
            <div>{{ item.paymentTime }}</div>
          </div>
          <el-pagination
            style="margin-top: 10px;"
            background
            @current-change="handleBillPageNoChange"
            :current-page.sync="pageNum"
            :page-size="10"
            layout="total, prev, pager, next"
            :total="billTotal">
          </el-pagination>
        </div>
        
      </div>
    </el-dialog>
  </div>
</template>

<script>
import RevenueCard from './card.vue'
import LineChart from './lineChart.vue'

import { 
  getTotalInfo,
  getUserBill, getBusinessArea, getResidentialQuartersById,
  getUserDetail, getUserDay, getUserHistoryBill
} from '@/api/revenue/user'

import { parseTime } from '@/utils'
import { getWeekDate } from '@/utils/time'

export default {
  name: 'UserCenter',
  components: { RevenueCard, LineChart },
  data() {
    return {
      queryForm: {
        businessArea: '',
        residentialQuarters: '',
        userName: '',
        userCode: '',
        // waterMeterCode: '',
        // factory: '',
        billMonth: ''
      },
      businessAreaList: [], // 营业区域镇
      residentialQuartersList: [],

      // 统计信息
      cardLoadind: false,
      cardList: [
        {
          color: '#5280fd',
          label: '总用户',
          value: 0,
          unit: '户'
        },
        {
          color: '#a44cfc',
          label: '本期应收金额',
          value: 0,
          unit: '元'
        },
        {
          color: '#f096c4',
          label: '本期已收金额',
          value: 0,
          unit: '元'
        },
        {
          color: '#64d5f8',
          label: '本期售水量',
          value: 0,
          unit: '吨'
        }
      ],
      // 账单
      tableData: [],
      total: 0,
      pageNo: 1,
      isLoading: false,

      // 用户信息
      visible: false,
      userInfo: {},
      currentValue: 1,
      // 用水账单
      currentMonth: null,
      list: [
        { label: '本期用水（m³）', value: '--' },
        { label: '上期读数（m³）', value: '--' },
        { label: '本期读数（m³）', value: '--' },
        { label: '本期水费（元）', value: '--' },
        { label: '总用水（m³）', value: '--' },
        { label: '总水费（元）', value: '--' },
        { label: '总缴费（元）', value: '--' },
        { label: '总减免水费（元）', value: '--' },
      ],
      // 用水分析
      rangeUse: null, // 期间用水量
      dateRange: '',
      waterMeterId: '',
      useWaterInfo: [],
      xAxisData: [],
      seriesData: [[], []],
      // 历史账单
      userId: null,
      currentBillMonth: null,
      historyList: [],
      pageNum: 1,
      billTotal: 0
    }
  },
  filters: {
    formatTime(value) {
      return value ? parseTime(value, '{y}-{m}-{d}') : '--'
    }
  },
  mounted() {
    this.getInitData()
    this.getPage()
  },
  methods: {
    handleQuery() {
      this.isLoading = true
      this.pageNo = 1
      this.getPage()
      // 同时查询统计数据
      this._getTotalInfo()
    },
    resetQuery() {
      this.queryForm = {
        businessArea: '',
        residentialQuarters: '',
        userName: '',
        userCode: '',
        billMonth: ''
      }
      this.handleQuery()
    },
    getTown(id) {
      const item = this.businessAreaList.find(item => item.id === id)
      return item ? item.name : ''
    },
    // 用户账单
    getPage() {
      getUserBill({
        pageNo: this.pageNo,
        pageSize: 10,
        ...this.queryForm
      }).then(res => {
        this.isLoading = false
        this.tableData = res.records || []
        this.total = res.total || 0
      }).catch(error => {
        this.isLoading = false
      })
    },
    // 分页
    handlePageNoChange() {
      this.getPage()
    },
    handleBusinessAreaChange(id) {
      this.queryForm.residentialQuarters = ''
      getResidentialQuartersById(id).then(res => {
        this.residentialQuartersList = res || []
      })
    },
    getInitData() {
      this._getTotalInfo()
      getBusinessArea().then(res => {
        this.businessAreaList = res || []
      })
    },
    _getTotalInfo() {
      this.cardLoadind = true
      const payload = {
        month: this.queryForm.billMonth,
        businessArea: this.queryForm.businessArea,
        residentialQuarters: this.queryForm.residentialQuarters
      }
      getTotalInfo(payload).then(res => {
        this.isLoading = false
        this.cardList[0].value = res.userCount || 0
        this.cardList[1].value = res.receivableAmount || 0
        this.cardList[2].value = res.receivedAmount || 0
        this.cardList[3].value = res.waterSell || 0
      }).catch(err => {
        this.isLoading = false
      })
    },
    handleExport() {},
    // 弹窗
    showInfo(userId, billMonth) {
      this.userInfo = {}
      this.userId = userId
      this.currentBillMonth = billMonth
      // 设置默认时间
      this.currentMonth = parseTime(Date.now())
      this.dateRange = getWeekDate()
      this.currentValue = 1
      getUserDetail({
        userId,
        billMonth
      }).then(res => {
        this.userInfo = res
        this.waterMeterId = res.waterMeterId || ''
        this.visible = true

        let usage = res.usage
        // 用水账单
        if (usage) {
          this.list[0].value = res.waterAmount
          this.list[1].value = usage.lastWaterRead
          this.list[2].value = usage.currentWaterRead
          this.list[3].value = usage.currentWaterBill
          this.list[4].value = usage.allWaterUseAmount
          this.list[5].value = usage.allWaterBillAmount
          this.list[6].value = usage.allJfAmount
          this.list[7].value = usage.allReduceBillAmount
        }
        
      })
    },
    toggleValue(value) {
      this.currentValue = value
      if(value === 2) {
        this._getDayData()
      }
      if(value === 3) {
        this.getHistoryBill()
      }
    },
    // 用水分析查询
    queryDay() {
      this._getDayData()
    },
    _getDayData() {
      getUserDay({
        waterMeterId: this.waterMeterId,
        beginTime: this.dateRange[0],
        endTime: this.dateRange[1]
      }).then(res => {
        this.useWaterInfo = res || []
        this.xAxisData = res.map(item => item.time)
        let y1 = res.map(item => item.current)
        let y2 = res.map(item => item.all)
        this.seriesData = [y1, y2]
        const first = y2[0]
        const last = y2[y2.length - 1]
        if(first && last) {

          this.rangeUse = (last - first).toFixed(2)
        }
      })
    },
    // 历史账单
    getHistoryBill() {
      const payload = {
        userId: this.userId,
        pageNo: this.pageNum,
        pageSize: 10,
        orderBy: 'readTime',
        asc: true
      }
      getUserHistoryBill(payload).then(res => {
        this.historyList = res.records || []
        this.billTotal = res.total
      })
    },
    handleBillPageNoChange() {
      this.getHistoryBill()
    }
  }
}
</script>

<style lang="scss">
.userCenter {
  .el-form-item__label {
    color: #333;
  }
  .el-input__inner {
    background: rgba(223,235,255,0.4) !important;
    color: #333 !important;
  }
  .el-dialog {
    background-color: #e4effb !important;
  }
  .el-dialog__header {
    background: #1D7DFF;
  }
  .el-dialog__body {
    padding: 20px;
  }
  .el-range-input {
    background-color: transparent;
  }
  .el-pagination {
    .el-pagination__total {
      color: #303133;
    }
  }
}
</style>

<style lang="scss" scoped>
.userCenter {
  height: 100%;
  .queryBox {
    display: flex;
    flex-wrap: wrap;
    .queryItem {
      display: flex;
      align-items: center;
      // margin-right: 30px;
      margin-bottom: 15px;
      .fieldName {
        // width: 64px;
        // text-align: right;
        padding: 0 10px 0 20px;
      }
    }
    .myButton {
      height: 36px;
      color: #ffff;
      line-height: 36px;
      text-align: center;
      background-color: #1363ff;
      border-radius: 8px;
      padding: 0 20px;
      cursor: pointer;
    }
  }
  .cardBox {
    display: flex;
    justify-content: space-between;
  }
  .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    // width: 200px;
  }
  .userTable {
    margin-top: 15px;
    min-height: 440px;
    .header {
      height: 40px;
      display: flex;
      line-height: 40px;
      text-align: center;
      div {
        flex: 1;
        border-right: 1px solid #fff;
        background-color: #74aaff;
        color: #fff;
        &:first-child {
          border-top-left-radius: 8px;
        }
        &:last-child {
          border-top-right-radius: 8px;
        }
      }
    }
    .row {
      height: 40px;
      display: flex;
      line-height: 40px;
      text-align: center;
      div {
        flex: 1;
        background-color: #E5F0FF;
        border-right: 1px solid #fff;
        border-bottom: 1px solid #fff;
      }
      .userAction {
        width: 20px;
        cursor: pointer;
      }
      &:last-child {
        div {
          border-bottom: none;
          &:first-child {
            border-bottom-left-radius: 8px;
          }
          &:last-child {
            border-bottom-right-radius: 8px;
          }
        }
      }
    }
  }
  .dialogTitle {
    color: #fff;
    font-size: 20px;
  }
  .top {
    color: #fff;
    .currentRead {
      padding: 0 20px;
      height: 46px;
      background-color: rgba(27, 118, 255, 0.6);
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 18px;
      span {
        padding-right: 15px;
      }
    }
    .card {
      padding: 10px;
      background: rgba(243, 248, 254, 0.49);
      display: flex;
      flex-wrap: wrap;
      .item {
        width: calc(100% / 6);
        border-top: 1px solid #FFFFFF;
        border-bottom: 1px solid #FFFFFF;
        font-size: 16px;
        height: 40px;
        line-height: 38px;
        padding-left: 20px;
      }
      .label { 
        background-color: rgba(27 ,118, 255, 0.6);
      }
      .value {
        color: #666666;
      }
    }
  }
  .bottom {
    color: #fff;
    .toggleBox {
      display: flex;
    }
    .item {
      width: 50%;
      font-weight: 500;
      font-size: 20px;
      color: #1D2129;
    }
    .left {
      display: flex;
      .toggleItem {
        padding: 10px 0;
        margin: 0 20px;
        border-bottom: 2px solid transparent;
        cursor: pointer;
      }
      .active {
        color: #5280FD;
        border-bottom-color: #5280FD;
      }
    }
    .tabContent {
      .half {
        width: 50%;
        padding-top: 10px;
      }
      .lineBox {
        background-color: #fff;
        height: 320px;
        position: relative;
        .rangeUse {
          color: #666;
          position: absolute;
          left: 453px;
          top: 8px;
        }
      }
      .cardContainer {
        padding-top: 20px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .smallCard {
          width: 170px;
          height: 88px;
          background: rgba(27, 118, 255, 0.6);
          box-shadow: 0px 0px 124px 15px rgba(197, 224, 251, 0.32);
          border-radius: 15px;
          margin-bottom: 10px;
          text-align: center;
        }
      }
      .tab1Row {
        height: 40px;
        display: flex;
        text-align: center;
        line-height: 40px;
        div {
          flex: 1;
        }
      }
      .body {
        height: 280px;
        overflow-y: auto;
      }
      .headerRow {
        background-color: rgba(27, 118, 255, 0.6);
      }
      .bodyRow {
        color: #666666;
        background-color: #fff;
      }
      .tab1LongRow {
        height: 40px;
        display: flex;
        text-align: center;
        line-height: 40px;
        div {
          flex: 1;
        }
      }
      .longHeaderRow {
        background-color: #1D7DFF;
      }
      .oneRow {
        color: #666666;
        background-color: #fff;
      }

    }
    .historyData {
      padding-top: 20px;
      .row {
        display: flex;
        height: 40px;
        text-align: center;
        line-height: 40px;
        div {
          flex: 1;
        }
      }
      .headerRow {
        background-color: #1D7DFF;
      }
      .bodyRow {
        color: #666666;
      }
    }
  }
}
</style>