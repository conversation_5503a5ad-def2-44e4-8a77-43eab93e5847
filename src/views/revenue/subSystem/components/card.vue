<template>
  <div class="revenueCard">
    <div class="left" :style="{ 'border-right': `2px dashed ${obj.color}` }">
      <div class="square" :style="{ 'background-color': obj.color }">
        <img src="@/assets/img/revenue/icon/squareInner.png" alt="">
      </div>
    </div>

    <div class="right">
      <div>{{ obj.label }}</div>
      <div style="font-size: 34px;">{{ obj.value }}<span style="font-size: 22px;">{{ obj.unit }}</span></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RevenueCard',
  props: {
    obj: {
      type: Object
    }
  }
}
</script>

<style lang="scss" scoped>
.revenueCard {
  width: 390px;
  height: 130px;
  padding: 26.5px 0;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  .left {
    width: 147px;
    padding: 0 35px;
    .square {
      width: 77px;
      height: 77px;
      border-radius: 8px;
      padding: 22px 0 0 22px;
      img {
        width: 33px;
      }
    }
  }
  .right {
    padding-left: 25px;
    font-size: 22px;
  }
}
</style>