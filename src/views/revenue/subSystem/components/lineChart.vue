<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption(
        {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            backgroundColor: 'rgba(2, 50, 128, 0.6)'
          },
          legend: {
            top: 10,
            textStyle: {
              fontSize: 12,
              color: "#666",
            },
            data: ['日常水量', '总用水量']
          },
          grid: {
            containLabel: true
          },
          xAxis: [
            {
              boundaryGap: false,
              name: '日期',
              nameTextStyle: {
                color: '#666'
              },
              axisLine: {
                lineStyle: {
                  color: '#999'
                }
              },
              axisLabel: {
                color: '#666',
              },
              splitLine: {
                show: false,
              },
              data: this.xAxisData
              // data: ['12-01', '12-02', '12-03', '12-04', '12-05', '12-06', '12-07', '12-08', '12-09', '12-10']
            }
          ],
          yAxis: [
            {
              name: '日常水量(m³)',
              axisLine: {
                show: false,
              },
              axisLabel: {
                color: '#666',
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#999',
                }
              },
            },
            {
              name: '总用水量(m³)',
              axisLine: {
                show: false,
              },
              axisLabel: {
                color: '#666',
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#999',
                }
              },
            }
          ],
          series: [
            {
              name: '日常水量',
              type: 'line',
              color: '#38E364',
              symbol: 'circle',
              data: this.seriesData[0],
              // data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
            },
            {
              name: '总用水量',
              type: 'line',
              color: '#1C9BFF',
              symbol: 'circle',
              data: this.seriesData[1],
              // data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
            }
          ]
        },
        {
          notMerge: true
        }
      )
    }
  }
}
</script>
