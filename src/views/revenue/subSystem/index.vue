<template>
  <div class="subSystem">
    <div class="sidebar">
      <div class="menuCollapse">
        <i class="el-icon-s-fold" v-if="!isCollapse" @click="isCollapse = true"></i>
        <i class="el-icon-s-unfold" v-else @click="isCollapse = false"></i>
      </div>
      <el-menu
        :default-active="defaultActiveMenu"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
        @select="handleSelect"
        :collapse="isCollapse"
        text-color="#333"
        active-text-color="#fff"
        :collapse-transition="false">
        <!-- 首页 用户中心 营业所结构管理 用户档案管理 水价管理 计费管理 收费管理 报价统计 -->
        <el-menu-item index="1">
          <img class="menuIcon" src="@/assets/img/revenue/icon/menuIcon1Blue.png" alt="">
          <span slot="title">首页</span>
        </el-menu-item>
        <el-menu-item index="2">
          <img v-show="activeMenu != 2" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon2Blue.png" alt="" >
          <img v-show="activeMenu == 2" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon2.png" alt="" >
          <span slot="title">用户中心</span>
        </el-menu-item>
        <el-menu-item index="3">
          <img v-show="activeMenu != 3" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon2Blue.png" alt="" >
          <img v-show="activeMenu == 3" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon2.png" alt="" >
          <span slot="title">水价制度</span>
        </el-menu-item>
        <!-- <el-menu-item index="3">
          <img v-show="activeMenu != 3" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon3Blue.png" alt="">
          <img v-show="activeMenu == 3" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon3.png" alt="">
          <span slot="title">营业所结构管理</span>
        </el-menu-item>
        <el-menu-item index="4">
          <img v-show="activeMenu != 4" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon4Blue.png" alt="">
          <img v-show="activeMenu == 4 " class="menuIcon" src="@/assets/img/revenue/icon/menuIcon4.png" alt="">
          <span slot="title">用户档案管理</span>
        </el-menu-item>
        <el-menu-item index="5">
          <img v-show="activeMenu != 5" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon5Blue.png" alt="">
          <img v-show="activeMenu == 5" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon5.png" alt="">
          <span slot="title">水价管理</span>
        </el-menu-item>
        <el-menu-item index="6">
          <img v-show="activeMenu != 6" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon6Blue.png" alt="">
          <img v-show="activeMenu == 6" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon6.png" alt="">
          <span slot="title">计费管理</span>
        </el-menu-item>
        <el-menu-item index="7">
          <img v-show="activeMenu != 7" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon7Blue.png" alt="">
          <img v-show="activeMenu == 7" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon7.png" alt="">
          <span slot="title">收费管理</span>
        </el-menu-item>
        <el-menu-item index="8">
          <img v-show="activeMenu != 8" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon8Blue.png" alt="">
          <img v-show="activeMenu == 8" class="menuIcon" src="@/assets/img/revenue/icon/menuIcon8.png" alt="">
          <span slot="title">报价统计</span>
        </el-menu-item> -->
      </el-menu>
    </div>

    <div class="content">
      <UserCenter v-if="activeMenu == 2" />
      <WaterPrice v-if="activeMenu == 3" />
    </div>
  </div>
</template>

<script>
import UserCenter from './components/userCenter.vue'
import WaterPrice from './components/waterPrice.vue'

export default {
  name: 'SubSystem',
  components: {
    UserCenter,
    WaterPrice
  },
  data() {
    return {
      isCollapse: false,
      defaultActiveMenu: '2',
      activeMenu: '2',
      flag: true,
      showUserSub: false
    }
  },
  methods: {
    handleOpen() {},
    handleClose() {},
    handleSelect(index) {
      console.log(index)
      this.activeMenu = index
      if(index == 1) {
        this.$router.push({ name: 'Revenue' })
      }
      if(index == 7) {
        // window.open('http://************:8986/')
      }

    },
    temp() {
      this.flag = false
      this.showUserSub = true
    }
  }
}
</script>

<style lang="scss">
.subSystem {
  .el-menu {
    border-right: none;
  }
  .el-menu-item.is-active {
    // background: linear-gradient(270deg, #2458C2 0%, #003970 100%);
    background: #1a74ff;
  } 
}
</style>

<style lang="scss" scoped>
.subSystem {
  height: calc(100vh - 84px);
  // padding-bottom: 30px;
  background-color: #fff;
  display: flex;
  .sidebar {
    // width: 240px;
    height: 100%;
    // background-color: rgba(42,93,210,0.1);
    // border: 1px solid rgba(42,93,210,0.5);
    box-shadow: 0px 0px 27px 0px rgba(154,177,222,0.39);
    .menuCollapse {
      color: #156dff;
      font-size: 18px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      i {
        cursor: pointer;
      }
    }
    .menuIcon {
      width: 20px;
      height: 20px;
      margin-right: 5px;
    }
  }
  .content {
    flex: 1;
    overflow: hidden;
    padding: 20px;
    background: linear-gradient(180deg, #F4F6FA, #E5F0FF);
  }
}
</style>