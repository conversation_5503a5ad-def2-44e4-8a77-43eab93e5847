<template>
  <div class="bottombar">
    <div class="title">营收数据日历</div>
    <div class="dataBox">
      <div class="item" v-for="item, index in list" :key="index">
        <div class="quarter" v-if="index % 2 == 0">
          <div class="quarterTitle">{{ item.label }}</div>
          <div class="quarterValue">
            <span class="quarterNumber">{{ item.value | filterToW }}</span><span style="font-size: 12px;margin-left: 5px;">万元</span>
          </div>
        </div>
        <!-- 月份数据 -->
        <div class="month" v-for="m, i in item.list" :key="i">
          <div style="color: #B0D7FF;font-size: 14px;text-align: center;">{{ m.time }}</div>
          <div class="monthValue">{{ m.amount | filterToW }}</div>
          <div style="font-size: 12px;text-align: center;">万元</div>
        </div>

        <div class="quarter" v-if="index % 2 == 1" style="margin-left: 10px;">
          <div class="quarterTitle">{{ item.label }}</div>
          <div class="quarterValue">
            <div class="quarterValue">
              <span class="quarterNumber">{{ item.value | filterToW }}</span><span style="font-size: 12px;margin-left: 5px;">万元</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getYearDetail } from '@/api/revenue/screen'

export default {
  data() {
    return {
      list: []
    }
  },
  filters: {
    filterToW(value) {
      return (value / 10000).toFixed(2)
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      getYearDetail().then(res => {
        const data = res.data
        const quarterSum = [
          {
            label: '第一季度',
            value: data.quarter1
          },
          {
            label: '第二季度',
            value: data.quarter2
          },
          {
            label: '第三季度',
            value: data.quarter3
          },
          {
            label: '第四季度',
            value: data.quarter4
          }
        ]

        const group = this.groupArray(data.months)
        this.list = group.map((g, index) => {
          return {
            label: quarterSum[index].label,
            value: quarterSum[index].value,
            list: g
          }
        })
      })
    },
    groupArray(array, size = 3) {
      const result = []
      for (let i = 0; i < array.length; i += size) {
        result.push(array.slice(i, i + size))
      }
      return result
    }
  }
}
</script>

<style lang="scss" scoped>
.bottombar {
  color: #ffffff;
  width: 940px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 20px;
  .title {
    background-image: url("../../../assets/img/revenue/longBg.png");
    background-size: 940px 40px;
    width: 100%;
    height: 40px;
    font-size: 22px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    line-height: 40px;
    padding-left: 48px;
  }
  .dataBox {
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    background-color: rgba(4, 15, 45, 0.3);
    .item {
      display: flex;
      &:first-child {
        margin-bottom: 10px;
      }
    }
    .quarter {
      width: 159px;
      height: 110px;
      background-image: url("../../../assets/img/revenue/quarterDataBg.png");
      background-size: 159px 110px;
      padding: 7px 13px 0;
      .quarterTitle {
        font-weight: 700;
        height: 30px;
        width: 100%;
        background-image: url('~@/assets/img/revenue/areaBg.png');
        background-size: 100%;
        text-align: center;
        line-height: 30px;
      }
      .quarterValue {
        height: 54px;
        width: 100%;
        margin-top: 14px;
        background-image: url('~@/assets/img/revenue/pane.png');
        background-size: 100%;
        text-align: center;
        .quarterNumber {
          font-size: 20px;
          font-family: DIN;
          font-weight: 700;
        }
      }
    }
    .month {
      padding-top: 23px;
      width: 85px;
      height: 110px;
      background-image: url('~@/assets/img/revenue/monthDataBg.png');
      background-size: 100%;
      margin-left: 10px;
      .monthValue {
        font-family: DIN;
        font-size: 18px;
        font-weight: 700;
        text-align: center;
         margin-top: 10px;
      }
    }
  }
}
</style>