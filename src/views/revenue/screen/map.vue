<template>
  <div class="revnueMap" id="map">
    
  </div>
</template>

<script>
import L, { icon } from 'leaflet'
import 'leaflet/dist/leaflet.css'
import axios from 'axios'

export default {
  name: 'RevnueMap',
  data() {
    return {
      geoUrl: '',
    }
  },
  created() {
    this.geoUrl = process.env.VUE_APP_GEO_URL
  },
  mounted() {
    this.initMap()
  },
  methods: {
    initMap() {
      let map = L.map('map', {
        attributionControl: false,
        zoomControl: false,
        zoom: 10,
        // center: [31.25307163396126, 110.75210994390093],
        center: [31.329917907714844, 110.71060180664062],
        crs: L.CRS.EPSG4326
      })

      // 天地图影像标注
      let cia_c_url = 'http://t{s}.tianditu.gov.cn/cia_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6'
      let tdtImageNoteLayer = L.tileLayer(cia_c_url, {
        subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
        zoomOffset: 1
      })
      map.addLayer(tdtImageNoteLayer)

      // geojson
      let popup = L.popup()
      let geojsonUrl = this.geoUrl + '/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan:t_revenue_year_town_sell&outputFormat=application/json'
      axios.get(geojsonUrl)
        .then(res => {
          let geojsonLayer = L.geoJson(
            res.data,
            {
              onEachFeature: (feature, layer) => {
                let textIcon = L.divIcon({
                  className: 'leaflet-text-icon',
                  html: this.createDivIconHTML(feature.properties),
                  iconSize: [100, null],
                  // iconAnchor: [50, 20]
                })
                // console.log(feature.properties.ZLDWMC)
                L.marker([feature.properties.y, feature.properties.x], { icon: textIcon }).addTo(map)
              },
              style: (geoJsonFeature) => {
                return {
                  weight: 1
                }
              }
            }
          )
          geojsonLayer.on('click', (data) => {
            console.log(data)
            let feature =  data.sourceTarget.feature
            if(feature) {
              popup.setLatLng(data.latlng)
              popup.setContent(`${feature.properties.zldwmc}: ${feature.properties.receive || 0}元`)
              popup.openOn(map)
            }
          })
          map.addLayer(geojsonLayer)
        })

      map.on('click', (event) => {
        console.log(map.getCenter())
      })
    },
    createDivIconHTML(properties) {
      let receive = properties.receive ? properties.receive : 0
      return `<div style="text-align: center;color: #11e6ff;">${properties.zldwmc}</div><div style="text-align: center;color: #11e6ff;">${receive}元</div>`
    }
  }
}
</script>

<style lang="scss" scoped>
.revnueMap {
  height: 100%;
  background-color: transparent;
  .customDivIcon {
    color: red;
  }
}
</style>