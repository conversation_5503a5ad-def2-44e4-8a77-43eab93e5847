<template>
  <div class="leftbar">
    <div class="top commonBg">
      <div class="title">缴费分析</div>
      <div class="chartBox">
        <div>
          <SmallCircle  width="80px" height="80px" :seriesData="wxPay" />
          <div class="label">微信</div>
        </div>
        <div>
          <SmallCircle  width="80px" height="80px" :seriesData="aliPay" :colorList="['#5BC0FC', '#0098FF']" />
          <div class="label">支付宝</div>
        </div>
        <div>
          <SmallCircle  width="80px" height="80px" :seriesData="cashPay" :colorList="['#9849FF', '#724CFF']" />
          <div class="label">现金</div>
        </div>
        <div>
          <SmallCircle  width="80px" height="80px" :seriesData="otherPay" :colorList="['#FFA166', '#F6BD16']" />
          <div class="label">其他</div>
        </div>
      </div>
    </div>

    <div class="middle commonBg">
      <div class="title">
        <span>营收数据</span>
        <el-date-picker
          v-model="month"
          type="month"
          value-format="yyyy-MM"
          :picker-options="pickerOptions"
          size="mini"
          placeholder="选择月"
          @change="monthChange"
          :clearable="false"
          style="width: 120px;margin-left: auto;">
        </el-date-picker>
      </div>

      <div style="padding: 10px;">
        <div class="totalData">
          <div class="dataItem" :class="{ activeDataItem: currentField === 1 }" @click="changeShowField(1)">
            <div class="dataLabel">本月营业额</div>
            <div class="dataValue">
              <span class="value">{{ monthInfo.allBillAmount }}</span>
              <span class="unit">元</span>
            </div>
          </div>
          <div class="dataItem" :class="{ activeDataItem: currentField === 2 }" @click="changeShowField(2)">
            <div class="dataLabel">本月预缴额</div>
            <div class="dataValue">
              <!-- <span class="value">{{ monthInfo.allWater }}</span> -->
              <span class="value">{{ monthInfo.currentPayment }}</span>
              <span class="unit">元</span>
            </div>
          </div>
        </div>

        <div class="townData">
          <div class="townValue">{{ currentItem.name }} {{ currentField === 1 ? currentItem.amount : currentItem.payment }}元</div>
          <div class="cardBox">
            <div class="innerItem" v-for="v in currentItem.list" :key="v.name">
              <div style="font-size: 12px;height: 26px;line-height: 26px;">{{ v.name }}</div>
              <div style="padding-top: 6px;">
                <span class="value">{{ currentField === 1 ? v.amount : v.payment }}</span>
                <span style="font-size: 12px;opacity: 0.5;margin-left: 4px;">元</span>
              </div>
            </div>
          </div>
        </div>

      </div>
      <!-- <div class="dataBox">
        <div class="totalData">
          <div class="dataItem">
            <div class="label">本月营业额</div>
            <div style="color: #11E6FF;">
              <span class="value">{{ monthInfo.allReceive | filterValue}}</span>
              <span class="unit">{{ monthInfo.allReceive | filterUnit }}元</span>
            </div>
          </div>
          <div class="dataItem">
            <div class="label">本月售水量</div>
            <div style="color: #11E6FF;">
              <span class="value">{{ monthInfo.allWater | filterValue }}</span>
              <span class="unit">{{ monthInfo.allWater | filterUnit }}吨</span>
            </div>
          </div>
        </div>

        <div class="areaData">
          <div class="item" v-for="item, index in monthList" :key="index">
            <div class="areaValue">{{ item.town.name }}:{{ item.receive }}元</div>
            <div class="innerItem" v-for="d, i in item.villages" :key="i">
              <div style="font-size: 14px;">{{ d.name }}</div>
              <div style="font-family: DIN;font-size: 18px;font-weight: 700;padding-top: 6px;">{{ d.receive }}</div>
              <div style="font-size: 12px;">元</div>
            </div>
          </div>
        </div>
      </div> -->
    </div>

    <div class="bottom commonBg">
      <div class="title">年营收额与售水量</div>
      <div class="totalData" style="padding: 10px 10px 0;">
        <div class="dataItem">
          <div class="label">年营业额</div>
          <div style="color: rgb(17, 230, 255);">
            <span class="value">{{ yearInfo.amountSum | filterValue}}</span>
            <span class="unit">{{ yearInfo.amountSum | filterUnit }}元</span>
          </div>
        </div>
        <div class="dataItem">
          <div class="label">年售水量</div>
          <div style="color: rgb(17, 230, 255);">
            <span class="value">{{ yearInfo.waterSum | filterValue }}</span>
            <span class="unit">{{ yearInfo.waterSum | filterUnit }}方</span>
          </div>
        </div>
      </div>      
      <div style="height: calc(100% - 124px);">
        <MixChart :xAxisData="xAxisData" :seriesData="seriesData" />
      </div>
    </div>
  </div>
</template>

<script>
import SmallCircle from './components/smallCircle.vue'
import MixChart from './components/mixChart.vue'

import { getCurrentMonthInfo, getYearInfo } from '@/api/revenue/screen'
import { getCurrentMonthRevenueData, getYearAndMonthRevenueData } from '@/api/revenue/data'

import { parseTime } from '@/utils'
import { Decimal } from 'decimal.js'

export default {
  components: { SmallCircle, MixChart },
  data() {
    return {
      wxPay: { value: 0, aValue: '' },
      aliPay: { value: 0, aValue: '' },
      cashPay: { value: 0, aValue: '' },
      otherPay: { value: 0, aValue: '' },
      month: '',
      // 本月
      monthInfo: {
        amount: '',
        water: ''
      },
      currentField: 1,
      currentIndex: 0,
      currentItem: {},
      timeId: null,
      monthData: [],
      monthList: [],
      // 年
      yearInfo: {},
      xAxisData:  ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      seriesData: [[], []],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  filters: {
    filterValue(value) {
      if(value > 10000) {
        return (value / 10000).toFixed(2)
      }
      return value
    },
    filterUnit(value) {
      if(value > 10000) {
        return '万'
      }
      return ''
    }
  },
  created() {
    this.month = parseTime(new Date(), '{y}-{m}')
  },
  mounted() {
    this.getData()
  },
  beforeDestroy() {
    if(this.timeId) clearInterval(this.timeId)
  },
  methods: {
    monthChange() {
      getCurrentMonthRevenueData({ time: this.month + '-01' }).then(res => {
        this.monthInfo = {
          allBillAmount: res.allBillAmount || 0,
          currentPayment: res.currentPayment || 0
        }
        this.monthData = res.list || []
        this.currentItem = this.monthData[this.currentIndex]
      })
    },
    // 
    getData() {
      // getCurrentMonthInfo().then(res => {
      //   this.monthInfo = res.data
      //   this.monthList = res.data.list || []
      // })

      // 缴费分析
      getYearAndMonthRevenueData().then(res => {
        const data = res || {}

        let total = Decimal(data.wxPay).plus(data.aliPay).plus(data.cashPay).plus(data.otherPay)
        this.wxPay = {
          value: Decimal(data.wxPay).div(total).mul(100).toDecimalPlaces(2).toNumber(),
          aValue: this.filterToW(data.wxPay)
        }
        this.aliPay = {
          value: Decimal(data.aliPay).div(total).mul(100).toDecimalPlaces(2).toNumber(),
          aValue: this.filterToW(data.aliPay)
        }
        this.cashPay = {
          value: Decimal(data.cashPay).div(total).mul(100).toDecimalPlaces(2).toNumber(),
          aValue: this.filterToW(data.cashPay)
        }
        this.otherPay = {
          value: Decimal(data.otherPay).div(total).mul(100).toDecimalPlaces(2).toNumber(),
          aValue: this.filterToW(data.otherPay)
        }


        this.$emit('getTopTwoData', {
          yearReceived: data.yearReceived,
          yearPrePay: data.yearPrePay
        })
      })

      // 营收数据
      getCurrentMonthRevenueData({ time: this.month + '-01' }).then(res => {
        this.monthInfo = {
          allBillAmount: res.allBillAmount || 0,
          currentPayment: res.currentPayment || 0
        }
        this.monthData = res.list || []
        this.currentItem = this.monthData[this.currentIndex]
        let length = this.monthData.length
        this.timeId = setInterval(() => {
          this.currentIndex++
          if(this.currentIndex >= length) this.currentIndex = 0
          this.currentItem = this.monthData[this.currentIndex]
        }, 3000)
      })

      getYearInfo().then(res => {
        this.yearInfo = {
          amountSum: res.data.amountSum,
          waterSum: res.data.waterSum,
        }
        const data = res.data.months
        const y1 = data.map(item => (item.amount / 10000).toFixed(2))
        const y2 = data.map(item => (item.water / 10000).toFixed(2))
        this.seriesData = [y1, y2]
      })
    },
    changeShowField(val) {
      this.currentField = val
    },
    filterToW(value) {
      return (value / 10000).toFixed(2)
    }
  }
}
</script>


<style lang="scss" scoped>
.leftbar {
  font-family: 'Microsoft YaHei';
  width: 440px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 30px;
  color: #ffffff;
  .title {
    display: flex;
    background: url("../../../assets/img/title.png") center no-repeat;
    background-size: 100%;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    line-height: 42px;
    padding-left: 48px;
    padding-right: 20px;
  }
  .commonBg {
    background-color: rgba(4, 15, 45, 0.3);
  }
  .top {
    .chartBox {
      display: flex;
      justify-content: space-between;
      padding: 10px;
      .label {
        font-size: 14px;
        text-align: center;
      }
    }
  }
  .middle {
    height: 45%;
    margin-top: 18px;
    overflow: hidden;
    .totalData {
      display: flex;
      justify-content: space-between;
      .dataItem {
        width: 195px;
        height: 72px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100% 100%;
        color: #50C9E3;
        cursor: pointer;
        .dataLabel {
          font-size: 16px;
          height: 27px;
          text-align: center;
          line-height: 27px;
          // color: #50C9E3;
        }
        .dataValue {
          padding-top: 6px;
          text-align: center;
        }
      }
      .activeDataItem {
        background-image: url('~@/assets/img/home/<USER>');
        color: #fff;
      }
    }
    .value {
      font-size: 24px;
      font-weight: 500;
      text-shadow: 0px 0px 10px #4EC9FF;
    }
    .unit {
      font-size: 12px;
      color: #FFFFFF;
      opacity: 0.5;
      margin-left: 4px;
    }
    .townData {
      margin-top: 10px;
      .cardBox {
        display: flex;
        flex-wrap: wrap;
        padding-left: 14px;
      }
      .item {
        width: 134px;
      }
      .townValue {
        height: 30px;
        font-weight: 700;
        color: #FFFFFF;
        font-size: 14px;
        line-height: 30px;
        text-align: center;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100%;
      }
      .innerItem {
        margin-top: 8px;
        margin-right: 10px;
        width: 120px;
        height: 70px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100% 100%;
        color: #FFFFFF;
        div {
          text-align: center;
        }
      }
    }
    .dataBox {
      padding: 10px;

      .areaData {
        display: flex;
        margin-top: 14px;
        justify-content: space-between;
        .item {
          width: 134px;
          .areaValue {
            height: 30px;
            font-weight: 700;
            color: #FFFFFF;
            font-size: 14px;
            line-height: 30px;
            text-align: center;
            background-image: url('~@/assets/img/revenue/areaBg.png');
            background-size: 100%;
          }
          .innerItem {
            margin-top: 8px;
            height: 88px;
            background-image: url('~@/assets/img/revenue/square.png');
            background-size: 100%;
            color: #FFFFFF;
            padding-top: 12px;
            div {
              text-align: center;
            }
          }
        }
      }
    }
  }
  .bottom {
    height: calc(55% - 197px);
    .totalData {
      display: flex;
      justify-content: space-between;
      .dataItem {
        width: 195px;
        height: 72px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 16px;
        background-image: url('~@/assets/img/revenue/smallCard.png');
        background-size: 100%;
        .label {
          color: #fff;
          font-size: 14px;
        }
        .value {
          font-size: 20px;
          font-family: DIN;
          font-weight: 700;
        }
        .unit {
          font-size: 14px;
          padding-left: 5px;
        }
      }
    }
  }
}
</style>