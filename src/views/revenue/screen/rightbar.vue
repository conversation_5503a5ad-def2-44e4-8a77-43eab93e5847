<template>
  <div class="rightbar">
    <div class="top commonBg">
      <div class="title">水表情况</div>
      <div class="chartBox">
        <PieChart :pieData="meterType" />
      </div>
    </div>

    <div class="cardHeight commonBg">
      <div class="title">
        <span>月售水量排名TOP5</span>
        <el-date-picker
          v-model="month1"
          type="month"
          value-format="yyyy-MM"
          :picker-options="pickerOptions"
          size="mini"
          placeholder="选择月"
          @change="timeChange"
          :clearable="false"
          style="width: 120px;margin-left: auto;">
        </el-date-picker>
      </div>
      <div class="chartBox">
        <RankBar :xAxisData="villages1" :seriesData="seriesData1" />
      </div>
    </div>

    <div class="cardHeight commonBg">
      <div class="title">
        <span>月营收额排名TOP5</span>
        <el-date-picker
          v-model="month1"
          type="month"
          value-format="yyyy-MM"
          :picker-options="pickerOptions"
          size="mini"
          placeholder="选择月"
          @change="timeChange"
          :clearable="false"
          style="width: 120px;margin-left: auto;">
        </el-date-picker>
      </div>
      <div class="chartBox">
        <SquareBar :xAxisData="villages1" :seriesData="seriesData2" />
      </div>
    </div>
  </div>
</template>

<script>
import PieChart from './components/pieChart'
import RankBar from './components/rankBar'
import SquareBar from './components/squareBar'

import { getMeterTypeAndCount } from '@/api/revenue/screen'
import { getTwoRankData } from '@/api/revenue/data'

import { parseTime } from '@/utils'

export default {
  components: { PieChart, RankBar, SquareBar },
  data() {
    return {
      meterType: [],
      month1: '',
      month2: '',
      villages1: [],
      seriesData1: [],
      villages2: [],
      seriesData2: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 24 * 60 * 60 * 1000 * 30
        }
      }
    }
  },
  created() {
    const now = new Date()
    const lastMonth = new Date(now.getUTCFullYear(), now.getMonth() - 1)
    this.month1 = this.month2 = parseTime(lastMonth, '{y}-{m}')
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      // 水表统计
      getMeterTypeAndCount().then(res => {
        const data = res.data
        this.meterType = data.map(item => {
          return {
            name: item.name,
            value: item.number
          }
        })
      })
      // 排名
      getTwoRankData(this.month1 + '-01').then(res => {
        const { status, data } = res
      
        if(status === 200) {
          const temp = data
          if(data.length >= 5) {
            temp = data.slice(0, 5)
          }
          this.villages1 = temp.water.map(item => item.villageName)
          this.seriesData1 = temp.water.map(item => item.amount)
          this.seriesData2 = temp.receive.map(item => item.amount)
          
          // this.seriesData1 = [data.water[0].amount || 0, 0, 0, 0, 0]
          // this.seriesData2 = [data.receive[0].amount || 0, 0, 0, 0, 0]
        }
      })
    },
    timeChange() {
      // 排名
      getTwoRankData(this.month1 + '-01').then(res => {
        const { status, data } = res
        if(status === 200) {
          const temp = data
          if(data.length >= 5) {
            temp = data.slice(0, 5)
          }
          this.villages1 = temp.water.map(item => item.villageName)
          this.seriesData1 = temp.water.map(item => item.amount)
          this.seriesData2 = temp.receive.map(item => item.amount)
          // if(data.water.length) {
          //   this.seriesData1 = [data.water[0].amount]
          // } else {
          //   this.seriesData1 = [0, 0, 0, 0, 0]
          // }

          // if(data.receive.length) {
          //   this.seriesData2 = [data.receive[0].amount]
          // } else {
          //   this.seriesData2 = [0, 0, 0, 0, 0]
          // }
          
          // this.seriesData1 = [data.water[0].amount || 0, 0, 0, 0, 0]
          // this.seriesData2 = [data.receive[0].amount || 0, 0, 0, 0, 0]
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.rightbar {
  width: 440px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 30px;
  .title {
    background: url("../../../assets/img/title.png") center no-repeat;
    background-size: 100%;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
    padding-right: 20px;
    display: flex;
  }
  .commonBg {
    background-color: rgba(4, 15, 45, 0.3);
  }
  .top {
    height: calc(40% - 54px);
  }
  .cardHeight {
    margin-top: 18px;
    height: 30%;
  }
  .chartBox {
    height: calc(100% - 42px);
  }
}
</style>