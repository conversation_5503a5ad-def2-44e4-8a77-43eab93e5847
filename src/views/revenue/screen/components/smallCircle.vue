<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'
import { Decimal } from 'decimal.js'
import { number } from 'echarts/lib/export'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Object,
      default: () => ({ rate: 0, value: '' })
    },
    colorList: {
      type: Array,
      default: () => ['#47E4E5', '#2B8EF3']
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      // deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        polar: {
          radius: 72,
          center: ["50%", "50%"],
        },
        angleAxis: {
          max: 100,
          show: false,
        },
        radiusAxis: {
          type: "category",
          show: true,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        tooltip: {
          // trigger: 'axis',
          position: [40, 40],
          formatter: (params) => {
            return `${params.marker}${this.seriesData.aValue}万元`
          }
        },
        title: {
          text: this.seriesData.value + '%',
          textStyle: {
            color: this.colorList[1],
            fontFamily: 'Microsoft YaHei',
            fontWeight: 400
          },
          left: 'center',
          top: 'middle'
        },
        series: [
          {
            name: "",
            type: "bar",
            roundCap: true,
            barWidth: 8,
            showBackground: true,
            backgroundStyle: {
              color: "rgba(42, 93, 210, 0.5)",
            },
            data: [this.seriesData.value],
            coordinateSystem: "polar",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: this.colorList[0],
                  },
                  {
                    offset: 1,
                    color: this.colorList[1],
                  },
                ]),
              },
            },
          }
        ],
      })
    }
  }
}
</script>
