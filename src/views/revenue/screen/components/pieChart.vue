<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    pieData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    pieData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        tooltip: {
          trigger: 'item'
        },
        series: [
          // 内
          // {
          //   color: ['#54E3F4', '#6E9DC9'],
          //   type: 'pie',
          //   radius: [0, '35%'],
          //   center: ['50%', '55%'],
          //   label: {
          //     position: 'inner'
          //   },
          //   labelLine: {
          //     show: false
          //   },
          //   data: [
          //     {
          //       name: '接入',
          //       value: 2000
          //     },
          //     {
          //       name: '未接入',
          //       value: 1800
          //     }            
          //   ]
          // },
          // 外
          {
            color: ['#03CCFF', '#006BE7', '#C6ECE1'],
            type: 'pie',
            radius: ['55%', '69%'],
            center: ['50%', '55%'],
            labelLine: {
              length: 10,
              length2: 60,
              lineStyle: {
                color: '#ACC6EA'
              }
            },
            label: {
              // '{top | {b}} \n {c}万m³({d}%)'
              formatter: '{top|{b}} \n {bottom|{c}支}({d}%)',
              padding: -50,
              // distanceToLabelLine: 5,
              rich: {
                top: {
                  padding: 5
                },
                bottom: {
                  padding: 5
                }
              }
            },
            data: this.pieData
          }
        ]
      })
    }
  }
}
</script>
