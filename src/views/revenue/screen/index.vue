<template>
  <div class="RevenueIndex">
    <RevnueMap />

    <!-- 面板控制 -->
    <div class="mapMode" @click="changeMapMode" :class="{ mapModeFull: isFull }">
      <img v-if="isFull" src="@/assets/img/home/<USER>/full-active.png" alt="">
      <img v-else src="@/assets/img/home/<USER>/full.png" alt="">
    </div>

    <LeftBar @getTopTwoData="getTopTwoData" v-if="!isFull" style="z-index: 1001;" />
    <!-- 上部数据 -->
    <div class="topData" style="z-index: 1001;">
      <div class="dataItem">
        <img src="@/assets/img/home/<USER>" alt="" />
        <div style="margin-left: 10px">
          <div>年供水量</div>
          <div class="value">
            {{ topInfo[0] }} <span style="font-size: 14px">万吨</span>
          </div>
        </div>
      </div>
      <div class="dataItem">
        <img src="@/assets/img/revenue/type1.png" alt="" />
        <div style="margin-left: 10px">
          <div>年营业额</div>
          <div class="value">
            {{ yearReceived }} <span style="font-size: 14px">万元</span>
          </div>
        </div>
      </div>
      <div class="dataItem">
        <img src="@/assets/img/revenue/type1.png" alt="" />
        <div style="margin-left: 10px">
          <div>年预存额</div>
          <div class="value">
            {{ yearPrePay }} <span style="font-size: 14px">万元</span>
          </div>
        </div>
      </div>
      <div class="dataItem">
        <img src="@/assets/img/home/<USER>" alt="" />
        <div style="margin-left: 10px">
          <div>用户总数</div>
          <div class="value">
            {{ topInfo[2] }} <span style="font-size: 14px">户</span>
          </div>
        </div>
      </div>
      <div class="dataItem">
        <img src="@/assets/img/revenue/type2.png" alt="" />
        <div style="margin-left: 10px">
          <div>水表总数</div>
          <div class="value">
            {{ topInfo[3] }} <span style="font-size: 14px">支</span>
          </div>
        </div>
      </div>
    </div>
    <BottomBar v-if="!isFull" style="z-index: 1001;" />
    <RightBar v-if="!isFull" style="z-index: 1001;" />
  </div>
</template>

<script>
import RevnueMap from './map.vue'
import LeftBar from './leftbar.vue'
import BottomBar from './bottombar.vue'
import RightBar from './rightbar.vue'

import { getTopInfo } from '@/api/revenue/screen'
// import { getYearAndMonthRevenueData } from '@/api/revenue/data'

export default {
  name: 'RevenueIndex',
  components: {
    RevnueMap,
    LeftBar,
    BottomBar,
    RightBar
  },
  data() {
    return {
      yearReceived: 0, // 年营业额
      yearPrePay: 0, // 年预存额
      topInfo: [],
      isFull: false,
      payInfo: {}
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getTopTwoData(data) {
      this.yearReceived = this.filterToW(data.yearReceived || 0)
      this.yearPrePay = this.filterToW(data.yearPrePay || 0)
    },
    getData() {
      getTopInfo().then(res=> {
        this.topInfo = res.data
      })
      // getYearAndMonthRevenueData().then(res => {
      //   const data = res || {}
      //   this.yearReceived = this.filterToW(data.yearReceived || 0)
      //   this.yearPrePay = this.filterToW(data.yearPrePay || 0)
      //   this.payInfo = {
      //     wxPay: data.wxPay,
      //     aliPay: data.aliPay,
      //     cashPay: data.cashPay,
      //     otherPay: data.otherPay,
      //     total: data.wxPay + data.aliPay + data.cashPay + data.otherPay
      //   }
      // })
    },
    filterToW(value) {
      return (value / 10000).toFixed(2)
    },
    changeMapMode() {
      this.isFull = !this.isFull
    }
  }
}
</script>

<style lang="scss" scoped>
.RevenueIndex {
  height: calc(100vh - 84px);
  // background-image: url('~@/assets/img/revenue/staticMap.png');
  // background-size: 100%;
  position: relative;
  .mapMode {
    position: absolute;
    top: 20px;
    left: 480px;
    z-index: 1002;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .mapModeFull {
    left: 30px;
  }
  .topData {
    position: absolute;
    padding: 0 90px;
    top: 0;
    left: 470px;
    right: 470px;
    display: flex;
    justify-content: space-between;
    .dataItem {
      display: flex;
      align-items: center;
      color: #b0d7ff;
      font-size: 16px;
      .value {
        // font-family: DIN;
        color: #ffffff;
        font-weight: bold;
        font-size: 24px;
      }
    }
    img {
      width: 60px;
    }
  }
}
</style>