<template>
  <div id="remap">
    <div
      style="
        position: absolute;
        top: 10px;
        left: 20px;
        display: flex;
        flex-direction: column;
        background-color: aqua;
      "
    >
      <i
        class="el-icon-share"
        style="color: black; width: 20px; height: 20px; margin-bottom: 10px"
        @click="drawline()"
      ></i>
      <i
        class="el-icon-edit"
        style="color: black; width: 20px; height: 20px; margin-bottom: 10px"
        @click="initDrawOptions()"
      ></i>
      <i
        class="el-icon-delete"
        style="color: black; width: 20px; height: 20px"
        @click="clearline()"
      ></i>
    </div>
  </div>
</template>

<script>
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import Ground from "@arcgis/core/Ground";
import WMSLayer from "@arcgis/core/layers/WMSLayer";
import WFSLayer from "@arcgis/core/layers/WFSLayer";
import Vue from "vue";
import Graphic from "@arcgis/core/Graphic";
import TileInfo from "@arcgis/core/layers/support/TileInfo";
import WebTileLayer from "@arcgis/core/layers/WebTileLayer";
import Polygon from "@arcgis/core/geometry/Polygon";
import Polyline from "@arcgis/core/geometry/Polyline";
import Draw from "@arcgis/core/views/draw/Draw";
export default {
  data() {
    return {
      map: null,
      view: null,
      imglayer: null,
      pressure: null,
      geourl: "",
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.createmap();
  },
  methods: {
    createmap() {
      let tileInfo = new TileInfo({
        dpi: 90.71428571427429, // 切片方案的每英寸点数（即像素）。
        size: 256, // 设置每个瓷砖的高度和宽度为[256, 256]像素。
        origin: {
          // 切片方案的原点。
          type: "point",
          x: -180,
          y: 90,
          spatialReference: { wkid: 4326 },
        },
        spatialReference: { wkid: 4326 },
        lods: [
          // 定义平铺方案的详细级别数组。
          {
            level: 2,
            levelValue: 2,
            resolution: 0.3515625,
            scale: 147748796.52937502,
          },
          {
            level: 3,
            levelValue: 3,
            resolution: 0.17578125,
            scale: 73874398.264687508,
          },
          {
            level: 4,
            levelValue: 4,
            resolution: 0.087890625,
            scale: 36937199.132343754,
          },
          {
            level: 5,
            levelValue: 5,
            resolution: 0.0439453125,
            scale: 18468599.566171877,
          },
          {
            level: 6,
            levelValue: 6,
            resolution: 0.02197265625,
            scale: 9234299.7830859385,
          },
          {
            level: 7,
            levelValue: 7,
            resolution: 0.010986328125,
            scale: 4617149.8915429693,
          },
          {
            level: 8,
            levelValue: 8,
            resolution: 0.0054931640625,
            scale: 2308574.9457714846,
          },
          {
            level: 9,
            levelValue: 9,
            resolution: 0.00274658203125,
            scale: 1154287.4728857423,
          },
          {
            level: 10,
            levelValue: 10,
            resolution: 0.001373291015625,
            scale: 577143.73644287116,
          },
          {
            level: 11,
            levelValue: 11,
            resolution: 0.0006866455078125,
            scale: 288571.86822143558,
          },
          {
            level: 12,
            levelValue: 12,
            resolution: 0.00034332275390625,
            scale: 144285.93411071779,
          },
          {
            level: 13,
            levelValue: 13,
            resolution: 0.000171661376953125,
            scale: 72142.967055358895,
          },
          {
            level: 14,
            levelValue: 14,
            resolution: 8.58306884765625e-5,
            scale: 36071.483527679447,
          },
          {
            level: 15,
            levelValue: 15,
            resolution: 4.291534423828125e-5,
            scale: 18035.741763839724,
          },
          {
            level: 16,
            levelValue: 16,
            resolution: 2.1457672119140625e-5,
            scale: 9017.8708819198619,
          },
          {
            level: 17,
            levelValue: 17,
            resolution: 1.0728836059570313e-5,
            scale: 4508.9354409599309,
          },
          {
            level: 18,
            levelValue: 18,
            resolution: 5.3644180297851563e-6,
            scale: 2254.4677204799655,
          },
          {
            level: 19,
            levelValue: 19,
            resolution: 2.68220901489257815e-6,
            scale: 1127.23386023998275,
          },
          {
            level: 20,
            levelValue: 2,
            resolution: 1.341104507446289075e-6,
            scale: 563.616930119991375,
          },
        ],
      });
      let tk = "1c66e85936cb892f7cf53782e0dce6a4";
      // 电子地图
      this.vectorBasemapLayer = new WebTileLayer({
        id: "vectorBasemapMap",
        title: "vectorBasemapMap",
        urlTemplate:
          "http://{subDomain}.tianditu.gov.cn/vec_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=" +
          tk,
        subDomains: ["t0"],
        tileInfo: tileInfo,
        spatialReference: { wkid: 4326 },
        id: "elec",
      });
      this.vectorNoteLayer = new WebTileLayer({
        id: "vectorNoteMap",
        title: "vectorNoteMap",
        urlTemplate:
          "http://{subDomain}.tianditu.gov.cn/cva_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=" +
          tk,
        subDomains: ["t0"],
        tileInfo: tileInfo,
        spatialReference: { wkid: 4326 },
        id: "elec",
      });
      this.imglayer = new WFSLayer({
        url: this.geourl + "/geoserver/area/wms",
      });
      this.pipe_line = new WFSLayer({
        url: this.geourl + "/geoserver/pipe_line/wms",
      });
      this.map = new Map({
        ground: new Ground({
          layers: [],
          surfaceColor: "transparent",
          opacity: 0,
        }),
        layers: [],
      });
      this.view = new MapView({
        container: "remap",
        map: this.map,
        background: {
          type: "color",
          color: [255, 252, 244, 0],
        },
        extent: {
          xmax: 110.78310314022976,
          xmin: 110.71962062546056,
          ymax: 31.261356703995727,
          ymin: 31.233123690848377,
        },
        spatialReference: {
          wkid: 4326,
        },
      });
      this.view.ui.remove("attribution");
      this.view.ui.empty("top-left");
      this.view.map.add(this.vectorBasemapLayer);
      this.view.map.add(this.vectorNoteLayer);
      this.view.map.add(this.imglayer);
      this.view.map.add(this.pipe_line);
      this.view.on("click", async (e) => {
        let point = [e.mapPoint.x, e.mapPoint.y];
        // console.log(point);
        // this.view.hitTest(e).then((res) => {
        //   console.log(res);
        // });
      });
    },
    // 绘制线
    drawline() {
      this.draw = new Draw({
        view: this.view,
      });
      var action = this.draw.create("polyline", {
        mode: "click",
      });
      let that = this;
      action.on("vertex-add", function (evt) {
        that.getgraphic(evt.vertices);
      });
      action.on("vertex-remove", function (evt) {
        that.getgraphic(evt.vertices);
      });
      action.on("cursor-update", function (evt) {
        that.getgraphic(evt.vertices);
      });
      action.on("draw-complete", function (evt) {
        that.getgraphic(evt.vertices, true);
      });
    },
    getgraphic(vertices, state) {
      this.view.graphics.removeAll();
      let polyline = new Polyline({
        paths: vertices,
        spatialReference: this.view.spatialReference,
      });
      var graphic = new Graphic({
        geometry: polyline,
        symbol: {
          type: "simple-line",
          color: [51, 51, 204, 1],
          width: 3,
        },
      });
      this.view.graphics.add(graphic);
      if (state) {
        console.log(vertices);
      }
    },
    clearline() {
      this.view.graphics.removeAll();
    },
    createpolygon(event,state) {
      //获取所有顶点
      var vertices = event.vertices;
      var graphic;
      if (vertices.length < 2) {
        return;
      }
      // 清除之前绘制
      this.view.graphics.removeAll();
      //两点画矩形
      let polygon = new Polygon({
        hasZ: false,
        hasM: false,
        rings: [vertices],
        spatialReference: this.view.spatialReference,
      });
      // 生成绘制的图形
      graphic = new Graphic({
        geometry: polygon,
        symbol: {
          type: "simple-fill",
          color: [51, 51, 204, 0.2],
          style: "solid",
          outline: {
            color: [51, 51, 204, 1],
            width: 2,
          },
        },
      });
      this.view.graphics.add(graphic);
      if(state){
        console.log([vertices]);
      }
    },
    initDrawOptions() {
      this.draw = null;
      this.draw = new Draw({
        view: this.view,
      });
      this.action = this.draw.create("polygon", {
        mode: "click",
      });
      //顶点移除事件
      this.action.on("vertex-remove", (event) => {
        this.createpolygon(event);
      });
      // 鼠标移动事件
      this.action.on("cursor-update", (event) => {
        this.createpolygon(event);
      });
      this.action.on("draw-complete", (event) => {
        this.createpolygon(event,true);
      });
    },
  },
};
</script>
<style lang="scss">
.esri-view-width-medium .esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0.3);
}
.esri-popup__header {
  background-color: rgba(4, 15, 45, 0.3) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  background-color: rgba(4, 15, 45, 0) !important;
}
.esri-popup__icon,
.esri-icon-close {
  color: #fff !important;
}
.esri-popup__content {
  margin: 0;
}
.esri-feature {
  background-color: rgba(4, 15, 45, 0.3);
  padding: 0 15px 12px;
}
.esri-widget__table {
  color: #fff;
}
.esri-popup__header-title {
  background-color: rgba(4, 15, 45, 0) !important;
  color: #fff;
}
.esri-popup__pointer-direction {
  background-color: rgba(4, 15, 45, 0.3);
}

.esri-popup__footer {
  display: none;
}
.esri-popup__feature-menu {
  display: none;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
.esri-view-surface {
  outline: none !important;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: auto 0px Highlight !important;
  outline: auto 0px -webkit-focus-ring-color !important;
}
[class*="esri-popup--is-docked-top-"] .esri-popup__footer,
[class*="esri-popup--aligned-bottom-"] .esri-popup__footer {
  border-bottom: solid 1px #6e6e6e4d;
}
.esri-popup__header {
  border-top-left-radius: 5px !important;
  border-top-right-radius: 5px;
  background-color: #fff;
  color: #fff;
}
.esri-popup--shadow {
  box-shadow: 0 0 0 0 rgb(155, 155, 155) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  outline: none !important;
}
.esri-ui .esri-popup {
  border-radius: 5px !important;
}
.esri-popup {
  position: absolute !important;
}
.esri-popup__button {
  background-color: transparent !important;
  outline: none;
}
</style>
<style scoped lang="scss">
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
#remap {
  position: absolute;
  left: 0;
  top: 84px;
  width: 100%;
  height: 100%;
}
</style>