<template>
  <div class="v2-home">
    <!-- 背景 -->
    <div class="background-container">
      <img src="@/assets/img/homev2/大bg.png" alt="背景" class="bg-image" />
    </div>

    <!-- 页面头部 -->
    <div class="header">
      <div class="header-left">
        <div class="logo-section">
          <span class="logo-text">兴山农村智慧供水平台</span>
        </div>
      </div>
      <div class="header-right">
        <div class="user-info">
          <span class="back-btn" @click="goBack">
            <img src="@/assets/img/homev2/展开.png" alt="返回" />
            白沙下app
          </span>
          <div class="user-section">
            <img src="@/assets/img/homev2/用户名.png" alt="用户" />
            <span>您好，管理员</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 供水监管平台 -->
      <div class="platform-section">
        <div class="platform-icon" @click="handlePlatformClick">
          <img src="@/assets/img/homev2/平台.png" alt="供水监管平台" />
          <div class="platform-title">供水监管平台</div>
        </div>
      </div>

      <!-- 数据统计区域 -->
      <div class="stats-section">
        <div class="stat-item">
          <img src="@/assets/img/homev2/供水总量.png" alt="供水总量" />
          <div class="stat-content">
            <div class="stat-number">125,890</div>
            <div class="stat-label">供水总量(m³)</div>
          </div>
        </div>
        <div class="stat-item">
          <img src="@/assets/img/homev2/管网长度.png" alt="管网长度" />
          <div class="stat-content">
            <div class="stat-number">1256</div>
            <div class="stat-label">管网长度(km)</div>
          </div>
        </div>
        <div class="stat-item">
          <img src="@/assets/img/homev2/人口.png" alt="服务人口" />
          <div class="stat-content">
            <div class="stat-number">89,657</div>
            <div class="stat-label">服务人口(人)</div>
          </div>
        </div>
        <div class="stat-item">
          <img src="@/assets/img/homev2/达标率.png" alt="达标率" />
          <div class="stat-content">
            <div class="stat-number">99.8</div>
            <div class="stat-label">达标率(%)</div>
          </div>
        </div>
      </div>

      <!-- 业务模块区域 -->
      <div class="business-section">
        <div class="business-row">
          <div class="business-card" @click="handleCardClick(businessCards[0])">
            <div class="card-bg supply-dispatch"></div>
            <div class="card-content">
              <div class="card-icon">
                <img src="@/assets/img/homev2/按钮.png" alt="供水调度" />
              </div>
              <div class="card-title">供水调度</div>
            </div>
          </div>
          <div class="business-card" @click="handleCardClick(businessCards[1])">
            <div class="card-bg pipe-leakage"></div>
            <div class="card-content">
              <div class="card-icon">
                <img src="@/assets/img/homev2/按钮.png" alt="管网漏损" />
              </div>
              <div class="card-title">管网漏损</div>
            </div>
          </div>
          <div class="business-card" @click="handleCardClick(businessCards[2])">
            <div class="card-bg business-charge"></div>
            <div class="card-content">
              <div class="card-icon">
                <img src="@/assets/img/homev2/按钮.png" alt="营业收费" />
              </div>
              <div class="card-title">营业收费</div>
            </div>
          </div>
          <div class="business-card" @click="handleCardClick(businessCards[3])">
            <div class="card-bg pipe-gis"></div>
            <div class="card-content">
              <div class="card-icon">
                <img src="@/assets/img/homev2/按钮.png" alt="管网GIS" />
              </div>
              <div class="card-title">管网GIS</div>
            </div>
          </div>
          <div class="business-card" @click="handleCardClick(businessCards[4])">
            <div class="card-bg water-quality"></div>
            <div class="card-content">
              <div class="card-icon">
                <img src="@/assets/img/homev2/按钮.png" alt="水质检测" />
              </div>
              <div class="card-title">水质检测</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台管理区域 -->
      <div class="platform-management">
        <div class="platform-row">
          <div class="platform-card">
            <img
              src="@/assets/img/homev2/公共管理平台.png"
              alt="公共管理平台"
            />
            <div class="platform-name">公共管理平台</div>
          </div>
          <div class="platform-card">
            <img
              src="@/assets/img/homev2/物联网接入平台.png"
              alt="物联网接入平台"
            />
            <div class="platform-name">物联网接入平台</div>
          </div>
          <div class="platform-card">
            <img src="@/assets/img/homev2/GIS.png" alt="GIS服务平台" />
            <div class="platform-name">GIS服务平台</div>
          </div>
          <div class="platform-card">
            <img src="@/assets/img/homev2/工单.png" alt="工单接入平台" />
            <div class="platform-name">工单接入平台</div>
          </div>
          <div class="platform-card">
            <img src="@/assets/img/homev2/统一报警.png" alt="统一报警中心" />
            <div class="platform-name">统一报警中心</div>
          </div>
          <div class="platform-card">
            <img src="@/assets/img/homev2/视频监控.png" alt="视频监控平台" />
            <div class="platform-name">视频监控平台</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "V2Home",
  data() {
    return {
      // 业务模块卡片数据
      businessCards: [
        {
          id: 1,
          title: "供水调度",
          route: { name: "WsScreen" },
        },
        {
          id: 2,
          title: "管网漏损",
          route: { name: "WlScreen" },
        },
        {
          id: 3,
          title: "营业收费",
          route: null, // 暂时不做跳转
        },
        {
          id: 4,
          title: "管网GIS",
          route: { name: "PipeNetworkIndex" },
        },
        {
          id: 5,
          title: "水质检测",
          route: { name: "WqScreen" },
        },
      ],
    };
  },
  methods: {
    // 处理业务卡片点击
    handleCardClick(card) {
      if (card && card.route) {
        this.$router.push(card.route);
      }
    },
    // 处理供水监管平台点击 - 跳转到供水一张图
    handlePlatformClick() {
      this.$router.push({ name: "InfoMap" });
    },
    // 返回按钮
    goBack() {
      // 可以根据需要实现返回逻辑
      console.log("返回白沙下app");
    },
  },
};
</script>

<style lang="scss" scoped>
.v2-home {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  color: #fff;

  // 背景容器
  .background-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .bg-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  // 页面头部
  .header {
    position: relative;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;

    .header-left {
      .logo-section {
        .logo-text {
          font-size: 28px;
          font-weight: bold;
          color: #fff;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
      }
    }

    .header-right {
      .user-info {
        display: flex;
        align-items: center;
        gap: 30px;

        .back-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          color: #fff;
          font-size: 14px;

          img {
            width: 16px;
            height: 16px;
          }

          &:hover {
            opacity: 0.8;
          }
        }

        .user-section {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #fff;
          font-size: 14px;

          img {
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    position: relative;
    z-index: 5;
    padding: 0 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
  }

  // 供水监管平台
  .platform-section {
    text-align: center;

    .platform-icon {
      cursor: pointer;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }

      img {
        width: 120px;
        height: auto;
        margin-bottom: 10px;
      }

      .platform-title {
        font-size: 18px;
        font-weight: bold;
        color: #fff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
    }
  }

  // 数据统计区域
  .stats-section {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin: 20px 0;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 15px;

      img {
        width: 40px;
        height: 40px;
      }

      .stat-content {
        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #fff;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }

  // 业务模块区域
  .business-section {
    width: 100%;
    max-width: 1000px;

    .business-row {
      display: flex;
      justify-content: center;
      gap: 20px;
      flex-wrap: wrap;

      .business-card {
        position: relative;
        width: 180px;
        height: 120px;
        cursor: pointer;
        transition: transform 0.3s ease;
        border-radius: 12px;
        overflow: hidden;

        &:hover {
          transform: translateY(-5px);
        }

        .card-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;

          // 不同业务模块的背景色
          &.supply-dispatch {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
          }

          &.pipe-leakage {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.business-charge {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }

          &.pipe-gis {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }

          &.water-quality {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
          }
        }

        .card-content {
          position: relative;
          z-index: 2;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          padding: 20px;

          .card-icon {
            margin-bottom: 10px;

            img {
              width: 32px;
              height: 32px;
            }
          }

          .card-title {
            font-size: 14px;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }

  // 平台管理区域
  .platform-management {
    width: 100%;
    max-width: 1200px;

    .platform-row {
      display: flex;
      justify-content: center;
      gap: 30px;
      flex-wrap: wrap;

      .platform-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }

        img {
          width: 80px;
          height: 80px;
        }

        .platform-name {
          font-size: 12px;
          color: #fff;
          text-align: center;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .v2-home {
    .main-content {
      padding: 0 20px;
      gap: 30px;
    }

    .stats-section {
      gap: 40px;
      flex-wrap: wrap;
    }

    .business-section .business-row {
      gap: 15px;

      .business-card {
        width: 160px;
        height: 100px;
      }
    }

    .platform-management .platform-row {
      gap: 20px;

      .platform-card img {
        width: 60px;
        height: 60px;
      }
    }
  }
}

@media (max-width: 768px) {
  .v2-home {
    .header {
      padding: 15px 20px;
      flex-direction: column;
      gap: 15px;

      .header-left .logo-section .logo-text {
        font-size: 24px;
      }

      .header-right .user-info {
        gap: 20px;
      }
    }

    .main-content {
      padding: 0 15px;
      gap: 25px;
    }

    .stats-section {
      flex-direction: column;
      gap: 20px;
      align-items: center;

      .stat-item {
        gap: 10px;

        img {
          width: 32px;
          height: 32px;
        }

        .stat-content {
          .stat-number {
            font-size: 20px;
          }

          .stat-label {
            font-size: 11px;
          }
        }
      }
    }

    .business-section .business-row {
      flex-direction: column;
      align-items: center;
      gap: 15px;

      .business-card {
        width: 200px;
        height: 80px;

        .card-content {
          flex-direction: row;
          gap: 15px;

          .card-icon {
            margin-bottom: 0;

            img {
              width: 28px;
              height: 28px;
            }
          }
        }
      }
    }

    .platform-management .platform-row {
      gap: 15px;

      .platform-card {
        img {
          width: 50px;
          height: 50px;
        }

        .platform-name {
          font-size: 11px;
        }
      }
    }
  }
}
</style>
