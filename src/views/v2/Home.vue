<template>
  <div class="v2-home">
    <!-- 页面头部 -->
    <div class="header">
      <h1 class="page-title">智慧水务管理系统</h1>
    </div>

    <!-- 卡片容器 -->
    <div class="card-container">
      <!-- 第一行：供水一张图 -->
      <div class="first-row">
        <NavCard
          :card="mainCard"
          :is-main="true"
          @card-click="handleCardClick"
        />
      </div>

      <!-- 第二行：四个业务模块 -->
      <div class="second-row">
        <NavCard
          v-for="card in businessCards"
          :key="card.id"
          :card="card"
          @card-click="handleCardClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import NavCard from './components/NavCard.vue'

export default {
  name: 'V2Home',
  components: {
    NavCard
  },
  data() {
    return {
      // 主卡片：供水一张图
      mainCard: {
        id: 1,
        title: '供水一张图',
        icon: 'water-map',
        color: '#4A90E2',
        route: { name: 'InfoMap' }, // 跳转到原始页面
        description: '水务综合概览'
      },
      // 四个业务模块卡片 - 跳转到原始页面
      businessCards: [
        {
          id: 2,
          title: '供水调度',
          icon: 'dispatch',
          color: '#FF6B6B',
          route: { name: 'WsScreen' }, // 跳转到原始页面
          description: '实时监测、预警预报'
        },
        {
          id: 3,
          title: '管网漏损',
          icon: 'leakage',
          color: '#4ECDC4',
          route: { name: 'WlScreen' }, // 跳转到原始页面
          description: '漏损分析、智能诊断'
        },
        {
          id: 4,
          title: '管网GIS',
          icon: 'gis',
          color: '#45B7D1',
          route: { name: 'PipeNetworkIndex' }, // 跳转到原始页面
          description: '管网分析、专题展示'
        },
        {
          id: 5,
          title: '水质检测',
          icon: 'quality',
          color: '#96CEB4',
          route: { name: 'WqScreen' }, // 跳转到原始页面
          description: '水质监测、数据分析'
        }
      ]
    }
  },
  methods: {
    handleCardClick(card) {
      // 路由跳转
      this.$router.push(card.route)
    }
  }
}
</script>

<style lang="scss" scoped>
.v2-home {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
  color: #fff;

  .header {
    text-align: center;
    margin-bottom: 60px;

    .page-title {
      font-size: 36px;
      font-weight: bold;
      color: #fff;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      margin: 0;
    }
  }

  .card-container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-rows: auto auto;
    gap: 60px;

    .first-row {
      display: flex;
      justify-content: center;
    }

    .second-row {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 30px;
      justify-items: center;
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .v2-home {
    .card-container {
      .second-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .v2-home {
    padding: 20px 10px;
    
    .header {
      margin-bottom: 40px;
      
      .page-title {
        font-size: 28px;
      }
    }
    
    .card-container {
      gap: 40px;
      
      .second-row {
        grid-template-columns: 1fr;
        gap: 20px;
      }
    }
  }
}
</style>