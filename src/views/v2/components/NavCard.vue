<template>
  <div 
    class="nav-card" 
    :class="{ 'main-card': isMain }"
    :style="{ backgroundColor: card.color }"
    @click="handleClick"
  >
    <div class="card-content">
      <!-- 图标区域 -->
      <div class="icon-container">
        <div class="icon" :class="`icon-${card.icon}`">
          <i :class="getIconClass(card.icon)"></i>
        </div>
      </div>
      
      <!-- 文本区域 -->
      <div class="text-container">
        <h3 class="card-title">{{ card.title }}</h3>
        <p class="card-description" v-if="card.description">{{ card.description }}</p>
      </div>
    </div>
    
    <!-- 悬浮效果遮罩 -->
    <div class="hover-overlay"></div>
  </div>
</template>

<script>
export default {
  name: 'NavCard',
  props: {
    card: {
      type: Object,
      required: true
    },
    isMain: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClick() {
      this.$emit('card-click', this.card)
    },
    getIconClass(iconType) {
      const iconMap = {
        'water-map': 'el-icon-location',
        'dispatch': 'el-icon-setting',
        'leakage': 'el-icon-warning',
        'gis': 'el-icon-map-location',
        'quality': 'el-icon-data-analysis'
      }
      return iconMap[iconType] || 'el-icon-menu'
    }
  }
}
</script>

<style lang="scss" scoped>
.nav-card {
  position: relative;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  // 默认卡片尺寸
  width: 220px;
  height: 140px;
  
  // 主卡片尺寸
  &.main-card {
    width: 320px;
    height: 200px;
  }

  .card-content {
    position: relative;
    height: 100%;
    padding: 24px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    z-index: 2;
  }

  .icon-container {
    margin-bottom: 16px;
    
    .icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: #fff;
      transition: transform 0.3s ease;
      
      .main-card & {
        width: 64px;
        height: 64px;
        font-size: 32px;
      }
    }
  }

  .text-container {
    .card-title {
      font-size: 18px;
      font-weight: bold;
      color: #fff;
      margin: 0 0 8px 0;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      
      .main-card & {
        font-size: 24px;
        margin-bottom: 12px;
      }
    }

    .card-description {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
      line-height: 1.4;
      
      .main-card & {
        font-size: 14px;
      }
    }
  }

  .hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
      rgba(255, 255, 255, 0.1) 0%, 
      rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  // 悬浮效果
  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
    
    .hover-overlay {
      opacity: 1;
    }
    
    .icon {
      transform: scale(1.1);
    }
  }

  // 激活效果
  &:active {
    transform: translateY(-4px) scale(1.01);
  }
}

// 不同业务模块的特殊样式
.nav-card {
  // 供水一张图
  &[style*="#4A90E2"] {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  }
  
  // 供水调度
  &[style*="#FF6B6B"] {
    background: linear-gradient(135deg, #FF6B6B 0%, #EE5A52 100%);
  }
  
  // 管网漏损
  &[style*="#4ECDC4"] {
    background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  }
  
  // 管网GIS
  &[style*="#45B7D1"] {
    background: linear-gradient(135deg, #45B7D1 0%, #3A9BC1 100%);
  }
  
  // 水质检测
  &[style*="#96CEB4"] {
    background: linear-gradient(135deg, #96CEB4 0%, #7FB069 100%);
  }
}

// 响应式调整
@media (max-width: 768px) {
  .nav-card {
    width: 180px;
    height: 120px;
    
    &.main-card {
      width: 260px;
      height: 160px;
    }
    
    .card-content {
      padding: 16px;
    }
    
    .icon-container .icon {
      width: 40px;
      height: 40px;
      font-size: 20px;
      
      .main-card & {
        width: 56px;
        height: 56px;
        font-size: 28px;
      }
    }
    
    .text-container {
      .card-title {
        font-size: 16px;
        
        .main-card & {
          font-size: 20px;
        }
      }
      
      .card-description {
        font-size: 11px;
        
        .main-card & {
          font-size: 13px;
        }
      }
    }
  }
}
</style>