<template>
  <div class="wqReport">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="wq" />
    <div class="query">
      <span>报告名称：</span>
      <el-input v-model="reportName" placeholder="请输入关键字" size="medium" style="width: 200px;" class="mr-20"></el-input>
      <span>报告类型：</span>
      <el-select v-model="reportType" size="medium" style="width: 200px;" class="mr-20">
        <el-option v-for="t in reportTypeList" :key="t.value" :value="t.value" :label="t.label"></el-option>
      </el-select>
      <span>生成方式：</span>
      <el-select v-model="createWay" size="medium" style="width: 200px;" class="mr-20">
        <el-option v-for="t in createWayList" :key="t.value" :value="t.value" :label="t.label"></el-option>
      </el-select>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="getList">查询</el-button>
      <el-button type="info" icon="el-icon-refresh" size="medium" @click="resetQuery">重置</el-button>
      <el-button type="success" icon="el-icon-plus" size="medium" @click="openDialog">手动生成</el-button>
      <el-button type="success" icon="el-icon-upload" size="medium" @click="openUpload">人工上传</el-button>
    </div>

    <div class="tableContainer">
      <el-table
        :data="tableData"
        height="calc(100% - 50px)"
        size="medium"
      >
        <el-table-column label="报告名称" prop="reportName" width="500"></el-table-column>
        <el-table-column label="报告类型">
          <template slot-scope="{ row }">{{ row.reportType | reportTypeFilter }}</template>
        </el-table-column>
        <el-table-column label="生成方式">
          <template slot-scope="{ row }">{{ row.createWay | createWayFilter }}</template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="{ row }">{{ row.status | statusFilter }}</template>
        </el-table-column>
        <el-table-column label="生成时间" prop="createTime"></el-table-column>
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <el-tooltip content="预览" placement="bottom" v-if="row.status == 1">
              <i class="el-icon-view actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;"
                @click="previewFile(row.id)" />
            </el-tooltip>
            <el-tooltip content="下载" placement="bottom" v-if="row.status == 1">
              <i class="el-icon-download actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;"
                @click="handleDownloadFile(row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="bottom">
              <i class="el-icon-delete actionIcon" style="color: #f56c6c;font-size: 19px;margin-right: 15px;"
                @click="handleDelete(row.id)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination        
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageInfo.pageNo"
          :page-sizes="[10, 20, 30, 40]"
          :page-size.sync="pageInfo.pageSize"
          layout="total, sizes, ->, prev, pager, next, jumper"
          :total="pageInfo.total">
        </el-pagination>
      </div>
    </div>

    <el-dialog
      title="手动生成报告"
      :visible.sync="visible"
      width="576px"
    >
      <el-form :model="form" :rules="rules" label-width="auto" ref="ruleForm">
        <el-form-item label="报告名称：" prop="reportName">
          <el-input v-model="form.reportName" placeholder="请输入报告名称"></el-input>
        </el-form-item>
        <el-form-item label="站点：" prop="station">
          <el-cascader
            v-model="form.station"
            :options="stationList"
            :props="propObject"
            size="medium"
            style="width: 200px;"
            placeholder="请选择站点"
            clearable>
          </el-cascader>
        </el-form-item>
        <el-form-item label="报告模板：" prop="templateType">
          <el-select v-model="form.templateType" placeholder="请选择报告模板" size="medium" style="width: 200px;">
            <el-option v-for="t in reportTemplateList" :key="t.code" :value="t.code" :label="t.templateName"></el-option>
          </el-select>
          <i class="el-icon-view templateIcon" @click="handlePreviewTemplate"></i>
        </el-form-item>
        <el-form-item label="报告类型：" prop="reportType">
          <el-select v-model="form.reportType" placeholder="请选择报告类型" @change="reportTypeChange" size="medium" style="width: 200px;">
            <el-option v-for="t in reportTypeList" :key="t.value" :value="t.value" :label="t.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报告时间：" required>
          <el-row>
            <el-col :span="12">
              <el-form-item prop="year">
                <el-date-picker
                v-model="form.year"
                :type="timePickerType"
                value-format="yyyy-MM-dd HH:mm:ss"
                :format="formatter"
                size="medium"
                :clearable="false"
                style="width: 170px;"
                placeholder="选择时间">
              </el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 季度 -->
            <el-col :span="12">
              <el-form-item v-if="form.reportType === 3" prop="quarter">
                <el-select v-model="form.quarter" size="medium" style="width: 170px;" placeholder="请选择季度">
                  <el-option v-for="i in 4" :key="i" :value="i" :label="'第' + i + '季度'"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>

        <div class="actionBox">
          <el-button type="primary" size="medium" @click="visible = false">取消</el-button>
          <el-button type="primary" size="medium" @click="handeleSubmit">确定</el-button>
        </div>
      </el-form>
    </el-dialog>

    <!-- 人工上传报告 -->
    <el-dialog
      title="人工上传报告"
      :visible.sync="visible2"
      width="576px"
    >
      <el-form :model="uploadForm" :rules="rules" label-width="auto" ref="uploadFormRef">
        <el-form-item label="报告名称：" prop="reportName">
          <el-input v-model="uploadForm.reportName" placeholder="请输入报告名称"></el-input>
        </el-form-item>
        <el-form-item label="站点：" prop="station">
          <el-cascader
            v-model="uploadForm.station"
            :options="stationList"
            :props="propObject"
            size="medium"
            style="width: 200px;"
            placeholder="请选择站点"
            clearable>
          </el-cascader>
        </el-form-item>
        <el-form-item label="报告类型：" prop="reportType">
          <el-select v-model="uploadForm.reportType" @change="reportTypeChange" placeholder="请选择报告类型" size="medium" style="width: 200px;">
            <el-option v-for="t in reportTypeList" :key="t.value" :value="t.value" :label="t.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报告时间：" required>
          <el-row>
            <el-col :span="12">
              <el-form-item prop="year" style="margin-bottom: 0;">
                <el-date-picker
                v-model="uploadForm.year"
                :type="timePickerType"
                value-format="yyyy-MM-dd HH:mm:ss"
                :format="formatter"
                size="medium"
                :clearable="false"
                style="width: 170px;"
                placeholder="请选择报告时间">
              </el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 季度 -->
            <el-col :span="12">
              <el-form-item v-if="uploadForm.reportType === 3" prop="quarter" style="margin-bottom: 0;">
                <el-select v-model="uploadForm.quarter" size="medium" style="width: 170px;" placeholder="请选择季度">
                  <el-option v-for="i in 4" :key="i" :value="i" :label="'第' + i + '季度'"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="">
          <el-button type="success" @click="chooseFile">选择文件</el-button>
          <input type="file" accept=".pdf, .doc, .docx" @change="fileChange" ref="fileRef" id="fileRef" >
        </el-form-item>

        <div class="actionBox">
          <el-button type="primary" size="medium" @click="close2">取消</el-button>
          <el-button type="primary" size="medium" @click="handleUploadReport">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getReportPageList,
  getReportTemplateType,
  generateReport,
  uploadReport,
  deleteReportById,
  previewReport
} from '@/api/wq/report'
import { getStationList, commonFileDownload, uploadFile } from '@/api/common'

import { parseTime } from '@/utils'
import { handleDownload, getBlobByUrl } from '@/utils/file'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'WqReport',
  components: {
    FloatingSideMenu
  },
  data() {
    return {
      reportTypeList: [
        {
          label: '周度报告',
          value: 1
        },
        {
          label: '月度报告',
          value: 2
        },
        {
          label: '季度报告',
          value: 3
        },
        {
          label: '年度报告',
          value: 4
        }
      ],
      createWayList: [
        {
          label: '自动生成',
          value: 0
        },
        {
          label: '手动生成',
          value: 1
        },
        {
          label: '人工上传',
          value: 2
        }
      ],
      reportType: null,
      createWay: null,
      reportName: '',
      tableData: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      visible: false,
      visible2: false,
      form: {
        year: null,
        quarter: null
      },
      uploadForm: {
        year: null,
        quarter: null
      },
      file: null,
      stationList: [],
      reportTemplateList: [],
      propObject: {
        children: 'list',
        label: 'name',
        value: 'id'
      },
      // 时间选择
      timePickerType: 'week',
      formatter: 'yyyy年第WW周',
      rules: {
        reportName: [{ required: true, message: '请输入报告名称', trigger: 'blur' }],
        station: [{ required: true, message: '请选择站点', trigger: 'change' }],
        templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
        reportType: [{ required: true, message: '请选择报告类型', trigger: 'change' }],
        year: [{ required: true, message: '请选择报告时间', trigger: 'blur' }],
        quarter: [{ required: true, message: '请选择报告类型', trigger: 'change' }]
      }
    }
  },
  filters: {
    statusFilter(status) {
      let statusLabelMap = {
        0: '正在生成中',
        1: '生成成功',
        2: '生成失败'
      }
      return statusLabelMap[status]
    },
    reportTypeFilter(value) {
      let statusLabelMap = {
        1: '周度报告',
        2: '月度报告',
        3: '季度报告',
        4: '年度报告'
      }
      return statusLabelMap[value]
    },
    createWayFilter(value) {
      let statusLabelMap = {
        0: '自动生成',
        1: '手动生成',
        2: '人工上传'
      }
      return statusLabelMap[value]
    }
  },
  mounted() {
    this.init()
    this.getList()
  },
  methods: {
    init() {
      getReportTemplateType().then(res => {
        if(res.status === 200) {
          this.reportTemplateList = res.data
        }
      })
      getStationList().then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName:  item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
        }
      })
    },
    // 分页请求
    getList() {
      let data = {
        reportName: this.reportName,
        reportType: this.reportType,
        createWay: this.createWay,
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize
      }
      getReportPageList(data).then(res => {
        const { status, data, count } = res
        if(status === 200) {
          this.tableData = data
          this.pageInfo.total = count
        }
      })
    },
    // 重置
    resetQuery() {
      this.reportName = ''
      this.reportType = null
      this.createWay = null
      this.getList()
    },
    // 打开手动生成弹窗
    openDialog() {
      
      this.visible = true
    },
    // 选择文件
    chooseFile() {
      this.$refs.fileRef.click()
    },
    // 文件变化
    fileChange(e) {
      console.log(e)
      this.file = e.target.files[0]

      // 上传
      const fd = new FormData()
      fd.append('uploadFile', this.file)
      uploadFile(fd).then(res => {
        if(res.status === 200) {
          this.$message.success('上传成功')
          // this.uploadForm.reportName = res.data.annexName
          this.uploadForm.path = res.data.annexPath
          this.$refs.fileRef.value = null
        }
      })
    },
    // 打开人工上传弹窗
    openUpload() {      
      this.visible2 = true
    },
    // 模板预览
    handlePreviewTemplate() {
      if(!this.form.templateType) {
        this.$message.warning('请先选择报告模板')
        return
      }
      const target = this.reportTemplateList.find(item => item.code === this.form.templateType)
      window.open(target.url)
    },
    // 报告类型变化，设置时间选择类型
    reportTypeChange(value) {
      this.form.year = null
      if(value === 1) {
        this.timePickerType = 'week'
        this.formatter = 'yyyy年第WW周'
      }
      if(value === 2) {
        this.timePickerType = 'month'
        this.formatter = 'yyyy年MM月'
      }
      if(value === 3) {
        this.timePickerType = 'year'
        this.formatter = 'yyyy年'
      }
      if(value === 4) {
        this.timePickerType = 'year'
        this.formatter = 'yyyy年'
      }
    },
    // 手动生成水质报告
    handeleSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let reportStartTime = this.form.year
          if(this.form.reportType === 3) {
            let quarter = parseTime(this.form.year, '{y}') + '-' + (this.form.quarter  * 3 - 2) + '-1'
            reportStartTime = parseTime(quarter)
          }
          let data = {
            reportName: this.form.reportName,
            reportType: this.form.reportType,
            sourceType: this.form.station[0],
            facilityId: this.form.station[1],
            reportStartTime
          }
          generateReport(data).then(res => {
            if(res.status === 200) {
              this.$message.success('水质报告正在生成中')
              this.getList()
            } else {
              this.$message.error('生成失败，请稍后重试')
            }
            this.visible = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 关闭人工上传弹窗
    close2() {
      this.$refs.uploadFormRef.resetFields()
      document.getElementById('fileRef').value = null
      this.visible2 = false      
    },
    // 人工上传报告
    handleUploadReport() {
      if(!this.uploadForm.path) {
        this.$message.error('请选择需要上传的水质报告')
        return
      }
      this.$refs.uploadFormRef.validate((valid) => {
        if(valid) {
          let reportStartTime = this.uploadForm.year
          if(this.uploadForm.reportType === 3) {
            let quarter = parseTime(this.uploadForm.year, '{y}') + '-' + (this.uploadForm.quarter  * 3 - 2) + '-1'
            reportStartTime = parseTime(quarter)
          }
          let data = {
            reportName: this.uploadForm.reportName,
            reportType: this.uploadForm.reportType,
            sourceType: this.uploadForm.station[0],
            facilityId: this.uploadForm.station[1],
            reportStartTime,
            path: this.uploadForm.path
          }
          uploadReport(data).then(res => {
            if(res.status === 200) {
              this.$message.success('人工上传成功')
              this.getList()
            } else {
              this.$message.error('人工上传成功，请稍后重试')
            }
            this.close2()
          })
        }
      })
    },
    // 预览
    async previewFile(id) {
      const loading = this.$loading({
        lock: true,
        text: "加载中..."
      })
      await previewReport(id).then(res => {
        const blob = new Blob([res], { type: "application/pdf" })
        const pdfUrl = window.URL.createObjectURL(blob)
        window.open(pdfUrl)
      })
      loading.close()
    },
    // 下载
    handleDownloadFile(row) {
      commonFileDownload(row.path).then(res => {
        // console.log(res)
        handleDownload(row.reportName + '.docx', res)
      })
      // getBlobByUrl(row.path).then(res => {
      //   handleDownload(row.reportName + '.docx', res)
      // })
    },
    // 删除
    handleDelete(id) {
      this.$confirm('此操作将永久删除该数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        deleteReportById(id).then(res => {
          if(res.status === 200) {
            this.getList()
            this.$message.success('删除成功')
          } else {
            this.$message.warning('删除失败，请稍后重试')
          }
        })
      }).catch(() => {})      
    },
    handleSizeChange() {
      this.pageInfo1.pageNum = 1
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.wqReport {
  color: #ffffff;
  padding: 30px;
  height: calc(100vh - 84px);
  // border: 1px solid #3084B5;
  .query {
    padding-bottom: 20px;
    .mr-20 {
      margin-right: 20px;
    }
  }
  .tableContainer {
    padding: 20px;
    border: 1px solid #3084B5;
    height: calc(100% - 56px);
    .pagination-container {
      height: 50px;
      padding-top: 14px;
    }
  }
  .templateIcon {
    margin-left: 5px;
    font-size: 20px;
    color: #409eff;
    cursor: pointer;
  }
  .actionBox {
    display: flex;
    justify-content: center;
    padding-bottom: 20px;
  }
  #fileRef {
    display: none;
  }
}
</style>
