<template>
  <div class="WqMonitor">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="wq" />
    <div class="tab">
      <div class="first" :class="{ active: currentType === 'realtime' }" @click="handleTabClick('realtime')">实时数据</div>
      <div class="other" :class="{ active: currentType === 'history' }" @click="handleTabClick('history')">历史数据</div>
    </div>

    <div class="table-container" ref="table-container">
      <template v-if="currentType === 'realtime'">
        <div class="query">
          <span class="param">
            <span>站点：</span>
            <el-cascader
              v-model="stationName"
              :options="stationList"
              :props="propObject"
              size="medium"
              clearable>
            </el-cascader>
          </span>
          <el-button type="primary" icon="el-icon-search" size="medium" @click="getList">查询</el-button>
        </div>

        <el-table
          height="calc(100% - 106px)"
          :data="realtimeData"
          :row-class-name="tableRowClassName"
          size="medium"
        >
          <el-table-column prop="name" label="站点名称"></el-table-column>
          <el-table-column prop="sourceTypeName" label="站点类别"></el-table-column>
          <el-table-column label="监测时间" width="200px">
            <template slot-scope="{ row }">
              {{ parseTime(row.dataTime) }}
            </template>
          </el-table-column>
          <!-- <el-table-column prop="wqLevel" label="水质类别"></el-table-column> -->
          <el-table-column label="是否合格">
            <template slot-scope="{ row }">{{ row.pass ? '是' : '否' }}</template>
          </el-table-column>
          <el-table-column prop="ph" label="pH值(6.5~8.5)">
            <template slot-scope="{ row }"><span :class="{ paramError: row.mark.includes('ph') }">{{ row.ph }}</span></template>
          </el-table-column>
          <el-table-column prop="turbidity" label="浊度(≤1 NTU)">
            <template slot-scope="{ row }"><span :class="{ paramError: row.mark.includes('turbidity') }">{{ row.turbidity }}</span></template>
          </el-table-column>
          <el-table-column prop="temp" label="水温(℃)">
            <template slot-scope="{ row }"><span :class="{ paramError: row.mark.includes('temp') }">{{ row.temp }}</span></template>
          </el-table-column>
          <el-table-column prop="powerRate" label="电导率(μs/cm)">
          <template slot-scope="{ row }"><span :class="{ paramError: row.mark.includes('powerRate') }">{{ row.powerRate }}</span></template></el-table-column>
          <el-table-column prop="chlorine" label="余氯(0.05~2 mg/L)">
          <template slot-scope="{ row }"><span :class="{ paramError: row.mark.includes('chlorine') }">{{ row.chlorine }}</span></template></el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination        
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageInfo.pageNum"
            :page-sizes="[10, 20, 30, 40]"
            :page-size.sync="pageInfo.pageSize"
            layout="total, sizes, ->, prev, pager, next, jumper"
            :total="pageInfo.total">
          </el-pagination>
        </div>
      </template>

      <!-- 历史数据 -->
      <template v-if="currentType === 'history'">
        <div class="query">
          <span class="param">
            <span>站点：</span>
            <el-cascader
              v-model="stationName1"
              :options="stationList"
              :props="propObject"
              size="medium"
              clearable>
            </el-cascader>
          </span>
          <span class="param">
            <span>时间：</span>
            <span style="width: 200px; display: inline-box;">
              <el-date-picker
                :clearable="false"             
                v-model="time"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                size="medium"
                >
              </el-date-picker>
            </span>
          </span>
          <el-button type="primary" icon="el-icon-search" size="medium" @click="getHistoryList">查询</el-button>
          <el-button type="primary" icon="el-icon-upload2" size="medium" @click="openImportDialog">导入</el-button>
        </div>

        <el-table
          height="calc(100% - 106px)"
          :data="historyData"
          :row-class-name="tableRowClassName"
          size="medium"
        >
          <el-table-column prop="name" label="站点名称"></el-table-column>
          <el-table-column prop="sourceTypeName" label="站点类别"></el-table-column>
          <el-table-column label="监测时间" width="200px">
            <template slot-scope="{ row }">
              {{ parseTime(row.dataTime) }}
            </template>
          </el-table-column>
          <!-- <el-table-column prop="wqLevel" label="水质类别"></el-table-column> -->
          <el-table-column label="是否合格">
            <template slot-scope="{ row }">{{ row.pass ? '是' : '否' }}</template>
          </el-table-column>
          <el-table-column prop="ph" label="pH值(6.5~8.5)">
            <template slot-scope="{ row }"><span :class="{ paramError: row.mark.includes('ph') }">{{ row.ph }}</span></template>
          </el-table-column>
          <el-table-column prop="turbidity" label="浊度(≤1 NTU)">
            <template slot-scope="{ row }"><span :class="{ paramError: row.mark.includes('turbidity') }">{{ row.turbidity }}</span></template>
          </el-table-column>
          <el-table-column prop="temp" label="水温(℃)">
            <template slot-scope="{ row }"><span :class="{ paramError: row.mark.includes('temp') }">{{ row.temp }}</span></template>
          </el-table-column>
          <el-table-column prop="powerRate" label="电导率(μs/cm)">
            <template slot-scope="{ row }"><span :class="{ paramError: row.mark.includes('powerRate') }">{{ row.powerRate }}</span></template>
          </el-table-column>
          <el-table-column prop="chlorine" label="余氯(0.05~2 mg/L)">
            <template slot-scope="{ row }"><span :class="{ paramError: row.mark.includes('chlorine') }">{{ row.chlorine }}</span></template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="{ row }">
              <el-button type="text" @click="handleDelete(row.id)">删除</el-button>
              <el-button type="text" @click="openDialog(row)">粗差</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination        
            background
            @size-change="handleSizeChange1"
            @current-change="handleCurrentChange1"
            :current-page.sync="pageInfo1.pageNum"
            :page-sizes="[10, 20, 30, 40]"
            :page-size.sync="pageInfo1.pageSize"
            layout="total, sizes, ->, prev, pager, next, jumper"
            :total="pageInfo1.total">
          </el-pagination>
        </div>
      </template>

      <el-dialog
        title="数据粗差"
        width="30%"
        :visible.sync="setHistoryDataErrorDialog"
      >
        <el-form :model="form" label-width="100px">
          <el-form-item label="是否粗差">
            <el-switch
              style="margin-left: 20px;"
              v-model="form.outlierFlag"
              active-color="#ff4949"
              inactive-color="#13ce66"
              active-text="是"
              inactive-text="否"
              :active-value="1"
              :inactive-value="0"
              >
            </el-switch>
          </el-form-item>
          <el-form-item label="粗差原因" v-if="form.outlierFlag">
            <el-input v-model="form.outlierReason" style="margin-left: 20px;width: 300px;"></el-input>
          </el-form-item>
        </el-form>
        <div class="action-box">
          <el-button type="primary" size="medium" @click="handleCancel">取消</el-button>
          <el-button type="primary" size="medium" @click="handeleSubmit">确定</el-button>
        </div>
      </el-dialog>
      
      <!-- 导入水质数据 -->
      <el-dialog
        title="导入水质数据"
        width="30%"
        :visible.sync="importDialog"
      >
        <div>
          <div style="color: #ffffff;padding-bottom: 20px;">操作提示：请先下载模板，编辑模板后，选取文件进行导入</div>
          <el-button type="success" icon="el-icon-download" size="medium" @click="downloadWqTemplate">下载模板</el-button>
          <el-button type="success" icon="el-icon-plus" size="medium" @click="chooseFile">选取文件</el-button>
          <div class="action-box" style="margin-top: 20px;">
            <el-button type="primary" size="medium" @click="importDialog = false">取消</el-button>
            <el-button type="primary" size="medium" @click="uploadFileOK">确定</el-button>
          </div>

          <input type="file" accept=".xlsx" ref="myFile" style="display: none" @change="changeFile">
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getStationList } from '@/api/common'
import { getWqRealtimeDataPage, getWqHistoryDataPage, deleteWqDataById, setWqDataError, getWqImportTemplate, exportWqData } from '@/api/wq/monitor'

import { parseTime } from '@/utils'
import { handleDownload } from '@/utils/file'
import { getDaysDateWithDefault } from '@/utils/timeHelper'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'


export default {
  name: 'WqMonitor',
  components: {
    FloatingSideMenu
  },
  data() {
    return {
      currentType: 'realtime',
      stationList: [],
      propObject: {
        children: 'list',
        label: 'name',
        value: 'id'
      },
      // 实时数据
      stationName: [],      
      realtimeData: [],
      pageInfo: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      // 历史数据
      stationName1: [],
      time: [],
      historyData: [],
      pageInfo1: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      parseTime: parseTime,
      // 粗差
      setHistoryDataErrorDialog: false,
      form: {
        outlierFlag: null,
        outlierReason: null
      },
      // 导入
      importDialog: false,
      file: null,
      fileName: '',
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    // 设置默认事件
    this.time = getDaysDateWithDefault()
    if(this.currentType === 'realtime') {
      this.getList()
    } else {
      this.getHistoryList()
    }
  },
  methods: {
    handleTabClick(type) {
      this.currentType = type
      if(this.currentType === 'realtime') {
        this.getList()
      } else {
        this.getHistoryList()
      }
    },
    initData() {
      // 站点列表
      getStationList('WATER_QUALITY').then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName:  item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
          this.stationList[0].name = '出厂水'
          this.stationList[1].name = '水源水'
          this.stationList[2].name = '末梢水'
        }
      })
    },
    // 查询实时数据
    getList() {
      let facilityId = this.stationName.length > 0 ? this.stationName[1] : null
      let sourceType = this.stationName.length > 0 ? this.stationName[0] : null
      getWqRealtimeDataPage({
        facilityId,
        sourceType,
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize
      }).then(res => {
        if(res.status === 200) {
          this.pageInfo.total = res.count
          this.realtimeData = res.data
        }
      })
    },
    // 查询历数据
    getHistoryList() {
      let facilityId = this.stationName1.length > 0 ? this.stationName1[1] : null
      let sourceType = this.stationName1.length > 0 ? this.stationName1[0] : null

      let temp = this.time || []
      let beginTime = temp.length > 0 ?  this.time[0] : ''
      let endTime = temp.length > 0 ?  this.time[1] : ''

      getWqHistoryDataPage({
        facilityId,
        sourceType,
        beginTime,
        endTime,
        pageNo: this.pageInfo1.pageNum,
        pageSize: this.pageInfo1.pageSize
      }).then(res => {
        if(res.status === 200) {
          this.pageInfo1.total = res.count
          this.historyData = res.data
        }
      })
    },
    tableRowClassName({ row, rowIndx}) {
      if(row.outlierFlag) {
        return 'isBad'
      }
      return ''
    },
    // 导入水质数据
    openImportDialog() {
      this.importDialog = true
    },
    // 下载水质数据导入模板
    downloadWqTemplate() {
      getWqImportTemplate().then(res => {
        handleDownload('水质数据导入模板.xlsx', res)
      })
    },
    chooseFile() {
      this.$refs.myFile.click()
    },
    changeFile(e) {
      if(e.target.files.length > 0) {
        this.file = e.target.files[0]
        this.fileName = e.target.files[0].name
      }
    },
    uploadFileOK() {
      if(!this.file) {
        this.$message.error('请选择文件')
      } else {
        let fileType = this.file.name.split('.').reverse()[0]
        if(fileType !== 'xlsx') {
          this.$message.error('请选择正确类型的文件')
        } else {
          let formData = new FormData()
          formData.append('file', this.file)
          exportWqData(formData).then(res => {
            if(res.status === 200) {
              this.$message.success('导入成功')
              this.getHistoryList()
              this.importDialog = false
            } else {
              this.$refs.myFile.value = ''
              this.file = null
              this.fileName = ''
              console.log(this.fileName)
            }
          })
        }
      }
    },
    // 删除历史数据
    handleDelete(id) {
      this.$confirm('此操作将永久删除该数据，是否继续？', '删除', {
        type: 'error'
      }).then(() => {
        deleteWqDataById(id).then(res => {
          if(res.status === 200) {
            this.$message.success('删除成功')
            this.getHistoryList()
          } else {
            this.$message.error('删除失败')
          }
        })
      }).catch(() => {})
      
    },
    // 粗差
    openDialog(row) {
      this.form.id = row.id
      this.form.outlierFlag = row.outlierFlag === 1 ? 1 : 0
      this.form.outlierReason = row.outlierReason ? row.outlierReason : ''
      this.setHistoryDataErrorDialog = true
    },
    handleCancel() {
      this.setHistoryDataErrorDialog = false
    },
    // 标记粗差
    handeleSubmit() {
      let data = {
        id: this.form.id,
        outlierFlag: this.form.outlierFlag,
        outlierReason: this.form.outlierFlag ? this.form.outlierReason : ''
      }
      setWqDataError(data).then(res => {
        if(res.status === 200 && res.msg === 'Success') {
          this.getHistoryList()
        }
        this.setHistoryDataErrorDialog = false
      })
    },
    handleSizeChange(currentSize) {
      this.pageInfo.pageNum = 1
      this.getList()
    },
    handleCurrentChange(currentPage) {      
      this.getList()
    },
    handleSizeChange1(currentSize) {
      this.pageInfo1.pageNum = 1
      this.getHistoryList()
    },
    handleCurrentChange1(currentPage) {      
      this.getHistoryList()
    }
  }
}
</script>

<style lang="scss">
.WqMonitor {
  .isBad {
    background: gray !important;
  }
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
  .el-switch__label--left.is-active {
    color: #13ce66;
  }
  .el-switch__label--right.is-active {
    color: #ff4949;
  }
}
</style>

<style lang="scss" scoped>
.WqMonitor {
  padding: 0 30px;
  height: calc(100vh - 84px);
  .tab {
    color: #ffffff;    
    display: flex;
    div {
      width: 136px;
      height: 56px;
      opacity: 0.6;
      text-align: center;
      padding-top: 26px;
      cursor: pointer;
      // background-color: (180deg, #FFFFFF 0%, #67C6FF 100%);
    }
    .active {
      opacity: 1;
    }
    .first {
      background-image: url('~@/assets/img/wq/first-tab.png');
      background-size: 100%;
    }
    .other {
      background-image: url('~@/assets/img/wq/other-tab.png');
      background-size: 100%;
    }
  }
  .table-container {
    height: calc(100% - 76px);
    color: #ffffff;
    padding: 20px;
    border: 1px solid #3084B5;
    .query {
      padding-bottom: 20px;
      .param {
        margin-right: 20px;
      }
    }
    .pagination-container {
      height: 50px;
      padding-top: 14px;
    }
    .tabelHeight {
      height: calc(100% - 106px);
    }
  }
  .action-box {
    display: flex;
    padding-bottom: 20px;
    justify-content: center;
  }
  .paramError {
    color: red;
  }
}
</style>