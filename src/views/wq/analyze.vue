<template>
  <div class="WqAnalyze">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="wq" />
    <div class="tab">
      <div class="first" :class="{ active: currentType === 0 }" @click="handleTabClick(0)">单站多参</div>
      <div class="other" v-for="item in typeList" :key="item.value" :class="{ active: currentType === item.value }" @click="handleTabClick(item.value)">{{ item.label }}</div>
    </div>

    <div class="chart-container">
      <OneParam v-if="currentType === 0" />
      <MoreParam v-if="currentType === 1" />
      <TimeAnalyze v-if="currentType === 2" />
      <TimeAnalyzeTwo v-if="currentType === 3" />
    </div>
  </div>
</template>

<script>
import OneParam from './components/oneParam'
import MoreParam from './components/moreParam'
import TimeAnalyze from './components/timeAnalyze'
import TimeAnalyzeTwo from './components/timeAnalyzeTwo'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'WqAnalyze',
  components: {
    OneParam,
    MoreParam,
    TimeAnalyze,
    TimeAnalyzeTwo,
    FloatingSideMenu
  },
  data() {
    return {
      currentType: 0,
      typeList: [
        // {
        //   value: 1,
        //   label: '多站单参'
        // },
        {
          value: 2,
          label: '环比分析'
        },
        {
          value: 3,
          label: '同比分析'
        }
      ]
    }
  },
  methods: {
    handleTabClick(type) {
      this.currentType = type
    }
  }
}
</script>

<style lang="scss">
.WqAnalyze {
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.WqAnalyze {
  padding: 0 30px;
  height: calc(100vh - 84px);
  .tab {
    color: #ffffff;    
    display: flex;
    div {
      width: 136px;
      height: 56px;
      opacity: 0.6;
      text-align: center;
      padding-top: 26px;
      cursor: pointer;
      // background-color: (180deg, #FFFFFF 0%, #67C6FF 100%);
    }
    .active {
      opacity: 1;
    }
    .first {
      background-image: url('~@/assets/img/wq/first-tab.png');
      background-size: 100%;      
    }
    .other {
      background-image: url('~@/assets/img/wq/other-tab.png');
      background-size: 100%;      
    }
  }
  .chart-container {
    color: #ffffff;
    padding: 20px;
    height: calc(100% - 76px);
    border: 1px solid #2A5DD2;
    border-top-color: #3084B5;
  }
}
</style>
