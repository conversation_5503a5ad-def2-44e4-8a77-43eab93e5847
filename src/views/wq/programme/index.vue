<template>
  <div class="programme">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="wq" />
    <div class="form">
      <el-form :inline="true" :model="form">
        <el-row>
          <el-form-item label="方案名称：">
            <el-input v-model="form.name" placeholder="请输入方案名称" />
          </el-form-item>
          <el-form-item label="方案类型：">
            <el-select v-model="form.type" placeholder="请选择方案类型">
              <el-option v-for="option in typeList" :key="option.code" :label="option.name" :value="option.code" />
            </el-select>
          </el-form-item>
          <el-form-item label="方案等级：">
            <el-select v-model="form.level" placeholder="请选择方案等级">
              <el-option v-for="option in levelList" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间：">
            <el-date-picker 
            v-model="form.time" 
            type="datetimerange" 
            :clearable="false" 
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期" 
            value-format="yyyy-MM-dd HH:mm:ss" 
            :default-time="['00:00:00', '23:59:59']"
            style="width: 340px;" ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getData">搜索</el-button>
            <el-button type="primary" icon="el-icon-refresh"
              style="background: #91A4BA !important;border-color: #91A4BA !important;" @click="reSearch">重置</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleAdd">添加方案</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    
    <div class="table-container">
      <el-table :data="dataList" height="calc(100% - 106px)" style="width: 100%;">
        <el-table-column label="序号" width="80" type="index" />
        <el-table-column label="方案名称" prop="name" />
        <el-table-column label="方案等级" prop="level">
          <template slot-scope="scope">{{ levelFormat(scope.row) }}</template>
        </el-table-column>
        <el-table-column label="上传时间" prop="updateTime" />
        <el-table-column label="方案类型" prop="type" :formatter="typeFormat"></el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-tooltip content="预览" placement="bottom">
              <i class="el-icon-view actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;"
                @click="handlePreviewFile(scope.row)" />
            </el-tooltip>
            <el-tooltip content="修改" placement="bottom">
              <i class="el-icon-edit actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;"
                @click="handleEdit(scope.row)" />
            </el-tooltip>
            <el-tooltip content="下载" placement="bottom">
              <i class="el-icon-download actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;"
                @click="handleDownloadReport(scope.row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="bottom">
              <i class="el-icon-delete actionIcon" style="color: #f56c6c;font-size: 19px;margin-right: 15px;"
                @click="handleDelete(scope.row.id)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
          <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page.sync="pageInfo.pageNum" :page-sizes="[10, 20, 30, 40]" :page-size.sync="pageInfo.pageSize"
            layout="total, sizes, ->, prev, pager, next, jumper" :total="pageInfo.total">
          </el-pagination>
        </div>
    </div>

    <el-dialog
      :title="manageDialogTitle"
      :visible.sync="manageDialogVisible"
      width="30%"
      :close-on-click-modal="false">
      <el-form ref="manageForm" :model="manageForm" :rules="manageFormRules" label-width="auto">
        <el-form-item label="问题类型：" prop="type">
          <el-select v-model="manageForm.type" placeholder="请选择问题类型">
            <el-option v-for="item in typeList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>

        <template v-for="(item, index) in manageFormItems">
          <el-form-item v-if="item.type == 'select'" :key="index" :prop="item.field" :label="item.label + '：'">
            <el-select v-model="manageForm[item.field]" :placeholder="'请选择' + item.label">
              <el-option v-for="option in item.options" :key="option.id" :label="option.name" :value="option.id" />
            </el-select>
          </el-form-item>

          <el-form-item v-else-if="item.type == 'upload'" :key="index" prop="name" :label="item.label + '：'">
            <div class="upload-input">
              <el-input v-model="manageForm.name" readonly disabled placeholder="请选择文件" />
              <el-button type="success" @click.native="chooseFile" style="margin-top: 10px;">选择文件</el-button>
              
              <input
                ref="fileRef"
                id="uploadFile"
                type="file"
                style="display: none" 
                :accept="Number(manageForm.docType) - 1 >= 0
                  ? manageFormItems[index - 1].options[
                    Number(manageForm.docType) - 1
                  ].accept
                  : ''
                "
                @change="
                  triggerFile(
                    $event,
                    manageFormItems[index - 1].options[
                      Number(manageForm.docType) - 1
                    ].types
                  )
                "
              >
            </div>
          </el-form-item>
          <!-- <el-form-item v-else :key="index" :prop="item.field" :label="item.label">
            <el-input v-model="manageForm[item.field]" :type="item.type" :rows="3" :placeholder="'请输入' + item.label" />
          </el-form-item> -->
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="manageDialogVisible = false">取消</el-button>
        <el-button v-if="manageDialogTitle == '添加方案'" type="primary" @click="toAdd">保存</el-button>
        <el-button v-if="manageDialogTitle == '修改方案'" type="primary" @click="toEdit">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getWqProgramme, deleteWqProgramme, insertWqProgramme, uploadFile, previewFile, updateWqProgramme } from '@/api/wq/programme'

import { handleDownload, removeFileSuffix } from '@/utils/file'
import { commonFileDownload } from '@/api/common'

import _ from 'loadsh'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'WqProgramme',
  components: {
    FloatingSideMenu
  },
  data() {
    const checkProgrammeName = (rule, value, callback) => {
      if(value) {
        if(value.indexOf('.') >= 0) {
          callback(new Error('方案名称不能包含文件后缀名'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      dataList: [],
      pagination: {
        pageNo: 1,
        pageSize: 10
      },
      total: 0,
      form: {
        name: "",
        type: "",
        time: [],
        level: "",
        beginTime: "",
        endTime: "",
      },
      pageInfo: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      typeList: [
        {
          code: 'SOURCE_QUALITY_EMERGENCY_PLAN',
          name: '水源水质应急预案'
        },
        {
          code: 'DROUGHT_EMERGENCY_PLAN',
          name: '旱情应急预案'
        },
        {
          code: 'INSM_FAILURE_EMERGENCY_PLAN',
          name: '设备故障应预案'
        },
        {
          code: 'WATER_SUPPLY_QUALITY_EMERGENCY_PLAN',
          name: '供水水质应急预案'
        },
      ],
      levelList: [
        {
          value: 1,
          label: "一级"
        },
        {
          value: 2,
          label: "二级"
        },
        {
          value: 3,
          label: "三级"
        },
      ],
      manageDialogVisible: false,
      manageDialogTitle: "",
      manageForm: {
        name: "",
        type: "",
        docType: "",
        level: "",
        url: "",
        // remark: "",
        name: "",
      },
      manageFormRules: {
        name: [
          { required: true, message: "请选择文件", trigger: "blur" },
          { validator: checkProgrammeName, trigger: ["blur", "change"] }
        ],
        docType: [
          {
            required: true,
            message: "请选择方案类型",
            trigger: ["blur", "change"],
          },
        ],
        type: [
          {
            required: true,
            message: "请选择问题类型",
            trigger: ["blur", "change"],
          },
        ],
        level: [
          {
            required: true,
            message: "请选择方案等级",
            trigger: ["blur", "change"],
          },
        ],
        url: [{ required: true, message: '请选择文件', trigger: "blur" }],
      },
      manageFormItems: [
        // {
        //   label: "选择方案类型",
        //   field: "type",
        //   type: "select",
        //   options: [
        //     {
        //       code: 1,
        //       name: "水源水质应急预案"
        //     },
        //     {
        //       code: 2,
        //       name: "旱情应急预案"
        //     },
        //     {
        //       code: 3,
        //       name: "设备故障应预案"
        //     },
        //     {
        //       code: 4,
        //       name: "供水水质应急预案"
        //     },
        //   ],
        // },
        {
          label: "方案等级",
          field: "level",
          type: "select",
          options: [
            {
              id: 1,
              name: "一级"
            },
            {
              id: 2,
              name: "二级"
            },
            {
              id: 3,
              name: "三级"
            },
          ],
        },
        {
          label: "文档类型",
          field: "docType",
          type: "select",
          options: [
            {
              name: "PDF",
              id: "1",
              accept: ".pdf",
              types: ["application/pdf"],
            },
            {
              name: "DOC",
              id: "2",
              accept: ".doc",
              types: [
                "application/msword",
                // "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              ],
            },
            {
              name: "DOCX",
              id: "3",
              accept: ".docx",
              types: [
                // "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              ],
            },
          ],
        },
        {
          label: "上传方案",
          field: "name",
          type: "upload",
        },
      ],
      file: [],
      fileTypes: [
        {
          name: ".pdf",
          type: "application/pdf",
        },
        {
          name: ".doc",
          type: "application/msword",
        },
        {
          name: ".docx",
          type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        },
        {
          name: ".xls",
          type: "application/vnd.ms-excel",
        },
        {
          name: ".xlsx",
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        },
        {
          name: ".ppt",
          type: "application/vnd.ms-powerpoint",
        },
        {
          name: ".pptx",
          type: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        },
      ],
      previewDialogVisible: false,
      pdfUrl: "",
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    handleDelete(id) {
      deleteWqProgramme(id).then(res => {
        if (res.status == 200) {
          this.$message({
            type: 'success',
            message: '删除成功'
          })
          this.getData()
        }
      })
    },
    handleEdit(row) {
      this.manageDialogTitle = "修改方案";
      this.manageDialogVisible = true;
      console.log(row)
      
      this.$nextTick(() => {
        this.manageForm = _.cloneDeep(row);
      })
    },
    // 下载方案
    handleDownloadReport(row) {
      commonFileDownload(row.url).then(res => {
        let name = ''
        if(row.docType == 1) {
          name = row.name + '.pdf'
        } else {
          name = row.name + '.docx'
        }
        handleDownload(name, res)
      })
    },
    // 确认编辑
    toEdit() {
      this.$refs["manageForm"].validate((valid) => {
        if (valid) {
          updateWqProgramme(this.manageForm).then(() => {
            this.$message({
              message: "修改方案成功",
              type: "success",
            });
            this.closeManageDialog();
            this.getData();
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    async handlePreviewFile(row) {
      const loading = this.$loading({
        lock: true,
        text: "加载中..."
      });
      await previewFile(row.id).then((res) => {
        if (row.type == "PPT") {
          const blob = new Blob([res], { type: "image/jpeg" });
          this.pdfUrl = window.URL.createObjectURL(blob);
        } else {
          const blob = new Blob([res], { type: "application/pdf" });
          this.pdfUrl = window.URL.createObjectURL(blob);
        }
        window.open(this.pdfUrl)

      });
      loading.close();

    },
    levelFormat(row) {
      if (row.level === 1) {
        return "一级"
      } else if (row.level === 2) {
        return "二级"
      } else if (row.level === 3) {
        return "三级"
      }
    },
    typeFormat(row) {
      let typeMap = {
        'SOURCE_QUALITY_EMERGENCY_PLAN': '水源水质应急预案',
        'DROUGHT_EMERGENCY_PLAN': '旱情应急预案',
        'INSM_FAILURE_EMERGENCY_PLAN': '设备故障应预案',
        'WATER_SUPPLY_QUALITY_EMERGENCY_PLAN': '供水水质应急预案'
      }
      return typeMap[row.type];
    },
    // 新增按钮
    handleAdd() {
      this.manageDialogTitle = "添加方案";
      this.manageDialogVisible = true;
      this.$nextTick(() => {
        this.manageForm = {
          name: "",
          type: "",
          docType: "",
          level: "",
          url: "",
          // remark: "",
          name: "",
        };
      });
    },
    toAdd() {
      this.$refs["manageForm"].validate((valid) => {
        if (valid) {
          insertWqProgramme(this.manageForm).then(() => {
            this.$message({
              message: "新增方案成功",
              type: "success",
            });
            this.closeManageDialog();
            // this.reSearch();
            this.getData()
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 分页查询
    getData() {
      this.form.beginTime = ''
      this.form.endTime = ''
      let temp = this.form.time || []
      if (temp.length) {
        console.log(temp)
        this.form.beginTime = temp[0]
        this.form.endTime = temp[1]
      }
      
      const data = {
        ...this.form,
        ...this.pagination
      }
      getWqProgramme(data).then(res => {
        const { data, count } = res
        this.dataList = data
        this.pageInfo.total = res.count
      })
    },
    closeManageDialog() {
      this.$refs.manageForm.resetFields();
      this.manageDialogVisible = false;
    },
    // 选择文件按钮
    chooseFile() {
      if (this.manageForm.docType) {
        const clickDiv = document.getElementById("uploadFile");
        clickDiv.click();
      } else {
        this.$message({
          message: "请先选择方案类型",
          type: "warning",
        });
      }
    },
    // 打开选择文件窗口选择文件
    triggerFile(e, types) {
      this.file = e.target.files;
      const type = this.file[0].type;
      if (_.indexOf(types, type) >= 0) {
        this.toUploadFile();
      } else {
        this.$message({
          message: "方案类型不符合，请重新选择",
          type: "error",
        });
      }
      document.getElementById('uploadFile').value = null
    },
    handleSizeChange(currentSize) {
      this.pageInfo.pageNum = 1
      this.getList()
    },
    handleCurrentChange(currentPage) {
      this.getList()
    },
    // 上传文件
    async toUploadFile() {
      const file = new FormData();
      file.append("uploadFile", this.file[0]);
      console.log(file);
      await uploadFile(file).then((res) => {
        this.manageForm.name = removeFileSuffix(res.data.annexName);
        this.manageForm.url = res.data.annexPath;
        this.$message({
          message: "上传成功",
          type: "success",
        });
      });
    },
    // 重置查询
    reSearch() {
      this.form.name = "";
      this.form.type = "";
      this.form.docType = "";
      this.form.level = ''
      this.form.time = []
      this.form.beginTime = ''
      this.form.endTime = ''
      this.getData();
    },
  },
}
</script>

<style lang="scss">
.programme {
  padding: 30px 30px 0;
  height: calc(100vh - 84px);

  .table-container {
    height: calc(100% - 76px);
    color: #ffffff;
    padding: 20px;
    border: 1px solid #3084B5;
  }
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
.pagination-container {
    height: 50px;
}
</style>

<style scoped></style>