<template>
  <div class="config">
    <div class="query">
      <el-form :inline="true" :model="queryForm">
        <el-form-item label="站点：">
          <el-cascader v-model="queryForm.sourceType" :options="stationList" :props="propObject" size="medium" clearable>
          </el-cascader>
        </el-form-item>
        <el-form-item label="预警状态：">
          <el-select v-model="queryForm.isFlag" placeholder="" size="medium">
            <el-option
              v-for="option in statusList"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="medium" @click="getConfigList">查询</el-button>
          <el-button type="primary" icon="el-icon-refresh"
            style="background: #91A4BA !important;border-color: #91A4BA !important;"
            size="medium"
            @click="reSearch">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- <el-button type="primary" icon="el-icon-plus" size="medium" style="height: 36px;" @click="handelAdd">新增</el-button> -->
    </div>

    <el-table height="calc(100% - 108px)" :data="configTableData" size="medium">
      <el-table-column prop="name" label="站点" ></el-table-column>
      <el-table-column prop="sourceTypeName" label="站点类别"></el-table-column>
      <el-table-column prop="" label="温度">
        <template slot-scope="scope">
          <span>{{ scope.row.tempMin + ' ~ ' + scope.row.tempMax }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="ph">
        <template slot-scope="scope">
          <span>{{ scope.row.phMin + ' ~ ' + scope.row.phMax }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="电导率">
        <template slot-scope="scope">
          <span>{{ scope.row.powerRateMin + ' ~ ' + scope.row.powerRateMax }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="浊度">
        <template slot-scope="scope">
          <span>{{ scope.row.turbidityMin + ' ~ ' + scope.row.turbidityMax }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="余氯">
        <template slot-scope="scope">
          <span>{{ scope.row.chlorineMin + ' ~ ' + scope.row.chlorineMax }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100px" align="center">
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="bottom">
            <i class="el-icon-edit actionIcon" style="color: #409eff;font-size: 19px;" @click="handleEdit(scope.row.id)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.pageNum"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageInfo.pageSize"
        layout="total, sizes, ->, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      title="编辑预警配置"
      width="800px"
    >
      <el-form
        :model="configFrom"
        ref="configFromRef"
        :rules="rules"
        label-width="80px">

        <el-form-item label="温度：">
          <el-col :span="6">
            <el-form-item prop="tempMin">
              <el-input v-model="configFrom.tempMin" style="width: 100%;" placeholder="温度下限"></el-input>
            </el-form-item>            
          </el-col>
          <el-col class="line" :span="1">~</el-col>
          <el-col :span="6">
            <el-form-item prop="tempMax">
              <el-input v-model="configFrom.tempMax" style="width: 100%;" placeholder="温度上限"></el-input>
            </el-form-item> 
          </el-col>
          <el-col :span="6" :offset="1">
            <el-form-item prop="tempRate">
              <el-input v-model="configFrom.tempRate" style="width: 100%;" placeholder="温度变化速率"></el-input>
            </el-form-item> 
          </el-col>
        </el-form-item>

        <el-form-item label="ph：">
          <el-col :span="6">
            <el-form-item prop="phMin">
              <el-input v-model="configFrom.phMin" style="width: 100%;" placeholder="ph下限"></el-input>
            </el-form-item>            
          </el-col>
          <el-col class="line" :span="1">~</el-col>
          <el-col :span="6">
            <el-form-item prop="phMax">
              <el-input v-model="configFrom.phMax" style="width: 100%;" placeholder="ph上限"></el-input>
            </el-form-item> 
          </el-col>
          <el-col :span="6" :offset="1">
            <el-form-item prop="phRate">
              <el-input v-model="configFrom.phRate" style="width: 100%;" placeholder="ph变化速率"></el-input>
            </el-form-item> 
          </el-col>
        </el-form-item>

        <el-form-item label="电导率：">
          <el-col :span="6">
            <el-form-item prop="powerRateMin">
              <el-input v-model="configFrom.powerRateMin" style="width: 100%;" placeholder="电导率下限"></el-input>
            </el-form-item>            
          </el-col>
          <el-col class="line" :span="1">~</el-col>
          <el-col :span="6">
            <el-form-item prop="powerRateMax">
              <el-input v-model="configFrom.powerRateMax" style="width: 100%;" placeholder="电导率上限"></el-input>
            </el-form-item> 
          </el-col>
          <el-col :span="6" :offset="1">
            <el-form-item prop="powerRateRate">
              <el-input v-model="configFrom.powerRateRate" style="width: 100%;" placeholder="电导率变化速率"></el-input>
            </el-form-item> 
          </el-col>
        </el-form-item>

        <el-form-item label="浊度：">
          <el-col :span="6">
            <el-form-item prop="turbidityMin">
              <el-input v-model="configFrom.turbidityMin" style="width: 100%;" placeholder="浊度下限"></el-input>
            </el-form-item>            
          </el-col>
          <el-col class="line" :span="1">~</el-col>
          <el-col :span="6">
            <el-form-item prop="turbidityMax">
              <el-input v-model="configFrom.turbidityMax" style="width: 100%;" placeholder="浊度上限"></el-input>
            </el-form-item> 
          </el-col>
          <el-col :span="6" :offset="1">
            <el-form-item prop="turbidityRate">
              <el-input v-model="configFrom.turbidityRate" style="width: 100%;" placeholder="浊度变化速率"></el-input>
            </el-form-item> 
          </el-col>
        </el-form-item>

        <el-form-item label="余氯：">
          <el-col :span="6">
            <el-form-item prop="chlorineMin">
              <el-input v-model="configFrom.chlorineMin" style="width: 100%;" placeholder="余氯下限"></el-input>
            </el-form-item>            
          </el-col>
          <el-col class="line" :span="1">~</el-col>
          <el-col :span="6">
            <el-form-item prop="chlorineMax">
              <el-input v-model="configFrom.chlorineMax" style="width: 100%;" placeholder="余氯上限"></el-input>
            </el-form-item> 
          </el-col>
          <el-col :span="6" :offset="1">
            <el-form-item prop="chlorineRate">
              <el-input v-model="configFrom.chlorineRate" style="width: 100%;" placeholder="余氯变化速率"></el-input>
            </el-form-item> 
          </el-col>
        </el-form-item>
      </el-form>
      <div class="actionBox">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWqConfig, getWqConfigById,updateWqConfig } from '@/api/wq/warn'

import { parseTime } from '@/utils'

export default {
  name: 'Config',
  props: {
    stationList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    let validNumber = (rule, value, callback) => {
      let reg = /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g;
      if (value === '') {
        // callback(new Error('请输入内容'));
        callback();
      } else if (!reg.test(value)) {
        callback(new Error('请输入数字'));
      } else {
        callback();
      }
    }
    return {
      queryForm: {
        warnType: 'WATER_QUALITY',
        dataTime: "",
        sourceType: "",
        isFlag: "",
        timeRange: []
      },
      statusList: [
        {
          label: '开启',
          value: 1
        },
        {
          label: '关闭',
          value: 0
        }
      ],
      propObject: {
        children: 'list',
        label: 'name',
        value: 'id'
      },
      pageInfo: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      configTableData: [],
      // 表单
      dialogVisible: false,
      isUpdate: false,
      configFrom: {},
      rules: {
        tempMin: [
          { validator: validNumber, trigger: "blur" }
        ],
        tempMax: [
          { validator: validNumber, trigger: "blur" }
        ],
        tempRate: [
           { validator: validNumber, trigger: "blur" }
        ],
        phMin: [
          { validator: validNumber, trigger: "blur" }
        ],
        phMax: [
          { validator: validNumber, trigger: "blur" }
        ],
        phRate: [
           { validator: validNumber, trigger: "blur" }
        ],
        powerRateMin: [
          { validator: validNumber, trigger: "blur" }
        ],
        powerRateRate: [
          { validator: validNumber, trigger: "blur" }
        ],
        turbidityMin: [
           { validator: validNumber, trigger: "blur" }
        ],
        turbidityMax: [
          { validator: validNumber, trigger: "blur" }
        ],
        tempMax: [
          { validator: validNumber, trigger: "blur" }
        ],
        turbidityRate: [
           { validator: validNumber, trigger: "blur" }
        ],
        chlorineMin: [
          { validator: validNumber, trigger: "blur" }
        ],
        chlorineMax: [
          { validator: validNumber, trigger: "blur" }
        ],
        chlorineRate: [
           { validator: validNumber, trigger: "blur" }
        ]
      },
      commonRules: [
        { validator: validNumber, trigger: "blur" }
      ]
    }
  },
  mounted() {
    this.initData()
    this.getConfigList()
  },
  methods: {
    initData() {
      let now = new Date().getTime()
      let beginTs = now - 60 * 60 * 1000 * 24 * 7
      let endTime = parseTime(now)
      let beginTime = parseTime(beginTs)
      this.queryForm.timeRange.push(beginTime)
      this.queryForm.timeRange.push(endTime)
    },
    // 分页
    getConfigList() {
      let facilityId = this.queryForm.sourceType.length > 0 ? this.queryForm.sourceType[1] : null
      let sourceType = this.queryForm.sourceType.length > 0 ? this.queryForm.sourceType[0] : null
      let data = {
        facilityId,
        sourceType,
        isFlag: this.queryForm.isFlag,
      }
      getWqConfig({
        ...data,
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize
      }).then(res => {
        if (res.status === 200) {
          this.pageInfo.total = res.count
          this.configTableData = res.data
        }
      })
    },
    reSearch() {
      this.queryForm.warnType = null
      this.queryForm.dataTime = ''
      this.queryForm.sourceType = null
      this.queryForm.isFlag = ''
      this.queryForm.timeRange = []
      this.pageInfo.pageNum = 1
      this.getConfigList()
    },
    // 新增按钮
    handelAdd() {
      this.isUpdate = false
      this.dialogVisible = true
    },
    handleEdit(id) {
      console.log(id)
      getWqConfigById(id).then(res => {
        if(res.status === 200) {
          this.configFrom = res.data
          this.dialogVisible = true
        }
      })
    },
    handleSubmit() {
      this.$refs.configFromRef.validate((valid) => {
        if (valid) {
          updateWqConfig(this.configFrom).then(res => {
            if(res.status === 200) {
              this.$message.success('编辑成功')
              this.getConfigList()
              this.dialogVisible = false
            }
          })
        }
      })
    },
    handleSizeChange() {
      this.pageInfo.pageNum = 1
      this.getConfigList()
    },
    handleCurrentChange() {
      this.getConfigList()
    }
  }
}
</script>

<style lang="scss" scoped>
.config {
  height: 100%;
  .query {
    display: flex;
    justify-content: space-between;
  }
  .pagination-container {
    height: 50px;
  }
  .line {
    text-align: center;
    color: #ffffff;
  }
  .actionBox {
    display: flex;
    justify-content: center;
    padding-bottom: 30px;
  }
}
</style>