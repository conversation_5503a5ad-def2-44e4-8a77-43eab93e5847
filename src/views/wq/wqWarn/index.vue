<template>
  <div class="WqMonitor">
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="wq" />
    <div class="tab">
      <div class="first" :class="{ active: currentType === 'detail' }" @click="handleTabClick('detail')">预警详情</div>
      <div class="other" :class="{ active: currentType === 'config' }" @click="handleTabClick('config')">预警配置</div>
      <div class="other" :class="{ active: currentType === 'trend' }" @click="handleTabClick('trend')">预警趋势</div>
    </div>
    <div class="table-container">
      <template v-if="currentType === 'detail'">
        <div class="query">
          <el-form :inline="true" :model="queryForm">
            <el-row>
              <el-form-item label="站点：">
                <el-cascader v-model="queryForm.sourceType" :options="stationList" :props="propObject" size="medium" clearable>
                </el-cascader>
              </el-form-item>
              <!-- <el-form-item label="预警类别：">
                <el-select v-model="queryForm.warnType" placeholder="" size="medium">
                  <el-option
                    v-for="option in typeList"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item> -->
              <el-form-item label="时间：">
                <el-date-picker
                  v-model="queryForm.timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"                  
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="medium"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="medium" @click="handleDetailQuery">查询</el-button>
                <el-button type="primary" icon="el-icon-refresh"
                  style="background: #91A4BA !important;border-color: #91A4BA !important;"
                  size="medium"
                  @click="reSearch">重置</el-button>
              </el-form-item>
            </el-row>
          </el-form>
        </div>
        
        <div class="pieBox">
          <CountPie class="pieItem" :seriesData="statusCount" chartTitle="预警状态统计" />
          <CountPie class="pieItem" :seriesData="sourceCount" chartTitle="预警来源统计" />
        </div>

        <div class="warnTable">
        <el-table height="100%" :data="detailData" size="medium">
          <el-table-column prop="name" label="预警站点" width="200"></el-table-column>
          <el-table-column prop="dataTime" label="预警时间" width="300"></el-table-column>
          <!-- <el-table-column prop="alarmGrade" label="预警等级" :formatter="alarmGradeFormat">
            <template slot-scope="scope">
              {{ alarmGradeFormat(scope.row) }}
            </template>
          </el-table-column> -->
          <el-table-column prop="value" label="监测数据"></el-table-column>
          <!-- <el-table-column prop="warnType" label="预警类别" :formatter="warnTypeFormat"></el-table-column> -->
          <el-table-column prop="suggestion" label="解决方案"></el-table-column>
          <el-table-column prop="msg" label="预警描述"></el-table-column>
          <el-table-column prop="status" label="预警状态" :formatter="statusFormat" width="120"></el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-tooltip content="关闭预警" placement="bottom">
                <i class="el-icon-edit actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;" @click="closeWarn(scope.row)" v-if="scope.row.status === 1" />
              </el-tooltip>
              <el-tooltip content="生成工单" placement="bottom">
                <i class="el-icon-document-add actionIcon mr-4" style="color: #409eff;font-size: 19px;margin-right: 15px;" v-if="!scope.row.ticketId" @click="handleCreateOrder(scope.row.id, scope.row.ticketId)" />
              </el-tooltip>
              <el-tooltip content="删除" placement="bottom">
                <i class="el-icon-delete actionIcon" style="color: #f56c6c;font-size: 19px;margin-right: 15px;" @click="handleDelete(scope.row.id)" />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        </div>

        <div class="pagination-container">
          <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page.sync="pageInfo.pageNum" :page-sizes="[10, 20, 30, 40]" :page-size.sync="pageInfo.pageSize"
            layout="total, sizes, ->, prev, pager, next, jumper" :total="pageInfo.total">
          </el-pagination>
        </div>
      </template>

      <!-- 配置 -->
      <WarnConfig v-if="currentType === 'config'" :stationList="stationList" />

      <!-- 趋势 -->
      <div v-if="currentType === 'trend'">
        <div class="query">
          <el-form :inline="true" :model="queryForm">
            <el-row>
              <el-form-item label="站点：">
                <el-cascader v-model="queryForm.sourceType" @change="getCheckedNodes" :options="stationList" :props="propObject" size="medium" clearable>
                </el-cascader>  
              </el-form-item>
              <el-form-item label="预警时间：">
                <el-date-picker
                  v-model="queryForm.timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="medium"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="medium" @click="queryTrend(queryForm)">查询</el-button>
                <el-button type="primary" icon="el-icon-refresh"
                  style="background: #91A4BA !important;border-color: #91A4BA !important;"
                  size="medium"
                  @click="reSearch">重置</el-button>
              </el-form-item>
            </el-row>
          </el-form>
        </div>

        <div v-if="currentType === 'trend'" ref="test_ref" class="chart" style="position: relative;"></div>
        
      </div>
    </div>
  </div>
</template>

<script>
import { getStationList } from '@/api/common'
import { getWqWarn, getWqRealtimeDataPage, deleteWqWarn, warningTrend, updateWqWarn, getWqWarnCountWithStatus, getWqWarnCountWithSource } from '@/api/wq/warn'
import { createOrderByWarnId } from '@/api/order'

import { parseTime } from '@/utils'
import WarnConfig from './config'
import ConfigComponent from './ConfigComponent.vue'
import CountPie from '../../dispatch/warn/countPie'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: 'WqMonitor',
  components: {
    WarnConfig,
    ConfigComponent,
    CountPie,
    FloatingSideMenu
  },
  data() {
    return {
      statusCount: [
        { name: '待指派', value: null, key: 'WAIT_DISTRIBUTED' },
        { name: '处理中', value: null, key: 'PROCESSING' },
        { name: '已处理', value: null, key: 'DONE' }
      ],
      sourceCount: [
        { name: '水源水', value: null, key: 'WATER_SOURCE' },
        { name: '水厂水', value: null, key: 'WATER_FACTORY' },
        { name: '末梢水', value: null, key: 'END_USER' }
      ],
      queryForm: {
        warnType: 'WATER_QUALITY',
        dataTime: "",
        sourceType: [],
        timeRange: []
      },
      typeList: [
        {
          value: 'WATER_QUALITY',
          label: "水质预警"
        },
        {
          value: 'EQUIPMENT',
          label: "设备预警"
        },
      ],
      currentType: 'detail',
      stationList: [],
      propObject: {
        checkStrictly: true,
        children: 'list',
        label: 'name',
        value: 'id'
      },

      stationName: [],
      detailData: [],
      pageInfo: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      echartsData: {
        xData: [],
        yData1: [],
        yData2: [],
      },
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    if (this.currentType === 'detail') {
      this.handleDetailQuery()
    } else if (this.currentType === 'config') {
    } else if (this.currentType === 'trend') {
      // this.getTrendList(this.queryForm)
    }
  },
  methods: {
    alarmGradeFormat(row) {
      return row.alarmGrade == "1" && "Ⅰ级预警" || row.alarmGrade == "2" && "Ⅱ级预警" || row.alarmGrade == "3" && "Ⅲ级预警"
    },
    warnTypeFormat(row) {
      const labelMap = {
        'UN_KNOWN': '未知',
        'WATER_QUALITY': '水质预警',
        'EQUIPMENT': '设备预警',
        'WATER_LEVEL': '水位预警',
        'WATER_TRAFFIC': '流量预警',
        'WATER_PRESSURE': '压力预警'
      }
      return labelMap[row.warnType]
    },
    handleDetailQuery() {
      this.getPieData()
      this.getList()
    },
    getPieData() {
      const params = {
        begin: this.queryForm.timeRange[0],
        end: this.queryForm.timeRange[1],
        type: 'WATER_QUALITY'
      }
      getWqWarnCountWithStatus(params).then(res => {
        const list = res.data || []
        this.statusCount.forEach(item => {
          const matchedItem = list.find(innerItem => innerItem.key === item.key)
          if (matchedItem) item.value = matchedItem.count
        })
      })
      getWqWarnCountWithSource(params).then(res => {
        const list = res.data || []
        this.sourceCount.forEach(item => {
          const matchedItem = list.find(innerItem => innerItem.key === item.key)
          if (matchedItem) item.value = matchedItem.count
        })
      })
    },
    // 绘制
    drawChart() {
      // 基于准备好的dom，初始化echarts实例
      this.chartInstance = this.$echarts.init(this.$refs.test_ref);
      // echarts的全局自适应函数调用
      this.$echartsResize(this.chartInstance);
      this.chartInstance.setOption(
        {
          tooltip: {
            show: true,    // 是否显示提示框组件
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            } 
          }, 
          xAxis: [
            {
              name: "时间",
              type: "category",
              data: this.echartsData.xData,
              axisTick: {
                  show: false
              },
              axisLabel: {
                // 轴文字
                color: "#fff",
                fontSize: 16,
              },
              axisLine: {
                show: true,
                onZero: false,
                symbol: ["none", "arrow"],
                symbolSize: [8, 12],
                symbolOffset: 12,
                lineStyle: {
                  color: "rgba(172, 198, 234, 1)",
                  width: 1,
                },
              },
            },
          ],
          yAxis: [
            {
              name: "数量",
              type: "value",
              axisLabel: {
                // 轴文字
                color: "#fff",
                fontSize: 16,
              },
              axisLine: {
                show: true,
                onZero: true,
                symbol: ["none", "arrow"],
                symbolSize: [8, 12],
                symbolOffset: 12,
                lineStyle: {
                  color: "rgba(172, 198, 234, 1)",
                  width: 1,
                },
              },
            },
          ],
          series: [
            {
              name: "已处理",
              type: "bar", //形状为柱状图
              barWidth: 55,
              data: this.echartsData.yData1,
              color: {
                type: 'linear',
                x: 0, //右
                y: 0, //下
                x2: 0, //左
                y2: 1, //上
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0, 204, 255, 1)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(67, 85, 222, 0.35)' // 100% 处的颜色
                  }
                ]
              }
            },
            {
              name: "总数",
              type: "bar", //形状为柱状图
              barWidth: 55,
              data: this.echartsData.yData2,
              color: {
                type: 'linear',
                x: 0, //右
                y: 0, //下
                x2: 0, //左
                y2: 1, //上
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0, 204, 255, 1)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(67, 85, 222, 0.35)' // 100% 处的颜色
                  }
                ]
              }
            },
          ],
        },
        true
      );
    },
    statusFormat(row) {
      const statusMap = {
        'WAIT_DISTRIBUTED': '待指派',
        'PROCESSING': '处理中',
        'DONE': '已处理'
      }
      return statusMap[row.status] || '未知'
    },
    closeWarn(row) {
      this.$confirm('此操作将关闭该预警, 是否继续?', '提示',{
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = row
        data.status = 2
        updateWqWarn(data).then(res => {
          if(res.status == 200) {
            this.$message({
              type:'success',
              message: '关闭成功'
            })
          }
        })
      })
    },
    handleCreateOrder(warnId, ticketId) {
      this.$prompt('可填写工单名称', '生成工单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S/,
        inputErrorMessage: '工单名称不能为空'
      }).then(({ value }) => {
        let data = {
          recordId: warnId
        }
        if(value) {
          data.name = value.trim()
        }
        if(!ticketId) {
          createOrderByWarnId(data).then(res => {
            if(res.status === 200) {
              this.$message.success('生成工单成功')
            } else {
              this.$message.error('生成工单失败')
            }
          })
        } else {
          this.$message.warning('当前预警已生成工单')
        }
        this.getList()
        
      }).catch(() => {
        console.log('cancle')
      })
    },
    handleTabClick(type) {
      this.currentType = type
      if (this.currentType === 'detail') {
        this.queryForm.sourceType = ""
        this.getList()
      } else if (this.currentType === 'config') {
        this.queryForm.sourceType = ""
      } else if (this.currentType === 'trend') {
        // 设置默认站点
        // this.queryForm.sourceType = ""
        this.queryForm.sourceType = [this.stationList[0].sourceType, this.stationList[0].list[0].id]
        this.getTrendList(this.queryForm)
      }
    },
    initData() {
      // 站点列表
      getStationList('WATER_QUALITY').then(res => {
        if (res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName: item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list,
            }
          })
          this.stationList[0].name = '出厂水'
          this.stationList[1].name = '水源水'
          this.stationList[2].name = '末梢水'
        }
      })
      let now = new Date().getTime()
      let beginTs = now - 60 * 60 * 1000 * 24 * 7
      let endTime = parseTime(now)
      let beginTime = parseTime(beginTs)
      this.queryForm.timeRange.push(beginTime)
      this.queryForm.timeRange.push(endTime)
    },
    // 查询水质预警分页数据
    getList() {
      let beginTime = this.queryForm.timeRange[0]
      let endTime = this.queryForm.timeRange[1]
      let facilityId = this.queryForm.sourceType.length > 0 ? this.queryForm.sourceType[1] : null
      let sourceType = this.queryForm.sourceType.length > 0 ? this.queryForm.sourceType[0] : null
      let data = {
        facilityId,
        sourceType,
        warnType: this.queryForm.warnType,
        beginTime,
        endTime,
      }
      getWqWarn({
        ...data,
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize,
      }).then(res => {
        if (res.status === 200) {
          this.pageInfo.total = res.count
          this.detailData = res.data
        }
      })
    },
    // 预警趋势查询按钮
    queryTrend(data) {
      if(this.queryForm.sourceType.length === 0) {
        this.$message.warning('请选择站点')
        return
      }
      this.getTrendList(this.queryForm)
    },
    // 查询预警趋势
    getTrendList(data) {
      let beginTime = data.timeRange[0]
      let endTime = data.timeRange[1]
      let queryData = {
        beginTime,
        endTime,
        sourceType: this.queryForm.sourceType[0],
        id: this.queryForm.sourceType[1]
      }
      warningTrend(queryData).then(res => {
        if (res.status == 200) {
          let data = res.data
          this.echartsData.xData = Object.keys(data)
          let dateArray = Object.keys(data)
          let innerKeysArray = dateArray.map(item => Object.keys(data[item])[0])
          let valuesArray = dateArray.map(item => Object.values(data[item])[0])
          this.echartsData.yData1 = innerKeysArray
          this.echartsData.yData2 = valuesArray
          this.drawChart()
        }
      })
    },
    // 删除水质预警
    handleDelete(id) {
      this.$confirm('此操作将永久删除该数据，是否继续？', '删除', {
        type: 'error'
      }).then(() => {
        deleteWqWarn(id).then(res => {
          if (res.status === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        })
      }).catch(() => { })
    },
    getCheckedNodes(node) {
      console.log(node);
    },
    // 水质预警查询重置
    reSearch() {
      this.queryForm.dataTime = "";
      this.queryForm.sourceType = [];
      let now = new Date().getTime()
      let beginTs = now - 60 * 60 * 1000 * 24 * 7
      let endTime = parseTime(now)
      let beginTime = parseTime(beginTs)
      this.queryForm.timeRange.push(beginTime)
      this.queryForm.timeRange.push(endTime)
      this.pageInfo.pageNum = 1;
      if (this.currentType === 'detail') {
        this.handleDetailQuery()
      }
    },
    // 水质预警分页
    handleSizeChange(currentSize) {
      this.pageInfo.pageNum = 1
      this.getList()
    },
    handleCurrentChange(currentPage) {
      this.getList()
    }
  }
}
</script>

<style lang="scss">
.WqMonitor {
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }

  .el-range-separator {
    color: #ffffff;
  }

  .nameColumn {
    width: 150px;
  }
}
</style>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 600px;
  margin-bottom: 20px;
}
.WqMonitor {
  padding: 0 30px;
  height: calc(100vh - 84px);
  .nameColumn {
    width: 200px;
  }

  .tab {
    color: #ffffff;
    display: flex;

    div {
      width: 136px;
      height: 56px;
      opacity: 0.6;
      text-align: center;
      padding-top: 26px;
      cursor: pointer;
      // background-color: (180deg, #FFFFFF 0%, #67C6FF 100%);
    }

    .active {
      opacity: 1;
    }

    .first {
      background-image: url('~@/assets/img/wq/first-tab.png');
      background-size: 100%;
    }

    .other {
      background-image: url('~@/assets/img/wq/other-tab.png');
      background-size: 100%;
    }
  }

  .pieBox {
    height: 240px;
    display: flex;
    justify-content: space-around;
    .pieItem {
      width: 25% !important;
    }
  }

  .table-container {
    height: calc(100% - 76px);
    color: #ffffff;
    padding: 20px;
    border: 1px solid #3084B5;

    .query {
      // padding-bottom: 20px;

      .param {
        margin-right: 20px;
      }
    }

    .pagination-container {
      height: 50px;
    }
  }
  .warnTable {
    height: calc(100% - 338px);
    .tableHeight {
      height: calc(100% - 50px);
    }
  }
}
</style>