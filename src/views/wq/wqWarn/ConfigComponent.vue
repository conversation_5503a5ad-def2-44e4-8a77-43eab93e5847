<template>
  <div style="height: calc(100% - 106px)">
    <el-table height="calc(100% - 106px)" :data="configTableData" size="medium">
      <el-table-column prop="name" label="站点名称" ></el-table-column>
      <el-table-column prop="sourceTypeName" label="站点类别"></el-table-column>
      <el-table-column prop="ammoniaMax" label="余氯上限"></el-table-column>
      <el-table-column prop="ammoniaMin" label="余氯下限"></el-table-column>
      <el-table-column prop="o2Max" label="溶解氧上限"></el-table-column>
      <el-table-column prop="o2Min" label="溶解氧下限"></el-table-column>
      <el-table-column prop="permanganateMax" label="高猛酸盐上限"></el-table-column>
      <el-table-column prop="permanganateMin" label="高猛酸盐下限"></el-table-column>
      <el-table-column prop="phMax" label="ph上限"></el-table-column>
      <el-table-column prop="phMin" label="ph下限"></el-table-column>
      <el-table-column prop="powerRateMax" label="电导率上限"></el-table-column>
      <el-table-column prop="powerRateMin" label="电导率下限"></el-table-column>
      <el-table-column prop="tempMax" label="温度上限"></el-table-column>
      <el-table-column prop="tempMin" label="温度下限"></el-table-column>
      <el-table-column prop="turbidityMax" label="浊度上限"></el-table-column>
      <el-table-column prop="turbidityMin" label="浊度下限"></el-table-column>
      <el-table-column prop="zdMax" label="总氮上限"></el-table-column>
      <el-table-column prop="zdMin" label="总氮下限"></el-table-column>
      <el-table-column prop="zlMax" label="总磷上限"></el-table-column>
      <el-table-column prop="zlMin" label="总磷下限"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content="删除" placement="bottom">
            <i class="el-icon-delete actionIcon" style="color: #f56c6c;font-size: 19px;margin-right: 15px;" @click="handleDelete(scope.row.id)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'ConfigComponent',
  props: {
    configTableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      handleDelete(row) {
        this.$emit("handleDelete",row)
      },
    }
  }
}

</script>
<style scoped>
</style>