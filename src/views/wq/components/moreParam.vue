<template>
  <div class="MoreParam">
    <div class="query">
      <span class="param">
        <span>站点：</span>
        <el-cascader
          style="width: 300px;"
          v-model="stationName"
          :options="stationList"
          :props="propObject"
          collapse-tags
          size="medium"
          clearable>
        </el-cascader>
      </span>
      <span class="param">
        <span>参数：</span>
        <el-select v-model="key" size="medium" style="width: 200px;">
          <el-option
            v-for="item in paramList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </span>
      <span class="param">
        <span>时间：</span>
        <span style="width: 200px; display: inline-box;">
          <el-date-picker   
            :clearable="false"             
            v-model="time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            size="medium"
            >
          </el-date-picker>
        </span>
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="handleQuery">查询</el-button>
    </div>

    <div class="chart-box">
      <TimeChart :legendData="oneParamLenged" :chartData="oneParamData" />
    </div>
  </div>
</template>

<script>
import { getStationList } from '@/api/common'
import { getMoreStationSigleParam } from '@/api/wq/analyze'

import { parseTime } from '@/utils'

import TimeChart from './charts/timeChart'

export default {
  name: 'MoreParam',
  components: { TimeChart },
  data() {
    return {
      // 选中的站点
      stationName: [],
      // 站点列表，用于级联
      stationList: [],
      propObject: {
        // checkStrictly: true,
        multiple: true,
        children: 'list',
        label: 'name',
        value: 'id'
      },
      // 选中的参数
      key: 2,
      // 参数列表
      paramList: [
        {
          value: 1,
          label: 'PH值'
        },
        {
          value: 4,
          label: '浊度'
        },
        {
          value: 5,
          label: '余氯'
        },
        {
          value: 0,
          label: '水温'
        },
        {
          value: 2,
          label: '电导率'
        }
      ],
      // 时间
      time: [],
      // 图例
      oneParamLenged: [],
      // 表格数据
      oneParamData: []
    }
  },
  created() {
    this.getSigleList()
  },
  mounted() {
    // await this.getSigleList()
    // this.getData()
  },
  methods: {
    getSigleList() {
      getStationList('WATER_QUALITY').then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName:  item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
          // 设置默认值
          let now = new Date().getTime()
          let beginTs = now - 60 * 60 * 1000 * 24 * 7
          let endTime = parseTime(now, '{y}-{m}-{d} {h}:{i}:{s}')
          let beginTime = parseTime(beginTs, '{y}-{m}-{d} {h}:{i}:{s}')
          // this.time.push(beginTime)
          // this.time.push(endTime)

          // this.getData()
        }
      })
    },
    // 多站单参
    getData() {
      let beginTime = this.time.length > 0 ? this.time[0] : ''
      let endTime = this.time.length > 0 ? this.time[1] : ''
      let sources = this.stationName.map(s => {
        return {
          facilityId: s[1],
          sourceType: s[0]
        }
      })
      getMoreStationSigleParam({
        key: this.key,
        sources,
        beginTime,
        endTime
      }).then(res => {
        console.log(res)

        const { status, data } = res
        if(status === 200) {
          // 处理数据
          let chartData = data.map(record => {
            return {
              name: record.name,
              type: 'line',
              data: record.arr
            }
          })
          console.log(chartData)
          this.oneParamData = chartData
          this.oneParamLenged = data.map(record => {
            return record.name
          })
        }
      })
    },
    handleQuery() {
      if(this.stationName.length > 0) {
        this.getData()
      } else {
        this.$message.warning('请选择站点')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.MoreParam {
  height: 100%;
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .chart-box {
    height: calc(100% - 56px);
  }
}
</style>