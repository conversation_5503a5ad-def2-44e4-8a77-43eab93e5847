<template>
  <div class="OneParam">
    <div class="query">
      <span class="param">
        <span>站点：</span>
        <el-cascader
          v-model="stationName"
          :options="stationList"
          :props="propObject"
          size="medium"
          clearable>
        </el-cascader>
      </span>
      <span class="param">
        <span>时间：</span>
        <span style="width: 200px; display: inline-box;">
          <el-date-picker   
            :clearable="false"             
            v-model="time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            size="medium"
            >
          </el-date-picker>
        </span>
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="getData">查询</el-button>
    </div>

    <div class="chart-box" v-loading="dataLoading" element-loading-text="查询数据中" element-loading-background="rgba(0, 0, 0, 0.8)">
      <LineChartTwoY :legendData="oneParamLenged" :xAxisData="xAxisData" :chartData="OneParamData" />
    </div>
  </div>
</template>

<script>
import { getStationList } from '@/api/common'
import { getSigleStationMoreParam } from '@/api/wq/analyze'

import { parseTime } from '@/utils'

import LineChartTwoY from './charts/lineChartTwoY'

export default {
  name: 'OneParam',
  components: { LineChartTwoY },
  data() {
    return {
      stationName: [],
      stationList: [],
      propObject: {
        // checkStrictly: true,
        children: 'list',
        label: 'name',
        value: 'id'
      },
      time: [],
      dataLoading: false,
      oneParamLenged: ['PH值', '浊度', '余氯', '水温', '电导率'],
      xAxisData: [],
      OneParamData: []
    }
  },
  created() {
    this.getSigleList()
  },
  mounted() {
    // this.getData()
  },
  methods: {
    getSigleList() {
      getStationList('WATER_QUALITY').then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName: item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
          this.stationList[0].name = '出厂水'
          this.stationList[1].name = '水源水'
          this.stationList[2].name = '末梢水'

          // 设置默认值
          this.stationName.splice(0, 0, this.stationList[0].sourceType)
          this.stationName.splice(1, 0, this.stationList[0].list[0].id)
          let now = new Date().getTime()
          let beginTs = now - 60 * 60 * 1000 * 24 * 7
          let endTime = parseTime(now)
          let beginTime = parseTime(beginTs)
          this.time.push(beginTime)
          this.time.push(endTime)

          this.getData()
        }
      })
    },
    // 单站多参
    getData() {
      let facilityId = this.stationName.length > 0 ? this.stationName[1] : null
      let sourceType = this.stationName.length > 0 ? this.stationName[0] : null
      let beginTime = this.time.length > 0 ? this.time[0] : ''
      let endTime = this.time.length > 0 ? this.time[1] : ''
      this.dataLoading = true
      getSigleStationMoreParam({
        facilityId,
        sourceType,
        beginTime,
        endTime
      }).then(res => {
        console.log(res)
        if(res.status === 200) {
          this.dataLoading = false
          // 处理数据
          let ph = []
          let turbidity = []
          let chlorine = []
          let temp = []
          let powerRate = []
          let ts = []

          for(let i = 0; i < res.data.length; i++) {
            ph.push(res.data[i]['ph'])
            turbidity.push(res.data[i]['turbidity'])
            chlorine.push(res.data[i]['chlorine'])
            temp.push(res.data[i]['temp'])
            powerRate.push(res.data[i]['powerRate'])
            ts.push(parseTime(res.data[i]['dataTime']))
          }
          this.xAxisData = ts
          this.OneParamData = [
            {
              name: 'PH值',
              type: 'line',
              symbol: 'none',
              data: ph
            },
            {
              name: '浊度',
              type: 'line',
              symbol: 'none',
              data: turbidity
            },
            {
              name: '余氯',
              type: 'line',
              symbol: 'none',
              data: chlorine
            },
            {
              name: '水温',
              type: 'line',
              symbol: 'none',
              data: temp
            },
            {
              name: '电导率',
              type: 'line',
              symbol: 'none',
              yAxisIndex: 1,
              data: powerRate
            }
          ]
        }
      }).catch(err => {
        this.dataLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.OneParam {
  height: 100%;
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .chart-box {
    height: calc(100% - 56px);
  }
}
</style>