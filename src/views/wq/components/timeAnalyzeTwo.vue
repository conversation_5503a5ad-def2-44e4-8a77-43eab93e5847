<template>
  <div class="TimeAnalyzeTwo">
    <div class="query">
      <span class="param">
        <span>站点：</span>
        <el-cascader
          v-model="stationName"
          :options="stationList"
          :props="propObject"
          collapse-tags
          size="medium"
          clearable>
        </el-cascader>
      </span>

      <span class="param">
        <span>同比时间：</span>
        <span style="width: 200px; display: inline-box;">
          <el-date-picker
            v-model="month"
            type="month"
            value-format="yyyy-MM"
            placeholder="选择月"
            :picker-options="pickerOptions"
            size="medium"
            :clearable="false"
            >
          </el-date-picker>
        </span>
      </span>
      <el-button type="primary" icon="el-icon-search" size="medium" @click="handleQuery">查询</el-button>
    </div>

    <div class="chart-box">
      <CompareChart :legendData="oneParamLenged" :xAxisData="xAxisData" :chartData="oneParamData" />
      <!-- <EmptyData v-else /> -->
    </div>
  </div>
</template>

<script>
import { getStationList } from '@/api/common'
import { getTimeCompareSigleStation } from '@/api/wq/analyze'
import dayjs from 'dayjs'

import CompareChart from './charts/compareChart'
import EmptyData from '@/components/message/emptyData'

export default {
  name: 'TimeAnalyzeTwo',
  components: { CompareChart, EmptyData },
  data() {
    return {
      // 选中的站点
      stationName: [],
      // 站点列表，级用于联
      stationList: [],
      propObject: {
        children: 'list',
        label: 'name',
        value: 'id'
      },
      month0: '',
      month: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 24 * 60 * 60 * 1000 * 30
        }
      },
      // 图例
      oneParamLenged: [],
      // 表格数据
      xAxisData: ['PH值', '浊度', '余氯', '水温', '电导率'],
      oneParamData: []
    }
  },
  created() {
    const now = dayjs()
    this.month = now.subtract(1, 'month').format('YYYY-MM')
    this.month0 = now.subtract(1, 'year').subtract(1, 'month').format('YYYY-MM')
    this.getSigleList()
  },
  mounted() {
    // await this.getSigleList()
    // this.getData()
  },
  methods: {
    getSigleList() {
      getStationList('WATER_QUALITY').then(res => {
        if(res.status === 200) {
          this.stationList = res.data.map((item, index) => {
            return {
              name: item.sourceName,
              sourceName:  item.sourceName,
              sourceType: item.sourceType,
              id: item.sourceType,
              list: item.list
            }
          })
          this.stationList[0].name = '出厂水'
          this.stationList[1].name = '水源水'
          this.stationList[2].name = '末梢水'
          
          // 设置默认值
          this.stationName.splice(0, 0, this.stationList[0].sourceType)
          this.stationName.splice(1, 0, this.stationList[0].list[0].id)
          this.getData()
        }
      })
    },
    // 环比分析
    getData() {
      // 请求参数
      console.log(this.stationName)
      let facilityId = this.stationName.length > 0 ? this.stationName[1] : ''
      let sourceType = this.stationName.length > 0 ? this.stationName[0] : ''

      let timeValue = [this.month0, this.month]

      getTimeCompareSigleStation({
        facilityId,
        sourceType,
        timeValue
      }).then(res => {
        console.log(res)

        const { status, data } = res
        if(status === 200) {
          // 处理数据
          let curr = []
          let pre = []
          this.oneParamLenged =  data.map(item => item.timeValue)
          for(let i = 0; i < data.length; i++) {
            if(data[i]['timeValue'] === this.oneParamLenged[0]) {
              curr = [data[i]['ph'], data[i]['turbidity'], data[i]['ammonia'], data[i]['temp'], data[i]['powerRate']]
            }
            if(data[i]['timeValue'] === this.oneParamLenged[1]) {
              pre = [data[i]['ph'], data[i]['turbidity'], data[i]['ammonia'], data[i]['temp'], data[i]['powerRate']]
            }
          }
          this.oneParamData = [
            {
              name: this.oneParamLenged[0],
              type: 'bar',
              data: curr
            },
            {
              name: this.oneParamLenged[1],
              type: 'bar',
              data: pre
            }
          ]
        }
      })
    },
    handleQuery() {
      if(this.stationName.length > 0) {
        this.getData()
      } else {
        this.$message.warning('请选择站点')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.TimeAnalyzeTwo {
  height: 100%;
  .query {
    padding-bottom: 20px;
    .param {
      margin-right: 20px;
    }
  }
  .chart-box {
    height: calc(100% - 56px);
  }
}
</style>