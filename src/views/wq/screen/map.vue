<template>
  <div id="wqmap"></div>
</template>

<script>
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import Ground from "@arcgis/core/Ground";
import WMSLayer from "@arcgis/core/layers/WMSLayer";
import WFSLayer from "@arcgis/core/layers/WFSLayer";
import Vue from "vue";
import Graphic from "@arcgis/core/Graphic";
export default {
  data() {
    return {
      map: null,
      view: null,
      imglayer: null,
      pressure: null,
      geourl: "",
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.createmap();
  },
  methods: {
    createmap() {
      this.imglayer = new WMSLayer({
        url: this.geourl + "/geoserver/img/wms",
      });
      this.map = new Map({
        ground: new Ground({
          layers: [],
          surfaceColor: "transparent",
          opacity: 0,
        }),
        layers: [],
      });
      this.view = new MapView({
        container: "wqmap",
        map: this.map,
        background: {
          type: "color",
          color: [255, 252, 244, 0],
        },
        extent: {
          xmax: 111.15191207080001,
          xmin: 110.39483293,
          ymax: 31.591080100800003,
          ymin: 30.9630324254,
        },
        spatialReference: {
          wkid: 4326,
        },
      });
      this.view.ui.remove("attribution");
      this.view.ui.empty("top-left");
      this.view.map.add(this.imglayer);
      this.view.on("click", async (e) => {
        let point = [e.mapPoint.x, e.mapPoint.y];
        console.log(point);
        // this.view.hitTest(e).then((res) => {
        //   console.log(res);
        // });
      });

      const point = {
        type: "point", // autocasts as new Point()
        longitude: 110.70099240671911,
        latitude: 31.335293120084618,
      };
      const point2 = {
        type: "point", // autocasts as new Point()
        longitude: 110.73843038620923,
        latitude: 31.237954373410332,
      };
      const point3 = {
        type: "point", // autocasts as new Point()
        longitude: 110.84741650428043,
        latitude: 31.33030138948594,
      };
      const point4 = {
        type: "point", // autocasts as new Point()
        longitude: 110.8948379449679,
        latitude: 31.204676169419123,
      };
      const greenmarkerSymbol = {
        type: "simple-marker", // autocasts as new SimpleMarkerSymbol()
        color: [0, 128, 0],
        outline: {
          // autocasts as new SimpleLineSymbol()
          color: [255, 255, 255],
          width: 1,
        },
      };
      const yellowmarkerSymbol = {
        type: "simple-marker", // autocasts as new SimpleMarkerSymbol()
        color: [255, 255, 0],
        outline: {
          // autocasts as new SimpleLineSymbol()
          color: [255, 255, 255],
          width: 1,
        },
      };
      const redmarkerSymbol = {
        type: "simple-marker", // autocasts as new SimpleMarkerSymbol()
        color: [255, 0, 0],
        outline: {
          // autocasts as new SimpleLineSymbol()
          color: [255, 255, 255],
          width: 1,
        },
      };
      const orangemarkerSymbol = {
        type: "simple-marker", // autocasts as new SimpleMarkerSymbol()
        color: [255, 165, 0],
        outline: {
          // autocasts as new SimpleLineSymbol()
          color: [255, 255, 255],
          width: 1,
        },
      };
      const lineAtt = {
        监测站点: "自来水厂",
        ph: "2.1",
        余氯: "1.5",
        浊度: "1.2",
        采集时间: "2023-10-25 11:00:00",
      };
      const pointGraphic = new Graphic({
        geometry: point,
        symbol: greenmarkerSymbol,
        attributes: lineAtt,
        popupTemplate: {
          title: "{监测站点}",
          content: [
            {
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "监测站点",
                },
                {
                  fieldName: "ph",
                },
                {
                  fieldName: "余氯",
                },
                {
                  fieldName: "浊度",
                },
                {
                  fieldName: "采集时间",
                },
              ],
            },
          ],
        },
      });
      const pointGraphic2 = new Graphic({
        geometry: point2,
        symbol: yellowmarkerSymbol,
        attributes: lineAtt,
        popupTemplate: {
          title: "{监测站点}",
          content: [
            {
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "监测站点",
                },
                {
                  fieldName: "ph",
                },
                {
                  fieldName: "余氯",
                },
                {
                  fieldName: "浊度",
                },
                {
                  fieldName: "采集时间",
                },
              ],
            },
          ],
        },
      });
      const pointGraphic3 = new Graphic({
        geometry: point3,
        symbol: redmarkerSymbol,
        attributes: lineAtt,
        popupTemplate: {
          title: "{监测站点}",
          content: [
            {
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "监测站点",
                },
                {
                  fieldName: "ph",
                },
                {
                  fieldName: "余氯",
                },
                {
                  fieldName: "浊度",
                },
                {
                  fieldName: "采集时间",
                },
              ],
            },
          ],
        },
      });
      const pointGraphic4 = new Graphic({
        geometry: point4,
        symbol: orangemarkerSymbol,
        attributes: lineAtt,
        popupTemplate: {
          title: "{监测站点}",
          content: [
            {
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "监测站点",
                },
                {
                  fieldName: "ph",
                },
                {
                  fieldName: "余氯",
                },
                {
                  fieldName: "浊度",
                },
                {
                  fieldName: "采集时间",
                },
              ],
            },
          ],
        },
      });
      this.view.graphics.add(pointGraphic);
      this.view.graphics.add(pointGraphic2);
      this.view.graphics.add(pointGraphic3);
      this.view.graphics.add(pointGraphic4);
    },
  },
};
</script>
<style lang="scss">
.esri-view-width-medium .esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0.3);
}
.esri-popup__header {
  background-color: rgba(4, 15, 45, 0.3) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  background-color: rgba(4, 15, 45, 0) !important;
}
.esri-popup__icon,
.esri-icon-close {
  color: #fff !important;
}
.esri-popup__content {
  margin: 0;
}
.esri-feature {
  background-color: rgba(4, 15, 45, 0.3);
  padding: 0 15px 12px;
}
.esri-widget__table {
  color: #fff;
}
.esri-popup__header-title {
  background-color: rgba(4, 15, 45, 0) !important;
  color: #fff;
}
.esri-popup__pointer-direction {
  background-color: rgba(4, 15, 45, 0.3);
}

.esri-popup__footer {
  display: none;
}
.esri-popup__feature-menu {
  display: none;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
.esri-view-surface {
  outline: none !important;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: auto 0px Highlight !important;
  outline: auto 0px -webkit-focus-ring-color !important;
}
[class*="esri-popup--is-docked-top-"] .esri-popup__footer,
[class*="esri-popup--aligned-bottom-"] .esri-popup__footer {
  border-bottom: solid 1px #6e6e6e4d;
}
.esri-popup__header {
  border-top-left-radius: 5px !important;
  border-top-right-radius: 5px;
  background-color: #fff;
  color: #fff;
}
.esri-popup--shadow {
  box-shadow: 0 0 0 0 rgb(155, 155, 155) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  outline: none !important;
}
.esri-ui .esri-popup {
  border-radius: 5px !important;
}
.esri-popup {
  position: absolute !important;
}
.esri-popup__button {
  background-color: transparent !important;
  outline: none;
}
</style>
<style scoped lang="scss">
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
#wqmap {
  position: absolute;
  left: 500px;
  top: 30px;
  width: 910px;
  height: 820px;
}
</style>