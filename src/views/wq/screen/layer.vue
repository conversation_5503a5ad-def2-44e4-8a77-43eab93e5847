<template>
  <div class="wqMap">
    <div id="mars3dContainer" class="mars3d-container"></div>
  </div>
</template>
  
<script>
export default {
  data() {
    return {
      deepHeight: 100,
      map: null,
      tmd: 0.5,
      opean: true,
      mapOptions: {
        scene: {
          //默认视角参数
          center: {
            lat: 31.328109,
            lng: 110.770702,
            alt: 58000,
            heading: 0,
            pitch: -90,
          },
          sceneMode: 2,
        },
        control: {
          // baseLayerPicker: true, // basemaps底图切换按钮
          // homeButton: true, // 视角复位按钮
          // sceneModePicker: true, // 二三维切换按钮
          // navigationHelpButton: true, // 帮助按钮
          // fullscreenButton: true, // 全屏按钮
          // contextmenu: { hasDefault: true } // 右键菜单
          terrainProviderViewModels: [],
          contextmenu: {
            hasDefault:false
          }
        },
        // 每种不同type对应不同参数
        basemaps: [
          { "id": 10, "name": "地图底图", "type": "group" },
          // {
          //   "pid": 10,
          //   "name": "蓝色底图",
          //   "icon": "/img/basemaps/bd-c-midnight.png",
          //   "type": "xyz",
          //   "url": "http://map.geoq.cn/arcgis/rest/services/ChinaOnlineStreetPurplishBlue/MapServer/tile/{z}/{y}/{x}",
          //   "chinaCRS": "GCJ02",
          //   "enablePickFeatures": false,
          //   "show": true
          // },
          {
            brightness: 0.6,
            chinaCRS: "GCJ02",
            contrast: 1.8,
            filterColor: "#4e70a6",
            gamma: 0.3,
            hue: 1,
            icon: require("../../../assets/img/basemaps/bd-c-midnight.png"),
            id: 2017,
            invertColor: true,
            layer: "vec",
            name: "蓝色底图",
            pid: 10,
            saturation: 0,
            type: "gaode",
            "show": true
          }
        ]
      },
      param1: 1,
      underground: null,
      terrainPlanClip: null,
      terrainClip: null,
      eventTarget: new mars3d.BaseClass(),
      queryMapserver: null,
      geourl: '',
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.initMap();
  },
  beforeDestroy() {
    if(this.map) {
      this.map.destroy()
    }
  },
  methods: {
    initMap() {
      this.map = new mars3d.Map("mars3dContainer", this.mapOptions);
      this.map.container.style.backgroundColor = "#546a53"; // 背景色
      this.showPoint();
      this.showGeoJsonLayer()
    },
    showGeoJsonLayer() {
      const graphicLayerGeo = new mars3d.layer.GeoJsonLayer({
        name: "兴山管网",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Atotal_pipe_network&maxFeatures=3000&outputFormat=application%2Fjson&cql_filter=trunk='t'",
        // format: this.simplifyGeoJSON, // 用于自定义处理geojson
        symbol: {
          type: "polylineP",
          styleOptions: {
            clampToGround: true,
            width: 2,
            materialType: mars3d.MaterialType.LineFlow,
            materialOptions: {
              color: "#00ffff",
              // image: "img/textures/fence-line.png",
              speed: 100,
              repeat_x: 10,
            },
            // distanceDisplayCondition: true,
            // distanceDisplayCondition_far: 12000000,
            // distanceDisplayCondition_near: 100000,
            label: {
              font_size: 15,
              text: "{name},{length}m",
              color: "#ffffff",
              outline: true,
              outlineColor: "#ffffff",
              // scaleByDistance: true,
              // scaleByDistance_far: 60000000,
              // scaleByDistance_farValue: 0.2,
              // scaleByDistance_near: 1000000,
              // scaleByDistance_nearValue: 1,
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 50000000,
              distanceDisplayCondition_near: 0,
              setHeight: 10000,
            },
          },
        },
        popup: [
          { field: "name", name: "名称" },
          { field: "length", name: "长度" },
          { field: "code", name: "编码" },
          { field: "material", name: "材质" },
          { field: "diameter", name: "管径" },
          { field: "deep", name: "深度" },
        ],
        flyTo: true,
      });
      // graphicLayerGeo.bindPopup(function(event) {
      //   const attr = event.graphic.attr || {}
      //   attr["名称"] = attr["name"]
      //   attr["长度"] = attr["length"] + "m"
      // return mars3d.Util.getTemplateHtml({ title: "矢量图层", template: `
      //         <div class="grapopup">{name} {length} m</div>
      //        `, attr })
      // })
      console.log(graphicLayerGeo)
      
      this.map.addLayer(graphicLayerGeo);
      // console.log(graphicLayerGeo)
      // 绑定事件
      // graphicLayerGeo.on(mars3d.EventType.load, function (event) {
      //     console.log("数据加载完成", event)
      // })
    },
    
    showPoint() {
      const pointLayer = new mars3d.layer.GeoJsonLayer({
        name: "末端用户水质",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aview_user_quality&maxFeatures=50&outputFormat=application%2Fjson",
        symbol: {
          styleOptions: {
            // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            image: require("../../../assets/img/wq/wqPoint.png"),
            // scale: 1,
            // scaleByDistance: true,
            // scaleByDistance_far: 20000,
            // scaleByDistance_farValue: 0.5,
            // scaleByDistance_near: 1000,
            // scaleByDistance_nearValue: 1,
          }
        },
        flyTo: true
      })

      // pointLayer.on(mars3d.EventType.popupOpen, function (event) {
      //   const container = event.container // popup对应的DOM
      //   console.log("打开了popup", container)

      //   const btnDetails = container.querySelector("#btnDetails")
      //   if (btnDetails) {
      //     btnDetails.addEventListener("click", (e) => {
      //       showXQ()
      //     })
      //   }
      // })
      // pointLayer.on(mars3d.EventType.popupClose, function (event) {
      //   const container = event.container // popup对应的DOM
      //   console.log("移除了popup", container)
      // })

      pointLayer.bindPopup(function(event) {
        const attr = event.graphic.attr || {}
        // attr["名称"] = attr["name"]
        return mars3d.Util.getTemplateHtml({ title: "矢量图层", template: `
              <div class="wqpoup">
                <div class="name">{name}</div>
                <div class="fields">
                  <div>浊度 : {turbidity} NTU</div>
                  <div>ph : {ph}</div>
                  <div>余氯 : {chlorine} mg/L</div>
                  <div>电导率 : {power_rate} μs/cm</div>
                </div>
              </div>`, attr })
      }, {
        autoClose: false,
        closeOnClick: false
      })

      let pointLayer2 = new mars3d.layer.GeoJsonLayer({
        name: "水厂水质",
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Aview_factory_quality&maxFeatures=50&outputFormat=application%2Fjson",
        symbol: {
          styleOptions: {
            // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            image: require("../../../assets/img/wq/wqPoint.png"),
            // scale: 1,
            // scaleByDistance: true,
            // scaleByDistance_far: 20000,
            // scaleByDistance_farValue: 0.5,
            // scaleByDistance_near: 1000,
            // scaleByDistance_nearValue: 1,
          }
        },
        flyTo: true
      })
      pointLayer2.bindPopup(function(event) {
        const attr = event.graphic.attr || {}
        // attr["名称"] = attr["name"]
        return mars3d.Util.getTemplateHtml({ title: "矢量图层", template: `
              <div class="wqpoup">
                <div class="name">{name}</div>
                <div class="fields">
                  <div>浊度 : {turbidity} NTU</div>
                  <div>ph : {ph}</div>
                  <div>电导率 : {power_rate} μs/cm</div>
                </div>
              </div>`, attr })
      }, {
        autoClose: false,
        closeOnClick: false
      })

      this.map.addLayer(pointLayer)
      this.map.addLayer(pointLayer2)
      pointLayer.on(mars3d.EventType.load, function (event) {
        console.log("数据加载完成", event)
        for (let index = 0; index < event.graphics.length; index++) {
          const element = event.graphics[index];
          element.openPopup()
        }
      })
      pointLayer2.on(mars3d.EventType.load, function (event) {
        for (let index = 0; index < event.graphics.length; index++) {
          const element = event.graphics[index];
          element.openPopup()
        }
      })
    },
    simplifyGeoJSON(geojson) {
      try {
        geojson = turf.simplify(geojson, {
          tolerance: 0.0001,
          highQuality: true,
          mutate: true,
        });
      } catch (e) {
        //
      }
      return geojson;
    },
  },
};
</script>

<style lang="scss">
.wqMap {
  .mars3d-popup-content {
    margin: 0px;
  }
  .wqpoup {
    // background-image: url('../../../assets/img/wq/wqPoupKim.png');
    border: 1px solid #F5BD16;
    border-radius: 4px;
    background-color: rgba(245, 189, 22, 0.3);
    // width: 160px;
    // height: 56px;
    display: flex;
    .name {
      width: 55px;
      border-right: 1px solid #F5BD16;
      display: flex;
      align-items: center;
    }
    .fields {
      div {
        height: 28px;
        line-height: 28px;
        padding: 0 5px;
        border-bottom: 1px solid #F5BD16;
      }
      div:last-child {
          border-bottom: none;
      }
    }
  }
}

.mars3dContainer {
  .cesium-viewer-toolbar {
    left: 465px;
  }
  .cesium-baseLayerPicker-dropDown-visible {
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    left: 48px
  }
}
</style>

<style scoped lang="scss">
  /**infoview浮动面板*/
  .infoview {
    position: absolute;
    top: 5px;
    left: 480px;
    padding: 10px 15px;
    border-radius: 4px;
    border: 1px solid rgba(128, 128, 128, 0.5);
    color: #ffffff;
    background: rgba(0, 0, 0, 0.4);
    box-shadow: 0 3px 14px rgba(128, 128, 128, 0.5);
    z-index: 19870101;
  }
  .mars-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  </style>
      