<template>
  <div class="leftPane">
    <!-- 监测分布 -->
    <div class="wqCount commonCard">
      <div class="title">
        <span>监测分布</span>
      </div>
      <div class="countBox">
        <div class="line"></div>
        <div class="item">
          <div class="count"><span>{{ waterFactoryCount }}</span><span class="unit">个</span></div>
          <img class="typeImg" src="@/assets/img/wq/water.png" alt="">
          <div class="label">水源</div>
        </div>
        <div class="line"></div>
        <div class="item">
          <div class="count"><span>{{ pipeNetworkCount }}</span><span class="unit">个</span></div>
          <img class="typeImg" src="@/assets/img/wq/pipe.png" alt="">
          <div class="label">水厂</div>
        </div>
        <div class="line"></div>
        <div class="item">
          <div class="count"><span>{{ userPointCount }}</span><span class="unit">个</span></div>
          <img class="typeImg" src="@/assets/img/wq/user.png" alt="">
          <div class="label">末梢</div>
        </div>
        <div class="line"></div>
      </div>
    </div>

    <!-- 水质实时监测数据 -->
    <div class="wq">
      <div class="title flex-between">
        <span>实时监测</span>
        <!-- <el-select v-model="currentStation" size="mini" style="width: 134px">
          <el-option
            v-for="item in stationList"
            :key="item.key"
            :label="item.label"
            :value="item.value"
            >
          </el-option>
        </el-select> -->

        <!-- 切换 -->
        <div class="tab">
          <div class="default" :class="{ active: currentTab === 1 }" @click="changeTab(1)">水源</div>
          <div class="default" :class="{ active: currentTab === 2 }" @click="changeTab(2)">末梢</div>
        </div>
      </div>
      <div class="list commonCard" >
        <div class="listHeader" style="text-align: center;">
          <span style="width: 100px;">监测站点</span>
          <!-- <span style="width: 70px;">是否健康</span> -->
          <span style="width: 70px;">电导率</span>
          <span style="width: 50px;">ph</span>
          <span style="width: 50px;">浊度</span>
          <span style="width: 50px;" v-if="currentTab === 2" >余氯</span>
          <span style="width: 100px;">监测时间</span>
        </div>
        <div style="height: 175px;overflow: hidden;" v-if="currentTab === 1">
          <div class="listItem" v-for="item in wqList" :key="item.id" style="text-align: center;">
            <div style="width: 100px;">{{ item.name }}</div>
            <!-- <div style="width: 70px;text-align: center;" :style="{ color: item.pass ? '#0FE800' : '#FF9900' }">{{ item.pass ? '是' : '否' }}</div> -->
            <div style="width: 70px;">{{ item.powerRate }}</div>
            <div style="width: 50px;">{{ item.ph }}</div>
            <div style="width: 50px;">{{ item.turbidity }}</div>
            <div style="width: 100px;">{{ item.dataTime | filterTime }}</div>
          </div>
        </div>
        <VueSeamlessScroll
          :data="wqList"
          :class-option="classOption"
          style="overflow: hidden;height: 175px;"
          v-if="currentTab === 2"
        >
        <div class="listItem" v-for="item in wqList" :key="item.id" style="text-align: center;">
          <div style="width: 100px;">{{ item.name }}</div>
          <!-- <div style="width: 70px;text-align: center;" :style="{ color: item.pass ? '#0FE800' : '#FF9900' }">{{ item.pass ? '是' : '否' }}</div> -->
          <div style="width: 70px;">{{ item.powerRate }}</div>
          <div style="width: 50px;">{{ item.ph }}</div>
          <div style="width: 50px;">{{ item.turbidity }}</div>
            <div style="width: 50px;">{{ item.chlorine }}</div>
          <div style="width: 100px;">{{ item.dataTime | filterTime }}</div>
        </div>
        </VueSeamlessScroll>
      </div>
    </div>

    <!-- 在线设备统计 -->
    <!-- <div class="insm commonCard">
      <div class="title">
        <span>在线设备统计</span>
      </div>
      <div class="data">
        <div class="item" v-for="item, index in insmList" :key="index">
          <div class="circle">
            <div class="label">{{ item.rate + '%' }}</div>
          </div>
          <div class="right">
            <div class="top">
              <span class="insmLabel">{{ item.label }}</span>
              <span class="insmValue" style="font-size: 18px;font-weight: bold;">{{ item.value }}</span>
            </div>
            <div class="bar">
              <div class="inner" :style="{ width: item.rate + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 水质达标情况 -->
    <!-- <div class="wqstandard">
      <div class="title">
        <span>水质达标情况</span>
      </div>
      <div class="chartBox commonCard">
        <WqLine :seriesData="seriesData" :xAxisData="xAxisData" :legendData="legendData" height="225px" />
      </div>
    </div> -->

    <!-- 水质预警 -->
    <div class="wqWarn commonCard">
      <div class="title flex-between">
        <span>水质预警</span>
      </div>
      <div class="list" style="padding: 15px 20px">
        <div class="listHeader" style="text-align: center;">
          <span style="width: 100px;">预警站点</span>
          <!-- <span style="width: 70px;">预警设备</span> -->
          <span style="width: 135px;">预警描述</span>
          <span style="width: 135px;">预警时间</span>
        </div>
        <div class="listItem" v-for="item in wqWarnList" :key="item.id" style="text-align: center;">
          <div style="width: 100px;" class="ellipsis">{{ item.name }}</div>
          <!-- <div style="width: 70px;">{{ item.insm }}</div> -->
          <div style="width: 135px;" class="ellipsis" :title="item.msg">{{ item.msg }}</div>
          <div style="width: 135px;">{{ item.dataTime | filterTime}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import WqLine from './components/wqLine'
import VueSeamlessScroll from 'vue-seamless-scroll'

import { getWqRealtimeDataPage } from '@/api/wq/monitor'
import { getOnlineInsm, getWqPassData, getWqCount } from '@/api/wq/screen'
import { getWqWarn } from '@/api/wq/warn'

import { parseTime } from '@/utils'

export default {
  components: { WqLine, VueSeamlessScroll },
  data() {
    return {
      // 监测分布
      waterFactoryCount: 5,
      pipeNetworkCount: 0,
      userPointCount: 11,
      // 水质实时数据
      currentStation: 1,
      stationList: [
        {
          id: 1,
          value: 1,
          label: '全部站点'
        }
      ],
      currentTab: 1,
      wqList: [],
      classOption: {
        direction: 1,
        singleHeight: 35,
      },
      // 在线设备统计
      insmList: [
        {
          label: '总监测设备(台)',
          value: 21,
          rate: 100
        },
        {
          label: '水厂水质监测设备(台)',
          value: 5,
          rate: 100
        },
        {
          label: '管网水质检测设备(台)',
          value: 5,
          rate: 100
        },
        {
          label: '末端用户水质监测设备(台)',
          value: 11,
          rate: 100
        }
      ],
      // 水质达标情况
      legendData: [],
      xAxisData: [],
      seriesData: [
        // {
        //   name: '蓄水池',
        //   symbol: 'circle',
        //   symbolSize: 6,
        //   type: 'line',
        //   data: [83, 80, 84, 90, 80, 79]
        // },
        // {
        //   name: '自来水厂',
        //   symbol: 'circle',
        //   symbolSize: 6,
        //   type: 'line',
        //   data: [70, 71, 69, 71, 72, 73]
        // },
        // {
        //   name: '供水管道',
        //   symbol: 'circle',
        //   symbolSize: 6,
        //   type: 'line',
        //   data: [79, 90, 80, 90, 88, 85]
        // }
      ],
      // 水质预警
      wqWarnList: []
    }
  },
  filters: {
    filterTime(val) {
      return parseTime(val, '{m}-{d} {h}:{i}')
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      // 监测分布
      getWqCount().then(res => {
        console.log(res)
        // this.waterFactoryCount = res.data['WATER_FACTORY_WATER_QUALITY_MONITOR']
        // this.pipeNetworkCount = res.data['PIPELINE_WATER_QUALITY_INSTRUMENT']
        // this.userPointCount = res.data['END_USER_WATER_QUALITY_MONITOR']
      })
      // 水质实时监测数据
      this.getWQData(this.currentTab, 5)
      // 水质预警
      getWqWarn({
        pageNo: 1,
        pageSize: 8,
        warnType: 'WATER_QUALITY'
      }).then(res => {
        if(res.status === 200) {
          this.wqWarnList = res.data
        }
      })
      // 在线设备统计
      // getOnlineInsm().then(res => {
      //   console.log(res)
      //   this.insmList[0].value = res.data.all
      //   this.insmList[0].rate = res.data.allOnlineRate
      //   this.insmList[1].value = res.data.factory
      //   this.insmList[1].rate = res.data.factoryOnlineRate
      //   this.insmList[2].value = res.data.waterSource
      //   this.insmList[2].rate = res.data.waterSourceRate
      //   this.insmList[3].value = res.data.pipe
      //   this.insmList[3].rate = res.data.pipeOnlineRate
      // })
      // getWqPassData().then(res => {
      //   console.log(res)
      //   const { status, data } = res
      //   if(status === 200) {
      //     // 图例
      //     this.legendData = data.map(item => item.stationTypeName)
      //     // x轴数据
      //     this.xAxisData = data[0].list.map(item => parseTime(item[0], '{y}-{m}'))
      //     // 数据
      //     this.seriesData = data.map(item => {
      //       return {
      //         name: item.stationTypeName,
      //         symbol: 'circle',
      //         symbolSize: 6,
      //         type: 'line',
      //         data: item.list.map(inner => {
      //           return inner[1]
      //         })
      //       }
      //     })
      //   }
      // })
    },
    changeTab(tab) {
      this.currentTab = tab
      this.getWQData(this.currentTab, this.currentTab === 1 ? 5 : 11)
    },
    // 按需获取水质实时监测数据
    getWQData(type, size) {
      const sourceType = type === 1 ? 'WATER_SOURCE' : 'END_USER'
      getWqRealtimeDataPage({
        sourceType,
        pageNo: 1,
        pageSize: size
      }).then(res => {
        if(res.status === 200) {
          this.wqList = res.data
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.leftPane {
  position: absolute;
  width: 430px;
  height: 100%;
  left: 30px;
  top: 0;
  .wqCount {
    height: 28%;
    .countBox {
      padding: 20px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .line {
        height: 150px;
        width: 3px;
        background-image: url('../../../assets/img/wq/line.png');
      }
      .item {
        display: flex;
        flex-direction: column;
        .count {
          font-family: DIN;
          font-size: 30px;
          color: rgba(102, 245, 254, 1);
          text-shadow: 0px 0px 10px rgba(0,145,255,0.5011);
          // background: linear-gradient(90deg, #66F5FE 0%, #1382E7 100%);
          text-align: center;
          .unit {
            font-family: Microsoft YaHei;
            font-size: 14px;
          }
        }
        .typeImg {
          width: 99px;
          height: 100px;
        }
        .label {
          margin-top: 13px;
          text-align: center;
          color: #D8F0FF;
        }
      }
    }
  }
  .wq {
    width: 100%;
    height: 31%;
    .tab {
      display: flex;
      align-items: center;
      font-family: Microsoft YaHei;
      .default {
        width: 72px;
        height: 28px;
        background: rgba(2,50,128,0.8);
        color: #ACC6EA;
        font-weight: 400;
        font-size: 12px;
        text-align: center;
        line-height: 28px;
        cursor: pointer;
        margin-right: 10px;
      }
      .active {
        color: #fff;
        background-image: url('../../../assets/img/wq/tab1.png');
        background-size: 80px;
        background-position: -5px -11px;
      }
    }
    .list {
      height: 239px;
      padding: 15px 10px;
    }
  }
  .listHeader {
    font-size: 14px;
    background-image: url("../../../assets/img/table.png");
    background-size: 100%;
    height: 34px;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    span:first-child {
      // width: 93px;
      // padding-left: 30px;
    }
    span:last-child {
      // flex-grow: 1;
      // padding-right: 30px;
    }
  }
  .listItem {
    font-size: 12px;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    height: 30px;
    background-color: rgba(2,50,128,0.23);
    border: 1px solid #023280;
    margin-top: 5px;
  }
  .insm {
    height: 36%;
    .data {
      padding: 20px;
      .item {
        display: flex;
        padding-bottom: 10px;
        &:last-child {
          padding-bottom: 0;
        }
        .circle {
          width: 48px;
          height: 48px;
          border: 2px solid #2A5DD2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          .label {
            width: 40px;
            height: 40px;
            border: 2px solid #52D0DF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #52D0DF;
            font-weight: bold
          }
        }
        .right {
          padding-left: 10px;
          flex-grow: 1;
          .top {
            display: flex;
            font-size: 14px;
            justify-content: space-between;
            padding-bottom: 8px;
          }
          .bar {
            height: 14px;
            border: 1px solid #267CD5;
            padding-top: 2px;
            .inner {
              position: relative;
              height: 8px;
              background: linear-gradient(90deg, rgba(45,72,173,0.1) 0%, #00B4FF 100%);
              &::after {
                display: block;
                content: '';
                width: 2px;
                height: 14px;
                background-color: #D8F0FF;
                position: absolute;
                right: -2px;
                top: -3px;
              }
            }
          }
        }
      }      
    }
  }
  .wqstandard {
    height: calc(64% - 274px);
    .chartBox {
      height: calc(100% - 35px);
      // height: 200px;
      display: flex;
      align-items: center;
    }
  }
  .wqWarn {
    // height: calc(100% - 539px);
    height: 41%;
    overflow: hidden;
  }
}
</style>
