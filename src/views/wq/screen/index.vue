<template>
  <div class="wqScreen">
    <!-- <WqMap class="map"/> -->
    <Map :mapMode="isFull" />

    <!-- 面板控制 -->
    <div class="mapMode" @click="changeMapMode" :class="{ mapModeFull: isFull }">
      <img v-if="isFull" src="@/assets/img/home/<USER>/full-active.png" alt="">
      <img v-else src="@/assets/img/home/<USER>/full.png" alt="">
    </div>

    <LeftPane v-if="!isFull" class="setIndex" />
    <RightPane v-if="!isFull" class="setIndex" />
    
    <!-- 浮动侧边栏 -->
    <FloatingSideMenu module-type="wq" />
  </div>
</template>

<script>
import LeftPane from "./leftPane";
import RightPane from "./rightPane";
// import WqMap from "./layer";

import Map from './components/map'
import FloatingSideMenu from '@/components/FloatingSideMenu.vue'

export default {
  name: "WqScreen",
  components: {
    LeftPane,
    RightPane,
    // WqMap,
    Map,
    FloatingSideMenu
  },
  data() {
    return {
      isFull: false
    }
  },
  methods: {
    changeMapMode() {
      this.isFull = !this.isFull
    }
  }
};
</script>

<style lang="scss">
.wqScreen {
  .title {
    height: 35px;
    width: 430px;
    font-family: PangMenZhengDao;
    font-size: 20px;
    padding-left: 44px;
    background-image: url("../../../assets/img/title.png");
    background-repeat: no-repeat;
    background-size: 430px 35px;
    line-height: 35px;
  }
  .flex-between {
    padding-right: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .commonCard {
    background-color: rgba(4, 15, 45, 0.3);
  }
}
</style>

<style lang="scss" scoped>
.wqScreen {
  height: calc(100vh - 84px);
  color: #ffffff;
  // padding: 0 30px;
  position: relative;
  .mapMode {
    position: absolute;
    top: 20px;
    left: 490px;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .mapModeFull {
    left: 30px;
  }
}
.map {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.setIndex {
  z-index: 1;
}
</style>
