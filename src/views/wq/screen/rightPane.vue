<template>
  <div class="rightPane">
    <!-- 水质报告 -->
    <div class="wqReport commonCard">
      <div class="title flex-between">
        <span>水质报告</span>
        <div class="tab">
          <div class="default" :class="{ active: currentTab === 0 }" @click="changeTab(0)">自动生成</div>
          <div class="default" :class="{ active: currentTab === 2 }" @click="changeTab(2)">人工上传</div>
          <div class="arrow" @click="pageLink('WqReport')">>></div>
        </div>
      </div>
      <div class="reportList">
        <div class="item" v-for="item in wqReportList" :key="item.id">
          <div class="icon pdf"></div>
          <div style="flex: 1;">{{ item.reportName }}</div>
          <div>{{ item.time }}</div>
        </div>
      </div>
    </div>

    <!-- 应急方案库 -->
    <div class="wqCase commonCard">
      <div class="title flex-between">
        <span>应急方案库</span>
        <div class="arrow" @click="pageLink('WqProgramme')">>></div>
      </div>
      <div class="caseList">
        <div class="item" v-for="item in caseList" :key="item.id">
          <div style="flex: 1;color: #ED3B04;">【{{ item.name }}】</div>
          <div>{{ item.updateTime | filterTime }}</div>
        </div>
      </div>
    </div>


    <!-- 水质达标情况 -->
    <div class="todayDay commonCard">
      <div class="title flex-between">
        <span>水质达标情况</span>
        <!-- <el-select v-model="params" placeholder="" size="mini" style="width: 134px">
          <el-option
            v-for="item in paramsList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            >
          </el-option>
        </el-select> -->
      </div>
      <div class="content">
        <div class="">
          <div class="item">
            <GaugeChart :rateNumber="waterSourceRate" height="140px" />
            <div class="name">水源</div>
          </div>
        </div>
        <div class="bottom">
          <!-- <div class="item" style="margin-right: 10px;">
            <GaugeChart :rateNumber="pipeNetworkRate" height="140px" />
            <div class="name">管网</div>
          </div> -->
          <div class="item">
            <GaugeChart :rateNumber="userPointRate" height="140px" />
            <div class="name">末梢</div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import GaugeChart from './components/gaugeChart.vue'

import { getWqHealthDataNew } from '@/api/wq/screen'
import { getWqProgramme } from '@/api/wq/programme'
import { getReportPageList } from '@/api/wq/report'

import { parseTime } from '@/utils'

export default {
  components: {
    GaugeChart  
  },
  data() {
    return {
      // 水质报告
      currentTab: 0,
      wqReportList: [
        {
          name: '2024年03月水质月报',
          time: '03-05'
        },
        {
          name: '2024年02月水质月报',
          time: '02-29'
        },
        {
          name: '2024年01月水质月报',
          time: '01-30'
        }
      ],
      // 应急方案库
      caseList: [],
      // 今日健康达标率
      paramsList: [
        {
          label: '今日',
          value: 'all'
        }
      ],
      params: 'all',
      // 水质达标情况
      waterSourceRate: 0,
      waterFactoryRate: 0,
      pipeNetworkRate: 0,
      userPointRate: 0,
    }
  },
  filters: {
    filterTime(val) {
      return parseTime(val, '{m}-{d} {h}:{i}')
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getWqHealthDataNew().then(res => {
        console.log(res)
        const { status, data }  = res
        if(status === 200) {
          if(data.length > 0) {
            data.forEach(element => {
              if(element.stationTypeName === '终端用户') {
                this.userPointRate = element.rate
              }
              // if(element.stationTypeName === '管网') {
              //   this.pipeNetworkRate = element.rate
              // }
              if(element.stationTypeName === '水源') {
                this.waterSourceRate = element.rate
              }
            })
          }
        }
      })

      getWqProgramme({
        pageSize: 4,
        pageNo: 1
      }).then(res => {
        this.caseList = res.data
      })

      this.getReport()
    },
    getReport() {
      getReportPageList({
        pageSize: 3,
        pageNo: 1,
        createWay: this.currentTab
      }).then(res => {
        this.wqReportList = res.data.map(item => {
          return {
            ...item,
            time: parseTime(item.updateTime, '{m}-{d}')
          }
        })
      })
    },
    changeTab(tab) {
      this.currentTab = tab
      this.getReport()
    },
    pageLink(name) {
      this.$router.push({ name })
    }
  }
}
</script>

<style lang="scss" scoped>
.rightPane {
  width: 430px;
  height: 100%;
  position: absolute;
  right: 30px;
  top: 0;
  .wqReport {
    height: 21.3%;
    .tab {
      display: flex;
      align-items: center;
      font-family: Microsoft YaHei;
      .default {
        width: 72px;
        height: 28px;
        background: rgba(2,50,128,0.8);
        color: #ACC6EA;
        font-weight: 400;
        font-size: 12px;
        text-align: center;
        line-height: 28px;
        cursor: pointer;
        margin-right: 10px;
      }
      .active {
        color: #fff;
        background-image: url('../../../assets/img/wq/tab1.png');
        background-size: 80px;
        background-position: -5px -11px;
      }
    }
    .reportList {
      padding: 15px 20px;
      height: 152px;
      .item {
        height: 34px;
        background-image: url('../../../assets/img/wq/itemBg.png');
        background-size: 100% 34px;
        margin-bottom: 10px;
        padding: 0 20px;
        font-size: 14px;
        display: flex;
        align-items: center;
        &:last-child {
          margin-bottom: 0;
        }
        .icon {
          width: 14px;
          height: 16px;
          background-size: 100%;
          margin-right: 10px;
        }
        .pdf {
          background-image: url('../../../assets/img/wq/pdfIcon.png');
        }
      }

    }
  }
  .arrow {
    font-family: Microsoft YaHei;
    color: rgba(0, 238, 253, 1);
    cursor: pointer;
  }
  .wqCase {
    height: 26%;
    .caseList {
      height: 196px;
      padding: 15px 20px;
      .item {
        height: 34px;
        background-image: url('../../../assets/img/wq/itemBg1.png');
        background-size: 100% 34px;
        margin-bottom: 10px;
        padding: 0 20px 0 29px;
        font-size: 14px;
        display: flex;
        align-items: center;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
  }
  .wqWarn {
    width: 100%;
    .list {
      height: 239px;
      padding: 15px 20px;
    }
  }
  
  .listHeader {
    font-size: 14px;
    background-image: url("../../../assets/img/table.png");
    background-size: 100%;
    height: 34px;
    display: flex;
    align-items: center;
  }
  .listItem {
    font-size: 12px;
    display: flex;
    align-items: center;
    height: 30px;
    background-color: rgba(2,50,128,0.23);
    border: 1px solid #023280;
    margin-top: 5px;
    div {
      text-align: center;
    }
  }
  .todayDay {
    // height: calc(100% - 438px);
    height: 52.7%;
    .content {
      height: calc(100% - 35px);
      padding: 15px 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      .item {
        width: 190px;
      }
      .name {
        width: 190px;
        height: 30px;
        background-image: url("../../../assets/img/wq/nameBg.png");
        background-size: 100%;
        font-weight: 700;
        font-size: 16px;
        text-align: center;
        line-height: 30px;
      }
      .top {
        display: flex;
        justify-content: center;
      }
      .bottom {
        display: flex;
        // justify-content: space-between;
      }
    }
  }
}
</style>
