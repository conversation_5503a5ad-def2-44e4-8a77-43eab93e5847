<template>
  <div class="labelPopup" ref="labelPopup">
    <i class="el-icon-close closeIcon" @click="handleClick"></i>
    <div class="name" :style="{ 'background-color': labelType === 1 ? '#4487FF' : '#00917F' }">{{ name }}</div>
    <!-- <div class="dataItem" v-if="this.labelType === 1">{{ speed }} m³/h</div> -->
    <div class="dataBox">
      <div class="dataItem" v-for="item, index in list" :key="index">
        <span>{{ item }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LabelPopup',
  props: {
    name: {
      type: String,
      default: ''
    },
    labelType: {
      type: Number
    },
    speed: {
      type: [Number, String]
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleClick() {
      this.$el.style.display = 'none'
      this.$emit('close', this.name)
    }
  }
}
</script>

<style lang="scss" scoped>
.labelPopup {
  position: relative;
  background: rgba(16,33,58,0.8);
  box-shadow: inset 0px 4px 14px 0px #52D0DF;
  border: 1px solid #52D0DF;
  width: 178px;
  .closeIcon {
    position: absolute;
    top: -1px;
    right: -1px;
    font-size: 20px;
    cursor: pointer;
  }
  .name {
    background-image: url('~@/assets/img/wq/popupTitleBg.png');
    background-size: 100%;
    padding: 0 10px;
    // width: 80px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #ffffff;
  }
  .dataBox {
    padding: 5px 10px;
  }
  .dataItem {
    // text-align: center;
    font-size: 13px;
    // color: black;
  }
}
</style>