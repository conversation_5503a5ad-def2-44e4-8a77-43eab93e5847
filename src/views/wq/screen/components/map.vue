<template>
  <div class="innerMap">
    <div id="map"></div>

    <div id="infoWindowItem"></div>
    <!-- 底图切换 -->
    <div class="baseBox" @click="changeBase" :class="{ mapModeFull: mapMode }" >
      <!-- <img v-if="isSatellite" src="@/assets/img/home/<USER>" alt=""> -->
      <!-- <img v-else src="@/assets/img/home/<USER>" alt=""> -->
      <img src="@/assets/img/home/<USER>/earth.png" alt="">
    </div>
    <div class="legend">
      <div class="item">
        <div class="check" @click="handleShowWq">
          <div class="checked" v-if="showWq"></div>
        </div>
        <div class="name">水质站点</div>
      </div>
    </div>
  </div>
</template>

<script>
import Map from "@arcgis/core/Map"
import MapView from "@arcgis/core/views/MapView"
import WMSLayer from "@arcgis/core/layers/WMSLayer"
import GeoJSONLayer from "@arcgis/core/layers/GeoJSONLayer"
import FeatureLayer from "@arcgis/core/layers/FeatureLayer"
import WebTileLayer from "@arcgis/core/layers/WebTileLayer"
import WFSLayer from '@arcgis/core/layers/WFSLayer'
import WMTSLayer from '@arcgis/core/layers/WMTSLayer'
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer'
import Graphic from "@arcgis/core/Graphic"
import Polygon from "@arcgis/core/geometry/Polygon"
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine"
import GroupLayer from '@arcgis/core/layers/GroupLayer'
import Popup from '@arcgis/core/widgets/Popup'
import Point from '@arcgis/core/geometry/Point'

import axios from 'axios'
import { getWQLatest } from '@/api/wq/screen'
import LabelPopup from './labelPopup'
import Vue from 'vue'
let labelPopup = Vue.extend(LabelPopup)

export default {
  props: {
    mapMode: {
      type: Boolean
    }
  },
  data() {
    return {
      isSatellite: false,
      view: null,
      map: null,
      geoUrl: null,
      borderLayer: null,
      wqLayer: null,
      wqData: [],
      showWq: false,
      // labelPopup: null
    }
  },
  mounted() {
    this.geoUrl = process.env.VUE_APP_GEO_URL
    this.createMap()
  },
  methods: {
    createMap() {
      // 边界
      this.borderLayer = new GeoJSONLayer({
        id: 'borderLayer',
        name: 'borderLayer',
        url: '/border.json',
        renderer: {
          type: 'simple',
          symbol: {
            type: 'simple-fill',
            color: [255, 255, 255, 0],
            outline: {
              width: 2,
              color: '#00B3FF'
            }
          }
        }
      })
      // 天地图影像图-标注
      this.tdtImageNoteLayer = new WebTileLayer({
        id: 'tdtImageNoteLayer',
        name: 'tdtImageNoteLayer',
        urlTemplate: 'http://{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
        subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
        visible: true
      })
      // mapbox 卫星瓦片
      // this.mapBoxTileLayer = new WebTileLayer({
      //   id: 'mapBoxTileLayer',
      //   name: 'mapBoxTileLayer',
      //   urlTemplate: 'https://api.mapbox.com/v4/mapbox.satellite/{level}/{col}/{row}.webp?access_token=pk.eyJ1IjoidGFuZ2d1b2NoYW5nIiwiYSI6ImNtNzE4dGYxdTA0aHUya3B4dWViM3l3cWsifQ.awtc46Zfl0VCpgNd5JCFXQ',
      //   visible: false
      // })
      this.mapBoxTileLayer = new WebTileLayer({
        id: 'JLTileLayer',
        name: 'JLTileLayer',
        urlTemplate: 'https://api.jl1mall.com/getMap/{z}/{x}/{y}?mk=73ad26c4aa6957eef051ecc5a15308b4&tk=24277043ba78c68ff6ed3b50ce486283&pro=ca3a3754837f49d7ac3068edce0e65f7&sch=wmts',
        visible: false
      })

      // 水厂点位
      this.factoryLayer = new WFSLayer({
        url: this.geoUrl + '/geoserver/xingshan/wfs',
        name: 'xingshan:view_factory_quality',
        id: 'factoryLayer',
        renderer: {
          type: 'simple',
          symbol: {
            type: 'picture-marker',
            url: require('@/assets/img/wq/wqPoint.png'),
            width: '24px',
            height: '15px',
            yoffset: '7.5px'
          }
        }
      })
      // 查询factoryLayer要素
      let query = this.factoryLayer.createQuery()
      query.outFields = ['*']
      this.factoryLayer.queryFeatures(query).then((result) => {
        console.log('queryFeatures', result)
        let queryFeatures =  result.features
        queryFeatures.forEach((item => {
          // console.log(item.geometry)
        }))
      })

      this.map = new Map({
        basemap: {
          baseLayers: [
            this.mapBoxTileLayer,
            this.tdtImageNoteLayer,
            this.borderLayer
          ]
        }
      })
      this.view = new MapView({
        container: 'map',
        map: this.map,
        extent: {
          xmin: 110.42369972561853,
          ymin: 31.08945033264494,
          xmax: 111.11518062163354,
          ymax: 31.572430067740488
        },
        constraints: {
          minZoom: 11,
          maxZoom: 18
        }
      })
      // this.view.map.add(this.factoryLayer)

      this.view.on('click', (event) => {
        this.view.hitTest(event).then((response) => {
          let hitResults = response.results
          let result = hitResults[0]
          if(hitResults.length && result.graphic.layer.id === 'wqLayer') {
            
            console.log('click hitTest', result)
            const name = result.graphic.attributes.name
            // find
            let index = this.wqData.findIndex((item) => item.name === name)
            console.log(index)
            if(index > -1) {
              this.wqData[index].style.show = true
              this.reLocatePopup()
            }
          }
        })
      })


      this.view.when(() => {
        console.log('MapView when')
        this.view.zoom = 12
        this.view.goTo({
          center: [110.75210994390093, 31.25307163396126]
        })

        this.createWQLayer()
      })
      this.view.watch('extent', () => {
        this.reLocatePopup()
      })

      // 获取兴山县边界geojson
      let borderPolygon = null
      async function getBorder () {
        await axios.get('/border.json').then(res => {
        let ret = res.data.features[0].geometry.coordinates
          borderPolygon = new Polygon({
            rings: ret.map(item => item[0]),
            spatialReference: { wkid: 4326 }
          })
        })
      }
      this.view.whenLayerView(this.tdtImageNoteLayer).then(async layerView => {
        // 创建一个大的多边形
        const fullPolygon = new Polygon({
          rings: [
            [-180, 90],  // 左上角
            [-180, -90], // 左下角
            [180, -90],  // 右下角
            [180, 90],   // 右上角
            [-180, 90]   // 闭合
          ],
          spatialReference: { wkid: 4326 }
        })

        await getBorder()
    
        // 创建两个几何图形的差异
        const maskPolygon = geometryEngine.difference(fullPolygon, borderPolygon)
        const maskSymbol = {
          type: 'simple-fill',
          color: [0, 0, 0, 0.7],
          outline: null
        }
        const maskGraphic = new Graphic({
          geometry: maskPolygon,
          symbol: maskSymbol
        })
        this.view.graphics.add(maskGraphic)
      })
    },
    handleShowWq() {
      this.showWq = !this.showWq
      this.wqLayer.visible = !this.wqLayer.visible

      if (this.showWq) {
        this.wqData.forEach(item => {
          item.style.show = true
        })
        this.reLocatePopup()
      } else {
        this.wqData.forEach(item => {
          item.style.show = false
        })
        this.reLocatePopup()
      }
    },
    // 水质图层
    createWQLayer() {
      getWQLatest().then(res => {
        let list = res.data || []
        this.wqData = list.map((item) => {
          return {
            ...item,
            style: {
              top: '-1000px',
              left: '-1000px',
              show: true
            }
          }
        })
        

        let graphics = this.wqData.map((item) => {
          return new Graphic(
            {
              geometry: {
                type: 'point',
                longitude: item.coordinate.x,
                latitude: item.coordinate.y
              },
              attributes: {
                name: item.name,
                ph: item.ph,
                powerRate: item.powerRate,
                temp: item.temp,
                turbidity: item.turbidity,
                chlorine: item.chlorine
              }
            }
          )
        })
        // console.log(graphics)

        this.wqLayer = new FeatureLayer({
          id: 'wqLayer',
          source: graphics,
          objectIdField: 'ObjectID',
          fields: [
            { name: 'ObjectID', type: 'oid' },
            { name: 'name', type: 'string' },
            { name: 'ph', type: 'double' },
            { name: 'powerRate', type: 'double' },
            { name: 'temp', type: 'double' },
            { name: 'turbidity', type: 'double' },
            // { name: 'chlorine', type: 'double' }
          ],
          visible: false,
          outFields: ['*'],
          geometryType: 'point',
          renderer: {
            type: 'simple',
            symbol: {
              type: 'picture-marker',
              url: require('@/assets/img/wq/wqPoint.png'),
              width: '24px',
              height: '15px',
              yoffset: '7.5px'
            }
          }
        })
        this.view.map.add(this.wqLayer)

        // this.reLocatePopup()
      })
    },
    // 切换底图
    changeBase() {
      this.isSatellite = !this.isSatellite
      if(this.isSatellite) {
        this.mapBoxTileLayer.visible = true
        this.tdtImageNoteLayer.visible = false
      } else {
        this.mapBoxTileLayer.visible = false
        this.tdtImageNoteLayer.visible = true
      }
    },
    // 定位
    reLocatePopup() {

      this.wqData.forEach((item) => {
        let point = new Point({
          longitude: item.coordinate.x,
          latitude: item.coordinate.y
        })
        let screenPoint = this.view.toScreen(point)
        
        item.style.top = screenPoint.y + 'px'
        item.style.left = screenPoint.x + 'px'
      })
      this.createPopup()
    },
    // 创建弹窗
    createPopup() {
      let infoWindowItem = document.getElementById('infoWindowItem')
      while (infoWindowItem.firstChild) {
        infoWindowItem.removeChild(infoWindowItem.firstChild)
      }

      let pList = []
      this.wqData.forEach((item) => {
        const Elem = document.createElement('div')
        infoWindowItem.appendChild(Elem)

        let list = []
        list.push(`浊度：${item.turbidity} NTU`)
        list.push(`ph：${item.ph}`)
        list.push(`电导率：${item.powerRate} μs/cm`)
        if(item.sourceType === 'END_USER') {
          list.push(`余氯：${item.chlorine}  mg/L`)
        }
        let p = new labelPopup({
          propsData: {
            name: item.name,
            labelType: item.sourceType === 'WATER_FACTORY' ? 1 : 2,
            list
          }
        })
        p.$mount(Elem)
        p.$on('close', (name) => {
          console.log(name)
          item.style.show = false
        })
        p.$el.style.top = item.style.top
        p.$el.style.left = item.style.left
        p.$el.style.position = 'absolute'
        p.$el.style.display = item.style.show ? 'block' : 'none'
        // Elem.innerHTML = p.$el.outerHTML
        // Elem.style.top = item.style.top
				// Elem.style.left = item.style.left
				// Elem.style.position = 'absolute'
				
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.innerMap {
  height: 100%;
  position: relative;
  overflow: hidden;
  #map {
    height: 100%;
  }
  .baseBox {
    position: absolute;
    top: 20px;
    right: 490px;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .mapModeFull {
    right: 30px;
  }
  .legend {
    background: rgba(4, 15, 45, 0.5);
    position: absolute;
    right: 450px;
    bottom: 20px;
    padding: 18px 14px;
    .item {
      display: flex;
      align-items: center;
    }
    .check {
      width: 16px;
      height: 16px;
      border: 1px solid #91d5ff;
      margin-right: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
    .checked {
      width: 8px;
      height: 8px;
      background: linear-gradient(180deg, #60dad1 0%, #1e9afc 100%);
    }
  }
}
</style>