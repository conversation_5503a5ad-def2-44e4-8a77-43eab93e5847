<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        color: ['#00E4FF', '#18D565', '#EBA827'],
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          textStyle: {
            color: '#fff'
          },
          top: 19,
          right: 15,
          data: this.legendData
        },
        grid: {
          left: 0,
          right: 15,
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          // boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#ACC6EA',
              width: 1
            }
          },
          axisLabel: {
            color: '#DEEBFF',
            interval: 0,
            showMaxLabel: true
          },
          axisTick: {
            show: false
          },
          data: this.xAxisData
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100,          
          axisLine: {
            lineStyle: {
              color: '#ACC6EA',
              width: 1
            }
          },
          axisLabel: {
            color: '#DEEBFF',
            formatter: '{value}%'
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#2D3C5C ',
            }
          },
        },
        series: this.seriesData
      })
    }
  }
}
</script>
