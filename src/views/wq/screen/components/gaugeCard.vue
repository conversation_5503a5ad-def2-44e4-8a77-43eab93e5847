<template>
  <div class="card">
    <div class="item" v-for="item, index in list" :key="index">
      <div class="chartBox">
        <GaugeChart :rateNumber="item.rate" />
      </div>
      <div class="smallCard">
        <div class="stationName">{{ item.name }}</div>
        <div class="bottom">
          <div class="param">
            实测范围：
            <span class="value listScroll">
              <VueSeamlessScroll
                :data="item.list"
                :class-option="classOption"
                class="warp"
              >
                <div class="ul-item">
                  <div style="width: 200px;" v-for="data, index in item.list" :key="index">{{ data }}</div>
                </div>
              </VueSeamlessScroll>
            </span>
          </div>
          <div class="param">实测次数：<span class="value">{{ item.value1 }}</span></div>
          <div class="param">达标次数：<span class="value">{{ item.value2 }}</span></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GaugeChart from './gaugeChart'
import VueSeamlessScroll from 'vue-seamless-scroll'
import { getWqHealthData } from '@/api/wq/screen'

export default {
  components: { GaugeChart, VueSeamlessScroll },
  data() {
    return {
      list: [],
      classOption: {
        direction: 2
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getWqHealthData().then(res => {
        this.list = res.data.map(item => {
          return {
            name: item.stationTypeName,
            rate: item.rate,
            list: item.list || [],
            value1: item.sumCount,
            value2: item.passCount
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .item {
    width: calc((100% - 20px) / 2);
    .chartBox {
      height: 140px;
    }
    .smallCard {
      width: 185px;
      background-image: url('../../../../assets/img/small_card.png');
      background-size: 185px 100%;
      .stationName {
        font-size: 14px;
        text-align: center;
        line-height: 30px;
        height: 30px;
      }
      .bottom {
        font-size: 12px;
        padding: 10px 10px 0;
        .param {
          padding-bottom: 10px;
          .value {
            color: #52D0DF;
            font-weight: 400;
            padding-left: 8px;

          }
          .listScroll {
            display: inline-block;
            width: 60%;
            height: 16px;
          }
          .warp {
            overflow: hidden;
          }
          .ul-item {
            display: flex;
            overflow: hidden;
          }
        }
      }
    }
  }
}
</style>
