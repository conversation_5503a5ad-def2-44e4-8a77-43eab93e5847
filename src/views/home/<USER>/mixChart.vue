<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'
import { parseTime } from  '@/utils'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        color: ['#0783FA', '#EEA02A'],
        tooltip: {
          trigger: 'axis',
          // formatter: function(params) {
          //   console.log(params)
          //   return '1'
          // }
        },
        legend: {
          textStyle: {
            color: '#fff'
          },
          data: ['漏损量', '漏损率']
        },
        grid: {
          top: 30,
          bottom: 2,
          containLabel: true
        },
        xAxis: {
          axisLine: {
            lineStyle: {
              color: '#3F4F5E',
              width: 2
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#FFFFFF'
          },
          splitLine: {
            show: false
          },
          data: ['谭湾', '大礼', '吴家坪', '青华', '双龙观']
        },
        yAxis: [
          {
            name: '单位：吨',
            nameTextStyle: {
              color: '#FFFFFF'
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              color: '#DEEBFF'
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#3F4F5E',
                type: 'dashed'
              }
            },
          },
          {
            name: '单位：%',
            nameTextStyle: {
              color: '#FFFFFF'
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              color: '#DEEBFF'
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#3F4F5E',
                type: 'dashed'
              }
            },
          }
        ],
        series: [
          {
            name: '漏损量',
            type: 'bar',
            barWidth: 16,
            data: [37, 19, 12, 18, 14]
            // data: []
          },
          {
            name: '漏损率',
            type: 'line',
            yAxisIndex: 1,
            symbol: 'circle',
            data: [30, 28, 13, 28, 8]
            // data: []
          }
        ]
      })
    }
  }
}
</script>