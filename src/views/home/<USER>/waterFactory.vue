<template>
  <div class="WaterFactory">
    <div class="pic" :style="{ 'background-image': `url(${filePrefix}${temp.picUrl})` }">
      <!-- 按钮 -->
      <div class="view">
        <div class="backIcon" @click="handelBack"></div>
        <div class="viewIcon" @click="jump720"></div>
      </div>

      <!-- 水厂选择 -->
      <div class="factoryListBox">
        <div class="item" v-for="item in factoryList" :key="item.value"
          :class="{ select: item.value === currentFactory }" :style="{ 'margin-left': item.left + 'px' }"
          @click="handleClick(item)">
          {{ item.name }}
        </div>
      </div>

      <!-- 图例 -->
      <div class="legend"></div>

      <!-- 超滤间 -->
      <div class="Bigpopup" :style="{ 'top': pxToVw(temp.top), 'left': pxToVw(temp.left1) }" >
        <div class="PopupContent">
          <div class="PopupName">
            超滤间
          </div>
          <div class="PopupTime">
            监测时间：{{ UFEDataTime }}
          </div>
          <div class="PopupItemContent">
            <div class="PopupItem" v-for=" item in infodatalist" :key="item.id">
              <div :style="{ background: getLevelColor(item.level) }">{{ item.label }}</div>
              <div class="Popupdata">{{ item.value + item.unit }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 蓄水池 -->
      <div class="Smallpopup" :style="{ 'top': pxToVw(temp.top1) }">
        <div class="SmallpopupContent">
          <div class="SmallpopupName">
            蓄水池
          </div>
          <div class="SmallpopupTime">
            监测时间：{{ MonTime2 }}
          </div>
          <div class="SmallPopupItemContent">
            <div class="SmallpopupItem" v-for="item in poolInfo" :key="item.id">
              <div class="itemStyle">
                <div class="poolItem">
                  <div class="poolLabel">{{ item.label }}:</div>
                  <div>{{ item.data + item.unit }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="test" v-if="currentFactory == 5">423m³蓄水池</div>

      <!-- 储备水箱 -->
      <div class="waterBox" :style="{ 'top': pxToVw(temp.top2) }">
        <div class="waterBoxContent">
          <div class="waterBoxName">
            储备水箱
          </div>
          <div class="waterBoxTime">
            监测时间：{{ MonTime1 }}
          </div>
          <div class="waterBoxItemContent">
            <div class="waterBoxItem" v-for="item in waterBoxInfo" :key="item.id">
              <div class="itemStyle">
                <div class="waterItem">
                  <div class="waterLabel">{{ item.label }}:</div>
                  <div>{{ item.data + item.unit }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 出水数据展示 -->
      <!-- <div class="dataShow">
        <span class="cell" v-for="item, index in temp.LEDData" :key="index" :style="{ top: item.y + 'px', left: item.x + 'px' }">{{ item.value }}</span>
      </div> -->
      <div class="cell" v-for="item, index in temp.LEDData" :key="index" :style="{ top: pxToVw(item.y), left: pxToVw(item.x) }">{{ item.value }}</div>
    </div>
  </div>
</template>

<script>
import { getUFEquipment, getUFEquipmentLatestDataNew, getFactoryLatestData, getFactoryOutInfo } from '@/api/screenData'

export default {
  name: "WaterFactory",
  data() {
    return {
      MonTime1: '--',
      MonTime2: '--',
      // 储备水箱参数
      waterBoxInfo: [
        // { label: "出水压力", data: '--', unit: 'MPa' },
        { label: "出水流量", data: '--', unit: 'm³/h' },
      ],
      // 蓄水池参数
      poolInfo: [
        { label: "水池液位", data: '--', unit: 'm' },
      ],
      currentFactory: 1, // 当前水厂标识
      UFEDataTime: '--', // 超滤设备数据监测时间
      // 五个标段水厂
      factoryList: [
        {
          id: 9, // 水厂id
          facilityId: 59, // 蓄水池id
          UEFId: 0,
          value: 1,
          name: "谭湾水厂",
          left: 20,
          left1: 810,
          top: 85,
          top1: 90,
          top2:45,
          picUrl: "/xsfile/factory/factory1.png",
          viewUrl: "https://www.720yun.com/vr/c25jzshmka4",
          LEDData: [
            {
              x:361,
              y:394,
              value: ''
            },
            {
              x:361,
              y:468,
              value: ''
            },
            {
              x:361,
              y:548,
              value: ''
            },
          ],
        },
        {
          id: 10,
          facilityId: 1939,
          UEFId: 0,
          value: 2,
          name: "双龙观水厂",
          left: 0,
          left1: 810,
          top: 168,
          top1: 178,
          top2:135,
          picUrl: "/xsfile/factory/factory2.png",
          viewUrl: "https://www.720yun.com/vr/f30jzshmkv8",
          LEDData: [
            {
              x:378,
              y:469,
              value: ''
            },
            {
              x:378,
              y:529,
              value: ''
            },
            {
              x:378,
              y:590,
              value: ''
            },
            {
              x:378,
              y:650,
              value: ''
            },
            {
              x:378,
              y:711,
              value: ''
            },
          ],
        },
        {
          id: 1,
          facilityId: 22,
          UEFId: 0,
          value: 3,
          name: "青华水厂",
          left: -20,
          left1:770,
          top: 230,
          top1: 250,
          top2:210,
          picUrl: "/xsfile/factory/factory3.png",
          viewUrl: "https://www.720yun.com/vr/1c5jzskOOy5",
          LEDData:[
            {
              x:408,
              y:665,
              value: ''
            },
            {
              x:408,
              y:771,
              value: ''
            },
          ]
        },
        {
          id: 2,
          facilityId: 2,
          UEFId: 0,
          value: 4,
          name: "大礼水厂",
          left: 0,
          left1: 810,
          top: 235,
          top1: 250,
          top2:210,
          picUrl: "/xsfile/factory/factory4.png",
          viewUrl: "https://www.720yun.com/vr/14bjzskOOk6",
          LEDData:[
            {
              x:408,
              y:557,
              value: ''
            },
            {
              x:408,
              y:657,
              value: ''
            },
            {
              x:408,
              y:772,
              value: ''
            },
          ]
        },
        {
          id: 5,
          facilityId: 34,
          UEFId: 3769, // 超滤设备id
          value: 5,
          name: "吴家坪水厂",
          left: 20,
          left1: 810,
          top: 230,
          top1: 250,
          top2:210,
          picUrl: "/xsfile/factory/factory5.png",
          viewUrl: "https://www.720yun.com/vr/1aejzpwazv5",
          LEDData:[
            {
              x:377,
              y:619,
              value: ''
            },
            {
              x:377,
              y:697,
              value: ''
            },
            {
              x:377,
              y:773,
              value: ''
            },
          ]
        },
      ],
      // 超滤设备参数
      infodatalist: [
        { label: "浊度", value: '--', unit: "NTU", level: "1" },
        { label: "温度", value: '--', unit: "°C", level: "2" },
        { label: "产水流量", value: '--', unit: "m³/h", level: "3" },
        { label: "累计产水量", value: '--', unit: "m³", level: "4" },
      ],
    };
  },
  computed: {
    temp() {
      return this.factoryList.find((item) => item.value === this.currentFactory);
    },
    filePrefix() {
      return process.env.VUE_APP_FILE_PREFIX;
    },
    factory() {
      return this.$route.query.factory
    }
  },
  mounted() {
    if(this.factory) {
      this.currentFactory =  this.factoryList.find(item => item.name === (this.factory)).value
    }
    if(this.temp.id) {
      this._getUFEquipmentLatestData(this.temp.id)
      this._getFactoryLatestData(this.temp.facilityId, this.temp.id)
    }
  },
  methods: {
    getLevelColor(level) {
      switch (level) {
        case "1":
          return "#1DB887";
          break;
        case "2":
          return "#C6ECE1";
          break;
        case "3":
          return "#0098FF";
          break;
        case "4":
          return "#00F872";
          break;
        case "5":
          return "#FFF566";
          break;
        case "6":
          return "#FF9845";
          break;
      }
    },
    handelBack() {
      this.$router.back();
    },
    handleClick(factory) {
      this.currentFactory = factory.value;
      if(factory.id) {
        this._getUFEquipmentLatestData(factory.id)
        this._getFactoryLatestData(factory.facilityId, factory.id)
      } else {
        console.log('无超滤设备')
        this.clearData()
      }
    },
    // 数据清除
    clearData() {
      this.UFEDataTime = '--'
      this.infodatalist[0].value = '--'
      this.infodatalist[1].value = '--'
      this.infodatalist[2].value = '--'
      this.infodatalist[3].value = '--'

      this.MonTime1 = '--'
      let index = this.factoryList.findIndex(item => item.value === this.currentFactory)
      console.log(index)
      this.factoryList[index].LEDData.map(item => {
        item.value = '--'
      })
      this.waterBoxInfo[0].data = '--'
      this.waterBoxInfo[1].data = '--'

      this.MonTime2 = '--'
      this.poolInfo[0].data = '--'

    },
    jump720() {
      if (this.temp.viewUrl) {
        window.open(this.temp.viewUrl);
      }
    },
    // 获取超滤设备最新数据
    _getUFEquipmentLatestData(facilityId) {
      getUFEquipmentLatestDataNew(facilityId).then(res => {
        let data = res.data || {}
        // const latestData = res.data[0] ? JSON.parse(res.data[0].data): {}
        // console.log(latestData)
        // this.UFEDataTime = res.data[0].dataTime
        // this.infodatalist[0].value = latestData['pH']
        // this.infodatalist[1].value = '--'
        // this.infodatalist[2].value = latestData['产水浊度']
        // this.infodatalist[3].value = latestData['膜池温度']
        this.UFEDataTime = this.setValue(data.dateTime)
        this.infodatalist[0].value = this.setValue(data.turbidity)
        this.infodatalist[1].value = this.setValue(data.temp)
        this.infodatalist[2].value = this.setValue(data.flow)
        this.infodatalist[3].value = this.setValue(data.accruedFlow)
      })
    },
    // 处理空字符串和null
    setValue(value) {
      return (value === null || value === '') ? '--' : value
    },
    // 储备水箱和蓄水池数据
    _getFactoryLatestData(facilityId, id) {
      getFactoryOutInfo(id).then(res => {
        const data = res.data
        const index = this.factoryList.findIndex(item => item.facilityId === facilityId)
        let speedSum = 0

        for(let i = 0; i < data.length; i++) {
          console.log(data[i].speed)
          if (data[i].speed !== '') {
            this.factoryList[index].LEDData[i].value = (data[i].speed).toFixed(2)
            speedSum += data[i].speed
          }
        }

        this.waterBoxInfo[0].data = speedSum.toFixed(2)

      })
      getFactoryLatestData(facilityId).then(res => {
        const data = res.data
        const index = this.factoryList.findIndex(item => item.facilityId === facilityId)
        this.MonTime1 = data[0].monitorTime

        let pressMax = 0 // 多组数据取压力最大值
        let waterLevelValueMax = 0 // 多组数据取液位最大值
        for(let i = 0; i < data.length; i++) {
          if (data[i].press) {
            pressMax = data[i].press > pressMax ? data[i].press : pressMax
          }
          waterLevelValueMax = data[i].waterLevelValue > waterLevelValueMax ? data[i].waterLevelValue : waterLevelValueMax
        }
        // this.waterBoxInfo[0].data = pressMax.toFixed(2)
        // this.waterBoxInfo[1].data = speedSum.toFixed(2)
        
        this.MonTime2 = data[0].monitorTime
        this.poolInfo[0].data = waterLevelValueMax.toFixed(2)
      })
    },
    pxToVw(pxValue, designWidth = 1920) {
      return (pxValue / designWidth) * 100 + 'vw'
    }
  },
};
</script>

<style lang="scss" scoped>
.WaterFactory {
  height: calc(100vh - 84px);
  position: relative;

  .pic {
    height: 100%;
    position: relative;
    background-size: 1920px 1080px;
    background-repeat: no-repeat;

    .factoryListBox {
      position: absolute;
      padding-left: 93px;
      height: 449px;
      left: 15px;
      top: 167px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      background-image: url("~@/assets/img/home/<USER>/leftBackPic.png");
      background-size: 93px 449px;
      background-repeat: no-repeat;

      .item {
        width: 200px;
        height: 48px;
        background-image: url("~@/assets/img/home/<USER>/normal.png");
        background-size: 100%;
        font-size: 18px;
        padding-left: 30px;
        line-height: 48px;
        color: #fff;
        cursor: pointer;
      }

      .select {
        background-image: url("~@/assets/img/home/<USER>/select.png");
      }
    }

    .view {
      position: absolute;
      top: 45px;
      left: 30px;
      display: flex;

      .backIcon {
        width: 120px;
        height: 37px;
        background-image: url("~@/assets/img/home/<USER>/backIcon.png");
        background-size: 100%;
        cursor: pointer;
      }

      .viewIcon {
        margin-left: 20px;
        width: 120px;
        height: 37px;
        background-image: url("~@/assets/img/home/<USER>/viewIcon.png");
        background-size: 100%;
        cursor: pointer;
      }
    }

    .legend {
      position: absolute;
      width: 110px;
      height: 160px;
      top: 45px;
      right: 20px;
      background-image: url("~@/assets/img/home/<USER>/legend.png");
      background-size: 100%;
    }
    .Bigpopup {
      width: 300px;
      position: absolute;
      // display: flex;
      height: 380px;
      background-image: url("~@/assets/img/home/<USER>/popup.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .PopupContent {
        position: relative;
        width: 220px;
        height: 180px;
        margin-top: 60px;
        margin-left: 40px;

        .PopupName {
          color: #fff;
          font-weight: 700;
          font-size: 16px;
          text-align: center;
          line-height: 30px;
          width: 200px;
          height: 30px;
        }

        .PopupTime {
          margin-top: 5px;
          font-size: 12px;
          text-align: center;
          color: #D8F0FF;
          width: 200px;
          height: 20px;
        }

        .PopupItemContent {
          width: 100%;
          height: calc(100% - 60px);
          display: flex;
          flex-wrap: wrap;

          .PopupItem {
            width: 90px;
            height: 45px;
            text-align: center;
            background: #073e69;
            margin: 10px 10px 5px 10px;

            div {
              width: 90px;
              height: 20px;
              font-size: 12px;
              line-height: 20px;
            }

            .Popupdata {
              height: 25px;
              font-size: 14px;
              line-height: 25px;
              color: #d8f0ff;
            }
          }

        }
      }
    }

    .Smallpopup {
      width: 180px;
      position: relative;
      height: 190px;
      left: 1515px;
      background-image: url("~@/assets/img/home/<USER>/popup2.png");
      background-size: 100% 100%;
      .SmallpopupContent {
        position: absolute;
        width: 175px;
        left: 3px;
        top: 2px;
        height: 110px;
        .SmallpopupName {
          width: 100%;
          height: 30px;
          color: #fff;
          font-size: 16px;
          line-height: 30px;
          text-align: center;
          font-weight: 700;
        }
        .SmallpopupTime {
          width: 100%;
          height: 20px;
          font-size: 12px;
          margin-top: 3px;
          color: #D8F0FF;
          line-height: 25px;
          text-align: center;
        }
        .SmallPopupItemContent {
          width: 100%;
          height: calc(100% - 50px);
          color: #fff;
          .SmallpopupItem {
            width: 100%;
            height: 50%;
            display: flex;
            .itemStyle {
              padding-left: 10px;
              padding-top: 5px;
              color: #00EAFF;
              flex-grow: 1;
              .poolItem {
                display: flex;
                font-size: 14px;
                padding-bottom: 8px;
                line-height: 28px;
                height: 28px;
              }
              .poolLabel {
                width: 75px;
                color: #FFF;
              }
            }
          }
        }

      }
    }

    .waterBox{
      width: 180px;
      position: relative;
      height: 190px;
      left: 545px;
      background-image: url("~@/assets/img/home/<USER>/popup1.png");
      background-size: 100%;
      .waterBoxContent {
        position: absolute;
        width: 175px;
        left: 3px;
        top: 2px;
        height: 110px;
        .waterBoxName {
          width: 100%;
          height: 30px;
          color: #fff;
          font-size: 16px;
          line-height: 30px;
          text-align: center;
          font-weight: 700;
        }
        .waterBoxTime {
          width: 100%;
          height: 20px;
          font-size: 12px;
          margin-top: 3px;
          color: #D8F0FF;
          line-height: 25px;
          text-align: center;
        }
        .waterBoxItemContent {
          width: 100%;
          height: calc(100% - 50px);
          color: #fff;
          .waterBoxItem {
            width: 100%;
            height: 50%;
            display: flex;
            .itemStyle {
              padding-left: 10px;
              padding-top: 5px;
              color: #00EAFF;
              flex-grow: 1;
              .waterItem {
                display: flex;
                font-size: 14px;
                line-height: 20px;
                height: 20px;
              }
              .waterLabel {
                width: 75px;
                color: #FFF;
              }
            }
          }
        }

      }
    }

    .dataShow{
      .cell {
        // display: inline-block;
        // min-width: 26px;
        position: relative;
        background-color:  #213136;
        font-family: LED;
        color: #00ffff;
      }
    }
    .cell {
      min-width: 26px;
      min-height: 18px;
      position: absolute;
      background-color:  #213136;
      font-family: LED;
      color: #00ffff;
    }
  }
  .test {
    position: absolute;
    right: 275px;
    top: 476px;
    color: #00ffff;
    font-size: 14px;
  }
}
</style>