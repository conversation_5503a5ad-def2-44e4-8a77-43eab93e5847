<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'


export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '100%'
        },
        seriesData: {
            type: Array,
            default: () => []
        },
        xAxisData: {
            type: Array,
            default: () => []
        },
        legendData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            chart: null
        }
    },
    watch: {
        seriesData: {
            deep: true,
            handler(val) {
                this.initChart()
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$el)
            this.chart.setOption({
                color:['#FFF566','#44DB78','#FF9845'],    
                tooltip: {
                  trigger: 'axis',
                  backgroundColor: "rgba(2,50,128,0.4)",
                },
                legend: {
                  top:'10',
                  data: this.legendData,
                  textStyle:{
                    color:'#fff'
                  }
                },
                grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '3%',
                  containLabel: true
                },
                xAxis: {
                   type: 'category',
                   boundaryGap: false,
                //    data: ['7-11', '7-12', '7-13','7-14', '7-15','7-16','7-17'],
                   data:this.xAxisData,  
                   axisLabel:{
                    color:' #DEEBFF'
                   },
                   axisLine:{
                    show:true,
                    lineStyle:{
                        color:' #DEEBFF'
                    }
                   }
                },
                yAxis: {
                   type: 'value',
                   min:0,
                   max:10,
                   axisLabel:{
                    color:' #DEEBFF'
                   },
                   axisLine:{
                    show:false
                   },
                   splitLine: {
                      show: true,
                      lineStyle:{
                        type:'dashed'
                    }
    }
                },
                series:this.seriesData

            })
        }
    }


}

</script>