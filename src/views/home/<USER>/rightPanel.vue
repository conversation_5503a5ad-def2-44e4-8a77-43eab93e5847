<template>
  <div class="rightPanel">
    <div class="top card">
      <div class="title flex-between">
        <span>水质实况</span>
        <div class="tab">
          <div
            class="default"
            :class="{ active: currentTab === 1 }"
            @click="changeTab(1)"
          >
            今日合格率
          </div>
          <div
            class="default"
            :class="{ active: currentTab === 2 }"
            @click="changeTab(2)"
          >
            实时数据
          </div>
        </div>
      </div>
      <div class="outerBox">
        <template v-if="currentTab === 1">
          <div class="listHeader" style="text-align: center;">
            <span>水质站点</span>
            <span>浊度</span>
            <span>电导率</span>
            <span>pH</span>
            <span>余氯</span>
          </div>
          <!-- 滚动 -->
          <VueSeamlessScroll
            :data="listNoSource"
            :class-option="classOption"
            style="overflow: hidden;height: calc(100% - 34px);"
          >
            <div
              class="listItem"
              v-for="(item, index) in listNoSource"
              :key="index"
              style="text-align: center;"
            >
              <div>
                {{
                  item.stationType === "WATER_SOURCE"
                    ? item.facilityName + "(水源)"
                    : item.facilityName
                }}
              </div>
              <div :style="{ color: setColorByRate(item.turbidityPassRate) }">
                {{
                  item.turbidityPassRate ? item.turbidityPassRate + "%" : "--"
                }}
              </div>
              <div :style="{ color: setColorByRate(item.powerRatePassRate) }">
                {{
                  item.powerRatePassRate ? item.powerRatePassRate + "%" : "--"
                }}
              </div>
              <div :style="{ color: setColorByRate(item.phPassRate) }">
                {{ item.phPassRate ? item.phPassRate + "%" : "--" }}
              </div>
              <div :style="{ color: setColorByRate(item.chlorinePassRate) }">
                {{ item.chlorinePassRate ? item.chlorinePassRate + "%" : "--" }}
              </div>
            </div>
          </VueSeamlessScroll>
        </template>
        <template v-else>
          <div class="listHeader" style="text-align: center;">
            <span>水质站点</span>
            <span>浊度</span>
            <span>电导率</span>
            <span>pH</span>
            <span>余氯</span>
          </div>
          <!-- 滚动 -->
          <VueSeamlessScroll
            :data="list"
            :class-option="classOption"
            style="overflow: hidden;height: calc(100% - 34px);"
          >
            <div
              class="listItem"
              v-for="(item, index) in list"
              :key="index"
              style="text-align: center;"
            >
              <div>
                {{
                  item.sourceType === "WATER_SOURCE"
                    ? item.name + "(水源)"
                    : item.name
                }}
              </div>
              <div>{{ item.turbidity ? item.turbidity : "--" }}</div>
              <div>{{ item.powerRate ? item.powerRate : "--" }}</div>
              <div>{{ item.ph ? item.ph : "--" }}</div>
              <div>{{ item.chlorine ? item.chlorine : "--" }}</div>
            </div>
          </VueSeamlessScroll>
        </template>
      </div>
    </div>

    <div class="middle card">
      <!-- <div class="title">规模化覆盖率</div>
      <div class="box">
        <div class="leftInfo">
          <span>当前覆盖率</span>
          <span
            style="color: #00FDFF;font-family: DIN;font-size: 40px;font-weight: bold;"
            >60.4%</span
          >
          <span style="color: #00FDFF;font-size: 14px;">【农村自来水普及率93.85%】</span>
        </div>
        <div style="height: 132px;width: 57%;">
          <GaugeChart
            :value="[60.4]"
            style="width: 100%;"
            height="263px"
            width="263px"
          />
        </div>
      </div> -->
      <div class="title">供水调度</div>
      <div class="outerBox">
        <div class="innerBox">
          <div
            class="item"
            v-for="item in countList"
            :key="item.dispatchType.code"
          >
            <div class="itemLable">{{ item.dispatchType.name }}</div>
            <div>
              <span class="itemValue">{{ item.doneCount + '/' + item.totalCount }}</span>
              <span class="unit">起</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="bottom card">
      <div class="title">营收分析</div>
      <div class="box">
        <div class="item" v-for="item in revenueInfo" :key="item.id">
          <img
            v-if="item.isNumber"
            src="@/assets/img/support/money.png"
            alt=""
          />
          <img v-else src="@/assets/img/support/rate.png" alt="" />
          <div style="margin-left: 16px;">
            <div style="color: #ccc;font-size: 14px;">{{ item.label }}</div>
            <div class="value">
              {{ item.value
              }}<span style="font-size: 14px;opacity: 0.5;">{{
                item.unit
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GaugeChart from "./gauge";
import VueSeamlessScroll from "vue-seamless-scroll";

import { getWaterQualityRate } from "@/api/home/<USER>";
import { getWqRealtimeDataPage } from "@/api/wq/monitor";
import { getSupplyTypeCount } from "@/api/home/<USER>"
import { getYearAndMonthRevenueData } from '@/api/revenue/data'

export default {
  // 供水右侧面板
  name: "RightPanel",
  components: {
    GaugeChart,
    VueSeamlessScroll,
  },
  data() {
    return {
      currentTab: 1,
      // 水质合格率
      listNoSource: [],
      list: [],
      classOption: {
        direction: 1,
        singleHeight: 35,
      },
      // 供水调度
      countList: [],
      // 营收分析
      revenueInfo: [
        {
          id: 1,
          isNumber: true,
          label: '本年预存水费',
          value: 17.2,
          unit: '万'
        },
        {
          id: 3,
          isNumber: true,
          label: '本月预存水费',
          value: 5,
          unit: '万'
        },
        {
          id: 2,
          isNumber: true,
          label: "本年应收水费",
          value: 0,
          unit: "万",
        },
        {
          id: 4,
          isNumber: true,
          label: "本月应收水费",
          value: 0,
          unit: "万",
        },
        {
          id: 5,
          isNumber: false,
          label: "本年水费收缴率",
          value: 0,
          unit: "%",
        },
        {
          id: 6,
          isNumber: false,
          label: "本月水费收缴率",
          value: 0,
          unit: "%",
        },
        // {
        //   id: 5,
        //   isNumber: false,
        //   label: "本月水费收缴率",
        //   value: 0,
        //   unit: "%",
        // },
        // {
        //   id: 6,
        //   isNumber: false,
        //   label: "本月抄表率",
        //   value: 0,
        //   unit: "%",
        // },
      ],
    };
  },
  mounted() {
    this.getData();
  },
  beforeDestroy() {},
  methods: {
    getData() {
      this.listNoSource = []
      this.list = [];
      if (this.currentTab === 1) {
        getWaterQualityRate().then((res) => {
          let resData = res.data || []
          this.listNoSource = resData.filter(item => item.stationType !== 'WATER_SOURCE')
          // this.list = res.data || [];
        });
      } else {
        getWqRealtimeDataPage({ pageNo: 1, pageSize: 16 }).then((res) => {
          this.list = res.data || [];
        });
      }

      const month = new Date().getMonth() + 1
      getSupplyTypeCount({ month }).then((res) => {
        this.countList = res.data;
      })

      // 年度月度营收费用统计
      getYearAndMonthRevenueData().then((res) => {
        this.revenueObject = res || {}
        this.revenueInfo[0].value = this.filterToW(this.revenueObject.yearPrePay || 0)
        this.revenueInfo[1].value = this.filterToW(this.revenueObject.monthPrePay || 0)
        this.revenueInfo[2].value = this.filterToW(this.revenueObject.yearReceivable || 0)
        this.revenueInfo[3].value = this.filterToW(this.revenueObject.monthReceivable || 0)
        this.revenueInfo[4].value = this.revenueObject.yearPercent || 0
        this.revenueInfo[5].value = this.revenueObject.monthPercent || 0
        this.$emit('yearReceived', this.filterToW(this.revenueObject.yearReceived || 0)) 
      })
    },
    changeTab(tab) {
      this.currentTab = tab;
      this.getData();
    },
    setColorByRate(rate) {
      if (!rate) return "";
      if (rate >= 100) return "#00ff00";
      if (rate >= 50) return "#fff100";
      return "orange";
    },
    filterToW(value) {
      return (value / 10000).toFixed(2)
    }
  },
};
</script>

<style lang="scss" scoped>
.rightPanel {
  position: absolute;
  right: 30px;
  height: 100%;
  width: 440px; /* 添加宽度以确保整体宽度一致性 */
  color: #ffffff;
  .card {
    background-color: rgba(4, 15, 45, 0.3);
    border: 1px solid rgba(23, 110, 217, 0.3);
    backdrop-filter: blur(16px);
    width: 100%; /* 确保卡片占满容器宽度 */
  }
  .middle {
    margin-top: 20px;
    width: 100%; /* 改为100%，确保相对于父容器的宽度 */
    height: calc((100% - 40px) * 0.215);
    .outerBox {
      height: calc(100% - 42px);
      padding: 5px 5px 0; /* 调整顶部和侧边内边距，移除底部内边距 */
    }
    .innerBox {
      height: calc(100% - 10px); /* 调整高度，给底部留出空间 */
      background-image: url("~@/assets/img/support/bg1.png");
      background-size: 100% 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      .item {
        width: 50%;
        // height: 74px;
        padding-left: 30px;
        font-size: 12px;
        .itemLable {
          font-weight: 500;
          padding-top: 5px;
        }
        .itemValue {
          font-family: DIN;
          font-size: 24px;
          color: #00fdff;
        }
        &:nth-child(even) {
          padding-left: 130px;
        }
      }
    }
    .box {
      flex: 1; /* 让内容区域占满剩余空间 */
      display: flex;
      align-items: center;
      padding: 5px 0; /* 添加上下内边距 */
      .leftInfo {
        width: 43%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .bottom {
    width: 100%; /* 改为100%，确保相对于父容器的宽度 */
    margin-top: 20px;
    overflow: hidden;
    height: calc((100% - 40px) * 0.3475);
    display: flex; /* 添加flex布局 */
    flex-direction: column; /* 纵向排列 */
    .box {
      flex: 1; /* 让内容区域占满剩余空间 */
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      padding: 10px 10px 0; /* 调整内边距 */
      .item {
        width: 50%;
        text-align: center;
        display: flex;
        margin-bottom: 10px;
        img {
          width: 64px;
        }
        .value {
          font-family: DIN;
          font-size: 30px;
          font-weight: bold;
        }
      }
    }
  }
  .title {
    background: url("~@/assets/img/title.png") center no-repeat;
    background-size: 100% 100%; /* 确保背景图片完全填充元素 */
    width: 100%; /* 改为100%，确保相对于父容器的宽度 */
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
  }
  .flex-between {
    display: flex;
    justify-content: space-between;
    padding-right: 20px;
  }
  .tab {
    display: flex;
    align-items: center;
    font-family: Microsoft YaHei;
    .default {
      width: 72px;
      height: 28px;
      background: rgba(2, 50, 128, 0.8);
      color: #acc6ea;
      font-weight: 400;
      font-size: 12px;
      text-align: center;
      line-height: 28px;
      cursor: pointer;
      margin-right: 10px;
    }
    .active {
      color: #fff;
      background-image: url("~@/assets/img/wq/tab1.png");
      background-size: 80px;
      background-position: -5px -11px;
    }
  }
  .top {
    width: 100%;
    height: calc((100% - 40px) * 0.4375);
    display: flex;
    flex-direction: column;
    .outerBox {
      flex: 1; /* 让内容区域自适应剩余空间 */
      padding: 5px;
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 42px); /* 限制最大高度 */
      overflow: hidden; /* 确保内容不会溢出 */

      .listHeader {
        flex-shrink: 0; /* 防止标题被压缩 */
      }
    }
  }
  .listHeader {
    font-size: 14px;
    font-weight: bold;
    background-image: url("~@/assets/img/table.png");
    background-size: 100%;
    height: 34px;
    display: flex;
    align-items: center;
    span {
      flex: 1;
    }
  }
  .listItem {
    font-size: 12px;
    display: flex;
    align-items: center;
    height: 30px;
    background-color: rgba(0, 170, 255, 0.08);
    margin-top: 5px;
    div {
      width: 25%;
    }
  }
}
</style>
