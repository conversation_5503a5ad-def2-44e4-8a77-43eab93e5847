<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import 'echarts-liquidfill'
import resize from '@/utils/chartResize'


export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        title: {
          text: '漏损评价',
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          left: 'center',
          top: 80
        },
        series: {
          type: "liquidFill",
          waveAnimation: 10,
          data: [0.1, 0.08],
          color: ["#3E8BFF", "#01FBE6"],
          // amplitude: 35,
          radius: "80%",
          backgroundStyle: {
            color: {
              type: "radial",
              // x: 0.5,
              // y: 0.5,
              // r: 0.55,
              colorStops: [
                {
                  offset: 0,
                  color: "black",
                },
                {
                  offset: 0.8,
                  color: "#083B67",
                },
                {
                  offset: 0.9,
                  color: "#083B67",
                },
              ],
              globalCoord: false, // 缺省为 false
            },
          },
          outline: {
            show: false,
          },
          label: {
            color: '#2CECEA',
            fontSize: 24,
          },
        }
      })
    }
  }
}
</script>