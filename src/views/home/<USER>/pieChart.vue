<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'
import { parseTime } from  '@/utils'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    pieData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    pieData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        color: ['#168FFF', '#00BAB7', '#0D867F', '#8E5CE0', '#EEA02A'],
        tooltip: {
          trigger: 'item',
          // formatter: function(params) {
          //   console.log(params)
          //   return '1'
          // }
        },
        legend: {
          orient: 'vertical',
          right: 20,
          top: 'middle',
          itemWidth: 10,
          itemHeight: 10,
          icon: 'circle',
          textStyle: {
            color: '#fff'
          },
          data: this.pieData.map(item => item.name)
        },
        series: [
          {
            name: '供水总量',
            type: 'pie',
            radius: ['65%', '80%'],
            center: [50, '50%'],
            label: {
              show: false
            },
            data: this.pieData
          }
        ]
      })
    }
  }
}
</script>