<template>
  <div class="popupbox">
    <div class="closeBtn">
      <i class="el-icon-close close" @click="closepopup" />
    </div>
    <div class="popuptitle">
      {{ baseStationInfo.stationName  || "" }}
    </div>
    <div class="popupVillage">
      <div class="village">
        {{ baseStationInfo.villageName || "" }}
      </div>
      <div class="popupCoordinate">
        <span class="lonlat"> 经度：</span>
        <span class="lonlatdata">
          {{ parseFloat(coordinate.x).toFixed(2) || ""}}
        </span>
        <div class="coordinateLine"></div>
        <span class="lonlat">纬度:</span>
        <span class="lonlatdata">
          {{ parseFloat(coordinate.y).toFixed(2) || ""}}</span>
      </div>
    </div>
    <div class="infotab">
      <div :style="{ borderBottom: activetab === 1 ? '1px solid #83D1FF' : '0' }" @click="changetab(1)">
        水质监测
      </div>
      <div :style="{ borderBottom: activetab === 2 ? '1px solid #83D1FF' : '0' }" @click="changetab(2)">
        历史信息
      </div>
      <div :style="{ borderBottom: activetab === 3 ? '1px solid #83D1FF' : '0' }" @click="changetab(3)">
        基本信息
      </div>
    </div>
    <!--水质监测  -->
    <div style="margin: 20px" v-if="activetab === 1">
      <div class="watertitle">
        <span><img src="../../../assets/img/home/<USER>" alt="" /></span>
        <div class="watertitleContent">
          监测指标(当前水质: {{ timeMonitor.pass === true ? '达标' : '不达标' }})
        </div>
        <div class="waterMonTime">监测时间: {{ timeMonitor.monitorTime }}</div>
      </div>
      <div class="waterMonContent">
        <div class="waterMonItem" v-for="item in infodatalist" :key="item.id">
          <div :style="{ background: getLevelColor(item.level) }">
            {{ item.label }}
          </div>
          <div class="waterMondata">{{ item.value + item.unit }}</div>
        </div>
      </div>
    </div>
    <!-- 历史数据 -->
    <div v-if="activetab === 2" class="historyData">
      <div class="innerTitle">
        <span><img src="../../../assets/img/home/<USER>" alt="" /></span>
        <span  class="endUserTitle">末端用户监测指标</span>
      </div>
      <div class="timePicker">
        <el-date-picker
          v-model="historyDataTime"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          size="mini"
          style="width: 330px;"
        >
        </el-date-picker>
        <div class="report" @click="handleOk">
          确定
        </div>
      </div>
      <div class="timeShortcuts">
        <div
          class="paramItem"
          :class="{ selected: item.day === currentShortcut }"
          style="width: 48px"
          v-for="item in shortcuts"
          :key="item.value"
          @click="changeShortcut(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <LineChart1
        :seriesData="lineData"
        :xAxisData="xAxisData"
        :legendData="legendData"
        height="200px"
      />
    </div>
    <!-- 基本信息 -->
    <div v-if="activetab === 3" style="margin: 20px 10px 0 10px;">
      <div
        class="waterStatusInfo"
        v-for="item in waterStatusInfo"
        :key="item.id"
      >
        <div class="waterStatusInfoLabel">
          {{ item.label }}
        </div>
        <div style="margin-left: 15px;">{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import LineChart1 from "../chart/lineChart.vue";
import { getShortcutTs } from "@/utils/time";

import { getPointWqData, getPointWqHistoryData } from '@/api/home/<USER>'

export default {
  name: "WqPopup",
  props: {
    detaildata: {
      type: Object,
      default: () => {}
    }
  },
  components: { 
    LineChart1
  },
  data() {
    return {
      infodatalist: [],
      xAxisData: [],
      legendData: ["电导率", "余氯", "浊度"],
      waterStatusInfo:[],
      baseStationInfo: {
        stationName: "",
        villageName: "",
        stationTypeName:'',
      },
      timeMonitor:{
        monitorTime:"",
        pass:'',
      },
      coordinate: {
        x: '',
        y: '',
      },
      activetab: 1,
      historyDataTime: [],
      shortcuts: [
        {
          label: "24h",
          day: 1,
        },
        {
          label: "48h",
          day: 2,
        },
        {
          label: "近一周",
          day: 7,
        },
        {
          label: "近一月",
          day: 30,
        },
      ],
      currentShortcut: 1,
      lineData: [
        {
          name: "电导率",
          type: "line",
          symbol: 'none',
          // stack: 'Total',
          data: [],
        },
        {
          name: "",
          type: "line",
          symbol: 'none',
          // stack: 'Total',
          data: [],
        },
        {
          name: "浊度",
          type: "line",
          symbol: 'none',
          // stack: 'Total',
          data: [],
        },
      ],
    };
  },
  watch: {
    detaildata: {
      handler(val) {
        this.activetab = 1
        this.getData()
      },
      deep: true
    }
  },
  created() {
    this.historyDataTime = getShortcutTs()
  },
  mounted() {
    this.getData()
  },
  methods: {
    closepopup() {
      this.$emit("closePopup");
    },
    getData() {
      // 获取水质监测数据
      getPointWqData(this.detaildata.id, this.detaildata.sourceType).then(res => {
        console.log('获取水质监测数据', res)
        if(res.status === 200) {
          this.baseStationInfo = res.data.baseInfo
          this.coordinate= res.data.coordinate
          this.timeMonitor = res.data.timeMonitor
        }
        this.waterStatusInfo= [
          { label: "站点名称", value:this.baseStationInfo.stationName  },
          { label: "站码", value: this.baseStationInfo.stationCode },
          { label: "地址", value: this.baseStationInfo.addr},
          { label: "所在区域", value: this.baseStationInfo.villageName },
          { label: "所属流域", value: "-" },
          { label: "站点类型", value:this.baseStationInfo.stationTypeName},
          { label: "接入码(MN号)", value: this.baseStationInfo.mnCode },
          { label: "监测要素", value: this.baseStationInfo.monitorType },
          { label: "功能区水质目标", value:this.baseStationInfo.pass },
          { label: "站点控制级别", value: this.baseStationInfo.stationLevel },
        ]
        this.infodatalist = []
        this.infodatalist.push({ label: "电导率", value: this.timeMonitor.powerRate, unit: "uS/cm", level: "1" })
        if (this.detaildata.sourceType === 'WATER_FACTORY') {
          this.infodatalist.push({ label: "PH", value: this.timeMonitor.ph, unit: "", level: "2" })
        } else if (this.detaildata.sourceType === 'END_USER') {
          this.infodatalist.push({ label: "余氯", value: this.timeMonitor.chlorine, unit: "mg/L", level: "2" })
        }
        this.infodatalist.push(
          { label: "浊度", value: this.timeMonitor.turbidity, unit: "NTU", level: "3" },
        )
      })
    },
    getLevelColor(level) {
      switch (level) {
        case "1":
          return "#1DB887";
          break;
        case "2":
          return "#C6ECE1";
          break;
        case "3":
          return "#0098FF";
          break;
        case "4":
          return "#00F872";
          break;
        case "5":
          return "#FFF566";
          break;
        case "6":
          return "#FF9845";
          break;
      }
    },
    changetab(val) {
      this.activetab = val;
      if (val === 1) {

      } else if (val === 3) {

      } else if (val === 2) {
        // this.currentShortcut = 1
        // 设置图例
        if (this.detaildata.sourceType === 'WATER_FACTORY') {
          this.legendData = ["电导率", "ph", "浊度"]
        } else if (this.detaildata.sourceType === 'END_USER') {
          this.legendData = ["电导率", "余氯", "浊度"]
        }
        this.getLineData()
      }
    },

    handleOk() {
      if (!this.historyDataTime.length) {
        this.$message.warning("请选择时间");
        return;
      }
      this.currentShortcut = ''
      this.getLineData();
    },
    // 历史信息-点击时间
    changeShortcut(item) {
      this.currentShortcut = item.day;
      const ret = getShortcutTs(item.day);
      this.historyDataTime = [ret[0], ret[1]];
      this.getLineData();
    },
    getLineData() {
      let payload = {
        "facilityId": this.detaildata.id,
        // "facilityId": 3,
        "sourceType": this.detaildata.sourceType,
        "beginTime": this.historyDataTime[0],
        "endTime": this.historyDataTime[1]
      }
      getPointWqHistoryData(payload).then(res => {
        console.log('4',res);
        if(res.status === 200){
          this.lineData[0].data = res.data.powerRate
          if(this.detaildata.sourceType === 'WATER_FACTORY') {
            this.lineData[1].name = 'ph'
            this.lineData[1].data = res.data.ph
          } else if (this.detaildata.sourceType === 'END_USER') {
            this.lineData[1].name = '余氯'
            this.lineData[1].data = res.data.chlorine
          }
          this.lineData[2].data = res.data.turbidity
        }

      })
    },
   
  },
};
</script>

<style lang="scss">
.historyData {
  color: #ffffff;
  padding: 0 20px;

  .innerTitle {
    color: #d8f0ff;
    display: block;
    .endUserTitle{
      font-size: 14px;
      margin-left: 5px;
    }

  }

  .report {
    width: 48px;
    height: 28px;
    line-height: 28px; 
    margin-left: 10px;
    background: linear-gradient(360deg, #043474 0%, #0e4c9a 39%, #1382e6 99%);
    border: 1px solid #2a5dd2;
    border-radius: 4px;
    color: #ffffff;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
  }

  .paramList {
    display: flex;
  }

  .paramItem {
    width: 68px;
    height: 28px;
    border: 2px solid transparent;
    background: rgba(2, 50, 128, 0.8);
    font-size: 12px;
    text-align: center;
    line-height: 24px;
    margin-right: 10px;
    cursor: pointer;
  }

  .selected {
    border-color: #3ad2e7;
  }

  .timePicker {
    margin: 10px 0;
    display: flex;
  }

  .timeShortcuts {
    display: flex;
    // flex-direction: row-reverse;
    justify-content: flex-end;
  }
}

.popupbox {
  background: url("~@/assets/img/home/<USER>");
  background-size: 100%;
  width: 460px;
  height: 640px;
  // background: rgba(13, 40, 71, 0.8);
  border-radius: 0px 0px 0px 0px;
  // border: 1px solid rgba(0, 148, 249, 0.8);
  position: absolute;
  left: 30px;
  top: 120px;
  .closeBtn {
    position: absolute;
    right: 10px;
    top: 10px;
    color: white;
    cursor: pointer;
  }
  .popupVillage{
    display: flex;
    height: 50px;
    padding: 9px 19px 7px 19px;
    justify-content: space-between;
    .popuptitle {
      height: 51px;
      padding: 13px 0 12px 19px;
      font-size: 22px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      color: #d8f0ff;
      line-height: 26px;
      letter-spacing: 1px;
      text-shadow: 0px 0px 10px rgba(0, 145, 255, 0.5011), 0px 0px 4px #0091ff;
    }
    .village {
      font-size: 24px;
      font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
      font-weight: 700;
      color: #d8f0ff;
      line-height: 34px;
      letter-spacing: 1px;
    }
    .popupCoordinate{
      display: flex; 
      align-items: center;
      .lonlat {
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        color: #cccccc;
        line-height: 17px;
      }
      .coordinateLine{
        width: 0px;
        height: 17px;
        opacity: 1;
        border: 1px solid #ccecfc;
        margin: 0 20px;
      }
      .lonlatdata {
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        color: #0097fd;
        line-height: 16px;
        text-shadow: 0px 0px 4px #00a7ff;
      }
    }

    
  }

  .infotab {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-around;

    div {
      font-size: 16px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      color: #d8f0ff;
      line-height: 34px;
    }
  }

  .watertitle {
    margin: 10px;
    display: flex;
    align-items: center;

    img {
      width: 16px;
      height: 16px;
    }

    .watertitleContent {
      margin-left: 10px;
      // display: flex;
      color: #d8f0ff;
      font-size: 14px;
    }

    .waterMonTime {
      // line-height: 20px;
      margin-left: 10px;
      // display: flex;
      color: #dddddd;
      font-size: 12px;
    }
  }

  .waterMonContent {
    display: flex;
    margin: 10px;

    .waterMonItem {
      width: 90px;
      height: 45px;
      text-align: center;
      background: #073e69;
      margin: 10px;

      div {
        height: 20px;
        font-size: 12px;
        line-height: 20px;
      }

      .waterMondata {
        height: 25px;
        font-size: 14px;
        line-height: 25px;
        color: #d8f0ff;
      }
    }
  }
  .waterStatusInfo {
    div {
      padding-right: 10px;
    }
    background: rgba(4, 15, 45, 0.2);
    border-bottom: 1px solid #193967;
    display: flex;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    div {
      display: flex;
      color: #fff;
    }
    .waterStatusInfoLabel {
      width: 105px;
      justify-content: flex-end;
      color: #d8f0ff;
      background: rgba(0, 57, 112, 0.5);
    }
  }
}

.infoitem {
  display: flex;

  div {
    font-size: 12px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
    line-height: 32px;
  }

  :first-child {
    background: rgba(0, 57, 112, 0.5);
    border-radius: 0px 0px 0px 0px;
    width: 110px;
    padding-right: 10px;
    text-align: right;
  }

  :last-child {
    background: rgba(4, 15, 45, 0.2);
    border-radius: 0px 0px 0px 0px;
    padding-left: 10px;
    text-align: left;
  }
}
</style>