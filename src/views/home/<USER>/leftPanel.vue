<template>
  <div class="leftPanel">
    <div class="top card">
      <div class="title">供水概况</div>
      <div class="general">
        <div class="item">
          <div class="itemLabel">
            <img src="@/assets/img/support/water1.png" alt="" />
            <span>今日供水量</span>
          </div>
          <div class="itemValue">
            <span>{{ generalInfo.today.toFixed(2) }}</span>
            <span class="unit">m³</span>
          </div>
        </div>
        <div class="item">
          <div class="itemLabel">
            <img src="@/assets/img/support/water1.png" alt="" />
            <span>本月供水量</span>
          </div>
          <div class="itemValue">
            <span>{{ generalInfo.month.toFixed(2) }}</span>
            <span class="unit">m³</span>
          </div>
        </div>
        <div class="item">
          <div class="itemLabel">
            <img src="@/assets/img/support/water1.png" alt="" />
            <span>本年供水量</span>
          </div>
          <div class="itemValue">
            <span>{{ generalInfo.year.toFixed(2) }}</span>
            <span class="unit">m³</span>
          </div>
        </div>
      </div>
      <div class="chart-container">
        <LineChart
          :xAxisData="xMonth"
          :seriesData="seriesData"
          yAxisName="单位:m³"
        />
      </div>
    </div>

    <div class="middle card">
      <!-- <div class="title">供水调度</div>
      <div class="outerBox">
        <div class="innerBox">
          <div
            class="item"
            v-for="item in countList"
            :key="item.dispatchType.code"
          >
            <div class="itemLable">{{ item.dispatchType.name }}</div>
            <div>
              <span class="itemValue">{{ item.doneCount + '/' + item.totalCount }}</span>
              <span class="unit">起</span>
            </div>
          </div>
        </div>
      </div> -->
      <div class="title">规模化覆盖率</div>
      <div class="box">
        <div class="leftInfo">
          <span>当前覆盖率</span>
          <span
            style="color: #00FDFF;font-family: DIN;font-size: 40px;font-weight: bold;"
            >60.4%</span
          >
          <span style="color: #00FDFF;font-size: 14px;">【农村自来水普及率93.85%】</span>
        </div>
        <div style="height: 132px;width: 57%;">
          <GaugeChart
            :value="[60.4]"
            style="width: 100%;"
            height="263px"
            width="263px"
          />
        </div>
      </div>
    </div>

    <div class="bottom card">
      <div class="title">管网资产</div>
      <div class="km">
        <span style="color: #00FDFF;">管网公里数</span>
        <span style="opacity: 0.5;">km</span>
      </div>
      <div class="number">
        <div
          class="cell"
          v-for="(b, index) in formatterNumber(pipeNetworkLength)"
          :key="index"
        >
          {{ b }}
        </div>
      </div>
      <div class="chartBox">
        <!-- <BigPie
          width="50%"
          height="300px"
          title="管材"
          :seriesData="pieData1"
          :allLength="allLength"
        /> -->
        <BigPie
          title="管径"
          :seriesData="typeBarData"
          :allLength="allLength"
        />
      </div>
    </div>
  </div>
</template>

<script>
import LineChart from "./line";
import BigPie from "./bigPie";
import GaugeChart from "./gauge";

import { getPipeNetworkTypeLength } from "@/api/pipenetwork/screen";
import {
  factorySupplyDataNew,
  factorySupplyDataWithTimeNew,
} from "@/api/screenData";
import { getSupplyTypeCount } from "@/api/home/<USER>";

import { Decimal } from "decimal.js";

export default {
  // 供水左侧面板
  name: "LeftPanel",
  components: {
    LineChart,
    BigPie,
    GaugeChart
  },
  data() {
    return {
      // 供水概况
      generalInfo: {
        today: 0,
        month: 0,
        year: 0,
      },
      xMonth: [],
      seriesData: [
        {
          type: "line",
          color: "#FFD15C",
          symbol: "none",
          data: [],
        },
      ],
      // 供水调度
      countList: [],
      // 官网资产
      pipeNetworkLength: "2929.91",
      allLength: 0,
      pieData1: [],
      typeList: [],
      typeBarData: [],
    };
  },
  mounted() {
    this.getData();
  },
  beforeDestroy() {},
  methods: {
    getData() {
      // 统计
      factorySupplyDataNew().then((res) => {
        const target2 = res.data.find((item) => item.type === 2);
        this.generalInfo.today = target2.value;
        const target3 = res.data.find((item) => item.type === 3);
        this.generalInfo.month = target3.value;
        const target4 = res.data.find((item) => item.type === 4);
        this.generalInfo.year = target4.value;
      });
      // 曲线
      factorySupplyDataWithTimeNew(3).then((res) => {
        let temp = res.data.sort((a, b) => a.id - b.id);
        this.xMonth = temp.map((item) => item.timeValue.slice(5) + "月");
        this.seriesData[0].data = temp.map((item) => item.value);
      });
      const month = new Date().getMonth() + 1
      getSupplyTypeCount({ month }).then((res) => {
        this.countList = res.data;
      });
      getPipeNetworkTypeLength().then((res) => {
        const { status, data } = res;
        if (status === 200) {
          this.allLength = data.allLength;
          const allLength = data.allLength;
          // 材质统计
          this.pieData1 = data.material.map((item) => {
            return {
              name: item[0],
              value: item[1],
              length: Decimal(item[1])
                .div(1000)
                .toFixed(1),
            };
          });
          // 管径统计
          this.typeList = data.diameter.map((item) => {
            return {
              // name: '口径' + item[0],
              length: Decimal(allLength)
                .mul(item[1])
                .div(1000)
                .floor()
                .toNumber(),
            };
          });
          this.typeBarData = data.diameter.map((item) => {
            return {
              name: item[0],
              // percent: Decimal(item[1]).mul(100).toNumber(),
              // value: Decimal(item[1]).mul(100).toNumber(),
              value: Decimal(allLength)
                .mul(item[1])
                .div(1000)
                .floor()
                .toNumber(),
              length: Decimal(allLength)
                .mul(item[1])
                .div(1000)
                .floor()
                .toNumber(),
            };
          });
        }
      });
    },
    formatterNumber(num, totalLength = 10) {
      let str = num.toString();
      let zerosToAdd = totalLength - str.length;
      if (zerosToAdd > 0) {
        str = "0".repeat(zerosToAdd) + str;
      }
      return str;
    },
  },
};
</script>

<style lang="scss" scoped>
.leftPanel {
  position: absolute;
  left: 30px;
  height: 100%;
  width: 440px; /* 添加宽度以确保整体宽度一致性 */
  color: #ffffff;
  .card {
    background-color: rgba(4, 15, 45, 0.3);
    border: 1px solid rgba(23, 110, 217, 0.3);
    backdrop-filter: blur(16px);
    width: 100%; /* 确保卡片占满容器宽度 */
  }
  .top {
    width: 100%; /* 改为100%，确保相对于父容器的宽度 */
    height: calc((100% - 40px) * 0.3484);
    display: flex;
    flex-direction: column;

    .general {
      padding-top: 5px; /* 减小顶部内边距 */
      margin-bottom: 5px; /* 添加底部间距 */
      display: flex;
      justify-content: space-around;
      .itemValue {
        font-family: DIN;
        font-size: 24px;
        font-weight: 500;
        .unit {
          font-size: 14px;
          color: #ccc;
          font-weight: normal;
        }
      }
      .itemLabel {
        font-size: 14px;
        color: #31b3ff;
        display: flex;
        align-items: center;
      }
    }
  }
  .middle {
    margin-top: 20px;
    width: 100%;
    height: calc((100% - 40px) * 0.2378);
    display: flex; /* 添加flex布局 */
    flex-direction: column; /* 纵向排列 */

    .outerBox {
      padding: 5px 5px 0; /* 调整顶部和侧边内边距，移除底部内边距 */
      flex: 1; /* 让内容区域占满剩余空间 */
      display: flex; /* 添加flex布局 */
      flex-direction: column; /* 纵向排列 */
    }
    .innerBox {
      height: calc(100% - 10px); /* 调整高度，给底部留出空间 */
      margin-bottom: 5px; /* 底部边距 */
      background-image: url("~@/assets/img/support/bg1.png");
      background-size: 100% 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      .item {
        width: 50%;
        height: 74px;
        padding-left: 30px;
        font-size: 12px;
        .itemLable {
          font-weight: 500;
          padding-top: 5px;
        }
        .itemValue {
          font-family: DIN;
          font-size: 24px;
          color: #00fdff;
        }
        &:nth-child(even) {
          padding-left: 130px;
        }
      }
    }
    .box {
      flex: 1; /* 让内容区域占满剩余空间 */
      display: flex;
      align-items: center;
      padding: 5px 0; /* 添加上下内边距 */
      .leftInfo {
        width: 43%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .bottom {
    margin-top: 20px;
    overflow: hidden;
    width: 100%;
    height: calc((100% - 40px) * 0.4138);
    display: flex; /* 添加flex布局 */
    flex-direction: column; /* 纵向排列 */

    .km {
      padding: 3px 10px; /* 减小上下内边距 */
      display: flex;
      justify-content: space-between;
    }
    .number {
      display: flex;
      justify-content: space-around;
      .cell {
        height: 50px;
        width: 38px;
        border: 1px solid rgba(204, 204, 204, 0.5);
        background-color: #000616;
        border-radius: 6px;
        text-align: center;
        font-size: 30px;
        font-weight: bold;
        line-height: 48px;
        color: #2cecea;
        // margin-left: 5px;
      }
      margin-bottom: 5px; /* 添加底部边距 */
    }
    .chartBox {
      // display: flex;
      // align-items: center;
      flex: 1; /* 让图表区域自适应剩余高度 */
      min-height: 180px; /* 确保最小高度 */
      padding-bottom: 5px; /* 底部内边距 */
      height: calc(100% - 165px);
    }
  }
  .title {
    background: url("~@/assets/img/title.png") center no-repeat;
    background-size: 100% 100%;
    width: 100%; /* 改为100%，确保相对于父容器的宽度 */
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
  }
}
.chart-container {
  width: 100%;
  height: calc(100% - 90px); /* 给图表容器一个明确的高度 */
  min-height: 120px; /* 确保至少有这个高度 */
  margin-bottom: 5px; /* 减小底部边距 */
}
</style>
