<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    yAxisName: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption(
        {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            backgroundColor: 'rgba(2, 50, 128, 0.6)'
          },
          legend: {
            itemWidth: 10,
            itemHeight: 10,
            right: 10,
            top: 10,
            textStyle: {
              fontSize: 12,
              fontWeight: "normal",
              color: "#fff",
            },
            data: ['当日', '昨日']
          },
          grid: {
            top: 50,
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            // boundaryGap: false,
            axisLine: {
              show: false
            },
            axisLabel: {
              color: '#FFFFFF',
              rotate: 90
            },
            axisTick: {
              show: false
            },
            data: [
              "00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", 
              "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", 
              "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00"
            ]
          },
          yAxis: {
            type: 'value',
            axisLine: {
              show: false,
            },
            axisLabel: {
              color: '#FFFFFF',
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#00B3FF',
                type: 'dashed'
              }
            },
          },
          series: this.seriesData,
        },
        {
          notMerge: true
        }
      )
    }
  }
}
</script>
