<template>
  <div class="projectPopup">
    <div class="closeBtn">
      <i class="el-icon-close close" @click="closepopup" />
    </div>
    <div class="popupTitle">{{ baseStationInfo.name }}</div>
    <div class="popupVillage">
      <div class="village">{{ baseStationInfo.villageName || "" }}</div>
      <div class="popupCoordinate">
        <span class="lonlat"> 经度：</span>
        <span class="lonlatdata">
          {{ (baseStationInfo.coordinate.x).toFixed(2) || ""}}
        </span>
        <div class="coordinateLine"></div>
        <span class="lonlat">纬度:</span>
        <span class="lonlatdata">
          {{ (baseStationInfo.coordinate.y).toFixed(2) || ""}}
        </span>
      </div>
    </div>
    <div class="InfoDataBox">
      <div
        class="InfoData"
        v-for="item in infoDatalist"
        :key="item.id"
      >
        <div class="InfoDataLabel">{{ item.label }}</div>
        <div style="margin-left: 15px;">{{ item.value }}</div>
      </div>

    </div>
    <!-- <div class="imgBox">
      <div>点位照片</div>
        <img
          v-if="baseStationInfo.pic"
          :src="baseStationInfo.pic"
        />
    </div> -->
  </div>
</template>

<script>
import { getProjectDetailForOther } from '@/api/home/<USER>'

export default {
  name: 'ProjectPopupOther',
  props: {
    detaildata: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      baseStationInfo: {
        coordinate: {}
      },
      infoDatalist: [],
      type: {
        'HUNDRED_P': '百人供水工程',
        'THOUSAND_P': '千人供水工程',
        'THOUSAND_T' : '千吨万人供水工程'
      },
    }
  },
  mounted(){
    this.getList()
  },
  watch: {
    detaildata: {
      handler() {
        this.getList()
      },
      deep: true
    }
  },
  methods:{
    closepopup() {
      this.$emit("closePopup");
    },
    getList() {
      getProjectDetailForOther(this.detaildata.id).then(res => {
        if (res.status === 200) {
          this.baseStationInfo = res.data
          this.infoDatalist = [
            { label: '工程名称', value: this.baseStationInfo.name },
            { label: '工程类型', value: this.type[this.baseStationInfo.type] },
            { label: '设计供水规模(m³/d)', value: this.baseStationInfo.supplyScale },
            { label: '覆盖人口数', value: this.baseStationInfo.population },
            { label: '供水到户人口', value: this.baseStationInfo.supplyPopulation },
            { label: '供水到户户数', value: this.baseStationInfo.households },
          ]
        }
      })
    },

  },

}
</script>

<style lang="scss" scoped>
.projectPopup {
  background: url("~@/assets/img/home/<USER>");
  background-size: 100%;
  width: 460px;
  height: 640px;
  position: absolute;
  left: 30px;
  top: 120px;
  .closeBtn {
    position: absolute;
    right: 10px;
    top: 10px;
    color: white;
    cursor: pointer;
  }
  .popupTitle {
    height: 51px;
    padding: 13px 0 12px 19px;
    font-size: 22px;
    font-family: 'Microsoft YaHei';
    color: #d8f0ff;
    line-height: 26px;
    letter-spacing: 1px;
    text-shadow: 0px 0px 10px rgba(0, 145, 255, 0.5011), 0px 0px 4px #0091ff;
  }
  .popupVillage{
    display: flex;
    height: 50px;
    padding: 9px 19px 7px 19px;
    justify-content: space-between;
    .village {
      font-size: 24px;
      font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
      font-weight: 700;
      color: #d8f0ff;
      line-height: 34px;
      letter-spacing: 1px;
    }
    .popupCoordinate{
      display: flex; 
      align-items: center;
      .lonlat {
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        color: #cccccc;
        line-height: 17px;
      }
      .coordinateLine{
        width: 0px;
        height: 17px;
        opacity: 1;
        border: 1px solid #ccecfc;
        margin: 0 20px;
      }
      .lonlatdata {
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        color: #0097fd;
        line-height: 16px;
        text-shadow: 0px 0px 4px #00a7ff;
      }
    }

    
  }
  .InfoDataBox{
    margin: 20px 20px 0 20px;
    .InfoData {
      background: rgba(4, 15, 45, 0.2);
      border-bottom: 1px solid #193967;
      display: flex;
      height: 32px;
      line-height: 32px;
      font-size: 12px;
      div {
        padding-right: 10px;
        display: flex;
        color: #fff;
      }
      .InfoDataLabel {
        width: 125px;
        justify-content: flex-end;
        color: #d8f0ff;
        background: rgba(0, 57, 112, 0.5);
      }
    }
  }
  .imgBox {
    margin: 10px 20px 20px 20px;
    div {
      font-size: 12px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      color: #d8f0ff;
      line-height: 12px;
      margin-bottom: 10px;
    }
    img {
      width: 420px;
      height: 205px;
    }
  }

}
</style>