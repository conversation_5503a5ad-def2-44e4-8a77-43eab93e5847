<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    yAxisName: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption(
        {
          tooltip: {
            trigger: 'axis',
            // formatter: function(params) {
            //   console.log(params)
            //   return '1'
            // }
          },
          legend: {},
          grid: {
            top: 30,
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            boundaryGap: false,
            axisLine: {
              lineStyle: {
                color: '#666'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#B4C0CC'
            },
            splitLine: {
              show: false
            },
            data: this.xAxisData
          },
          yAxis: {
            name: this.yAxisName,
            nameTextStyle: {
              color: '#fff'
            },
            type: 'value',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#fff'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#35414D',
                type: 'dashed'
              }
            },
          },
          series: this.seriesData
        },
        {
          notMerge: true
        }
      )
    }
  }
}
</script>
