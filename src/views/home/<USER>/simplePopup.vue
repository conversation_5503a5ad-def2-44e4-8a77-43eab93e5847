<template>
  <div class="simplePopup">
    <span>{{ msg + ': ' +  speed }}</span>
  </div>
</template>

<script>
import { getAllMonitorData } from '@/api/home/<USER>'

export default {
  name: 'SimplePopup',
  props: {
    msg: {
      type: String,
      default: ''
    },
    id: {
      type: Number
    }
  },
  data() {
    return {
      speed: ''
    }
  },
  mounted() {
    if(this.id) {
      getAllMonitorData({
        facilityId: this.id,
        stationType: 'WATER_FACTORY'
      }).then(res => {
        let flowData = res.data.flowResps
        if(flowData.length) {
          // this.speed = `${ flow3in1.speed ? flow3in1.speed.toFixed(2) : '--' }m³/h`
          this.speed = `${flowData[0].speed}m³/h`
        } else {
          this.speed = '--m³/h'
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.simplePopup {
  padding: 5px;
  color: #fff;
  background-color: rgba(5, 58, 127, 0.5);
}
</style>