<template>
  <div class="layer">
    <div id="mapbox"></div>
    <!-- 底图切换 -->
    <div class="baseBox" @click="changeBase">
      <img src="@/assets/img/home/<USER>/earth.png" alt="">
    </div>
    <!-- 图层中心 -->
    <div class="control" v-if="!sitestate">
      <div class="analysis">
        <div class="title titleFlex">图层中心</div>
        <div class="content">
          <!-- <div style="display: flex; margin-top: 10px; height: 36px">
            <el-select
              v-model="value"
              placeholder="请选择"
              style="width: 180px; height: 36px"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-input
              placeholder="请输入站点名称"
              suffix-icon="el-icon-search"
              v-model="input"
              style="width: 200px; margin-left: 20px; height: 36px"
            >
            </el-input>
          </div> -->
          <!-- 管网监测 -->
          <div class="subtitle" @click="openpipe">管网监测</div>
          <div class="layerlabel">
            <div
              :class="item.state ? 'activebtn' : 'unactivebtn'"
              class="btn"
              v-for="(item, index) in pipelist"
              :key="item.id"
              :style="{ marginTop: index > 2 ? '25px' : '0' }"
              @click="changelayer(item)"
            >
              {{ item.label }}
            </div>
          </div>
          
          <!-- 视频监测 -->
          <!-- <div class="subtitle">视频监测</div>
          <div class="layerlabel">
            <div
              :class="item.state ? 'activebtn' : 'unactivebtn'"
              class="btn"
              v-for="(item, index) in videolist"
              :key="item.id"
              :style="{ marginTop: index > 2 ? '25px' : '0' }"
              @click="changelayer(item)"
            >
              {{ item.label }}
            </div>
          </div> -->
          <!-- 水情监测 -->
          <div class="subtitle">水情监测</div>
          <div class="layerlabel">
            <div
              :class="item.state ? 'activebtn' : 'unactivebtn'"
              class="btn"
              v-for="(item, index) in regimenlist"
              :key="item.id"
              :style="{ marginTop: index > 2 ? '25px' : '0' }"
              @click="changelayer(item)"
            >
              {{ item.label }}
            </div>
          </div>
          <div class="subtitle">水质监测</div>
          <div class="layerlabel">
            <div
              :class="item.state ? 'activebtn' : 'unactivebtn'"
              class="btn"
              v-for="(item, index) in qualitylist"
              :key="item.id"
              :style="{ marginTop: index > 2 ? '25px' : '0' }"
              @click="changelayer(item)"
            >
              {{ item.label }}
            </div>
          </div>
          <div class="subtitle">工程管理</div>
          <div class="layerlabel">
            <div
              :class="item.state ? 'activebtn' : 'unactivebtn'"
              class="btn"
              v-for="(item, index) in managerlist"
              :key="item.id"
              :style="{ marginTop: index > 2 ? '25px' : '0' }"
              @click="changelayer(item)"
            >
              {{ item.label }}
            </div>
          </div>
          <div class="subtitle">基本信息</div>
          <div class="layerlabel">
            <div
              :class="item.state ? 'activebtn' : 'unactivebtn'"
              class="btn"
              v-for="(item, index) in infolist"
              :key="item.id"
              :style="{ marginTop: index > 2 ? '25px' : '0' }"
              @click="changebaselayer(item)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 列表 -->
    <div class="site" v-if="sitestate">
      <div class="sitebox">
        <div class="title titleFlex">{{ siteItemName }}</div>
        <div class="content">
          <div
            style="
              display: flex;
              margin-top: 10px;
              height: 36px;
              margin-bottom: 20px;
            "
          >
            <el-input
              placeholder="请输入站点名称"
              suffix-icon="el-icon-search"
              v-model="searchText"
              style="width: 400px; height: 36px; margin-left: 10px"
              @input="handleSearch"
            >
            </el-input>
          </div>
          <div
            class="siteitem"
            v-for="item in sitelistTemp"
            :key="item.facilityId"
            :class="{ activesite: activeid === item.facilityId }"
            @click="changeactiveid(item)"
          >
            {{ item.facilityName }}
          </div>
        </div>
      </div>
    </div>
    <div class="back" @click="backcenter" v-show="popupstate"></div>
    <Popup1 :detaildata="detaildata" :stationId="stationId" v-if="popupstate === 'flow' || popupstate === 'pressure' || popupstate === 'level'" />
    <MeterPopup :meterId="meterId" :detaildata="detaildata" v-if="popupstate === 'meter'" />
    <VideoPopup :detaildata="detaildata" v-if="popupstate === 'factoryVideo' || popupstate === 'sourceVideo' || popupstate === 'storagePondVideo'" />
    <WqPopup :detaildata="detaildata" v-if="popupstate === 'pipenetwork'  || popupstate === 'factoryAndSource' || popupstate === 'endUser' || popupstate === 'labourMonitor'" />
    <ProjectPopup :detaildata="detaildata" v-if="popupstate === 'sourceProject'  || popupstate === 'factoryProject' || popupstate === 'storagePondProject'" />
  </div>
</template>

<script>
// 图层中心
import Popup1 from "./popup/popup1.vue";
import MeterPopup from './popup/meterPopup.vue'
import VideoPopup from './popup/videoPopup.vue'
import WqPopup from './popup/wqPopup.vue'
import ProjectPopup from './popup/projectPopup.vue'

import _ from 'loadsh'

import {
  getpoint,
  getinsmTypes,
  getwaterfactory,
  getstoragepond,
  getAllMonitorData,
  layermenu,
  getAllMonitorDatadetail
} from "@/api/home/<USER>";
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import Ground from "@arcgis/core/Ground";
import Graphic from "@arcgis/core/Graphic";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import TileInfo from "@arcgis/core/layers/support/TileInfo";
import WebTileLayer from "@arcgis/core/layers/WebTileLayer";
import WMSLayer from '@arcgis/core/layers/WMSLayer';
import WMSLayerInfo from '@arcgis/core/layers/WMSLayer';
import Extent from '@arcgis/core/geometry/Extent'
import Point from '@arcgis/core/geometry/Point';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';

export default {
  components: {
    Popup1,
    MeterPopup,
    VideoPopup,
    WqPopup,
    ProjectPopup
  },
  data() {
    return {
      isSatellite: false,
      geourl: '',
      activeid: null,
      sitelist: [],
      sitelistTemp: [],
      sitestate: false,
      map: null,
      view: null,
      options: [],
      value: "",
      searchText: "",
      handleSearch: null,
      pipelist: [
        {
          id: 1,
          label: "流量监测",
          src: "",
          state: true,
          value: "flow",
        },
        {
          id: 2,
          label: "压力监测",
          src: "",
          state: false,
          value: "pressure",
        },
        {
          id: 3,
          label: "液位监测",
          src: "",
          state: false,
          value: "level",
        },
        {
          id: 4,
          label: "水表抄表",
          src: "",
          state: false,
          value: "meter"
        },
      ],
      videolist: [
        {
          id: 21,
          label: "水厂视频",
          src: "",
          state: false,
          value: "factoryVideo",
        },
        {
          id: 22,
          label: "水源地视频",
          src: "",
          state: false,
          value: "sourceVideo",
        },
        {
          id: 23,
          label: "蓄水池视频",
          src: "",
          state: false,
          value: "storagePondVideo",
        },
      ],
      regimenlist: [
        {
          id: 31,
          label: "水文一体化监测",
          src: "",
          state: false,
          value: "hydro"
        },
      ],
      qualitylist: [
        {
          id: 41,
          label: "水源水厂水质",
          src: "",
          state: false,
          value: 'factoryAndSource'
        },
        // {
        //   id: 42,
        //   label: "管网水质",
        //   src: "",
        //   state: false,
        //   value: 'pipenetwork'
        // },
        {
          id: 43,
          label: "末端用户水质",
          src: "",
          state: false,
          value: 'endUser'
        },
        {
          id: 44,
          label: "人工监测水质",
          src: "",
          state: false,
          value: 'labourMonitor'
        },
      ],
      managerlist: [
        {
          id: 1,
          label: "水源工程",
          src: "",
          state: false,
          value: 'sourceProject'
        },
        {
          id: 2,
          label: "水厂工程",
          src: "",
          state: false,
          value: 'factoryProject'
        },
        {
          id: 3,
          label: "蓄水工程",
          src: "",
          state: false,
          value: 'storagePondProject'
        },
        // {
        //   id: 4,
        //   label: "净水工程",
        //   src: "",
        //   state: false,
        // },
        // {
        //   id: 5,
        //   label: "管道工程",
        //   src: "",
        //   state: false,
        // },
      ],
      infolist: [
        {
          id: 61,
          label: "管网分布",
          src: "",
          state: true,
          value: "main_pipe_network"
        },
        {
          id: 62,
          label: "水系图",
          src: "",
          state: true,
          value: "river_track"
        },
        {
          id: 63,
          label: "行政区",
          src: "",
          state: true,
          value: "xingzheng"
        },
      ],
      pointdata: {},
      /* 图层 */
      // 管网监测
      flowLayer: null,
      levelLayer: null,
      pressureLayer: null,
      meterLayer: null,
      meterlayerView: null,
      highlightHandle: null,
      // 视频监测
      factoryVideoLayer: null,
      sourceVideoLayer: null,
      storagePondVideoLayer: null,
      // 水情监测
      hydroGraphicLayer: null,
      // 水质监测
      factoryAndSourceLayer: null,
      pipenetworkLayer: null,
      endUserLayer: null,
      labourMonitorLayer: null,
      // 工程管理
      sourceProjectLayer: null,
      factoryProjectLayer: null,
      storagePondProjectLayer: null,
      projectMap: {
        'sourceProject': 'water-source',
        'factoryProject': 'water-factory',
        'storagePondProject': 'water-storage-pond'
      },
      // 天地图底图
      imageNoteLayer: null,
      // 基本信息图层
      baseLayer: null,

      alllayers: [],
      popupstate: false,
      detaildata: {},
      meterId: null,
      currentShortcut: "",
      requestBody: {
        sourceType: "",
        facilityId: null,
        beginTime: "",
        endTime: "",
      },
      lineData: [],
      pipestate: false,
      lods: [
        // 定义平铺方案的详细级别数组。
        {
          level: 2,
          levelValue: 2,
          resolution: 0.3515625,
          scale: 147748796.52937502,
        },
        {
          level: 3,
          levelValue: 3,
          resolution: 0.17578125,
          scale: 73874398.264687508,
        },
        {
          level: 4,
          levelValue: 4,
          resolution: 0.087890625,
          scale: 36937199.132343754,
        },
        {
          level: 5,
          levelValue: 5,
          resolution: 0.0439453125,
          scale: 18468599.566171877,
        },
        {
          level: 6,
          levelValue: 6,
          resolution: 0.02197265625,
          scale: 9234299.7830859385,
        },
        {
          level: 7,
          levelValue: 7,
          resolution: 0.010986328125,
          scale: 4617149.8915429693,
        },
        {
          level: 8,
          levelValue: 8,
          resolution: 0.0054931640625,
          scale: 2308574.9457714846,
        },
        {
          level: 9,
          levelValue: 9,
          resolution: 0.00274658203125,
          scale: 1154287.4728857423,
        },
        {
          level: 10,
          levelValue: 10,
          resolution: 0.001373291015625,
          scale: 577143.73644287116,
        },
        {
          level: 11,
          levelValue: 11,
          resolution: 0.0006866455078125,
          scale: 288571.86822143558,
        },
        {
          level: 12,
          levelValue: 12,
          resolution: 0.00034332275390625,
          scale: 144285.93411071779,
        },
        {
          level: 13,
          levelValue: 13,
          resolution: 0.000171661376953125,
          scale: 72142.967055358895,
        },
        {
          level: 14,
          levelValue: 14,
          resolution: 8.58306884765625e-5,
          scale: 36071.483527679447,
        },
        {
          level: 15,
          levelValue: 15,
          resolution: 4.291534423828125e-5,
          scale: 18035.741763839724,
        },
        {
          level: 16,
          levelValue: 16,
          resolution: 2.1457672119140625e-5,
          scale: 9017.8708819198619,
        },
        {
          level: 17,
          levelValue: 17,
          resolution: 1.0728836059570313e-5,
          scale: 4508.9354409599309,
        },
        {
          level: 18,
          levelValue: 18,
          resolution: 5.3644180297851563e-6,
          scale: 2254.4677204799655,
        },
        {
          level: 19,
          levelValue: 19,
          resolution: 2.68220901489257815e-6,
          scale: 1127.23386023998275,
        },
        {
          level: 20,
          levelValue: 20,
          resolution: 1.341104507446289075e-6,
          scale: 563.616930119991375,
        },
      ],
    };
  },
  computed: {
    siteItemName() {
      let temp = ''
      switch(this.popupstate) {
        case 'flow':
        case 'pressure':
        case 'level':
        case 'meter':
          temp = '管网监测列表'
          break
        case 'factoryVideo':
        case 'sourceVideo':
        case 'storagePondVideo':
          temp = '视频监测列表'
          break
        case 'hydro':
          temp = '水情监测列表'
          break
        case 'factoryAndSource':
        case 'pipenetwork':
        case 'endUser':
        case 'labourMonitor':
          temp = '水质监测列表'
          break
        case 'sourceProject':
        case 'factoryProject':
        case 'storagePondProject':
          temp = '工程管理列表'
          break
        default: temp = '列表'
      }
      return temp
    },
    filterSitelist() {
      
    }
  },
  watch: {},
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.getmenu();
    this.handleSearch = _.debounce(() => {
      this.getInputValue()
    }, 1000)
  },
  methods: {
    // 获取管网监测点位数据
    async getmenu() {
      await layermenu().then((res) => {
        // 管网监测点位
        this.pointdata.flow = res.data.pipeMonitoringResp.waterFlow;
        this.pointdata.level = res.data.pipeMonitoringResp.waterLevel;
        this.pointdata.pressure = res.data.pipeMonitoringResp.waterPressure;
        this.pointdata.meter = res.data.pipeMonitoringResp.waterMeter

        // 视频监测点位
        this.pointdata.factory = res.data.videoMonitorResp.factory
        this.pointdata.source = res.data.videoMonitorResp.source
        this.pointdata.storagePond = res.data.videoMonitorResp.storagePond

        // 水情监测
        this.pointdata.hydrographic = res.data.hydrographicResp.hydrographic

        // 水质监测
        this.pointdata.factoryAndSource = res.data.waterQualityMonitoringResp.factoryAndSource
        this.pointdata.pipenetwork = res.data.waterQualityMonitoringResp.pipenetwork
        this.pointdata.endUser = res.data.waterQualityMonitoringResp.endUser
        this.pointdata.labourMonitor = res.data.waterQualityMonitoringResp.labourMonitor

        // 工程管理
        this.pointdata.factoryProject = res.data.waterSupplyProjectResp.waterFactoryRespList
        this.pointdata.sourceProject = res.data.waterSupplyProjectResp.waterSourceRespList
        this.pointdata.storagePondProject = res.data.waterSupplyProjectResp.waterStoragePondRespList
      });
      this.createmap();
    },
    getInputValue() {
      // 过滤
      this.sitelistTemp = this.sitelist.filter(item => item.facilityName.includes(this.searchText))
    },
    // 创建地图
    createmap() {
      //切片方案
      let tileInfo = new TileInfo({
        dpi: 90.71428571427429, // 切片方案的每英寸点数（即像素）。
        size: 256, // 设置每个瓷砖的高度和宽度为[256, 256]像素。
        origin: {
          // 切片方案的原点。
          type: "point",
          x: -180,
          y: 90,
          spatialReference: { wkid: 4326 },
        },
        spatialReference: { wkid: 4326 },
        lods: this.lods
      });
      // 注记图层(天地图)
      this.imageNoteLayer = new WebTileLayer({
        id: "imageNoteMap",
        title: "imageNoteMap",
        urlTemplate:
          "http://{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6",
        subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
        // tileInfo: tileInfo,
        // spatialReference: { wkid: 4326 },
      });
      // mapbox 卫星瓦片
      this.mapBoxTileLayer = new WebTileLayer({
        id: 'mapBoxTileLayer',
        name: 'mapBoxTileLayer',
        urlTemplate: 'https://api.mapbox.com/v4/mapbox.satellite/{level}/{col}/{row}.webp?access_token=pk.eyJ1IjoidGFuZ2d1b2NoYW5nIiwiYSI6ImNtNzE4dGYxdTA0aHUya3B4dWViM3l3cWsifQ.awtc46Zfl0VCpgNd5JCFXQ',
        visible: false
      })

      // 基本信息图层(geoserver发布)
      this.baseLayer = new WMSLayer({
        id: 'baseInfoLayer',
        url: this.geourl + '/geoserver/xingshan/wms',
        sublayers: [
          {
            name: 'river_track',
            visible: true
          },
          {
            name: 'xingzheng',
            visible: true
          },
          {
            name: 'main_pipe_network',
            visible: true
          }
        ],
        visible: true
      })
      this.map = new Map({
        // 背景透明
        // ground: new Ground({
        //   layers: [],
        //   surfaceColor: "transparent",
        //   opacity: 0,
        // }),
        basemap: {
          baseLayers: [
            this.imageNoteLayer
          ]
        }
      });
      this.view = new MapView({
        container: "mapbox",
        map: this.map,
        background: {
          type: "color",
          color: [255, 252, 244, 0],
        },
        // 地图默认定位范围
        extent: {
          xmax: 110.82556434056983,
          xmin: 110.74325912092598,
          ymax: 31.176874262674232,
          ymin: 31.1399655157402,
        },
        // spatialReference: {
        //   wkid: 4326,
        // },
        // constraints: {
        //   lods: this.lods
        // }
      });
      // 去掉地图多余组件
      this.view.ui.remove("attribution");
      this.view.ui.empty("top-left");
      const pt = new Point({
        latitude: 31.25,
        longitude: 110.80
      })
      this.view.goTo({
        target: pt,
        zoom: 12
      })

      // 地图创建结束后调用
      this.view.when((e) => {
        // this.view.map.add(this.imageNoteLayer); // tdt
        this.view.map.add(this.mapBoxTileLayer) // 卫星影像
        // 管网监测
        this.flowLayer = this.createfealayer(
          this.pointdata.flow,
          "flow",
          "maphome1",
          true
        );
        this.levelLayer = this.createfealayer(
          this.pointdata.level,
          "level",
          "maphome2"
        );
        this.pressureLayer = this.createfealayer(
          this.pointdata.pressure,
          "pressure",
          "maphome3"
        );
        this.meterLayer = this.createfealayer(
          this.pointdata.meter,
          "meter",
          "maphome4"
        );
        // 视频监测
        this.factoryVideoLayer = this.createfealayer(
          this.pointdata.factory,
          "factoryVideo",
          "maphome5"
        );
        this.sourceVideoLayer = this.createfealayer(
          this.pointdata.source,
          "sourceVideo",
          "maphome6"
        );
        this.storagePondVideoLayer = this.createfealayer(
          this.pointdata.storagePond,
          "storagePondVideo",
          "maphome7"
        );
        // 水情监测
        this.hydroGraphicLayer = this.createfealayer(
          this.pointdata.hydrographic,
          "hydro",
          "maphome8"
        );
        // 水质监测
        this.factoryAndSourceLayer = this.createfealayer(
          this.pointdata.factoryAndSource,
          "factoryAndSource",
          "maphome9"
        );
        // this.pipenetworkLayer = this.createfealayer(
        //   this.pointdata.pipenetwork,
        //   "pipenetwork",
        //   "maphome10"
        // );
        this.endUserLayer = this.createfealayer(
          this.pointdata.endUser,
          "endUser",
          "maphome11"
        );
        this.labourMonitorLayer = this.createfealayer(
          this.pointdata.labourMonitor,
          "labourMonitor",
          "maphome12"
        );
        // 工程管理
        this.sourceProjectLayer = this.createfealayer(
          this.pointdata.sourceProject,
          "sourceProject",
          "maphome13"
        );
        this.factoryProjectLayer = this.createfealayer(
          this.pointdata.factoryProject,
          "factoryProject",
          "maphome14"
        );
        this.storagePondProjectLayer = this.createfealayer(
          this.pointdata.storagePondProject,
          "storagePondProject",
          "maphome15"
        );

        // 图层处理，放在同一个数组内，方便操作显隐
        this.alllayers.push(this.flowLayer);
        this.alllayers.push(this.levelLayer);
        this.alllayers.push(this.pressureLayer);
        this.alllayers.push(this.meterLayer)
        this.alllayers.push(this.factoryVideoLayer)
        this.alllayers.push(this.sourceVideoLayer)
        this.alllayers.push(this.storagePondVideoLayer)
        this.alllayers.push(this.hydroGraphicLayer)
        this.alllayers.push(this.factoryAndSourceLayer)
        // this.alllayers.push(this.pipenetworkLayer)
        this.alllayers.push(this.endUserLayer)
        this.alllayers.push(this.labourMonitorLayer)
        this.alllayers.push(this.sourceProjectLayer)
        this.alllayers.push(this.factoryProjectLayer)
        this.alllayers.push(this.storagePondProjectLayer)

        this.view.map.add(this.baseLayer)

        // 图层全部添加进地图
        this.view.map.add(this.flowLayer);
        this.view.map.add(this.levelLayer);
        this.view.map.add(this.pressureLayer);
        this.view.map.add(this.meterLayer);
        // this.view.whenLayerView(this.meterLayer).then(layerView => {
        //   this.meterlayerView = layerView
        // })
        this.view.map.add(this.factoryVideoLayer);
        this.view.map.add(this.sourceVideoLayer);
        this.view.map.add(this.storagePondVideoLayer);
        this.view.map.add(this.hydroGraphicLayer);
        this.view.map.add(this.factoryAndSourceLayer);
        // this.view.map.add(this.pipenetworkLayer);
        this.view.map.add(this.endUserLayer);
        this.view.map.add(this.labourMonitorLayer);
        this.view.map.add(this.sourceProjectLayer);
        this.view.map.add(this.factoryProjectLayer);
        this.view.map.add(this.storagePondProjectLayer);
      });
      // 地图点击
      this.view.on("click", (e) => {
        // 查询当前点击位置是否有点位
        this.view.hitTest(e).then((res) => {
          // res.results所有结果，数组，可能有多个结果。越近在前面。
          let graphicOne = res.results[0].graphic
          if (res.results[0]) {
            res.results.forEach((ele, idx) => {
              if (
                idx > 0 &&
                ele.graphic.attributes.facilityId ===
                  res.results[0].graphic.attributes.facilityId
              ) {
                res.results[0].graphic.attributes.extra =
                  res.results[0].graphic.attributes.extra +
                  "," +
                  ele.graphic.attributes.extra;
              }
            });
            console.log(res.results[0]);
            // 定位
            this.view.goTo(res.results[0].graphic);
            this.getsitelist(res.results[0]);

            // 点击高亮
            if(graphicOne.geometry.type === 'point') {
              // if(this.highlightHandle) this.highlightHandle.remove()
              // this.view.whenLayerView(graphicOne.layer).then(layerView => {
              //   this.highlightHandle = layerView.highlight(graphicOne)
              // })
              // console.log(graphicOne)

              this.view.graphics.removeAll()
              let graphic = new Graphic({
                geometry: graphicOne.geometry,
                symbol: {
                  type: 'simple-marker',
                  style: 'circle',
                  color: [255, 0, 0, 1],
                  size: 16,
                  yoffset: 1
                }
              })
              this.view.graphics.add(graphic)

            }
          }
        });
      });
    },
    // 切换底图
    changeBase() {
      this.isSatellite = !this.isSatellite
      console.log(this.mapBoxTileLayer)
      if(this.isSatellite) {
        this.mapBoxTileLayer.visible = true
      } else {
        this.mapBoxTileLayer.visible = false
      }
    },
    // 管网监测全选
    openpipe() {
      this.pipestate = !this.pipestate;
      this.pipelist.forEach((e) => {
        e.state = this.pipestate;
      });
      this.alllayers.forEach((e) => {
        if (e.id === "flow" || e.id === "level" || e.id === "pressure") {
          e.visible = this.pipestate;
        }
      });
    },
    // 关闭左侧弹窗
    backcenter() {
      this.sitestate = false;
      this.popupstate = false;
      this.searchText = ''
    },
    // 点击点位获取信息，打开弹窗
    getdetail(val) {
      console.log('getdetail', val)
      // 地图定位到当前点位所在经纬度
      if (val.coordinate) {
        //创建点
        let point = {
          type: "point",
          latitude: val.coordinate.y,
          longitude: val.coordinate.x,
        };
        let graphic = new Graphic({
          geometry: point,
        });
        this.view.goTo(graphic);
      }
      let params = {
        type: val.extra,
      };
      this.stationId = {
        facilityId: val.facilityId,
        stationType: val.sourceType,
        type: val.extra
      }
      /* 以下为 popup 按条件打开 */
      // 管网监测 flow pressure level
      if(val.datatype === 'flow' || val.datatype === 'pressure' || val.datatype === 'level') {
        getAllMonitorDatadetail(params, val.facilityId, val.sourceType).then(
          (res) => {
            this.detaildata = res.data;
            this.popupstate = val.datatype;
          }
        );
      }
      // 管网监测 水表抄表      
      if(val.extra === 'WATER_METER') {
        this.detaildata = {
          meterTitle: val.facilityName
        }
        this.meterId = val.facilityId
        this.popupstate = val.datatype
      }
      // 视频监测
      if(val.datatype.includes('Video')) {
        this.popupstate = val.datatype
        this.detaildata = {
          videoCode: val.videoCode,
          videoName: val.facilityName
        }
      }
      // 水情监测
      if(val.datatype === 'hydro') {
        this.popupstate = val.datatype
      }
      // 水质监测
      if(val.datatype === 'pipenetwork'  || val.datatype === 'factoryAndSource' || val.datatype === 'endUser' || val.datatype === 'labourMonitor') {
        this.detaildata = {
          id: val.facilityId,
          sourceType: val.sourceType,
          datatype: val.datatype
        }
        this.popupstate = val.datatype
      }
      // 工程管理
      if(val.datatype === 'sourceProject'  || val.datatype === 'factoryProject' || val.datatype === 'storagePondProject') {
        this.detaildata = {
          type: this.projectMap[val.datatype],
          id: val.facilityId
        }
        this.popupstate = val.datatype
      }

    },
    // 切换图层显隐
    changelayer(val) {
      val.state = !val.state;
      // 图层切换
      this.alllayers.forEach((layer) => {
        if (layer.id === val.value) {
          layer.visible = val.state;
        }
      });
    },
    // 基本信息子图层切换
    changebaselayer(item) {
      item.state = !item.state
      this.baseLayer.findSublayerByName(item.value).visible = item.state
    },

    // 当前选择的点位所在的图层列表
    getsitelist(data) {
      this.sitestate = true;
      this.activeid = data.graphic.attributes.facilityId;
      this.getdetail(data.graphic.attributes);
      console.log('点位graphic', data.graphic.attributes)
      switch (data.graphic.attributes.datatype) {
        case "flow":
          this.sitelist = this.sitelistTemp = this.pointdata.flow.map(item => {
            return {
              ...item,
              datatype: 'flow'
            }
          });
          break;
        case "level":
          this.sitelist = this.sitelistTemp = this.pointdata.level.map(item => {
            return {
              ...item,
              datatype: 'level'
            }
          });
          break;
        case "pressure":
          this.sitelist = this.sitelistTemp = this.pointdata.pressure.map(item => {
            return {
              ...item,
              datatype: 'pressure'
            }
          });
          break;
        case "meter":
          this.sitelist = this.sitelistTemp = this.pointdata.meter.map(item => {
            return {
              ...item,
              datatype: 'meter'
            }
          });
          break;
        case "factoryVideo":
          this.sitelist = this.sitelistTemp = this.pointdata.factory.map(item => {
            return {
              ...item,
              datatype: 'factoryVideo'
            }
          });
          break;
        case "sourceVideo":
          this.sitelist = this.sitelistTemp = this.pointdata.source.map(item => {
            return {
              ...item,
              datatype: 'sourceVideo'
            }
          });
          break;
        case "storagePondVideo":
          this.sitelist = this.sitelistTemp = this.pointdata.storagePond.map(item => {
            return {
              ...item,
              datatype: 'storagePondVideo'
            }
          });;
          break;
        case "hydro":
          this.sitelist = this.sitelistTemp = this.pointdata.hydrographic;
          break;
        case "factoryAndSource":
          this.sitelist = this.sitelistTemp = this.pointdata.factoryAndSource.map(item => {
            return {
              ...item,
              datatype: 'factoryAndSource'
            }
          });
          break;
        case "pipenetwork":
          this.sitelist = this.sitelistTemp = this.pointdata.pipenetwork.map(item => {
            return {
              ...item,
              datatype: 'pipenetwork'
            }
          });
          break;
        case "endUser":
          this.sitelist = this.sitelistTemp = this.pointdata.endUser.map(item => {
            return {
              ...item,
              datatype: 'endUser'
            }
          });
          break;
        case "labourMonitor":
          this.sitelist = this.sitelistTemp = this.pointdata.labourMonitor.map(item => {
            return {
              ...item,
              datatype: 'labourMonitor'
            }
          });
          break;
        case "sourceProject":
          this.sitelist = this.sitelistTemp = this.pointdata.sourceProject.map(item => {
            return {
              ...item,
              datatype: 'sourceProject'
            }
          });
          break;
        case "factoryProject":
          this.sitelist = this.sitelistTemp = this.pointdata.factoryProject.map(item => {
            return {
              ...item,
              datatype: 'factoryProject'
            }
          });
          break;
        case "storagePondProject":
          this.sitelist = this.sitelistTemp = this.pointdata.storagePondProject.map(item => {
            return {
              ...item,
              datatype: 'storagePondProject'
            }
          });
          break;
      }
    },
    // 点位列表点击
    changeactiveid(item) {
      this.activeid = item.facilityId;
      this.getdetail(item);
      console.log('右侧列表', item)
      this.popupstate = item.datatype;

      // 点击列表项高亮水表图层对应要素
      let targetLayer = this[item.datatype + 'Layer']
      console.log(targetLayer)
      let query = targetLayer.createQuery()
      query.where = `facilityId = ${item.facilityId}`
      query.outFields = ['*']


      this.view.whenLayerView(targetLayer).then(layerView => {
        this.meterlayerView = layerView
        targetLayer.queryFeatures(query).then(result => {
          let featureOne = result.features[0]
          // if (this.highlightHandle) this.highlightHandle.remove()
          // this.highlightHandle = this.meterlayerView.highlight(featureOne)

          this.view.graphics.removeAll()
          let graphic = new Graphic({
            geometry: featureOne.geometry,
            symbol: {
              type: 'simple-marker',
              style: 'circle',
              color: [255, 0, 0, 1],
              size: 16,
              yoffset: 1
            }
          })
          this.view.graphics.add(graphic)
        })
      })

      


      // let query = this.meterLayer.createQuery()
      // query.where = `facilityId = ${item.facilityId}`
      // query.outFields = ['*']
      // this.meterLayer.queryFeatures(query).then(result => {
      //   let featureOne = result.features[0]

      //   if (this.highlightHandle) this.highlightHandle.remove()
      //   this.highlightHandle = this.meterlayerView.highlight(featureOne)
      // })
    },
    // 创建业务图层
    createfealayer(data, id, icon, visible = false) {
      let graphics = [];
      data.forEach((item) => {
        if(item.coordinate) {
        let point = {
          type: "point", // autocasts as new Point()
          longitude: item.coordinate.x,
          latitude: item.coordinate.y,
        };
        let symbol = {
          type: "picture-marker",
          url: require(`../../assets/img/home/<USER>
          width: "20px",
          height: "24px",
        };
        const attributes = {
          datatype: id,
          facilityId: item.facilityId,
          sourceType: item.sourceType,
          extra: item.extra,
          facilityName: item.facilityName,
          videoCode: item.videoCode
        }
        let graphic = new Graphic({
          geometry: point,
          symbol: symbol,
          attributes
        });
        graphics.push(graphic);
        }
      });
      const fields = [
        {
          name: "datatype",
          type: "string",
        },
        {
          name: "facilityId",
          type: "integer",
        },
        {
          name: "sourceType",
          type: "string",
        },
        {
          name: "extra",
          type: "string",
        },
        {
          name: "facilityName",
          type: "string",
        },
      ]
      if(id.includes('Video')) {
        fields.push({
          name: "videoCode",
          type: "string",
        })
      }
      return new FeatureLayer({
        source: graphics,
        objectIdField: "OBJECTID",
        outFields: "*",
        id: id,
        fields: fields,
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require(`../../assets/img/home/<USER>
            width: "20px",
            height: "24px",
          },
        },
        visible: visible,
      });
    },
  },
};
</script>

<style lang="scss">
.layer {
  .el-range-input {
    background-color: transparent;
    color: #ffffff;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
</style>

<style scoped lang="scss">
.activesite {
  background: linear-gradient(
    180deg,
    rgba(67, 51, 0, 0.5) 0%,
    rgba(135, 104, 7, 0.5) 100%
  ) !important;
}
#mapbox {
  width: 100%;
  height: 100%;
  position: absolute;
}
.baseBox {
  position: absolute;
  top: 20px;
  right: 490px;
  cursor: pointer;
  img {
    width: 35px;
    height: 35px;
  }
}
.layerlabel {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  cursor: pointer;
}
.btn {
  font-size: 14px;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  color: #ffffff;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 36px;
}
.activebtn {
  background: url("../../assets/img/home/<USER>") center no-repeat;
}
.unactivebtn {
  background: url("../../assets/img/home/<USER>") center no-repeat;
}
.el-select .el-input {
  ::v-deep.el-input__inner {
    height: 36px;
    line-height: 36px;
  }
}
.back {
  background: url("~@/assets/img/home/<USER>");
  width: 120px;
  height: 37px;
  position: absolute;
  top: 50px;
  left: 30px;
}
.layer {
  height: 100%;
  .title {
    background: url("../../assets/img/title.png") center no-repeat;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
    padding-right: 30px;
  }
  .subtitle {
    font-size: 16px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    color: #d8f0ff;
    line-height: 24px;
    text-shadow: 0px 0px 10px rgba(0, 145, 255, 0.5011), 0px 0px 4px #0091ff;
    margin: 15px 0 11px 0;
    cursor: pointer;
  }
  .control {
    width: 440px;
    position: absolute;
    right: 30px;
    height: 100%;
    .analysis {
      width: 440px;
      height: 100%;
      .content {
        height: calc(100% - 42px);
        padding: 5px 10px;
        background-color: rgba(4, 15, 45, 0.3);
      }
    }
  }
  .site {
    width: 440px;
    position: absolute;
    right: 30px;
    height: 100%;
    z-index: 999;
    .sitebox {
      width: 440px;
      height: 100%;
      .content {
        height: calc(100% - 42px);
        padding: 5px 10px;
        background-color: rgba(4, 15, 45, 0.3);
        overflow-y: auto;
        .siteitem {
          margin-bottom: 10px;
          margin-left: 10px;
          width: 400px;
          height: 36px;
          background: linear-gradient(
            180deg,
            rgba(17, 62, 102, 0.5) 0%,
            rgba(2, 50, 128, 0.5) 100%
          );
          border: 1px solid #1377d0;
          font-size: 14px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
          line-height: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }
      .content::-webkit-scrollbar {
        width: 4px;
      }
      .content::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: rgba(0, 0, 0, 0.2);
      }
      .content::-webkit-scrollbar-track {
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 0;
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>