<template>
  <div>
    <div id="mars3dContainer" class="mars3d-container homeMapContainer"></div>
  </div>
</template>
    
<script>
export default {
  data() {
    return {
      deepHeight: 100,
      map: null,
      tmd: 0.5,
      opean: true,
      mapOptions: {
        scene: {
          //默认视角参数
          center: {
            lat: 31.320435,
            lng: 110.768503,
            alt: 149000,
            heading: 0,
            pitch: -90,
          },
          // extent: {
          //   xmin: 110.425,
          //   xmax: 111.116,
          //   ymin: 31.09,
          //   ymax: 31.56,
          // },
          cameraController: {
            // 滚轮缩小最大高度
            maximumZoomDistance: 200000,
          },
          backgroundColor: "#013453",
          sceneMode: 2,
          globe: {
            baseColor: " #363635", // 地球地面背景色
            // showGroundAtmosphere: false,
            // enableLighting: false
          },
        },
        control: {
          homeButton: true, // 视角复位按钮
        },
        basemaps: [
          { id: 10, name: "地图底图", type: "group" },
          // {
          //   "pid": 10,
          //   "name": "蓝色底图",
          //   // "icon": "/img/basemaps/bd-c-midnight.png",
          //   "type": "xyz",
          //   "url": "http://map.geoq.cn/arcgis/rest/services/ChinaOnlineStreetPurplishBlue/MapServer/tile/{z}/{y}/{x}",
          //   "chinaCRS": "GCJ02",
          //   "enablePickFeatures": false,
          //   "show": true
          //   },
          // {
          //   "id": 2023,
          //   // "pid": 10,
          //   "name": "无底图",
          //   "icon": "/img/basemaps/null.png",
          //   "type": "grid",
          //   "color": rgba(1,52,83,1),
          //   "backgroundColor": rgba(1,52,83,1),
          //   "alpha": 0.03,
          //   "cells": 2,
          //   "show": true
          // },
          // {
          //     "pid": 10,
          //     "name": "单张图片 (本地离线)",
          //     "icon": "/img/basemaps/offline.png",
          //     "type": "image",
          //     // "url": require("../../assets/img/basemaps/indexDitu.png"),
          //     "show": true
          // },
          {
            pid: 10,
            name: "单张图片 (本地离线)",
            icon: "/img/basemaps/offline.png",
            type: "image",
            url: require("../../assets/img/pipe_network/bjpic.png"),
            show: true,
          },
        ],
      },
      param1: 1,
      underground: null,
      terrainPlanClip: null,
      terrainClip: null,
      eventTarget: new mars3d.BaseClass(),
      queryMapserver: null,
      geourl: "",
      waterFactoryLayer: null,
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL;
    this.initMap();
  },
  beforeDestroy() {
    if(this.map) {
      this.map.destroy()
    }
  },
  methods: {
    initMap() {
      this.map = new mars3d.Map("mars3dContainer", this.mapOptions);
      this.map.container.style.backgroundColor = "#546a53"; // 背景色
      this.map.on('load', () => {     
        this.addImage();
        this.addWMS();   
        // this.addShuixi();  // 水系geojson
        this.addShuixiWms();  // 水系wms图层
        this.showWaterFactory();
      })
    },
    addShuixiWms() {
      const shuixiLayer = new mars3d.layer.WmsLayer({
        url: this.geourl + "/geoserver/xingshan/wms",
        // url: "http://**************:8080/geoserver/img/wms?service=WMS&version=1.1.0&request=GetMap&layers=img%3Apng2&bbox=110.39483%2C30.9630324%2C111.15191207%2C31.59108010&width=768&height=637&srs=EPSG%3A4326&styles=&format=application/openlayers",
        // /?srs=EPSG%3A4326&format=application/openlayers
        layers: "xingshan:river_track",
        parameters: {
          service: "WMS",
          version: "1.1.0",
          request: "GetMap",
          srs: "EPSG:4326",
          transparent: true,
          format: "image/png",
          // format: 'application/openlayers'
        },
        getFeatureInfoParameters: {
          feature_count: 10,
          INFO_FORMAT: "text/plain",
        },
        zIndex: 200,
        flyTo: true,
      });
      this.map.addLayer(shuixiLayer);
    },
    addShuixi() {
      const graphicLayerGeo = new mars3d.layer.GeoJsonLayer({
          name: "兴山管网",
          url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Ariver_track&outputFormat=application/json",
          // format: this.simplifyGeoJSON, // 用于自定义处理geojson
          symbol: {
            type: "polylineP",
            styleOptions: {
              clampToGround: true,
              width: 2,
              materialType: mars3d.MaterialType.LineFlow,
              materialOptions: {
                color: "yellow",
                // image: "img/textures/fence-line.png",
                speed: 100,
                repeat_x: 10,
              },
              // distanceDisplayCondition: true,
              // distanceDisplayCondition_far: 12000000,
              // distanceDisplayCondition_near: 100000,
              label: {
                font_size: 15,
                // text: "{name},{length}m",
                color: "#ffffff",
                outline: true,
                outlineColor: "#ffffff",
                // scaleByDistance: true,
                // scaleByDistance_far: 60000000,
                // scaleByDistance_farValue: 0.2,
                // scaleByDistance_near: 1000000,
                // scaleByDistance_nearValue: 1,
                distanceDisplayCondition: true,
                distanceDisplayCondition_far: 50000000,
                distanceDisplayCondition_near: 0,
                setHeight: 10000,
              },
            },
          }
        });
        console.log(graphicLayerGeo)
        
        this.map.addLayer(graphicLayerGeo);
        // graphicLayerGeo.on(mars3d.EventType.load, function (event) {
        //     console.log("数据加载完成", event)
        // })
    },
    addImage() {
      const tileLayer = new mars3d.layer.ImageLayer({
        url: require("../../assets/img/basemaps/indexBg.png"),
        rectangle: {
          xmin: 109.825,
          xmax: 111.716,
          ymin: 30.49,
          ymax: 32.06,
        },
        alpha: 0.7,
      });
      this.map.addLayer(tileLayer);
    },
    addDemoGeoJsonLayer1() {
      // geojson 合肥边界线
      const geoJsonLayer = new mars3d.layer.GeoJsonLayer({
        url: this.geourl + "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Axingzheng&outputFormat=application%2Fjson",
        mask: true, // 标识为遮罩层【重点参数】
        symbol: {
          styleOptions: {
            fill: true,
            color: "rgb(31,60,92)",
            opacity: 1,
            outline: true,
            outlineColor: "#39E09B",
            outlineWidth: 8,
            outlineOpacity: 0,
            arcType: Cesium.ArcType.GEODESIC,
            clampToGround: true,
          },
        },
        // flyTo: true
      });
      this.map.addLayer(geoJsonLayer);
    },
    showWaterFactory() {
      this.waterFactoryLayer = new mars3d.layer.GeoJsonLayer({
        name: "水厂",
        url:
          this.geourl +
          "/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Awater_factory&maxFeatures=50&outputFormat=application%2Fjson",
        show: true,
        zIndex: 100,
        symbol: {
          styleOptions: {
            // clampToGround: true,
            image: require("@/assets/img/wq/wqPoint.png"),
            // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
        },
      });

      this.waterFactoryLayer.bindPopup(
        function (event) {
          const attr = event.graphic.attr || {};
          // attr["名称"] = attr["name"]
          return mars3d.Util.getTemplateHtml({
            title: "矢量图层",
            template: `
                <div>
                  <div class="wfpopup">{name}</div>
                </div>`,
            attr,
          });
        },
        {
          // offsetY: -10,
          autoClose: false,
          closeOnClick: false,
          closeButton: false,
        }
      );
      this.map.addLayer(this.waterFactoryLayer);
      this.waterFactoryLayer.on(mars3d.EventType.load, (event) => {
        console.log("数据加载完成", event);
        const list = document.getElementsByClassName("wfpopup");

        for (let index = 0; index < event.graphics.length; index++) {
          const element = event.graphics[index];
          element.openPopup();

          list[index].addEventListener("click", () => {
            console.log(element);
            this.handlePointClick(element.name);
          });
        }
      });
    },
    //   addPic() {
    //     const tileLayer = new mars3d.layer.ImageLayer({
    //       name: "中科大-西区",
    //       url: require("../../../assets/img/pipe_network/xsblue.png"),
    //       rectangle: { xmin: 110.611, xmax: 111.083, ymin: 31.033, ymax: 31.533 },
    //       zIndex: 20,
    //       opacity: 0.3
    //     })
    //     this.map.addLayer(tileLayer)
    //   },
    handlePointClick(name) {
      this.$router.push({
        name: "HomeWaterFactory",
        query: {
          factory: name,
        },
      });
    },
    addWMS() {
      const tileLayer = new mars3d.layer.WmsLayer({
        url: this.geourl + "/geoserver/img/wms",
        // url: "http://**************:8080/geoserver/img/wms?service=WMS&version=1.1.0&request=GetMap&layers=img%3Apng2&bbox=110.39483%2C30.9630324%2C111.15191207%2C31.59108010&width=768&height=637&srs=EPSG%3A4326&styles=&format=application/openlayers",
        // /?srs=EPSG%3A4326&format=application/openlayers
        layers: "img:png2",
        // rectangle: {
        //   xmin: 110.5,
        //       xmax: 111,
        //       ymin: 30,
        //       ymax: 32.5,
        // },
        parameters: {
          service: "WMS",
          version: "1.1.0",
          request: "GetMap",
          // bbox: '110.39483293%2C30.9630324254%2C111.15191207080001%2C31.591080100800003',
          // width: 768,
          // height: 637,
          srs: "EPSG:4326",
          transparent: true,
          format: "image/png",
          // format: 'application/openlayers'
        },
        getFeatureInfoParameters: {
          feature_count: 10,
          INFO_FORMAT: "text/plain",
        },
        zIndex: 20,
        flyTo: true,
      });
      this.map.addLayer(tileLayer);
    },
    simplifyGeoJSON(geojson) {
      try {
        geojson = turf.simplify(geojson, {
          tolerance: 0.0001,
          highQuality: true,
          mutate: true,
        });
      } catch (e) {
        //
      }
      return geojson;
    },
  },
};
</script>
  
<style lang="scss">
.homeMapContainer {
  background-color: #22df74;
  .wfpopup {
    height: 28px;
    padding: 0px;
    font-size: 15px;
    text-align: center;
    line-height: 26px;
    border: 1px solid #00EAFF;
    border-radius: 4px;
    padding: 0 10px;
  }
  .cesium-viewer-toolbar {
    left: 468px;
    top: 60px
  }
  .mars3d-popup-background {
    background: RGBA(26, 97, 153, 0.9);
    .mars3d-popup-content {
      margin: 0;
    }
  }
  .cesium-baseLayerPicker-dropDown-visible {
    transform: translate(0, 0);
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    left: 48px;
  }
}
</style>
  
<style scoped lang="scss">
</style>
      