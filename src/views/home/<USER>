<template>
  <div class="indexLayout">
    <div class="viewBox">
      <router-view />
    </div>

    <!-- 首页下方切换 -->
    <!-- <div class="bottomContainer">
      <div class="bottomAction setIndex" v-if="isInfoMap">
        <div class="centerBox" @mouseleave="hoverValue = 0">
          <div
            v-for="item in bottomData"
            :key="item.value"
            class="typeCard"
            :style="{
              'background-image':
                'url(' +
                (currentValue == item.value ? item.hover : item.normal) +
                ')',
            }"
            @click="handleClick(item)"
          >{{ item.label }}</div>
        </div>
      </div>
    </div> -->

    <div class="menuBox">
      <div 
        class="menuItem"
        v-for="item in menuData" :key="item.id"
        @click="handleMenuClick(item)">
        <div>{{ item.label }}</div>
        <div class="iconBox" :style="{ 'background-image': `url(${menuId == item.id ? item.active : item.normal })` }"></div>  
      </div>
    </div>

  </div>
</template>

<script>
// 首页布局组件
export default {
  name: 'IndexLayout',
  data() {
    return {
      hoverValue: 0,
      currentValue: 1,
      bottomData: [
        {
          value: 0,
          label: '总览',
          pageName: '',
          // normal: require('@/assets/img/home/<USER>'),
          // hover: require('@/assets/img/home/<USER>'),
          normal: require('@/assets/img/home/<USER>'),
          hover: require('@/assets/img/home/<USER>')
        },
        {
          value: 1,
          label: '项目概况',
          pageName: 'InfoMap',
          // normal: require('@/assets/img/home/<USER>'),
          // hover: require('@/assets/img/home/<USER>'),
          normal: require('@/assets/img/home/<USER>'),
          hover: require('@/assets/img/home/<USER>')
        },
        {
          value: 2,
          label: '无人机值守',
          pageName: 'UAV',
          // normal: require('@/assets/img/home/<USER>'),
          // hover: require('@/assets/img/home/<USER>'),
          normal: require('@/assets/img/home/<USER>'),
          hover: require('@/assets/img/home/<USER>')
        },
        {
          value: 3,
          label: '数据中心',
          pageName: 'LayerCenter',
          // normal: require('@/assets/img/home/<USER>'),
          // hover: require('@/assets/img/home/<USER>'),
          normal: require('@/assets/img/home/<USER>'),
          hover: require('@/assets/img/home/<USER>')
        },
      ],
      // menuId: 1,
      menuData: [
        {
          id: 1,
          value: 1,
          label: '供水',
          routerName: 'InfoMap',
          normal: require('@/assets/img/support/m1.png'),
          active: require('@/assets/img/support/m1a.png')
        },
        {
          id: 2,
          value: 2,
          label: '调度',
          routerName: 'DispatchMap',
          normal: require('@/assets/img/support/m2.png'),
          active: require('@/assets/img/support/m2a.png')
        },
        {
          id: 3,
          value: 3,
          label: '漏损',
          routerName: 'LeakageLossMap',
          normal: require('@/assets/img/support/m3.png'),
          active: require('@/assets/img/support/m3a.png')
        },
        {
          id: 7,
          value: 7,
          label: '无人值守',
          routerName: 'MonitorPage',
          normal: require('@/assets/img/support/m0.png'),
          active: require('@/assets/img/support/m0a.png')
        },
        {
          id: 4,
          value: 4,
          label: '管网',
          routerName: 'PipNetworkMap',
          normal: require('@/assets/img/support/m4.png'),
          active: require('@/assets/img/support/m4a.png')
        },
        {
          id: 5,
          value: 5,
          label: '水质',
          routerName: 'WqMap',
          normal: require('@/assets/img/support/m5.png'),
          active: require('@/assets/img/support/m5a.png')
        },
        {
          id: 6,
          value: 6,
          label: '营收',
          routerName: 'RevenueMap',
          normal: require('@/assets/img/support/m6.png'),
          active: require('@/assets/img/support/m6a.png')
        }
      ]
    }
  },
  computed: {
    isInfoMap() {
      return this.$route.name === 'InfoMap'
    },
    menuId() {
      return this.$route.query.id || 1
    }
  },
  methods: {
    handleClick(item) {
      this.currentValue = item.value
      if(item.pageName) {
        this.$router.push({ name: item.pageName })
        // this.$store.commit('app/SET_homeIsOverview', false)
      } else {
        // this.$store.commit('app/SET_homeIsOverview', true)
        this.$router.push({ name: 'InfoMap', query: { state: 'overview' } })
      }
      // this.SET_homeActive(this.currentValue)
      // if (this.currentValue === 2) {
      //   this.$router.push({ name: "HomeUav" })
      // }
    },
    handleMenuClick(menu) {
      // this.menuId = menu.id
      if(menu.routerName) {
        this.$router.push({ name: menu.routerName, query: { id: menu.id } })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.indexLayout {
  height: calc(100vh - 84px);
  position: relative;
  .viewBox {
    width: 100%;
    height: 100%;
  }
  .bottomContainer {
    position: absolute;
    bottom: 0;
    width: 100%;
  }
  .bottomAction {
    // height: 60px;
    // background-image: url("~@/assets/img/home/<USER>");
    // background-size: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .typeCard {
      margin-top: 5px;
      color: #fff;
      text-align: center;
    }
    // .typeCard:nth-child(2) {
    //   margin-top: 10px;
    // }
    .centerBox {
      display: flex;
      margin: 0 auto;
      div {
        width: 137px;
        height: 30px;
        background-size: 137px 30px;
        cursor: pointer;
      }
    }
  }
  .menuBox {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    z-index: 1001;
    color: #fff;
    .menuItem {
      text-align: center;
      cursor: pointer;
      margin-right: 20px;
      &:last-child {
        margin-right: 0;
      }
      &:nth-child(2), &:nth-child(6) {
        transform: translateY(-10px);
      }
      &:nth-child(3), &:nth-child(5) {
        transform: translateY(-15px);
      }
      &:nth-child(4) {
        transform: translateY(-20px);
      }
      .iconBox {
        width: 60px;
        height: 60px;
        background-size: 100%;
      }
    }
  }
}
</style>