<template>
  <div class="left">
    <div class="warn">
      <div class="title">工程概况</div>
      <div class="overviewData">
        <img src="../../assets/img/home/<USER>" style="width:" alt="">
        <div class="projectText">本项目涉及古夫镇、昭君镇及峡口镇3个乡镇共计11个村，总人口16832人，其中移民人口2618人。项目总投资7623.4万元。项目主要建设内容包括6项：</div>
        <div class="projectText1">1、水源工程(37座) 2、水厂工程(3座) 3、蓄水工程(79座) </div>
        <div class="projectText1">4、净水工程(161套) 5、管道工程(260.35千米) 6、信息化工程</div>
      </div>
    </div>
    
    <div class="data">
      <div class="title">营收数据</div>
      <div class="content">
        <div class="chart" id="chart"></div>
      </div>
    </div>

    <div class="dma">
      <div class="title">漏损分析</div>
      <div class="top">
        <div class="smallLeft">
          <div class="leftTilte">今日供水量</div>
          <div class="valueBox">
            <span class="value">245</span>
            <span>m³</span>
          </div>
          <div class="rate">
            <span>环比：2.48%</span>
            <i class="el-icon-top" style="color: #00FF00;"></i>
          </div>
          <div class="rate">
            <span>同比：3.5%</span>
            <i class="el-icon-bottom" style="color: #FF200C;"></i>
          </div>
        </div>
        <div style="width: 144px;height:144px;">
          <BallChart />
        </div>
        <div class="smallRight">
          <div class="leftTilte">今日售水量</div>
          <div class="valueBox">
            <span class="value">180</span>
            <span>m³</span>
          </div>
          <div class="rate">
            <span>环比：2.48%</span>
            <i class="el-icon-top" style="color: #00FF00;"></i>
          </div>
          <div class="rate">
            <span>同比：3.5%</span>
            <i class="el-icon-bottom" style="color: #FF200C;"></i>
          </div>
        </div>
        <!-- 箭头 -->
        <div class="arrow leftArrow"></div>
        <div class="arrow rightArrow"></div>
      </div>
      <div class="cardList">
        <div v-for="item, index in damList" :key="index" class="card">
          <div class="label">{{ item.label }}</div>
          <div class="borderValue">
            <span class="value">{{ item.value }}</span>
            <span style="font-size: 12px;">m³</span>
          </div>
          <div class="innerRate">
            <span>环比：{{ item.rate1 }}%</span>
            <i class="el-icon-top" style="color: #00FF00;"></i>
          </div>
          <div class="innerRate">
            <span>同比：{{ item.rate1 }}%</span>
            <i class="el-icon-bottom" style="color: #FF200C;"></i>
          </div>
        </div>
      </div>
      <!-- <div class="dataBox">
        <div class="dataItem">
          <img src="@/assets/img/home/<USER>" alt="">
          <div style="margin-left: 10px;">
            <div>上月供水量</div>
            <div class="value">245 <span style="font-size: 12px;">m³</span></div>
          </div>
        </div>
        <div class="dataItem">
          <img src="@/assets/img/home/<USER>" alt="">
          <div style="margin-left: 10px;">
            <div>上月用水量</div>
            <div class="value">180 <span style="font-size: 12px;">m³</span></div>
          </div>
        </div>
        <div class="dataItem">
          <img src="@/assets/img/home/<USER>" alt="">
          <div style="margin-left: 10px;">
            <div>综合漏损率</div>
            <div class="value">20%</div>
          </div>
        </div>
      </div>
      <div class="chart2" id="chart2"></div> -->
    </div>
  </div>
</template>

<script>
import echarts from "echarts";
import { debounce } from '@/utils'
import BallChart from './chart/ballChart.vue'

export default {
  components: { BallChart },
  data() {
    return {
      centerChart: null,
      centerChart2: null,
      dmaData: [
        {
          name: '上月供水量',
          value: 245
        },
        {
          name: '上月供水量',
          value: 245
        },
        {
          name: '上月供水量',
          value: '20%'
        }
      ],
      damList: [
        {
          label: '咋日供水量',
          value: 245,
          rate1: 2.3,
          rate2: 3.5
        },
        {
          label: '上月供水量',
          value: 245,
          rate1: 2.3,
          rate2: 3.5
        },
        {
          label: '咋日售水量',
          value: 245,
          rate1: 2.3,
          rate2: 3.5
        },
        {
          label: '上月售水量',
          value: 245,
          rate1: 2.3,
          rate2: 3.5
        }
      ]
    };
  },
  filters: {
    warnTypeFilter(value) {
      const labelMap = {
        'UN_KNOWN': '未知',
        'WATER_QUALITY': '水质预警',
        'EQUIPMENT': '设备预警',
        'WATER_LEVEL': '水位预警',
        'WATER_TRAFFIC': '流量预警',
        'WATER_PRESSURE': '压力预警'
      }
      if(!value) return '--'
      return labelMap[value]
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initchart();
      // this.initchart2();
    });
    const resizeHandler = debounce(() => {
      this.chartResize()
    }, 100)
    window.addEventListener('resize', resizeHandler)
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', resizeHandler)
    })
  },
  methods: {
    initchart() {
      this.centerChart = echarts.init(document.getElementById("chart"));
      let option = {
        color: ["#05CFF7", "#0269E9", "#C6ECDF", "#01F871"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {          
          itemWidth: 16,
          itemHeight: 8,
          textStyle: {
            fontSize: 12,
            fontWeight: "normal",
            color: "#ACC6EA",
          },
        },
        grid: {
          top: 30,
          left: 30,
          right: 30,
          bottom: 20
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          axisLine: {
            lineStyle: {
              color: '#B0D7FF'
            }
          },
          axisLabel: {
            interval: 0
          },
          axisTick: {
            show: false
          },
        },
        yAxis: [
          {
            type: "value",
            axisLabel: {
              show: true,
              textStyle: {
                color: "#B0D7FF",
                fontSize: "12",
              },
            },
            axisLine: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#B0D7FF'
              }            
            }
          },
          {
            type: "value",
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#B0D7FF",
                fontSize: "12",
              },
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#B0D7FF'
              }            
            }
          },
        ],
        series: [
          {
            name: '供水量(万方)',
            type: 'bar',
            data: [150, 130, 120, 145, 180, 195, 198, 205, 175, 152, 110, 75],
            itemStyle: {
              color: '#00B4FF'
            },
            barWidth: 12
          },
          {
            barGap: '-100%',
            name: '售水量(万方)',
            type: 'bar',
            data: [100, 80, 90, 95, 150, 155, 157, 180, 160, 130, 90, 60],
            itemStyle: {
              color: '#2CECEA'
            },
            barWidth: 12
          },
          {
            name: '营业额(万元)',
            type: 'line',
            yAxisIndex: 1,
            data: [153, 133, 123, 148, 183, 201, 202, 206, 178, 155, 115, 80],
            itemStyle: {
              color: '#E2BA04'
            },
            symbol: 'circle'
          }
        ],
      };
      this.centerChart.setOption(option, true);
    },
    initchart2() {
      this.centerChart2 = echarts.init(document.getElementById("chart2"));
      let option = {
        legend: {
          itemWidth: 16,
          itemHeight: 8,
          top: 10,
          textStyle: {
            fontSize: 12,
            fontWeight: "normal",
            color: "#ACC6EA",
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(2, 50, 128, 0.6)'
        },
        grid: {
          // 让图表占满容器
          top: "40px",
          left: "40px",
          right: "40px",
          bottom: "20px",
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          axisLine: {
            lineStyle: {
              color: '#B0D7FF'
            }
          },
          axisLabel: {
            // textStyle: {
            //   color: "#B0D7FF", //x轴上的字体颜色
            //   fontSize: "12", // x轴字体大小
            // },
            interval: 0
          },
          axisTick: {
            show: false
          },
        },
        yAxis: [
          {
            type: "value",
            axisLabel: {
              show: true,
              textStyle: {
                color: "#B0D7FF",
                fontSize: "12",
              },
            },
            axisLine: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#B0D7FF'
              }            
            }
          },
          {
            type: "value",
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              color: "#B0D7FF",
              fontSize: "12",
              formatter: '{value}%'
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#B0D7FF'
              }            
            },

          },
        ],
        visualMap: [
          {
            type: 'piecewise',
            show: false,
            seriesIndex: 1,
            pieces: [
              {
                gt: 0,
                lte: 20,
                color: '#00FE00'
              },
              {
                gt: 20,
                color: '#FF1F11'
              }
            ]
          }
        ],
        series: [
          // {
          //   name: '',
          //   type: 'pictorialBar',
          //   symbol: 'diamond',
          //   // symbolSize: [16, 8],
          //   // symbolOffset: [0, 4], 
          //   // symbolSize: [16, 10], // 宽，高
          //   symbolSize: ['40', '30'],
          //   symbolOffset: [0, 2],// 左 上
          //   symbolPosition: 'start',

          //   z: 0,
          //   data: [150, 130, 120, 145, 180, 195, 198, 205, 175, 152, 110, 75]
          // },
          // {
          //   name: '',
          //   type: 'pictorialBar',
          //   symbol: 'diamond',
          //   // symbolSize: [16, 8],
          //   // symbolOffset: [0, 4], 
          //   symbolSize: [12, 12], // 宽，高
          //   symbolOffset: [-6, 0],// 左 上
          //   symbolPosition: 'end',
          //   z: 0,
          //   data: [150, 130, 120, 145, 180, 195, 198, 205, 175, 152, 110, 75]
          // },
          {
            name: '漏水量(万方)',
            type: 'bar',
            data: [5.5, 5.4, 6.2, 7, 8, 8.5, 9.2, 11, 9, 7.5, 5.8, 5],
            itemStyle: {
              color: '#5FF3F3'
            },
            z: 1,
            barWidth: 12
          },
          {
            name: '漏损率',
            type: 'line',
            yAxisIndex: 1,
            data: [7, 10, 12, 13, 15, 20, 27, 28, 26, 12, 8, 6],
            itemStyle: {
              color: '#00FE00'
            },
            symbol: 'circle',
            markLine: {
              symbol: ['none', 'none'],
              data: [
                {
                  name: '漏损红线',
                  yAxis: 20,
                  lineStyle: {
                    color: '#D23904',
                    type: 'solid',
                  },
                  label: {
                    position: 'insideEndTop',
                    formatter: '{b}'
                  }
                }
              ]
            },
          }
        ],
      };
      this.centerChart2.setOption(option, true);
    },
    chartResize() {
      this.centerChart && this.centerChart.resize()
      this.centerChart2 && this.centerChart2.resize()
    }
  },
};
</script>

<style scoped lang="scss">
.left {
  position: absolute;
  left: 30px;
  height: 100%;
  .warn {
    width: 440px;
    .overviewData {
      padding: 13px 20px 0;
      background-color: rgba(4, 15, 45, 0.3);
      color: #E4F1FF;
      font-size: 14px;
      font-family: MicrosoftYaHei;
      .projectText {
        line-height: 1.5;
        // color: #E4F1FF;
        // font-size: 14px;
      }
      .projectText1 {
        padding-top: 5px;
      }
    }
  }
  .data {
    margin-top: 20px;
    width: 440px;
    height: calc((100% - 273px) / 2 - 40px);
    .content {
      padding: 0 20px;
      height: calc(100% - 42px);
      background-color: rgba(4, 15, 45, 0.3);
      .chart {
        // height: calc(100% - 42px);
        height: 100%;
      }
    }
  }
  .dma {
    margin-top: 20px;
    overflow: hidden;
    background-color: rgba(4, 15, 45, 0.3);
    // height: calc(100% - 560px);
    height: calc((100% - 273px) / 2 - 20px);
    color: #ffffff;
    .dataBox {
      padding: 10px 20px 0;
      display: flex;
      justify-content: space-between;
      .dataItem {
        display: flex;
        align-items: center;
        color: #B0D7FF;
        font-size: 12px;
        .value {
          color: #ffffff;
          font-weight: bold;
          font-size: 18px;
        }
      }
      img {
        width: 46px;
      }
    }
    .chart2 {
      height: calc(100% - 98px);
    }
    .top {
      display: flex;
      margin: 10px 20px 0;
      justify-content: center;
      align-items: center;
      position: relative;
      background-image: url('~@/assets/img/home/<USER>');
      background-size: 100%;
      .arrow {
        position: absolute;
        width: 14px;
        height: 26px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100%;
      }
      .leftArrow {
        top: 60px;
        left: 100px;
      }
      .rightArrow {
        transform: rotate(180deg);
        top: 60px;
        right: 100px;
      }
    }
    .smallLeft {
      text-align: right;
      position: absolute;
      left: -20px;
      .leftTilte {
        width: 105px;
        height: 28px;
        font-size: 14px;
        padding: 4px 8px 0 0;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100%;
      }
    }
    .smallRight {
      position: absolute;
      right: -20px;
      .leftTilte {
        width: 105px;
        height: 28px;
        font-size: 14px;
        padding: 4px 0 0 11px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100%;
      }
    }
    .valueBox {
      margin: 10px 0;
      .value {
        padding-right: 7px;
        font-family: DIN;
        font-weight: bold;
        font-size: 24px;
      }
    }
    .rate {
      font-size: 12px;
    }
    .cardList {
      display: flex;
      justify-content: space-between;
      padding: 10px 20px 0;
      .card {
        width: 93px;
        height: 113px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100%;
        .label {
          font-size: 14px;
          height: 29px;
          text-align: center;
          padding-top: 6px;
        }
        .borderValue {
          height: 20px;
          margin: 4px 10px 0;
          background-image: url('~@/assets/img/home/<USER>');
          background-size: 100%;
          text-align: center;
          margin-right: 4px;
          .value {
            font-family: DIN;
            font-weight: bold;
            font-size: 18px;
          }
        }
        .innerRate {
          font-size: 12px;
          text-align: center;
          margin-top: 6px;
        }
      }
    }
  }
  .title {
    background: url("../../assets/img/title.png") center no-repeat;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
  }
  .calcHeight {
    height: 210px;
    padding-top: 5px;
    .listHeader {
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 34px;
      background: url("../../assets/img/table.png") center no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 34px;
      span {
        display: block;
        width: 80px;
      }
      span:first-child {
        width: 93px;
        padding-left: 30px;
      }
      span:last-child {
        flex-grow: 1;
      }
    }
    .listItem {
      text-align: center;
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 34px;
      .small {
        width: 80px;
        // font-size: 14px;
      }
      .small:first-child {
        width: 93px;
        padding-left: 30px;
      }
      .big {
        display: flex;
        align-items: center;
        max-width: 195px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
</style>