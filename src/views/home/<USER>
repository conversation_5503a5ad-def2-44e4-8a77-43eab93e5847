<template>
  <div class="infoMap">
    <InnerMap ref="innerMapRef" :mapMode="isFull" @fromInnerMap="layerClick" />

    <!-- <div class="allmapBox">
      <img class="allmap" src="@/assets/img/home/<USER>" alt="" />
      <div class="pointBox">
        <div
          class="point"
          v-for="(p, index) in mapPoints"
          :key="index"
          :style="{ left: p.left + 'px', top: p.top + 'px' }"
          @click="handlePointClick(p.name)"
        >
          <div class="pointName">{{ p.name }}</div>
        </div>
      </div>
    </div> -->

    <!-- <LeftBar style="top: 0;" /> -->

    <!-- 面板控制 -->
    <div
      class="mapMode"
      @click="changeMapMode"
      :class="{ mapModeFull: isFull }"
    >
      <img v-if="isFull" src="@/assets/img/home/<USER>/full-active.png" alt="" />
      <img v-else src="@/assets/img/home/<USER>/full.png" alt="" />
    </div>

    <!-- 左侧数据 -->
    <!-- <div class="leftData" :style="{ left: isFull ? '10px' : '490px' }">
      <div class="item" v-for="item in leftCount" :key="item.name">
        <img :src="item.img" alt="">
        <div class="nameAndCount">
          <div>{{ item.name }}</div>
          <div>{{ isOverview ? item.count : item.current }}个</div>
        </div>
      </div>
    </div> -->

    <!-- 底部数据 -->
    <!-- <div class="bottomData">
      <div class="item" v-for="item in bottomCount" :key="item.name">
        <img :src="item.img" alt="">
        <div class="nameAndCount">
          <div>{{ item.name }}</div>
          <div>{{ item.count }}个</div>
        </div>
      </div>
    </div> -->

    <LeftPanel v-if="!isFull" style="top: 0;" />
    <RightPanel @yearReceived="setYearReceived" v-if="!isFull" style="top: 0;" />

    <!-- <LeftPanel v-if="!isFull" style="top: 0;" /> -->
    <!-- <RightBar v-if="!isFull" style="top: 0;" /> -->

    <!-- 上部数据 -->
    <div class="topData setIndex">
      <div class="dataItem">
        <img src="@/assets/img/home/<USER>" alt="" />
        <div style="margin-left: 10px">
          <div>服务人口</div>
          <div class="value">16832 <span style="font-size: 16px">人</span></div>
        </div>
      </div>
      <!-- <div class="dataItem">
        <img src="@/assets/img/home/<USER>" alt="" />
        <div style="margin-left: 10px">
          <div>年蓄水量</div>
          <div class="value">-- <span style="font-size: 16px">m³</span>
          </div>
        </div>
      </div> -->
      <div class="dataItem">
        <img src="@/assets/img/home/<USER>" alt="" />
        <div style="margin-left: 10px">
          <div>年供水量</div>
          <div class="value">
            {{ yearTotal }} <span style="font-size: 16px">m³</span>
          </div>
        </div>
      </div>
      <div class="dataItem">
        <img src="@/assets/img/home/<USER>" alt="" />
        <div style="margin-left: 10px">
          <div>管网总长</div>
          <div class="value">
            {{ pipeNetworkLength }}<span style="font-size: 16px"> km</span>
          </div>
        </div>
      </div>
      <div class="dataItem">
        <img src="@/assets/img/home/<USER>" alt="" />
        <div style="margin-left: 10px">
          <div>年总营收额</div>
          <div class="value">
            {{ yearReceived }} <span style="font-size: 16px">万元</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 首页地图容器组件
import InnerMap from "./innerMap";
// import LeftBar from './leftbar'
// import RightBar from './rightbar'
// import LeftPanel from './leftPanel'

import LeftPanel from "./support/leftPanel";
import RightPanel from "./support/rightPanel";

import { getStationTypeCount, factorySupplyDataNew } from "@/api/screenData";
import { getPipeNetworkTypeLength } from "@/api/pipenetwork/screen";
import { getYearInfo } from "@/api/revenue/screen";

import { Decimal } from "decimal.js";

export default {
  name: "InfoMap",
  components: {
    InnerMap,
    LeftPanel,
    RightPanel,
  },
  data() {
    return {
      pipeNetworkLength: 0,
      amountSum: 0,
      yearTotal: 0,
      mapPoints: [
        {
          name: "谭湾水厂",
          left: 326,
          top: 136,
        },
        {
          name: "双龙观水厂",
          left: 253,
          top: 193,
        },
        {
          name: "大礼水厂",
          left: 277,
          top: 292,
        },
        {
          name: "青华水厂",
          left: 406,
          top: 286,
        },
        {
          name: "吴家坪水厂",
          left: 447,
          top: 373,
        },
      ],
      isFull: false,
      leftCount: [
        {
          name: "水厂",
          count: 0,
          current: 0,
          img: require("@/assets/img/home/<USER>/leftCount1.png"),
        },
        {
          name: "蓄水池",
          count: 0,
          current: 0,
          img: require("@/assets/img/home/<USER>/leftCount2.png"),
        },
        {
          name: "泵站",
          count: 0,
          current: 0,
          img: require("@/assets/img/home/<USER>/leftCount3.png"),
        },
        {
          name: "取水口",
          count: 0,
          current: 0,
          img: require("@/assets/img/home/<USER>/leftCount4.png"),
        },
      ],
      bottomCount: [
        {
          name: "视频监控",
          count: 55,
          img: require("@/assets/img/home/<USER>/bottomCount1.png"),
        },
        {
          name: "水表",
          count: 5000,
          img: require("@/assets/img/home/<USER>/bottomCount2.png"),
        },
        {
          name: "流量计",
          count: 105,
          img: require("@/assets/img/home/<USER>/bottomCount3.png"),
        },
        {
          name: "液位计",
          count: 77,
          img: require("@/assets/img/home/<USER>/bottomCount4.png"),
        },
        {
          name: "压力传感器",
          count: 69,
          img: require("@/assets/img/home/<USER>/bottomCount5.png"),
        },
        {
          name: "水质监测仪",
          count: 16,
          img: require("@/assets/img/home/<USER>/bottomCount6.png"),
        },
        {
          name: "水文一体化站",
          count: 4,
          img: require("@/assets/img/home/<USER>/bottomCount7.png"),
        },
      ],
      yearReceived: 0
    };
  },
  computed: {
    isOverview() {
      return this.$route.query.state === "overview";
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    setYearReceived(data) {
      this.yearReceived = data
    },
    getData() {
      // 统计
      factorySupplyDataNew().then((res) => {
        const target4 = res.data.find((item) => item.type === 4);
        this.yearTotal = target4.value;
      });
      // 管网长度
      getPipeNetworkTypeLength().then((res) => {
        const { data } = res;
        this.pipeNetworkLength = Decimal(data.allLength)
          .div(1000)
          .toFixed(2);
      });
      // getYearInfo().then((res) => {
      //   let total = res.data.amountSum;
      //   if (total) {
      //     this.amountSum = this.filterToW(total);
      //   }
      // });
      // getStationTypeCount().then((res) => {
      //   let list = res.data;
      //   this.leftCount.forEach((item) => {
      //     let matchPoint = list.find((d) => d.name === item.name);
      //     if (matchPoint) {
      //       item.count = matchPoint.totalCount;
      //       item.current = matchPoint.count;
      //     }
      //   });
      // });
    },
    handlePointClick(name) {
      this.$router.push({
        name: "HomeWaterFactory",
        query: {
          factory: name,
        },
      });
    },
    changeMapMode() {
      this.isFull = !this.isFull;
      this.$refs.innerMapRef.closeAll();
    },
    layerClick() {
      if (!this.isFull) this.isFull = true;
    },
    filterToW(value) {
      return (value / 10000).toFixed(2);
    },
  },
};
</script>

<style lang="scss" scoped>
/* PostCSS 会自动将 px 转换为 vw，下面是一些需要注意的样式 */
.infoMap {
  height: 100%; /* 这个会保持 100% 不被转换 */
  position: relative;

  /* 如果你希望某些特定的属性不被转换，可以在数值后加上注释 */
  /* 例如: width: 100px; !* no-transform *! */

  .allmapBox {
    position: absolute;
    width: 908px;
    left: 506px;
    top: 120px;
    .pointBox {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
    }
    .point {
      width: 108px;
      height: 68px;
      font-size: 14px;
      font-family: Microsoft YaHei;
      color: #ffffff;
      background-image: url("~@/assets/img/home/<USER>");
      background-size: 100%;
      position: absolute;
      cursor: pointer;
      .pointName {
        text-align: center;
        padding-top: 12px;
      }
    }
  }
  .allmap {
    // position: absolute;
    // width: 908px;
    // left: 470px;
    // top: 100px;
    width: 908px;
  }
  .mapMode {
    position: absolute;
    top: 60px;
    left: 490px;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .mapModeFull {
    left: 30px;
  }
  .layerControl {
    position: absolute;
    right: 480px;
    top: 100px;
  }
  .leftData {
    position: absolute;
    left: 480px;
    top: 100px;
    color: #ffffff;
    .item {
      margin-top: 20px;
      display: flex;
      img {
        width: 61px;
      }
    }
  }
  .nameAndCount {
    padding-top: 5px;
  }
  .bottomData {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 70px;
    color: #ffffff;
    display: flex;
    img {
      width: 61px;
    }
    .item {
      display: flex;
      margin-left: 5px;
    }
  }
  .topData {
    position: absolute;
    padding: 0 60px;
    top: 0;
    left: 470px;
    right: 470px;
    display: flex;
    justify-content: space-between;

    // 确保在小屏幕上元素也有合理的间距
    @media screen and (max-width: 1440px) {
      padding: 0 30px;
    }

    .dataItem {
      display: flex;
      align-items: center;
      color: #b0d7ff;
      font-size: 16px;
      .value {
        // font-family: DIN;
        color: #ffffff;
        font-weight: bold;
        font-size: 24px;
      }
    }
    img {
      width: 60px;
    }
  }
}
</style>
