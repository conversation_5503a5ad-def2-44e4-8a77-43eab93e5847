<template>
  <div class="innerMap">
    <div id="map"></div>

    <!-- 底图切换 -->
    <!-- <div class="baseBox" @click="changeBase" :style="{ right: mapMode ? '30px' : '490px' }">
      <img v-if="isImg" src="@/assets/img/home/<USER>" alt="">
      <img v-else src="@/assets/img/home/<USER>" alt="">
      <img src="@/assets/img/home/<USER>/earth.png" alt="">
    </div> -->

    <transition name="left-fade">
      <div class="control" v-show="showDetail">
        <div class="closeBtn">
          <i class="el-icon-close close" @click="closepopup" />
        </div>

        <!-- 测点 工程 -->
        <!-- 修改为从上往下显示，一行一个 -->
        <div class="analysis" v-show="siteShow">
          <div class="content">
            <div class="subtitle" v-show="!engineShow">管网监测</div>
            <div class="layerlabel" v-show="!engineShow">
              <div class="layerItem" v-for="(item) in pipelist" :key="item.id">
                <div
                  :class="item.state ? 'activebtn' : 'unactivebtn'"
                  class="btn"
                  @click="changelayer(item)"
                >
                  {{ item.label }}
                </div>
                <img class="layerIcon" :src="item.src" alt="">
                <span class="layerCount">{{ item.count || '' }}</span>
              </div>
              
            </div>
            <div class="subtitle" v-show="!engineShow">水情监测</div>
            <div class="layerlabel" v-show="!engineShow">
              <div class="layerItem" v-for="(item) in regimenlist" :key="item.id">
                <div
                  :class="item.state ? 'activebtn' : 'unactivebtn'"
                  class="btn"
                  @click="changelayer(item)"
                >
                  {{ item.label }}
                </div>
                <img class="layerIcon" :src="item.src" alt="">
                <span class="layerCount">{{ item.count || '' }}</span>
              </div>
            </div>
            <div class="subtitle" v-show="!engineShow">水质监测</div>
            <div class="layerlabel" v-show="!engineShow">
              <div class="layerItem" v-for="(item) in qualitylist" :key="item.id">
                <div
                  :class="item.state ? 'activebtn' : 'unactivebtn'"
                  class="btn"
                  @click="changelayer(item)"
                >
                  {{ item.label }}
                </div>
                <img class="layerIcon" :src="item.src" alt="">
                <span class="layerCount">{{ item.count || '' }}</span>
              </div>
            </div>
            <div class="subtitle" v-show="engineShow">工程管理</div>
            <div class="layerlabel" v-show="engineShow">
              <div class="layerItem" v-for="(item) in managerlist" :key="item.id">
                <div
                  :class="item.state ? 'activebtn' : 'unactivebtn'"
                  class="btn"
                  @click="changelayer(item)"
                >
                  {{ item.label }}
                </div>
                <img class="layerIcon" :src="item.src" alt="">
                <span class="layerCount">{{ item.count || '' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 图例 -->
        <div class="legendAna" v-show="legendShow">
          <div class="content">
            <div
              class="legendItem"
              v-for="item in legendCount"
              :key="item.name"
            >
              <img :src="item.img" alt="" />
              <div style="width: 100px;text-align: center;">
                {{ item.name }}
              </div>
              <div style="width: 80px;text-align: center;">
                {{ item.count }}个
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- 图层控制 -->
    <div
      class="layerControlBox"
      :class="{ 'layerControl-wide': !mapMode }"
      v-show="!isOverview"
    >
      <div
        class="item"
        v-for="(item, index) in layerControl2"
        :key="index"
        @click="changLayer2(item)"
      >
        <img :src="item.visible ? item.active : item.default" alt="" />
        <div class="name">{{ item.name }}</div>
      </div>
    </div>

    <transition name="left-fade">
      <!-- 列表 -->
      <div class="site" v-if="sitestate">
        <div class="closeBtn">
          <i class="el-icon-close close" @click="sitestate = false" />
        </div>
        <div class="sitebox">
          <div class="title titleFlex">{{ siteItemName }}</div>
          <div class="content">
            <div class="searchContainer">
              <el-input
                placeholder="请输入站点名称"
                suffix-icon="el-icon-search"
                v-model="searchText"
                class="searchInput"
                @input="handleSearch"
              >
              </el-input>
            </div>
            <div
              class="siteitem"
              v-for="item in sitelistTemp"
              :key="item.facilityId"
              :class="{ activesite: activeid === item.facilityId }"
              @click="changeactiveid(item)"
            >
              {{ item.facilityName }}
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- 图层控制 -->
    <!-- <div class="layerControlBox" v-show="!isOverview" :style="{ right: mapMode ? '10px' : '490px' }">
      <div class="item" v-for="item in layerControl" :key="item.id" @click="changLayer(item)">
        <div class="name">{{ item.name }}</div>
        <img :src="item.visible ? item.active : item.default" alt="">
      </div>
      <div class="lineLegend" v-if="currentActiveLayer === 'pipeLineLayer'">
        <div v-for="l in lineType" :key="l.label">
          <span class="lineSpan" :style="{ 'border-bottom': `2px solid ${l.color}` }"></span>
          <span>{{ l.label }}</span>
          <el-radio v-model="cLineType" :label="l.type" @input="changeLine" style="margin-left: 5px;"></el-radio>
        </div>
      </div>
    </div> -->

    <!-- <div class="legendBox" :style="{ right: mapMode ? '30px' : '490px' }">
      <div class="item">
        <el-checkbox v-model="layerControl.factory" @change="checkBoxChange($event, 'waterFactoryLayer')"></el-checkbox>
        <img src="@/assets/img/home/<USER>" alt="">
        <span>水厂</span>
      </div>
      <div class="item">
        <el-checkbox v-model="layerControl.pond" @change="checkBoxChange($event, 'mainPondLayer')"></el-checkbox>
        <img src="@/assets/img/home/<USER>" alt="">
        <span>蓄水池</span>
      </div>
      <div class="item">
        <el-checkbox v-model="layerControl.pipe" @change="checkBoxChange($event, 'mainFactoryPipeLayer')"></el-checkbox>
        <i class="el-icon-minus" style="color: #2CECEA;margin: 0 5px;"></i>
        <span style="margin-left: 5px;">管线</span>
      </div>
    </div> -->

    <transition name="right-fade">
      <Popup1
        @closePopup="popupstate = ''"
        :detaildata="detaildata"
        :stationId="stationId"
        v-if="
          popupstate === 'flow' ||
          popupstate === 'pressure' ||
          popupstate === 'level'
        "
      />
      <MeterPopup
        @closePopup="popupstate = ''"
        :meterId="meterId"
        :detaildata="detaildata"
        v-if="popupstate === 'meter' || popupstate === 'hydro'"
      />
      <VideoPopup
        :detaildata="detaildata"
        v-if="
          popupstate === 'factoryVideo' ||
          popupstate === 'sourceVideo' ||
          popupstate === 'storagePondVideo'
        "
      />
      <WqPopup
        @closePopup="popupstate = ''"
        :detaildata="detaildata"
        v-if="
          popupstate === 'pipenetwork' ||
          popupstate === 'factoryAndSource' ||
          popupstate === 'endUser' ||
          popupstate === 'labourMonitor'
        "
      />
      <ProjectPopup
        @closePopup="popupstate = ''"
        :detaildata="detaildata"
        v-if="
          popupstate === 'sourceProject' ||
          popupstate === 'factoryProject' ||
          popupstate === 'storagePondProject'
        "
      />
      <ProjectPopupOther
        @closePopup="popupstate = ''"
        :detaildata="detaildata"
        v-if="popupstate.includes('projectLayer')"
      />
    </transition>
  </div>
</template>

<script>
import Popup1 from "./popup/popup1.vue";
import MeterPopup from "./popup/meterPopup.vue";
import VideoPopup from "./popup/videoPopup.vue";
import WqPopup from "./popup/wqPopup.vue";
import ProjectPopup from "./popup/projectPopup.vue";
import ProjectPopupOther from "./popup/projectPopupOther.vue";
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import WMSLayer from "@arcgis/core/layers/WMSLayer";
import GeoJSONLayer from "@arcgis/core/layers/GeoJSONLayer";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import WebTileLayer from "@arcgis/core/layers/WebTileLayer";
import WFSLayer from "@arcgis/core/layers/WFSLayer";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import Graphic from "@arcgis/core/Graphic";
import Point from "@arcgis/core/geometry/Point";
import Polygon from "@arcgis/core/geometry/Polygon";
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine";
import GroupLayer from "@arcgis/core/layers/GroupLayer";
import {
  getpoint,
  getinsmTypes,
  getwaterfactory,
  getstoragepond,
  getAllMonitorData,
  layermenu,
  getAllMonitorDatadetail,
  getProjectList
} from "@/api/home/<USER>";
import Vue from "vue";
import SimplePopup from "./popup/simplePopup";
import BasePopup from "./popup/basePopup";
import polygons from "@/assets/ows.json";
import axios from "axios";

import { mapState } from "vuex";

export default {
  name: "InnerMap",
  props: {
    mapMode: {
      type: Boolean,
    },
  },
  components: {
    Popup1,
    MeterPopup,
    VideoPopup,
    WqPopup,
    ProjectPopup,
    ProjectPopupOther
  },
  data() {
    return {
      meterId: null,
      sitelistTemp: [], // 要素列表 temp 过滤
      searchText: "",
      handleSearch: null,
      siteShow: false,
      legendShow: false,
      projectMap: {
        sourceProject: "water-source",
        factoryProject: "water-factory",
        storagePondProject: "water-storage-pond",
      },
      legendCount: [
        {
          name: "水厂",
          count: 219,
          current: 0,
          img: require("@/assets/img/home/<USER>/leftCount1.png"),
        },
        {
          name: "蓄水池",
          count: 1267,
          current: 0,
          img: require("@/assets/img/home/<USER>/leftCount2.png"),
        },
        {
          name: "泵站",
          count: 33,
          current: 0,
          img: require("@/assets/img/home/<USER>/leftCount3.png"),
        },
        {
          name: "取水口",
          count: 877,
          current: 0,
          img: require("@/assets/img/home/<USER>/leftCount4.png"),
        },
        {
          name: "视频监控",
          count: 55,
          img: require("@/assets/img/home/<USER>/bottomCount1.png"),
        },
        {
          name: "水表",
          count: 5000,
          img: require("@/assets/img/home/<USER>/bottomCount2.png"),
        },
        {
          name: "流量计",
          count: 105,
          img: require("@/assets/img/home/<USER>/bottomCount3.png"),
        },
        {
          name: "液位计",
          count: 77,
          img: require("@/assets/img/home/<USER>/bottomCount4.png"),
        },
        {
          name: "压力传感器",
          count: 69,
          img: require("@/assets/img/home/<USER>/bottomCount5.png"),
        },
        {
          name: "水质监测仪",
          count: 16,
          img: require("@/assets/img/home/<USER>/bottomCount6.png"),
        },
        {
          name: "水文一体化站",
          count: 4,
          img: require("@/assets/img/home/<USER>/bottomCount7.png"),
        },
      ],
      engineShow: false, // 工程显示
      pointdata: {
        projectList: []
      },
      sitestate: false, // 列表显示
      isImg: false,
      view: null,
      map: null,
      geoUrl: null,
      borderLayer: null,
      tdtImageLayer: null,
      // layerControl: {
      //   factory: true,
      //   pond: true,
      //   pipe: true
      // },
      regimenlist: [
        {
          id: 31,
          label: "水文一体化",
          src: "",
          state: false,
          value: "hydro",
          count: 0,
        },
      ],
      pipelist: [
        {
          id: 1,
          label: "流量监测",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: "flow",
          count: 0,
        },
        {
          id: 2,
          label: "压力监测",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: "pressure",
          count: 0,
        },
        {
          id: 3,
          label: "液位监测",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: "level",
          count: 0,
        },
        {
          id: 4,
          label: "典型用户",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: "meter",
          count: 0,
        },
      ],
      qualitylist: [
        {
          id: 41,
          label: "水源水厂水质",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: "factoryAndSource",
          count: 0,
        },
        // {
        //   id: 42,
        //   label: "管网水质",
        //   src: "",
        //   state: false,
        //   value: 'pipenetwork'
        // },
        {
          id: 43,
          label: "末梢水质",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: "endUser",
          count: 0,
        },
        {
          id: 44,
          label: "人工监测水质",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: "labourMonitor",
          count: 0,
        },
      ],
      managerlist: [
        {
          id: 21,
          label: "水源工程",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: "sourceProject",
          count: 910
        },
        {
          id: 22,
          label: "水厂工程",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: "factoryProject",
          count: 219
        },
        {
          id: 23,
          label: "蓄水工程",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: "storagePondProject",
          count: 1267
        },
        // {
        //   id: 4,
        //   label: "净水工程",
        //   src: "",
        //   state: false,
        // },
        // {
        //   id: 5,
        //   label: "管道工程",
        //   src: "",
        //   state: false,
        // },
        {
          id: 24,
          label: "百人供水工程",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: 'projectLayer1',
          count: 0
        },
        {
          id: 25,
          label: "千人供水工程",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: 'projectLayer2',
          count: 0
        },
        {
          id: 26,
          label: "千吨万人供水工程",
          src: require("@/assets/img/home/<USER>"),
          state: false,
          value: 'projectLayer3',
          count: 0
        },
      ],
      layerControl: [
        {
          id: 1,
          name: "总图",
          default: require("@/assets/img/home/<USER>/layer1.png"),
          active: require("@/assets/img/home/<USER>/layer1-active.png"),
          layerId: "totalGroupLayer",
          visible: true,
        },
        {
          id: 2,
          name: "水厂",
          default: require("@/assets/img/home/<USER>/layer2.png"),
          active: require("@/assets/img/home/<USER>/layer2-active.png"),
          layerId: "factoryPointLayer",
          visible: false,
        },
        {
          id: 3,
          name: "蓄水池液位",
          default: require("@/assets/img/home/<USER>/layer3.png"),
          active: require("@/assets/img/home/<USER>/layer3-active.png"),
          layerId: "pondPointLayer",
          visible: false,
        },
        {
          id: 4,
          name: "压力点",
          default: require("@/assets/img/home/<USER>/layer4.png"),
          active: require("@/assets/img/home/<USER>/layer4-active.png"),
          layerId: "pressPointLayer",
          visible: false,
        },
        {
          id: 5,
          name: "水质",
          default: require("@/assets/img/home/<USER>/layer5.png"),
          active: require("@/assets/img/home/<USER>/layer5-active.png"),
          layerId: "qualityPointLayer",
          visible: false,
        },
        {
          id: 6,
          name: "泵站",
          default: require("@/assets/img/home/<USER>/layer6.png"),
          active: require("@/assets/img/home/<USER>/layer6-active.png"),
          layerId: "pumpPointLayer",
          visible: false,
        },
        {
          id: 7,
          name: "管网",
          default: require("@/assets/img/home/<USER>/layer7.png"),
          active: require("@/assets/img/home/<USER>/layer7-active.png"),
          layerId: "pipeLineLayer",
          visible: false,
        },
        {
          id: 8,
          name: "分区",
          default: require("@/assets/img/home/<USER>/layer8.png"),
          active: require("@/assets/img/home/<USER>/layer8-active.png"),
          layerId: "areaPolygonLayer",
          visible: false,
        },
      ],
      layerControl2: [
        {
          id: 1,
          name: "统计",
          default: require("@/assets/img/img566.png"),
          active: require("@/assets/img/img565.png"),
          layerId: "areaPolygonLayer",
          visible: false,
        },
        // {
        //   id: 2,
        //   name: "图例",
        //   default: require("@/assets/img/img566.png"),
        //   active: require("@/assets/img/img565.png"),
        //   layerId: "areaPolygonLayer",
        //   visible: false,
        // },
        {
          id: 3,
          name: "测点",
          default: require("@/assets/img/img566.png"),
          active: require("@/assets/img/img565.png"),
          layerId: "areaPolygonLayer",
          visible: false,
        },
        {
          id: 4,
          name: "工程",
          default: require("@/assets/img/img566.png"),
          active: require("@/assets/img/img565.png"),
          layerId: "areaPolygonLayer",
          visible: false,
        },
        {
          id: 5,
          name: "图层",
          default: require("@/assets/img/img566.png"),
          active: require("@/assets/img/img565.png"),
          layerId: "areaPolygonLayer",
          visible: false,
        },
      ],
      currentActiveLayer: "",
      juheStatus: false,
      showDetail: false, // 测点和图例显示
      lineType: [
        { type: 0, label: "引水管", color: "red", isActive: true },
        { type: 1, label: "配水管", color: "orange", isActive: false },
        { type: 2, label: "提水管", color: "green", isActive: false },
      ],
      cLineType: "",
      alllayers: [],
      popupstate: '',
      stationId: "",
      detaildata: {},
      pointCount: {
        flowCpunt: 0,
        pressureCount: 0,
        levelCount: 0,
        meterCount: 0,
        factoryCount: 0,
        sourceCount: 0,
        storagePondCount: 0,
        hydrographicCount: 0,
        factoryAndSourceCount: 0,
        endUserCount: 0,
        pipenetworkCount: 0,
        labourMonitorCount: 0,
        factoryProjectCount: 0,
        sourceProjectCount: 0,
        storagePondProjectCount: 0,
      },
      countryPolygons: [],
      countryPolygonsPointCount: [],
      centerPointLayer: null,
      waterFactoryPoints: [
        {
          id: 9,
          name: "谭湾水厂",
          longitude: 110.7227346666861,
          latitude: 31.316562565772447,
        },
        {
          id: 10,
          name: "双龙观水厂",
          longitude: 110.71362353984955,
          latitude: 31.3115926927495,
        },
        {
          id: 1,
          name: "青华水厂",
          longitude: 110.80494946149302,
          latitude: 31.250801558720447,
        },
        {
          id: 2,
          name: "大礼水厂",
          longitude: 110.71533482870338,
          latitude: 31.223431626828795,
        },
        {
          id: 5,
          name: "吴家坪水厂",
          longitude: 110.79361795570478,
          latitude: 31.16420279335171,
        },
      ],
      pointTimer: null
    };
  },
  computed: {
    isOverview() {
      return this.$route.query.state === "overview";
    },
    siteItemName() {
      let temp = "";
      switch (this.popupstate) {
        case "flow":
        case "pressure":
        case "level":
        case "meter":
          temp = "管网监测列表";
          break;
        case "factoryVideo":
        case "sourceVideo":
        case "storagePondVideo":
          temp = "视频监测列表";
          break;
        case "hydro":
          temp = "水情监测列表";
          break;
        case "factoryAndSource":
        case "pipenetwork":
        case "endUser":
        case "labourMonitor":
          temp = "水质监测列表";
          break;
        case "sourceProject":
        case "factoryProject":
        case "storagePondProject":
        case "projectLayer1":
        case "projectLayer2":
        case "projectLayer3":
          temp = "工程管理列表";
          break;
        default:
          temp = "列表";
      }
      return temp;
    },
  },
  mounted() {
    this.handleCountryPolygons();
    this.geoUrl = process.env.VUE_APP_GEO_URL;
    this.getmenu();
    this.handleSearch = _.debounce(() => {
      this.getInputValue();
    }, 1000);
  },
  methods: {
    closeAll() {
      this.siteShow = false;
      this.legendShow = false;
      this.sitestate = false;
      this.popupstate = "";
      this.layerControl2.forEach((item) => {
        item.visible = false;
      });
    },
    changeactiveid(item) {
      this.activeid = item.facilityId;
      this.getdetail(item);
      console.log("右侧列表", item);
      this.popupstate = item.datatype;

      // 点击列表项高亮水表图层对应要素
      let targetLayer = this[item.datatype + "Layer"];
      console.log(targetLayer);
      let query = targetLayer.createQuery();
      query.where = `facilityId = ${item.facilityId}`;
      query.outFields = ["*"];

      this.view.whenLayerView(targetLayer).then((layerView) => {
        this.meterlayerView = layerView;
        targetLayer.queryFeatures(query).then((result) => {
          let featureOne = result.features[0];
          // if (this.highlightHandle) this.highlightHandle.remove()
          // this.highlightHandle = this.meterlayerView.highlight(featureOne)
          
          // 点击列表点位的高亮
          this.view.graphics.removeAll();
          let graphic = new Graphic({
            geometry: featureOne.geometry,
            symbol: {
              type: "simple-marker",
              style: "circle",
              color: [255, 0, 0, 1],
              size: 16,
              yoffset: 1,
            },
          });
          this.view.graphics.add(graphic);
          this.flashPoint(graphic)
        });
      });
    },
    closepopup() {
      this.showDetail = false;
      this.layerControl2.forEach((obj) => {
        if (obj.name != "统计") {
          obj.visible = false;
        }
      });
    },
    async getmenu() {
      await layermenu().then((res) => {
        // 管网监测点位
        this.pointdata.flow = res.data.pipeMonitoringResp.waterFlow;
        this.pipelist[0].count = this.pointdata.flow.length
        this.pointdata.level = res.data.pipeMonitoringResp.waterLevel;
        this.pipelist[2].count = this.pointdata.level.length
        this.pointdata.pressure = res.data.pipeMonitoringResp.waterPressure;
        this.pipelist[1].count = this.pointdata.pressure.length
        this.pointdata.meter = res.data.pipeMonitoringResp.waterMeter;
        this.pipelist[3].count = this.pointdata.meter.length

        // 视频监测点位
        this.pointdata.factory = res.data.videoMonitorResp.factory;
        this.pointdata.source = res.data.videoMonitorResp.source;
        this.pointdata.storagePond = res.data.videoMonitorResp.storagePond;

        // 水情监测
        this.pointdata.hydro = res.data.hydrographicResp.hydrographic;
        this.regimenlist[0].count = this.pointdata.hydro.length

        // 水质监测
        this.pointdata.factoryAndSource = res.data.waterQualityMonitoringResp.factoryAndSource;
        this.qualitylist[0].count = this.pointdata.factoryAndSource.length
        this.pointdata.pipenetwork = res.data.waterQualityMonitoringResp.pipenetwork;
        this.pointdata.endUser = res.data.waterQualityMonitoringResp.endUser;
        this.qualitylist[1].count = this.pointdata.endUser.length
        this.pointdata.labourMonitor = res.data.waterQualityMonitoringResp.labourMonitor;
        this.qualitylist[2].count = this.pointdata.labourMonitor.length

        // 工程管理
        this.pointdata.factoryProject = res.data.waterSupplyProjectResp.waterFactoryRespList;
        this.pointdata.sourceProject = res.data.waterSupplyProjectResp.waterSourceRespList;
        this.pointdata.storagePondProject = res.data.waterSupplyProjectResp.waterStoragePondRespList;
      });
      await getProjectList().then(res => {
        // 取有坐标
        this.pointdata.projectList = res.data.filter(item => item.coordinate)
      }).catch(err => {
        this.createMap()
      })
      this.createMap();
    },
    changelayer(val) {
      val.state = !val.state;
      // 图层切换
      this.alllayers.forEach((layer) => {
        if (layer.id === val.value) {
          if (val.state && this.juheStatus) {
            layer.pointShow = val.state;
          } else {
            layer.visible = val.state;
            layer.pointShow = val.state;
          }
        }
      });
      const areAllLayersInvisible = this.alllayers.every(
        (layer) => !layer.pointShow
      );
      if (this.centerPointLayer) {
        if (areAllLayersInvisible) {
          this.view.map.remove(this.centerPointLayer);
          this.centerPointLayer = null;
        } else {
          this.view.map.remove(this.centerPointLayer);
          this.centerPointLayer = null;
          this.countryPolygonsPointCount = [];
          this.alllayers.forEach((layer) => {
            if (layer.pointShow) {
              this.handlePointInPolygonCounts(
                this.pointdata[`${layer.id}`],
                layer.id
              );
            }
          });
          const mergedData = Object.values(
            this.countryPolygonsPointCount.reduce((acc, curr) => {
              const key = `${curr.x}-${curr.y}`;
              if (!acc[key]) {
                acc[key] = { ...curr };
              } else {
                acc[key].count += curr.count;
              }
              return acc;
            }, {})
          );
          this.centerPointLayer = this.createCenterPoint(mergedData);
          this.view.map.add(this.centerPointLayer);
        }
      }
    },
    changeBase() {
      this.isImg = !this.isImg;
      if (this.isImg) {
        this.tdtImageLayer.visible = true;
        this.tdtImageNoteLayer.visible = false;
      } else {
        this.tdtImageLayer.visible = false;
        this.tdtImageNoteLayer.visible = true;
      }
    },

    changLayer2(item) {
      // 隐藏左右面板
      this.$emit("fromInnerMap");

      this.layerControl2.forEach((obj) => {
        if (obj.name != "统计") {
          obj.visible = false;
        }
      });
      item.visible = !item.visible;

      if (item.name == "测点") {
        this.siteShow = true;
        this.legendShow = false;
        this.showDetail = true;
        this.engineShow = false;
      } else if (item.name == "工程") {
        this.siteShow = true;
        this.legendShow = false;
        this.showDetail = true;
        this.engineShow = true;
      } else if (item.name == "图层") {
        this.changeBase();
      } else if (item.name == "图例") {
        this.showDetail = true;
        this.legendShow = true;
        this.siteShow = false;
      } else if (item.name == "统计") {
        //每次聚合，清空之前的聚合点计数信息，重新计算聚合点
        // const areAllLayersInvisible = this.alllayers.every(layer => !layer.visible);
        // if (areAllLayersInvisible) {
        //   this.$message.warning('请先打开测点')
        // }
        this.juheStatus = !this.juheStatus;
        this.countryPolygonsPointCount = [];
        this.alllayers.forEach((layer) => {
          if (layer.pointShow) {
            this.handlePointInPolygonCounts(
              this.pointdata[`${layer.id}`],
              layer.id
            );
          }
        });
        if (this.centerPointLayer) {
          this.alllayers.forEach((layer) => {
            if (layer.pointShow) {
              layer.visible = true;
            }
          });
          this.view.map.remove(this.centerPointLayer);
          this.centerPointLayer = null;
          return;
        } else {
          this.alllayers.forEach((layer) => {
            if (layer.pointShow) {
              layer.visible = false;
            }
          });
        }
        // 将具有相同的XY坐标的点的count合并计算
        const mergedData = Object.values(
          this.countryPolygonsPointCount.reduce((acc, curr) => {
            const key = `${curr.x}-${curr.y}`;
            if (!acc[key]) {
              acc[key] = { ...curr };
            } else {
              acc[key].count += curr.count;
            }
            return acc;
          }, {})
        );
        this.centerPointLayer = this.createCenterPoint(mergedData);
        this.view.map.add(this.centerPointLayer);
      }
    },
    changLayer(item) {
      this.view.popup.close();
      // console.log(this.view.map)
      // item.visible = !item.visible

      this.currentActiveLayer = item.layerId;

      this.layerControl.forEach((obj) => {
        obj.visible = false;
      });
      item.visible = true;

      let layers = this.view.map.layers;
      layers.forEach((layer) => {
        if (layer.id !== item.layers) {
          layer.visible = false;
        }
      });
      if (item.layerId) {
        let targrtLayer = this.view.map.findLayerById(item.layerId);
        targrtLayer && (targrtLayer.visible = true);
      }
    },

    getInputValue() {
      // 过滤
      this.sitelistTemp = this.sitelist.filter((item) =>
        item.facilityName.includes(this.searchText)
      );
    },

    createMap() {
      // 边界
      this.borderLayer = new GeoJSONLayer({
        id: "borderLayer",
        name: "borderLayer",
        // url: "/border.json",
        url: this.geoUrl + '/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Atown&maxFeatures=50&outputFormat=application%2Fjson',
        renderer: {
          type: "simple",
          symbol: {
            type: "simple-fill",
            color: [255, 255, 255, 0],
            outline: {
              width: 1,
              color: "#00B3FF",
            },
          },
        },
      });

      // 天地图影像图-标注
      this.tdtImageNoteLayer = new WebTileLayer({
        id: "tdtImageNoteLayer",
        // urlTemplate: 'http://t{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
        urlTemplate:
          "https://t{subDomain}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6",
        subDomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
        visible: false,
      });
      // 天地图影像图-影像图
      // this.tdtImageLayer = new WebTileLayer({
      //   id: 'tdtImageLayer',
      //   urlTemplate: 'http://{subDomain}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
      //   subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      //   visible: false
      // })

      // mapbox
      // this.tdtImageLayer = new WebTileLayer({
      //   id: 'mapBoxTileLayer',
      //   urlTemplate: 'https://api.mapbox.com/v4/mapbox.satellite/{level}/{col}/{row}.webp?access_token=pk.eyJ1IjoidGFuZ2d1b2NoYW5nIiwiYSI6ImNtNzE4dGYxdTA0aHUya3B4dWViM3l3cWsifQ.awtc46Zfl0VCpgNd5JCFXQ',
      //   visible: false
      // })

      // 吉林一号
      this.tdtImageLayer = new WebTileLayer({
        id: "JLTileLayer",
        urlTemplate:
          "https://api.jl1mall.com/getMap/{z}/{x}/{y}?mk=73ad26c4aa6957eef051ecc5a15308b4&tk=24277043ba78c68ff6ed3b50ce486283&pro=ca3a3754837f49d7ac3068edce0e65f7&sch=wmts",
        // visible: false,
      });

      /* 总图，展示之前挑选出来的水厂 蓄水池 干线水厂管线 */
      // 水厂
      let graphics = [];
      this.waterFactoryPoints.forEach((item) => {
        let point = {
          type: "point",
          longitude: item.longitude,
          latitude: item.latitude,
        };
        let attributes = {
          id: item.id,
          name: item.name,
        };
        let graphic = new Graphic({
          geometry: point,
          // symbol: pmSymbol,
          attributes,
        });
        graphics.push(graphic);
      });
      let waterFactoryLayer = new FeatureLayer({
        id: "waterFactoryLayer",
        source: graphics,
        objectIdField: "OBJECTID",
        fields: [
          { name: "id", type: "integer" },
          { name: "name", type: "string" },
        ],
        outFields: ["*"],
        labelingInfo: [
          {
            labelExpressionInfo: {
              expression: "$feature.name",
            },
            labelPlacement: "above-center",
            symbol: {
              type: "text",
              color: "white",
              font: { size: 12 },
            },
          },
        ],
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require("@/assets/img/home/<USER>"),
            // width: '21.5px',
            // height: '53px',
            width: "43px",
            height: "106px",
            yoffset: "53px",
          },
        },
      });
      // 干线蓄水池
      let mainPondLayer = new WFSLayer({
        url: this.geoUrl + "/geoserver/xingshan/wfs",
        name: "xingshan:main_pond",
        id: "mainPondLayer",
        outFields: ["*"],
        labelingInfo: [
          {
            labelExpressionInfo: {
              expression: "$feature.station_name",
            },
            labelPlacement: "above-center",
            symbol: {
              type: "text",
              color: "white",
              font: { size: 10 },
            },
            minScale: 72223,
          },
        ],
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require("@/assets/img/home/<USER>"),
            width: "20px",
            height: "27.5px",
            yoffset: "13.75px",
          },
        },
      });
      // 干线水厂管线
      let mainFactoryPipeLayer = new WFSLayer({
        url: this.geoUrl + "/geoserver/xingshan/wfs",
        name: "xingshan:main_factory_pipe",
        id: "mainFactoryPipeLayer",
        renderer: {
          type: "simple",
          symbol: {
            type: "simple-line",
            // color: '#2CECEA',
            color: "red",
            width: 2,
          },
        },
      });
      // GroupLayer
      this.totalGroupLayer = new GroupLayer({
        id: "totalGroupLayer",
        // layers: [mainFactoryPipeLayer, mainPondLayer, waterFactoryLayer],
        layers: [waterFactoryLayer],
      });

      // // 水厂
      // this.factoryPointLayer = new WFSLayer({
      //   url: this.geoUrl + "/geoserver/xingshan/wfs",
      //   name: "xingshan:water_factory",
      //   id: "factoryPointLayer",
      //   visible: false,
      //   outFields: ["*"],
      //   renderer: {
      //     type: "simple",
      //     symbol: {
      //       type: "picture-marker",
      //       url: require("@/assets/img/home/<USER>/factoryIcon.png"),
      //       width: 16,
      //       height: 16,
      //     },
      //   },
      // });

      // // 蓄水池液位
      // this.pondPointLayer = new WFSLayer({
      //   url: this.geoUrl + "/geoserver/xingshan/wfs",
      //   // name: 'xingshan:water_storage_pond',
      //   name: "xingshan:view_water_level",
      //   id: "pondPointLayer",
      //   visible: false,
      //   outFields: ["*"],
      //   renderer: {
      //     type: "simple",
      //     symbol: {
      //       type: "picture-marker",
      //       url: require("@/assets/img/home/<USER>/pondIcon.png"),
      //       width: 20,
      //       height: 20,
      //     },
      //   },
      // });

      // // 压力点
      // this.pressPointLayer = new WFSLayer({
      //   url: this.geoUrl + "/geoserver/xingshan/wfs",
      //   name: "xingshan:view_press_insm",
      //   id: "pressPointLayer",
      //   visible: false,
      //   outFields: ["*"],
      //   renderer: {
      //     type: "simple",
      //     symbol: {
      //       type: "picture-marker",
      //       url: require("@/assets/img/home/<USER>/pressIcon.png"),
      //       width: 16,
      //       height: 16,
      //     },
      //   },
      // });

      // // 水质
      // this.qualityPointLayer = new WFSLayer({
      //   url: this.geoUrl + "/geoserver/xingshan/wfs",
      //   name: "xingshan:view_quality_insm",
      //   id: "qualityPointLayer",
      //   visible: false,
      //   outFields: ["*"],
      //   renderer: {
      //     type: "simple",
      //     symbol: {
      //       type: "picture-marker",
      //       url: require("@/assets/img/home/<USER>/qualityIcon.png"),
      //       width: 16,
      //       height: 16,
      //     },
      //   },
      // });

      // // 泵站
      // this.pumpPointLayer = new WFSLayer({
      //   url: this.geoUrl + "/geoserver/xingshan/wfs",
      //   name: "xingshan:water_supply_pumping_station",
      //   id: "pumpPointLayer",
      //   visible: false,
      //   outFields: ["*"],
      //   renderer: {
      //     type: "simple",
      //     symbol: {
      //       type: "picture-marker",
      //       url: require("@/assets/img/home/<USER>/pumpIcon.png"),
      //       width: 16,
      //       height: 16,
      //     },
      //   },
      // });

      // // 管网
      // this.pipeLineLayer = new WFSLayer({
      //   url: this.geoUrl + "/geoserver/xingshan/wfs",
      //   name: "xingshan:total_pipe_network",
      //   id: "pipeLineLayer",
      //   visible: false,
      //   outFields: ["*"],
      //   definitionExpression: "",
      //   // 值渲染器
      //   renderer: {
      //     type: "unique-value",
      //     field: "type",
      //     defaultSymbol: { type: "simple-line" },
      //     uniqueValueInfos: [
      //       {
      //         value: 0,
      //         symbol: {
      //           type: "simple-line",
      //           // color: '#14B784',
      //           // color: '#2CECEA',
      //           // color: '#20A162',
      //           color: "red",
      //           width: 2,
      //         },
      //       },
      //       {
      //         value: 1,
      //         symbol: {
      //           type: "simple-line",
      //           // color: 'Magenta',
      //           color: "orange",
      //           width: 2,
      //         },
      //       },
      //       {
      //         value: 2,
      //         symbol: {
      //           type: "simple-line",
      //           color: "green",
      //           // color: '#2486b9',
      //           width: 2,
      //         },
      //       },
      //     ],
      //   },
      // });

      // // 分区
      // this.areaPolygonLayer = new WFSLayer({
      //   url: this.geoUrl + "/geoserver/xingshan/wfs",
      //   name: "xingshan:village",
      //   id: "areaPolygonLayer",
      //   visible: false,
      //   outFields: ["*"],
      //   renderer: {
      //     type: "simple",
      //     symbol: {
      //       type: "simple-fill",
      //       color: [51, 153, 255, 0.4],
      //       outline: {
      //         color: [0, 0, 0],
      //         width: 1,
      //       },
      //     },
      //   },
      // });

      this.map = new Map({
        basemap: {
          baseLayers: [
            // this.darkLayer,
            this.tdtImageLayer,
            this.tdtImageNoteLayer,
            this.borderLayer,
          ],
        },
      });
      this.view = new MapView({
        container: "map",
        // background: {
        //   type: 'color',
        //   color: [0, 0, 24, 1]
        // },
        map: this.map,
        // constraints 指定可应用于MapView的scale、zoom和旋转约束
        extent: {
          xmin: 110.42369972561853,
          ymin: 31.08945033264494,
          xmax: 111.11518062163354,
          ymax: 31.572430067740488,
        },
        constraints: {
          minZoom: 11,
          maxZoom: 18,
        },
      });

      // let maskLayer = new GraphicsLayer({
      //   id: "maskLayer",
      // });
      // maskLayer.add(
      //   new Graphic({
      //     geometry: new Polygon({
      //       rings: [
      //         [-180, 90], // 左上角
      //         [-180, -90], // 左下角
      //         [180, -90], // 右下角
      //         [180, 90], // 右上角
      //         [-180, 90], // 闭合
      //       ],
      //       spatialReference: { wkid: 4326 },
      //     }),
      //     symbol: {
      //       type: "simple-fill",
      //       color: [0, 0, 0, 0.7],
      //       outline: null,
      //     },
      //   })
      // );

      this.view.popup.autoOpenEnabled = false;
      // 去掉地图多余组件
      this.view.ui.remove("attribution");
      this.view.ui.empty("top-left");

      // this.view.map.add(this.tdtImageLayer)
      // this.view.map.add(this.borderLayer)
      // this.view.map.add(maskLayer)

      // this.view.map.add(this.mainFactoryPipeLayer)
      // this.view.map.add(this.mainPondLayer)
      // this.view.map.add(waterFactoryLayer)
      // console.log(waterFactoryLayer)
      this.view.map.add(this.totalGroupLayer); // 总图
      // this.view.map.add(this.factoryPointLayer);
      // this.view.map.add(this.pondPointLayer);
      // this.view.map.add(this.pressPointLayer);
      // this.view.map.add(this.qualityPointLayer);
      // this.view.map.add(this.pumpPointLayer);
      // this.view.map.add(this.pipeLineLayer);
      // this.view.map.add(this.areaPolygonLayer);

      // 监听地图点击
      this.view.on("click", (event) => {
        this.view.hitTest(event).then((res) => {
          let hitResults = res.results;

          if (hitResults.length) {
            let result = hitResults[0];
            console.log("click hitTest", result);

            if (result.graphic.layer.id === "waterFactoryLayer") {
              this.handleWaterFactoryPointClick(result.graphic.attributes.name);
            }

            if (result && result.graphic.layer.id.includes("Point")) {
              this.showPointName(result);
            }
          }
          let graphicOne = res.results[0].graphic;
          if (res.results[0]) {
            res.results.forEach((ele, idx) => {
              if (
                idx > 0 &&
                ele.graphic.attributes.facilityId ===
                  res.results[0].graphic.attributes.facilityId
              ) {
                res.results[0].graphic.attributes.extra =
                  res.results[0].graphic.attributes.extra +
                  "," +
                  ele.graphic.attributes.extra;
              }
            });
            // 定位
            if (
              res.results[0].graphic.geometry.type === "point" &&
              res.results[0].graphic.layer.id !== "mainPondLayer"
            ) {
              this.view.goTo(res.results[0].graphic);
              this.getsitelist(res.results[0]);

              // 点击高亮
              // if(graphicOne.geometry.type === 'point') {
              //   if(this.highlightHandle) this.highlightHandle.remove()
              //   this.view.whenLayerView(graphicOne.layer).then(layerView => {
              //     this.highlightHandle = layerView.highlight(graphicOne)
              //   })
              // }

              this.view.graphics.removeAll();
              let graphic = new Graphic({
                geometry: graphicOne.geometry,
                symbol: {
                  type: "simple-marker",
                  style: "circle",
                  color: [255, 0, 0, 1],
                  size: 16,
                  yoffset: 1,
                },
              });
              this.view.graphics.add(graphic);
              this.flashPoint(graphic) 
            }
          }
        });
      });

      let popupOpened = false;
      let lastPopupPoint;
      this.view.on("pointer-move", (event) => {
        let screenPoint = {
          x: event.x,
          y: event.y,
        };

        this.view.hitTest(screenPoint).then(async (response) => {
          let hitResults = response.results;

          let result = hitResults[0];
          // 水厂点位
          if (result && result.graphic.layer.id === "waterFactoryLayer") {
            let point = result.graphic.geometry;
            if (lastPopupPoint && lastPopupPoint.equals(point)) {
              return;
            }

            let p = Vue.extend(SimplePopup);
            let popupInstance = new p({
              propsData: {
                id: result.graphic.attributes.id,
                msg: result.graphic.attributes.name,
              },
            });
            popupInstance.$mount();
            this.view.popup.open({
              location: result.mapPoint,
              content: popupInstance.$el,
            });

            lastPopupPoint = point;
            popupOpened = true;
          } else {
            lastPopupPoint = null;
          }
        });
      });

      this.view.when(() => {
        this.view.zoom = 12;
        this.view.goTo({
          center: [110.75210994390093, 31.25307163396126],
        });
        // 管网监测
        this.flowLayer = this.createfealayer(
          this.pointdata.flow,
          "flow",
          "maphome1"
        );
        this.levelLayer = this.createfealayer(
          this.pointdata.level,
          "level",
          "maphome2"
        );
        this.pressureLayer = this.createfealayer(
          this.pointdata.pressure,
          "pressure",
          "maphome3"
        );
        this.meterLayer = this.createfealayer(
          this.pointdata.meter,
          "meter",
          "maphome4"
        );
        // 视频监测
        this.factoryVideoLayer = this.createfealayer(
          this.pointdata.factory,
          "factoryVideo",
          "maphome5"
        );
        this.sourceVideoLayer = this.createfealayer(
          this.pointdata.source,
          "sourceVideo",
          "maphome6"
        );
        this.storagePondVideoLayer = this.createfealayer(
          this.pointdata.storagePond,
          "storagePondVideo",
          "maphome7"
        );
        // 水情监测
        this.hydroGraphicLayer = this.createfealayer(
          this.pointdata.hydro,
          "hydro",
          "maphome8"
        );
        // 水质监测
        this.factoryAndSourceLayer = this.createfealayer(
          this.pointdata.factoryAndSource,
          "factoryAndSource",
          "maphome9"
        );
        // this.pipenetworkLayer = this.createfealayer(
        //   this.pointdata.pipenetwork,
        //   "pipenetwork",
        //   "maphome10"
        // );
        this.endUserLayer = this.createfealayer(
          this.pointdata.endUser,
          "endUser",
          "maphome11"
        );
        this.labourMonitorLayer = this.createfealayer(
          this.pointdata.labourMonitor,
          "labourMonitor",
          "maphome12"
        );
        // 工程管理
        this.sourceProjectLayer = this.createfealayer(
          this.pointdata.sourceProject,
          "sourceProject",
          "maphome13"
        );
        this.factoryProjectLayer = this.createfealayer(
          this.pointdata.factoryProject,
          "factoryProject",
          "maphome14"
        );
        this.storagePondProjectLayer = this.createfealayer(
          this.pointdata.storagePondProject,
          "storagePondProject",
          "maphome15"
        );

        // 供水工程
        const result1 = this.createProjectLayerByType('projectLayer1', 'HUNDRED_P', 'maphomeHP')
        this.projectLayer1Layer = result1.featureLayer
        this.managerlist[3].count = result1.count
        const result2 = this.createProjectLayerByType('projectLayer2', 'THOUSAND_P', 'maphomeTP')
        this.projectLayer2Layer = result2.featureLayer
        this.managerlist[4].count = result2.count
        const result3 = this.createProjectLayerByType('projectLayer3', 'THOUSAND_T', 'maphomeTT')
        this.projectLayer3Layer = result3.featureLayer
        this.managerlist[5].count = result3.count

        // 图层处理，放在同一个数组内，方便操作显隐
        this.alllayers.push(this.flowLayer);
        this.alllayers.push(this.levelLayer);
        this.alllayers.push(this.pressureLayer);
        this.alllayers.push(this.meterLayer);
        this.alllayers.push(this.factoryVideoLayer);
        this.alllayers.push(this.sourceVideoLayer);
        this.alllayers.push(this.storagePondVideoLayer);
        this.alllayers.push(this.hydroGraphicLayer);
        this.alllayers.push(this.factoryAndSourceLayer);
        // this.alllayers.push(this.pipenetworkLayer)
        this.alllayers.push(this.endUserLayer);
        this.alllayers.push(this.labourMonitorLayer);
        this.alllayers.push(this.sourceProjectLayer);
        this.alllayers.push(this.factoryProjectLayer);
        this.alllayers.push(this.storagePondProjectLayer);
        this.alllayers.push(this.projectLayer1Layer)
        this.alllayers.push(this.projectLayer2Layer)
        this.alllayers.push(this.projectLayer3Layer)

        this.view.map.add(this.baseLayer);

        // 图层全部添加进地图
        this.view.map.add(this.flowLayer);
        this.view.map.add(this.levelLayer);
        this.view.map.add(this.pressureLayer);
        this.view.map.add(this.meterLayer);
        // this.view.whenLayerView(this.meterLayer).then(layerView => {
        //   this.meterlayerView = layerView
        // })
        this.view.map.add(this.factoryVideoLayer);
        this.view.map.add(this.sourceVideoLayer);
        this.view.map.add(this.storagePondVideoLayer);
        this.view.map.add(this.hydroGraphicLayer);
        this.view.map.add(this.factoryAndSourceLayer);
        // this.view.map.add(this.pipenetworkLayer);
        this.view.map.add(this.endUserLayer);
        this.view.map.add(this.labourMonitorLayer);
        this.view.map.add(this.sourceProjectLayer);
        this.view.map.add(this.factoryProjectLayer);
        this.view.map.add(this.storagePondProjectLayer);
        this.view.map.add(this.projectLayer1Layer)
        this.view.map.add(this.projectLayer2Layer)
        this.view.map.add(this.projectLayer3Layer)
      });

      // 获取兴山县边界geojson
      let borderPolygon = [];
      let unionPolygon = null
      let townJsonUrl = this.geoUrl + '/geoserver/xingshan/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=xingshan%3Atown&maxFeatures=50&outputFormat=application%2Fjson'
      async function getBorder() {
        await axios.get('/xsBorder.geojson').then((res) => {
          
          // let ret = res.data.features
          // for (let i = 0; i < ret.length; i++) {
          //   borderPolygon.push(new Polygon({
          //     rings: ret[i].geometry.coordinates[0],
          //     spatialReference: { wkid: 4326 }
          //   }))
          // }
          let ret = res.data.features[0].geometry.coordinates;
          borderPolygon = new Polygon({
            rings: ret.map((item) => item[0]),
            spatialReference: { wkid: 4326 },
          });
          // unionPolygon = geometryEngine.union(borderPolygon)
        });
      }

      this.view.whenLayerView(this.tdtImageLayer).then(async (layerView) => {
        // 创建一个大的多边形
        const fullPolygon = new Polygon({
          rings: [
            [-180, 90], // 左上角
            [-180, -90], // 左下角
            [180, -90], // 右下角
            [180, 90], // 右上角
            [-180, 90], // 闭合
          ],
          spatialReference: { wkid: 4326 },
        });

        await getBorder();

        // 创建两个几何图形的差异
        const maskPolygon = geometryEngine.difference(
          fullPolygon,
          borderPolygon
          // unionPolygon
        );
        const maskSymbol = {
          type: "simple-fill",
          color: [0, 0, 0, 0.7],
          outline: null,
        };
        const maskGraphic = new Graphic({
          geometry: maskPolygon,
          symbol: maskSymbol,
        });
        this.view.graphics.add(maskGraphic);

        // this.view.graphics.add(fullGraphic)
      });
    },
    // 创建工程图层
    createProjectLayerByType(id, type, icon, visible = false) {
      const data = this.pointdata.projectList.filter(item => item.type === type)
      const graphics = data.map(item => {
        return new Graphic({
          geometry: {
            type: 'point',
            longitude: item.coordinate.x,
            latitude: item.coordinate.y
          },
          attributes: {
            datatype: id,
            facilityId: item.id,
            facilityName: item.name,
            id: item.id,
            type: item.type,
            name: item.name,
            supplyScale: item.supplyScale,
            population: item.population,
            supplyPopulation: item.supplyPopulation,
            households: item.households
          }
        })
      })

      const fields = [
        { name: 'ObjectID', type: 'oid' },
        { name: 'datatype', type: 'string' },
        { name: 'facilityId', type: 'integer' },
        { name: 'facilityName', type: 'string' },
        { name: 'id', type: 'integer' },
        { name: 'type', type: 'string' },
        { name: 'name', type: 'string' },
        { name: 'supplyScale', type: 'integer' },
        { name: 'population', type: 'integer' },
        { name: 'supplyPopulation', type: 'integer' },
        { name: 'households', type: 'integer' },
      ]

      const featureLayer  = new FeatureLayer({
        id,
        source: graphics,
        outFields: ['*'],
        fields,
        visible,
        renderer: {
          type: 'simple',
          symbol: {
            type: 'picture-marker',
            url: require(`../../assets/img/home/<USER>
            width: "20px",
            height: "24px",
          }
        }
      })
      return {
        featureLayer,
        count: data.length
      }
    },
    flashPoint(graphic) {
      let isOne = false
      if(this.pointTimer) {
        console.log('clearInterval')
        clearInterval(this.pointTimer)
      }
      this.pointTimer = setInterval(() => {
        isOne = !isOne
        if(isOne) {
          graphic.symbol = {
            type: "simple-marker",
            style: "circle",
            color: [241, 187, 23, 0.5],
            size: 16,
            yoffset: 1,
          }
        } else {
          graphic.symbol= {
            type: "simple-marker",
            style: "circle",
            color: [241, 187, 23, 1],
            size: 16,
            yoffset: 1,
          }
        }
      }, 2000)
    },
    // 当前选择的点位所在的图层列表
    getsitelist(data) {
      this.sitestate = true;
      this.activeid = data.graphic.attributes.facilityId;
      this.getdetail(data.graphic.attributes);
      switch (data.graphic.attributes.datatype) {
        case "flow":
          this.sitelist = this.sitelistTemp = this.pointdata.flow.map(
            (item) => {
              return {
                ...item,
                datatype: "flow",
              };
            }
          );
          break;
        case "level":
          this.sitelist = this.sitelistTemp = this.pointdata.level.map(
            (item) => {
              return {
                ...item,
                datatype: "level",
              };
            }
          );
          break;
        case "pressure":
          this.sitelist = this.sitelistTemp = this.pointdata.pressure.map(
            (item) => {
              return {
                ...item,
                datatype: "pressure",
              };
            }
          );
          break;
        case "meter":
          this.sitelist = this.sitelistTemp = this.pointdata.meter.map(
            (item) => {
              return {
                ...item,
                datatype: "meter",
              };
            }
          );
          break;
        case "factoryVideo":
          this.sitelist = this.sitelistTemp = this.pointdata.factory.map(
            (item) => {
              return {
                ...item,
                datatype: "factoryVideo",
              };
            }
          );
          break;
        case "sourceVideo":
          this.sitelist = this.sitelistTemp = this.pointdata.source.map(
            (item) => {
              return {
                ...item,
                datatype: "sourceVideo",
              };
            }
          );
          break;
        case "storagePondVideo":
          this.sitelist = this.sitelistTemp = this.pointdata.storagePond.map(
            (item) => {
              return {
                ...item,
                datatype: "storagePondVideo",
              };
            }
          );
          break;
        case "hydro":
          this.sitelist = this.sitelistTemp = this.pointdata.hydro.map(item => {
            return {
              ...item,
              datatype: 'hydro'
            }
          });
          break;
        case "factoryAndSource":
          this.sitelist = this.sitelistTemp = this.pointdata.factoryAndSource.map(
            (item) => {
              return {
                ...item,
                datatype: "factoryAndSource",
              };
            }
          );
          break;
        case "pipenetwork":
          this.sitelist = this.sitelistTemp = this.pointdata.pipenetwork.map(
            (item) => {
              return {
                ...item,
                datatype: "pipenetwork",
              };
            }
          );
          break;
        case "endUser":
          this.sitelist = this.sitelistTemp = this.pointdata.endUser.map(
            (item) => {
              return {
                ...item,
                datatype: "endUser",
              };
            }
          );
          break;
        case "labourMonitor":
          this.sitelist = this.sitelistTemp = this.pointdata.labourMonitor.map(
            (item) => {
              return {
                ...item,
                datatype: "labourMonitor",
              };
            }
          );
          break;
        case "sourceProject":
          this.sitelist = this.sitelistTemp = this.pointdata.sourceProject.map(
            (item) => {
              return {
                ...item,
                datatype: "sourceProject",
              };
            }
          );
          break;
        case "factoryProject":
          this.sitelist = this.sitelistTemp = this.pointdata.factoryProject.map(
            (item) => {
              return {
                ...item,
                datatype: "factoryProject",
              };
            }
          );
          break;
        case "storagePondProject":
          this.sitelist = this.sitelistTemp = this.pointdata.storagePondProject.map(
            (item) => {
              return {
                ...item,
                datatype: "storagePondProject",
              };
            }
          );
          break;
        case "projectLayer1":
          this.sitelist = this.sitelistTemp = this.pointdata.projectList.filter(item => item.type === 'HUNDRED_P')
            .map(
            (item) => {
              return {
                ...item,
                facilityId: item.id,
                facilityName: item.name,
                datatype: "projectLayer1",
              };
            }
          );
          break;
        case "projectLayer2":
          this.sitelist = this.sitelistTemp = this.pointdata.projectList.filter(item => item.type === 'THOUSAND_P')
            .map(
            (item) => {
              return {
                ...item,
                facilityId: item.id,
                facilityName: item.name,
                datatype: "projectLayer2",
              };
            }
          );
          break;
        case "projectLayer3":
          this.sitelist = this.sitelistTemp = this.pointdata.projectList.filter(item => item.type === 'THOUSAND_T')
            .map(
            (item) => {
              return {
                ...item,
                facilityId: item.id,
                facilityName: item.name,
                datatype: "projectLayer3",
              };
            }
          );
          break;
      }
    },
    // 点击点位获取信息，打开弹窗
    getdetail(val) {
      // 地图定位到当前点位所在经纬度
      if (val.coordinate) {
        //创建点
        let point = {
          type: "point",
          latitude: val.coordinate.y,
          longitude: val.coordinate.x,
        };
        let graphic = new Graphic({
          geometry: point,
        });
        this.view.goTo(graphic);
      }
      let params = {
        type: val.extra,
      };
      this.stationId = {
        facilityId: val.facilityId,
        stationType: val.sourceType,
        type: val.extra,
      };
      /* 以下为 popup 按条件打开 */
      // 管网监测 flow pressure level
      if (
        val.datatype === "flow" ||
        val.datatype === "pressure" ||
        val.datatype === "level"
      ) {
        getAllMonitorDatadetail(params, val.facilityId, val.sourceType).then(
          (res) => {
            this.detaildata = res.data;
            this.popupstate = val.datatype;
          }
        );
      }
      // 管网监测 水表抄表
      if (val.extra === "WATER_METER") {
        this.detaildata = {
          meterTitle: val.facilityName,
          type: 'meter'
        };
        this.meterId = val.facilityId;
        this.popupstate = val.datatype;
      }
      // 视频监测
      if (val.datatype.includes("Video")) {
        this.popupstate = val.datatype;
        this.detaildata = {
          videoCode: val.videoCode,
          videoName: val.facilityName,
        };
      }
      // 水情监测
      if (val.datatype === "hydro") {
        this.detaildata = {
          meterTitle: val.facilityName,
          type: 'hydro'
        };
        this.meterId = val.facilityId;
        this.popupstate = val.datatype;
      }
      // 水质监测
      if (
        val.datatype === "pipenetwork" ||
        val.datatype === "factoryAndSource" ||
        val.datatype === "endUser" ||
        val.datatype === "labourMonitor"
      ) {
        this.detaildata = {
          id: val.facilityId,
          sourceType: val.sourceType,
          datatype: val.datatype,
        };
        this.popupstate = val.datatype;
      }
      // 工程管理
      if (
        val.datatype === "sourceProject" ||
        val.datatype === "factoryProject" ||
        val.datatype === "storagePondProject"
      ) {
        this.detaildata = {
          type: this.projectMap[val.datatype],
          id: val.facilityId,
        };
        this.popupstate = val.datatype;
      }
      // 工程
      if (
        val.datatype === "projectLayer1" ||
        val.datatype === "projectLayer2" ||
        val.datatype === "projectLayer3"
      ) {
        this.detaildata = {
          id: val.facilityId,
        }
        this.popupstate = val.datatype
      }
    },
    // changeLine
    changeLine(type) {
      this.pipeLineLayer.definitionExpression = `type = ${type}`;
      this.pipeLineLayer.refresh();
    },

    // 点位图层显隐
    checkBoxChange(value, layerId) {
      this.view.popup.close();
      let targrtLayer = this.view.map.findLayerById(layerId);
      if (targrtLayer) {
        targrtLayer.visible = value;
      }
    },
    createfealayer(data, id, icon, visible = false) {
      let graphics = [];
      data.forEach((item) => {
        let point = {
          type: "point", // autocasts as new Point()
          longitude: item.coordinate.x,
          latitude: item.coordinate.y,
        };
        let symbol = {
          type: "picture-marker",
          url: require(`../../assets/img/home/<USER>
          width: "20px",
          height: "24px",
        };
        const attributes = {
          datatype: id,
          facilityId: item.facilityId,
          sourceType: item.sourceType,
          extra: item.extra,
          facilityName: item.facilityName,
          videoCode: item.videoCode,
        };
        let graphic = new Graphic({
          geometry: point,
          symbol: symbol,
          attributes,
        });
        graphics.push(graphic);
      });
      const fields = [
        {
          name: "datatype",
          type: "string",
        },
        {
          name: "facilityId",
          type: "integer",
        },
        {
          name: "sourceType",
          type: "string",
        },
        {
          name: "extra",
          type: "string",
        },
        {
          name: "facilityName",
          type: "string",
        },
      ];
      if (id.includes("Video")) {
        fields.push({
          name: "videoCode",
          type: "string",
        });
      }
      return new FeatureLayer({
        source: graphics,
        objectIdField: "OBJECTID",
        outFields: "*",
        id: id,
        fields: fields,
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require(`../../assets/img/home/<USER>
            width: "20px",
            height: "24px",
          },
        },
        visible: visible,
      });
    },
    createCenterPoint(data) {
      let graphics = [];
      data.forEach((item) => {
        if (item.count > 0) {
          let point = {
            type: "point", // autocasts as new Point()
            longitude: item.x,
            latitude: item.y,
          };
          let symbol = {
            type: "picture-marker",
            url: require(`../../assets/img/ball546.png`),
            width: "120px",
            height: "124px",
          };
          const attributes = {
            name: item.count,
          };
          let graphic = new Graphic({
            geometry: point,
            symbol: symbol,
            attributes,
          });
          graphics.push(graphic);
        }
      });
      return new FeatureLayer({
        source: graphics,
        objectIdField: "OBJECTID",
        id: "centerPoints",
        fields: [{ name: "name", type: "string" }],
        labelingInfo: [
          {
            labelExpressionInfo: {
              expression: "$feature.name",
            },
            labelPlacement: "above-center",
            symbol: {
              type: "text",
              color: "white",
              font: { size: 32 },
              yoffset: "-65px",
            },
          },
        ],
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require(`../../assets/img/ball546.png`),
            width: "90px",
            height: "90px",
          },
        },
        visible: true,
      });
    },
    // 点击水厂
    handleWaterFactoryPointClick(name) {
      this.$router.push({
        name: "HomeWaterFactory",
        query: {
          factory: name,
        },
      });
    },

    showPointName(result) {
      let P = Vue.extend(BasePopup);
      let pInstance = new P({
        propsData: {
          pointName: result.graphic.attributes.station_name,
        },
      });
      pInstance.$mount();
      this.view.popup.open({
        location: result.mapPoint,
        content: pInstance.$el,
      });
    },
    handleCountryPolygons() {
      polygons.features.forEach((polygon) => {
        this.countryPolygons.push(
          new Polygon({
            rings: polygon.geometry.coordinates[0],
            spatialReference: { wkid: 4326 },
          })
        );
      });
    },
    handlePointInPolygonCounts(points, type) {
      this.countryPolygons.forEach((polygon, index) => {
        let count = 0;
        points.forEach((item, i) => {
          if (
            item.coordinate &&
            item.coordinate.x != 0 &&
            item.coordinate.y != 0
          ) {
            const pt = new Point({
              longitude: item.coordinate.x,
              latitude: item.coordinate.y,
            });
            if (geometryEngine.contains(polygon, pt)) {
              count++;
            }
          }
        });
        this.countryPolygonsPointCount.push({
          type: type,
          x: polygon.centroid.x,
          y: polygon.centroid.y,
          count: count,
        });
      });
    },
  },
};
</script>

<style lang="scss">
.innerMap {
  .el-radio {
    .el-radio__label {
      display: none;
    }
  }
}
</style>

<style lang="scss" scoped>
.innerMap {
  height: 100%;
  position: relative;
  #map {
    height: 100%;
  }
  .baseBox {
    position: absolute;
    top: 60px;
    right: 490px;
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .layerControlBox {
    width: 101px;
    position: absolute;
    right: 30px; /* 默认右侧距离 - 全屏模式 */
    top: 100px;
    color: #fff;
    z-index: 10; /* 确保在其他元素上层 */
    .item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: center;
      align-items: center;
      margin-top: 20px;
      position: relative;
      cursor: pointer;
      .name {
        width: 48px;
        position: absolute;
      }
      img {
        width: 78px;
      }
    }
  }
  /* 添加一个专门处理宽屏幕下的控制框位置的类 */
  .layerControl-wide {
    right: 490px;
  }

  .legendBox {
    position: absolute;
    right: 490px;
    bottom: 20px;
    width: 131px;
    padding: 10px 0 10px 10px;
    background: rgba(5, 58, 127, 0.4);
    border-radius: 4px;
    border: 1px solid rgba(11, 11, 136, 1);
    .item {
      display: flex;
      align-items: center;
      margin-top: 5px;
      img {
        width: 18px;
        margin: 0 5px;
      }
      span {
        color: #fff;
        font-size: 12px;
      }
    }
  }
  .lineLegend {
    padding: 5px 0;
    background-color: rgba(4, 15, 45, 0.5);
    font-size: 14px;
    .lineSpan {
      display: inline-block;
      width: 40px;
    }
  }
}
.subtitle {
  font-size: 16px;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  color: #d8f0ff;
  line-height: 24px;
  text-shadow: 0px 0px 10px rgba(0, 145, 255, 0.5011), 0px 0px 4px #0091ff;
  margin: 15px 0 11px 0;
  cursor: pointer;
}
.layerlabel {
  // display: flex;
  // justify-content: space-between;
  // flex-wrap: wrap;
  // cursor: pointer;
  .layerItem {
    margin-top: 20px;
    display: flex;
    align-items: center;
  }
  .btn {
    cursor: pointer;
  }
  .layerIcon {
    width: 20px;
    margin: 0 20px;
  }
  .layerCount {
    color: #fff;
  }
}
.control {
  position: absolute;
  right: 140px;
  top: 71px;
  .analysis {
    // width: 440px;
    height: 100%;
    .content {
      height: calc(100% - 42px);
      padding: 5px 10px;
      background-color: rgba(4, 15, 45, 0.6);
    }
  }
  .legendAna {
    width: 310px;
    height: 100%;
    .content {
      height: calc(100% - 42px);
      padding: 20px 20px;
      background-color: rgba(4, 15, 45, 0.6);
    }
    .legendItem {
      display: flex;
      color: white;
      justify-content: space-between;
      line-height: 44px;
      margin-bottom: 10px;
      img {
        width: 50px;
      }
    }
  }
}
.closeBtn {
  position: absolute;
  right: 10px;
  top: 10px;
  color: white;
  cursor: pointer;
}
.btn {
  font-size: 14px;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  color: #ffffff;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 36px;
}
.activebtn {
  background: url("../../assets/img/home/<USER>") center no-repeat;
  background-size: 100%;
}
.unactivebtn {
  background: url("../../assets/img/home/<USER>") center no-repeat;
  background-size: 100%;
}
.left-fade-enter-active,
.left-fade-leave-active {
  transform: translateX(120%); // 算上左边padding距离
  transition: 0.4s;
}

.left-fade-enter-to {
  transform: translateX(0);
  transform: translateX(0);
}
.right-fade-enter-active,
.right-fade-leave-active {
  transform: translateX(-120%); // 算上左边padding距离
  transition: 0.4s;
}

.right-fade-enter-to {
  transform: translateX(0);
  transform: translateX(0);
}
.activesite {
  background: linear-gradient(
    180deg,
    rgba(67, 51, 0, 0.5) 0%,
    rgba(135, 104, 7, 0.5) 100%
  ) !important;
}
.site {
  width: 440px;
  position: absolute;
  top: 0px;
  right: 30px;
  height: 100%;
  z-index: 999;
  .title {
    background: url("../../assets/img/title.png") center no-repeat;
    background-size: 100%;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
    padding-right: 30px;
  }
  .searchContainer {
    display: flex;
    margin-top: 10px;
    height: 36px;
    margin-bottom: 20px;
  }
  .searchInput {
    width: 400px;
    height: 36px;
    margin-left: 10px;
  }
  .sitebox {
    width: 440px;
    height: 100%;
    .content {
      height: calc(100% - 42px);
      padding: 5px 10px;
      background-color: rgba(4, 15, 45, 0.6);
      overflow-y: auto;
      .siteitem {
        margin-bottom: 10px;
        margin-left: 10px;
        width: 400px;
        height: 36px;
        background: linear-gradient(
          180deg,
          rgba(17, 62, 102, 0.5) 0%,
          rgba(2, 50, 128, 0.5) 100%
        );
        border: 1px solid #1377d0;
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
    .content::-webkit-scrollbar {
      width: 4px;
    }
    .content::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(0, 0, 0, 0.2);
    }
    .content::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
