<template>
  <div class="videoWall">
    <div class="navbar">
      <span class="backArrow" @click="backToHome"></span>
      <span style="color: #ACC6EA;">当前位置：首页</span>
      <span><i class="el-icon-arrow-right"></i>视频监控</span>
    </div>

    <div class="contentCard">
      <div class="treeBox">
        <el-scrollbar class="treeContainer">
          <!-- 滚动条要包裹的tree内容 --> 
          <el-tree 
            :data="treeData"
            node-key="id"
            :props="elTreeProps"
            @node-click="nodeClick"
          ></el-tree>
        </el-scrollbar>
      </div>
      <div class="wallBox">
        <div class="videoBox" v-for="index in 9" :key="index"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoWall',
  data() {
    return {
      treeData: [
        {
          label: '一级 1',
          children: [
            {
              label: '二级 1-1',
            }
          ]
        },
        {
          label: '一级 2',
        }
      ],
      elTreeProps: {
        children: 'children',
        label: 'label'
      },
    }
  },
  methods: {
    backToHome() {
      this.$router.push('/main/index')
    },
    nodeClick() {}
  }
}
</script>

<style lang="scss">
.videoWall {
  .treeContainer {
    .el-scrollbar__wrap {
      height: 100%;
      overflow-x: auto;
    }
  }
  .el-tree {
    background: transparent;
    color: #fff;
    .el-tree-node:focus > .el-tree-node__content {
      background-color: #113b7a;
      color: #409eff; //节点的字体颜色
      font-weight: bold;
    }

    .el-tree-node.is-current > .el-tree-node__content {
      background-color: #113b7a;
    }
    .el-tree-node__content:hover {
      background-color: #113b7a;
    }
  }
}
</style>

<style lang="scss" scoped>
.videoWall {
  padding: 0 30px;
  height: calc(100vh - 84px);
  color: #ffffff;
  .navbar {
    font-size: 16px;
    height: 40px;
    .backArrow {
      display: inline-block;
      width: 28px;
      height: 17px;
      background-image: url('../../assets/img/home/<USER>');
      background-size: 100%;
      cursor: pointer;
      margin-right: 10px;
    }
  }
  .contentCard {
    height: calc(100% - 70px);
    border: 1px solid #2A5DD2;
    border-radius: 2px;
    background-color: rgba(3 ,30, 73, 0.5);
    padding: 20px;
    display: flex;
    .treeBox {
      width: 280px;
      height: 100%;
      border: 1px solid rgba(42, 93 ,210, 0.5);
      border-radius: 2px;
      padding: 20px;
      background-color: rgba(42 ,93, 210, 0.1);
      .treeContainer {
        height: 100%;
      }
    }
    .wallBox {
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      flex-grow: 1;
      padding-left: 20px;
      .videoBox {
        width: 33%;
        height: 230px;
        border: 3px solid #2A5DD2;
        background-color: black;
      }
    }
  }
}
</style>
