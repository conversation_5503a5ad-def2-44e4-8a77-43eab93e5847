<template>
  <div class="home">
    <!-- 返回按钮 -->
    <div class="backArrow" v-show="currentValue === 3" @click="currentValue = 1">
      <img src="@/assets/img/home/<USER>" alt="">
    </div>
    <LayerBar v-if="currentValue === 3" />
    <template v-if="currentValue === 1">
      <!-- <Layerblue class="map" /> -->

      <LeftBar class="setIndex" />
      <!-- <Map /> -->
      <!-- 首页中间静态图片，对应下方数字孪生水厂 -->
      <div class="allmapBox">
        <img class="allmap" src="@/assets/img/home/<USER>" alt="" />
        <div class="pointBox" v-show="currentValue === 1">
          <div
            class="point"
            v-for="(p, index) in mapPoints"
            :key="index"
            :style="{ left: p.left + 'px', top: p.top + 'px' }"
            @click="handlePointClick(p.name)"
          >
            <div class="pointName">{{ p.name }}</div>
          </div>
        </div>
      </div>

      

      <RightBar class="" />
      <!-- 上部数据 -->
      <div class="topData setIndex">
        <div class="dataItem">
          <img src="@/assets/img/home/<USER>" alt="" />
          <div style="margin-left: 10px">
            <div>管网总长</div>
            <div class="value">{{ pipeNetworkLength }}<span style="font-size: 16px"> km</span>
            </div>
          </div>
        </div>
        <div class="dataItem">
          <img src="@/assets/img/home/<USER>" alt="" />
          <div style="margin-left: 10px">
            <div>居民人口</div>
            <div class="value">16832 <span style="font-size: 16px">人</span>
            </div>
          </div>
        </div>
        <div class="dataItem">
          <img src="@/assets/img/home/<USER>" alt="" />
          <div style="margin-left: 10px">
            <div>年供水量</div>
            <div class="value">3605 <span style="font-size: 16px">m³</span>
            </div>
          </div>
        </div>
        <div class="dataItem">
          <img src="@/assets/img/home/<USER>" alt="" />
          <div style="margin-left: 10px">
            <div>年总营收额</div>
            <div class="value">85 <span style="font-size: 16px">万元</span>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 底部切换 -->
    <div class="bottomAction setIndex">
      <div class="centerBox" @mouseleave="hoverValue = 0">
        <div
          v-for="item in bottomData"
          :key="item.value"
          class="typeCard"
          :style="{
            'background-image':
              'url(' +
              (currentValue == item.value ? item.hover : item.normal) +
              ')',
          }"
          @click="handleClick(item)"
        ></div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <!-- <BottomVideo /> -->
  </div>
</template>

<script>
import Layerblue from "./layer.vue";
import LayerBar from "./layerbar.vue";
import LeftBar from "./leftbar.vue";
import RightBar from "./rightbar.vue";
import BottomVideo from "@/components/screen/bottomVideo.vue";
import { mapMutations } from 'vuex'

import { getPipeNetworkTypeLength } from '@/api/pipenetwork/screen'
import { Decimal } from 'decimal.js'

export default {
  name: "Home",
  components: {
    LeftBar,
    RightBar,
    Layerblue,
    BottomVideo,
    LayerBar,
  },
  data() {
    return {
      pipeNetworkLength: 0,
      hoverValue: 0,
      currentValue: 1,
      mapPoints: [
        {
          name: "谭湾水厂",
          left: 326,
          top: 136,
        },
        {
          name: "双龙观水厂",
          left: 253,
          top: 193,
        },
        {
          name: "大礼水厂",
          left: 277,
          top: 292,
        },
        {
          name: "青华水厂",
          left: 406,
          top: 286,
        },
        {
          name: "吴家坪水厂",
          left: 447,
          top: 373,
        },
      ],
      bottomData: [
        {
          value: 1,
          pageName: "HomeWaterFactory",
          normal: require("@/assets/img/home/<USER>"),
          hover: require("@/assets/img/home/<USER>"),
        },
        {
          value: 2,
          pageName: "",
          normal: require("@/assets/img/home/<USER>"),
          hover: require("@/assets/img/home/<USER>"),
        },
        {
          value: 3,
          pageName: "",
          normal: require("@/assets/img/home/<USER>"),
          hover: require("@/assets/img/home/<USER>"),
        },
      ],
    };
  },
  created() {
    this.getData()
  },
  methods: {
    ...mapMutations('app', ['SET_homeActive']),
    getData() {
      getPipeNetworkTypeLength().then(res => {
        const { status, data } = res
        this.pipeNetworkLength =  Decimal(data.allLength).div(1000).toFixed(2)
      })
    },
    handleMouseenter(value) {
      this.hoverValue = value;
    },
    handlePointClick(name) {
      this.$router.push({
        name: "HomeWaterFactory",
        query: {
          factory: name,
        },  
      });
    },
    handleClick(item) {
      // console.log(item);
      // if(pageName) {
      //   this.$router.push({ name: pageName })
      // }
      this.currentValue = item.value;
      this.SET_homeActive(this.currentValue)
      if (this.currentValue === 2) {
        this.$router.push({ name: "HomeUav" });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.map {
  position: absolute;
  // left: 485px;
  // right: auto;
  // top: 77px;
  // bottom: auto;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.setIndex {
  z-index: 1;
}
.home {
  height: calc(100vh - 84px);
  position: relative;
  .backArrow {
    position: absolute;
    left: 20px;
    z-index: 10;
    cursor: pointer;
    img {
      width: 28px;
      height: 17px;
    }
  }
  .allmapBox {
    position: absolute;
    width: 908px;
    left: 506px;
    top: 120px;
    .pointBox {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
    }
    .point {
      width: 108px;
      height: 68px;
      font-size: 14px;
      font-family: Microsoft YaHei;
      color: #ffffff;
      background-image: url("~@/assets/img/home/<USER>");
      background-size: 100%;
      position: absolute;
      cursor: pointer;
      .pointName {
        text-align: center;
        padding-top: 12px;
      }
    }
  }
  .allmap {
    // position: absolute;
    // width: 908px;
    // left: 470px;
    // top: 100px;
    width: 908px;
  }
  .topData {
    position: absolute;
    padding: 0 90px;
    left: 470px;
    right: 470px;
    display: flex;
    justify-content: space-between;
    .dataItem {
      display: flex;
      align-items: center;
      color: #b0d7ff;
      font-size: 16px;
      .value {
        // font-family: DIN;
        color: #ffffff;
        font-weight: bold;
        font-size: 24px;
      }
    }
    img {
      width: 60px;
    }
  }
  .bottomAction {
    height: 60px;
    position: absolute;
    bottom: 0;
    width: 100%;
    background-image: url("~@/assets/img/home/<USER>");
    background-size: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .typeCard {
       margin-top: 5px;
    }
    .typeCard:nth-child(2) {
      margin-top: 10px;
    }
    .centerBox {
      display: flex;
      margin: 0 auto;
      div {
        width: 140px;
        height: 40px;
        background-size: 140px 40px;
        cursor: pointer;
      }
    }
  }
}
</style>
