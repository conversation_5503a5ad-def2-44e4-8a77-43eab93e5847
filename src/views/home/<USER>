<template>
  <div class="right">
    <div class="analysis card">
      <div class="title titleFlex">AI视频监控 <span style="color: #00E4FF;font-size: 16px;cursor: pointer;font-family: 'Microsoft YaHei';" @click="toVideoPage">查看更多 >></span></div>
      <div class="content">
        <div class="videoBox">
          <div class="inner">
            <DHPlayer channelId="1000004$1$0$2" />
          </div>
          <!-- <img src="../../assets/img/home/<USER>" alt="" style="width: 100%;height: 100%;"> -->
        </div>
        <div class="videoListBox">
          <div v-for="item in picList" :key="item.id" class="videoSmallCard">
            <!-- <img :src="iccPrefix + item.alarmPicture + '?token=' + token" alt="" style="width: 100%;height: 100%;"> -->
             <img :src="item.alarmPicture" alt="" style="width: 100%;height: 100%;">
            <div class="stationName">{{ item.alarmType === 962 ? '人员入侵' : '车辆入侵' }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="monitor card">
      <!-- <div class="title titleFlex" style="align-items: center;">预警中心<div class="customButtom" @click="showOrderDialog">工单推送</div></div>
      <div class="calcHeight">
        <div class="listHeader" style="text-align: center;">
          <span>预警类型<i class="el-icon-caret-bottom"></i></span>
          <span style="width: 105px;">站点</span>
          <span style="width: 105px;">预警内容</span>
          <span>更新时间</span>
        </div>
        <VueSeamlessScroll
          :data="areaList"
          :class-option="classOption"
          class="warp"
        >
          <div class="listItem" v-for="item in areaList" :key="item.id" @click="toWarnPage">
            <div class="small" style="width: 90px;">{{ item.warnTypeName }}</div>
            <div class="small" style="width: 105px;">{{ item.name }}</div>
            <div class="small" style="width: 105px;color: #00E4FF;">{{ item.msg}}</div>
            <div class="big">{{ item.dataTime }}</div>
          </div>
        </VueSeamlessScroll>
      </div> -->

      <div class="title">供水调度</div>
      <div class="outer" style="height: 183px;">
        <div class="item">
          <div class="innerItem">原水调度</div>
          <div class="innerItem rightItem">水厂调度</div>
        </div>
        <div class="item">
          <div class="innerItem">管网调度</div>
          <div class="innerItem rightItem">维修调度</div>
        </div>
      </div>
    </div>

    <div class="situation card">
      <!-- <div class="title">水文水质监测</div>
      <div class="outerBox">
        <div class="outerBoxItem">
          <div class="smallTitle">水文监测</div>
          <div class="cardBox">
            <div class="card normal" :class="{ warn: item.status }" v-for="item, index in hyData" :key="index">
              <div>{{ item.name }}</div>
              <div>流量:{{ item.flow }}m³/h</div>
              <div>雨量:{{ item.rain }}mm</div>
            </div>
          </div>
        </div>
        <div class="outerBoxItem">
          <div class="smallTitle">水质监测</div>
          <div style="height: 90px">
            <el-carousel indicator-position="none" arrow="never" :interval="5000" height="90px">
              <el-carousel-item v-for="item, index in wqData1" :key="index">
                <div class="sCardBox">
                  <div class="sCard normal" :class="{ warn: item[0].status }">
                    <div>{{ item[0].name }}</div>
                    <div>{{ item[0].status ? '未达标' : '达标' }}</div>
                  </div>
                  <div class="sCard normal" :class="{ warn: item[1].status }" v-if="item[1]">
                    <div>{{ item[1].name }}</div>
                    <div>{{ item[1].status ? '未达标' : '达标' }}</div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>

          <div style="height: 90px">
            <el-carousel indicator-position="none" arrow="never" :interval="5000" height="90px">
              <el-carousel-item v-for="item, index in wqData2" :key="index">
                <div class="sCardBox">
                  <div class="sCard normal" :class="{ warn: item[0].status }">
                    <div>{{ item[0].name }}</div>
                    <div>{{ item[0].status ? '未达标' : '达标' }}</div>
                  </div>
                  <div class="sCard normal" :class="{ warn: item[1].status }" v-if="item[1]">
                    <div>{{ item[1].name }}</div>
                    <div>{{ item[1].status ? '未达标' : '达标' }}</div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>

          <div style="height: 90px">
            <el-carousel indicator-position="none" arrow="never" :interval="5000" height="90px">
              <el-carousel-item v-for="item, index in wqData3" :key="index">
                <div class="sCardBox">
                  <div class="sCard normal" :class="{ warn: item[0].status }">
                    <div>{{ item[0].name }}</div>
                    <div>{{ item[0].status ? '未达标' : '达标' }}</div>
                  </div>
                  <div class="sCard normal" :class="{ warn: item[1].status }" v-if="item[1]">
                    <div>{{ item[1].name }}</div>
                    <div>{{ item[1].status ? '未达标' : '达标' }}</div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>

        </div>
      </div> -->

      <div class="title">漏损分析</div>
      <div class="topBox">
        <div class="leftRate">
          <div class="rateValue">12.4%</div>
          <div style="font-size: 12px;">总漏损率</div>
        </div>
        <div class="rightData">
          <div style="display: flex;justify-content: space-between;padding-top: 10px;">
            <span>总漏损量</span>
            <span><span style="color: #F48B00;font-weight: 500;">3840</span><span>吨</span></span>
          </div>
          <div class="percentageBar">
            <div class="inner"></div>
            <div class="circle" style="left: calc(70% - 12px);"></div>
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 132px);">
        <MixChart />
      </div>
    </div>

    <!-- 工单推送弹窗 -->
    <el-dialog title="工单推送" :visible.sync="orderDialogVisible" width="1320px">
      <div class="contentBox">
        <div class="contentLeft">
          <div class="stepTitle">第一步，请选择预警信息</div>
          <div class="queryModelBox">
            <span class="param">
              <span>预警场景：</span>
              <el-select v-model="queryModel.sense" size="medium"></el-select>
            </span>
            <span class="param">
              <span>预警类型：</span>
              <el-select v-model="queryModel.sense" size="medium"></el-select>
            </span>
            <span class="param">
              <span>状态：</span>
              <el-select v-model="queryModel.sense" size="medium"></el-select>
            </span>
            <el-button type="primary" icon="el-icon-search" size="medium" @click="handleQuery">查询</el-button>
            
          </div>
          <el-table
            :data="orderList"
            size="mini"
            height="416px"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="序号" type="index" width="55"></el-table-column>
            <el-table-column label="预警场景" prop="sense"></el-table-column>
            <el-table-column label="预警类型" prop="warnType"></el-table-column>
            <el-table-column label="站点" prop="station"></el-table-column>
            <el-table-column label="更新时间" prop="updateTime" width="150"></el-table-column>
            <el-table-column label="状态" prop="status"></el-table-column>
            <el-table-column label="推送次数" prop="value1"></el-table-column>
            <el-table-column label="接收数" prop="value2"></el-table-column>
            <el-table-column label="操作">
              <template><span style="color: #00E4FF;">查看</span></template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page.sync="pageInfo.pageNo"
            :page-size="pageInfo.pageSize"
            layout="total, prev, pager, next, jumper"
            :total="pageInfo.total">
          </el-pagination>
        </div>

        <div class="contentRight">
          <div class="stepTitle">第二步，请选择推送范围</div>
          <div class="treeBox">
            <div>
              <el-input
                placeholder="请输入名称"
                suffix-icon="el-icon-search"
                size="small"
                v-model="search">
              </el-input>
            </div>
            <el-tree
              :data="treeData"
              :props="defaultProps"
              show-checkbox
              class="legendtree"
              @node-click="handleNodeClick"
            >
            </el-tree>
          </div>
          <div style="text-align: center;padding-top: 8px;">
            <el-button type="primary" size="mini">消息推送</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DHPlayer from '@/components/player/DHPlayer'
import MixChart from './chart/mixChart'

import { getWqWarn } from '@/api/wq/warn'
import { getCameraPic, getICCToken } from '@/api/camera'
import VueSeamlessScroll from 'vue-seamless-scroll'

export default {
  components: { VueSeamlessScroll, DHPlayer, MixChart },
  data() {
    return {
      picList: [],
      areaList: [],
      iccPrefix: 'https://**************:1443/evo-apigw/evo-oss/',
      token: '',
      classOption: {
        direction: 1,
        singleHeight: 30,
      },
      hyData: [
        {
          name: '古洞口水库',
          status: 0,
          flow: 23,
          rain: 50
        },
        {
          name: '琚坪水库',
          status: 0,
          flow: 23,
          rain: 50
        },
        {
          name: '野马洞水库',
          status: 1,
          flow: 23,
          rain: 50
        },
        {
          name: '黄家河水库',
          status: 0,
          flow: 23,
          rain: 50
        },
        {
          name: '喷水洞水库',
          status: 0,
          flow: 23,
          rain: 50
        },
        {
          name: '万佛山水库',
          status: 0,
          flow: 23,
          rain: 50
        }
      ],
      wqData1: [
        [
          {
            name: '青华水厂',
            status: 0
          },
          {
            name: '潭湾水厂',
            status: 0
          }
        ],
        [
          {
            name: '白杨树水厂',
            status: 0
          },
          {
            name: '双龙观水厂',
            status: 0
          }
        ]
      ],
      wqData2: [
        [
          {
            name: '管网7-5-P-15',
            status: 0
          },
          {
            name: '管网7-5-P-6',
            status: 0
          }
        ],
        [
          {
            name: '管网10-10-Y-7',
            status: 0
          },
          {
            name: '管网10-10-Y-10',
            status: 0
          }
        ]
      ],
      wqData3: [
        [
          {
            name: '终端用户1',
            status: 0
          },
          {
            name: '终端用户2',
            status: 0
          }
        ],
        [
          {
            name: '终端用户3',
            status: 1
          },
          {
            name: '终端用户4',
            status: 0
          }
        ]
      ],
      orderDialogVisible: false,
      queryModel: {},
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      },
      orderList: [
        {
          sense: '水库水情预警',
          warnType: '超汛限水位',
          station: '古洞口水',
          updateTime: '2024-01-11 12:35:28',
          status: '未推送',
          value1: 0,
          value2: '0/9'
        }
      ],
      multipleSelection: [],
      search: '',
      treeData: [
        {
          id: 1,
          label: '水利局科室',
          children: [
            {
              id: 2,
              label: '水利局办公室',
            },
            {
              id: 3,
              label: '水政水资源管理处',
            },
            {
              id: 4,
              label: '农饮水安全管理处',
            },
            {
              id: 5,
              label: '水电建设管理处',
            }
          ]
        },
        {
          id: 6,
          label: '乡镇管理科室',
          children: [
            {
              id: 7,
              label: '古夫镇管理站',
            },
            {
              id: 8,
              label: '昭君镇管理站',
            },
            {
              id: 9,
              label: '峡口管理站',
            }
          ]
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      warnType: {
        'UN_KNOWN': '未知',
        'WATER_QUALITY': '水质预警',
        'WATER_LEVEL': '水位预警',
        'WATER_TRAFFIC': '流量预警',
        'EQUIPMENT': '设备预警',
        'WATER_PRESSURE': '压力预警',
        'WEATHERWARN': '天气预警',
        'NATIONALWARNING': '国家地质灾害气象风险预警',
        'SUBAREA_FLOW': '分区流量报警',
        'RAIN': '强降雨',
        'WIND': '大风',
        'HIGH_TEMP': '高温',
        'LOW_TEMP': '低温',
        'EARTHQUAKE': '地震',
        'ARIDITY': '干旱',
      },
    };
  },
  filters: {
    warnTypeFilter(value) {
      const labelMap = {
        'UN_KNOWN': '未知',
        'WATER_QUALITY': '水质预警',
        'EQUIPMENT': '设备预警',
        'WATER_LEVEL': '水位预警',
        'WATER_TRAFFIC': '流量预警',
        'WATER_PRESSURE': '压力预警'
      }
      if(!value) return '--'
      return labelMap[value]
    }
  },
  created() {
    // getICCToken().then(res => {
    //   this.token = res.data.token.split(' ')[1] || ''
    // })
  },
  mounted() {
    this.getData()
  },
  methods: {
    toVideoPage() {
      this.$router.push({
        name: "VideoWall"
      })
    },
    toWarnPage() {},
    getData() {
      getCameraPic({
        pageNo: 1,
        pageSize: 2,
        alarmType: [963, 962]
      }).then(res => {
        this.picList = res.data || []
      })

      // 预警中心滚动
      // getWqWarn({
      //   pageNo: 1,
      //   pageSize: 10
      // }).then(res => {
      //   const { status, data } = res
      //   if(status === 200) {
      //     this.areaList = data.map(item => {
      //       return {
      //         ...item,
      //         warnTypeName: this.warnType[item.warnType]
      //       }
      //     })
      //     this.areaList = data
      //   }
      // })
    },
    // 显示工单推送弹窗
    showOrderDialog() {
      console.log(1)
      this.orderDialogVisible = true
    },
    // 查询按钮
    handleQuery() {},
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleCurrentChange() {},
    handleNodeClick(data) {
      console.log(data)
    }
  },
};
</script>

<style lang="scss">
.right {
  .el-dialog__body {
    padding: 0;
  }
  .el-select {
    width: 200px;
  }
  .el-table__row {
    font-size: 14px;
  }
  .el-tree {
    background: transparent;
    color: #fff;
    .el-tree-node:focus > .el-tree-node__content {
      background-color: #113b7a;
      color: #409eff; //节点的字体颜色
      font-weight: bold;
    }

    .el-tree-node.is-current > .el-tree-node__content {
      background-color: #113b7a;
    }
    .el-tree-node__content:hover {
      background-color: #113b7a;
    }
  }
}
</style>

<style scoped lang="scss">
.right {
  position: absolute;
  right: 30px;
  height: 100%;
  .card {
    background-color: rgba(4, 15, 45, 0.3);
    border: 1px solid rgba(23, 110, 217, 0.3);
    backdrop-filter: blur(16px);
  }
  .calcHeight {
    height: 210px;
    padding: 5px 10px 0;
    background-color: rgba(4, 15, 45, 0.3);
    .listHeader {
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 34px;
      background: url("../../assets/img/table.png") center no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 34px;
      span {
        display: block;
        width: 80px;
      }
      span:first-child {
        width: 90px;
        // padding-left: 30px;
      }
      span:last-child {
        flex-grow: 1;
      }
    }
    .warp {
      overflow: hidden;
      height: 176px;
    }
    .listItem {
      text-align: center;
      border: 1px solid rgba(2, 50, 128, 0.6);
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 30px;
      margin-top: 5px;
      box-shadow: 0 0 10px #023280 inset;
      div:last-child {
        // flex-grow: 1;
      }
      span {
        display: block;
      }
      .small {
        flex: 0 0 auto;
        // width: 80px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        // font-size: 14px;
      }
      .small:first-child {
        // width: 93px;
        // padding-left: 30px;
      }
      .big {
        display: flex;
        align-items: center;
        max-width: 195px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  .analysis {
    width: 440px;
    // height: 24.2%;
    .content {
      height: calc(100% - 42px);
      display: flex;
      padding: 5px 10px;
      background-color: rgba(4, 15, 45, 0.3);

      .videoBox {
        width: 300px;
        height: 200px;
        padding: 14px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100%;
        .inner {
          background-color: black;
          height: 100%;
        }
      }
      .videoListBox {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .videoSmallCard {
          width: 100px;
          height: 94px;
          padding-left: 6px;
          position: relative;
          // cursor: pointer;
          img {
            border: 2px solid #023280;
          }
          .stationName {
            font-size: 12px;
            color: #FFFFFF;
            background-color: rgba(42, 93, 210, 0.7);
            position: absolute;
            text-align: center;
            line-height: 20px;
            width: 90px;
            height: 20px;
            left: 8px;
            bottom: 2px;        
          }
        }
      }
    }
  }
  .monitor {
    width: 440px;
    // height: 326px;
    margin-top: 20px;
    .outer {
      background-image: url('~@/assets/img/home/<USER>');
      background-size: 313px 133px;
      background-repeat: no-repeat;
      background-position: center;
      padding: 0 16px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      color: #FFFFFF;
      font-weight: 500;
      .item {
        display: flex;
        justify-content: space-around;
      }
      .innerItem {
        width: 140px;
        height: 43px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100% 100%;
        text-align: center;
        line-height: 43px;
        cursor: pointer;
      }
      .rightItem {
        background-image: url('~@/assets/img/home/<USER>');
      }
    }
    .customButtom {
      width: 88px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      font-family: Microsoft YaHei;
      background-color: #1E49A7;
    }
  }
  .situation {
    width: 440px;
    margin-top: 20px;
    height: calc(100% - 541px);
    color: #FFFFFF;
    .topBox {
      display: flex;
      padding-top: 10px;
      .leftRate {
        width: 120px;
        height: 80px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100% 100%;
        text-align: center;
        .rateValue {
          color: #F48B00;
          font-size: 20px;
          font-weight: bold;
        }
      }
      .rightData {
        flex: 1;
        padding-right: 10px;
        font-size: 14px;
        .percentageBar {
          height: 12px;
          background-color: rgba(112, 112, 112, 0.23);
          position: relative;
          margin-top: 10px;
          .inner {
            width: 70%;
            height: 12px;
            background: linear-gradient(90deg, rgba(0, 85, 255, 0) 1%, #0783FA 99%);
          }
          .circle {
            position: absolute;
            top: -6px;
            left: -12px;
            width: 24px;
            height: 24px;
            background-image: url('~@/assets/img/home/<USER>');
            background-size: 100% 100%;
          }
        }
      }
    }
    .outerBox {
      display: flex;
      justify-content: space-between;
      height: calc(100% - 42px);
      overflow: hidden;
      padding: 20px;
      .outerBoxItem {
        width: 190px;
      }
      .smallTitle {
        width: 190px;
        height: 30px;
        color: #FFFFFF;
        line-height: 30px;
        text-align: center;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100%;
      }
      .cardBox {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .card {
          width: 90px;
          height: 82px;
          background-size: 100%;
          color: #ffffff;
          font-size: 12px;
          margin-top: 5px;
          div {
            text-align: center;
            padding-top: 5px;
          }
        }
      }
      .sCardBox {
        display: flex;
        justify-content: space-between;
        .sCard {
          width: 90px;
          height: 82px;
          background-size: 100%;
          color: #ffffff;
          font-size: 12px;
          margin-top: 5px;
          padding-top: 20px;
          div {
            text-align: center;
          }
        }
      }
      .normal {
        background-image: url('~@/assets/img/home/<USER>');
      }
      .warn {
        color: #F1BB17;
        background-image: url('~@/assets/img/home/<USER>');
      }
    }
  }
  .title {
    background: url("../../assets/img/title.png") center no-repeat;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
    padding-right: 30px;
  }
  .titleFlex {
    display: flex;
    justify-content: space-between;
  }
  .contentBox {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    .stepTitle {
      color: #D8F0FF;
    }
    .contentLeft {
      width: 970px;
      .queryModelBox {
        padding: 18px 0;
        display: flex;
        .param {
          color: #FFFFFF;
          margin-right: 20px;
        }
      }
    }
    .contentRight {
      width: 260px;
      .treeBox {
        margin-top: 18px;
        padding: 10px 20px;
        height: 470px;
        border: 1px solid #1A3D90;
        background-color: rgba(42, 93, 210, 0.1);
      }
    }
  }
}
</style>