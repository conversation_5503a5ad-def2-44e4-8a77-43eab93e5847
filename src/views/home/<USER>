<template>
  <div class="leftPanel">
    <div class="top card">
      <div class="title">供水统计</div>
      <div class="infoContent">
        <div class="item">
          <div class="leftTilte">
            <span style="font-family: PangMenZhengDao;">供水总量</span>
            <span 
              v-for="item in shortcuts" :key="item.value"
              class="shortcut"
              :class="{ activeText: item.value === currentValue }"
              @click="toggelValue(item.value)">{{ item.label }}</span>
          </div>
          <div style="height: 120px;position: relative;">
            <div class="totalText">
              <span style="text-shadow: 0px 0px 10px #4EC9FF;">{{ total }}</span>
              <span style="font-size: 10px;">供水总量m³</span>
            </div>
            <PieChart :pieData="pieData" />
          </div>
        </div>
        <div class="item">
          <div class="leftTilte">
            <span style="font-family: PangMenZhengDao;">实时水量</span>
            <!-- <span 
              v-for="item in shortcuts1" :key="item.value"
              class="shortcut"
              :class="{ activeText: item.value === currentValue1 }"
              @click="toggelValue1(item.value)">{{ item.label }}</span> -->
          </div>
          <div style="height: 120px;">
            <SmallBar :barData="hourBarData" />
          </div>
        </div>
      </div>
    </div>

    <div class="middle card">
      <!-- <div class="title">水厂供水总量</div>
      <div class="content">
        <div class="yAxisName">供水总量(m³)</div>
        <BarChart :seriesData="barData" />
      </div> -->
      <div class="title">营收数据</div>
      <div style="padding: 10px;">
        <div class="totalData">
          <div class="dataItem" @click="changeShowField(1)">
            <div class="dataLabel">本月营业额</div>
            <div class="dataValue">
              <span class="value">{{ monthInfo.allBillAmount }}</span>
              <span class="unit">元</span>
            </div>
          </div>
          <div class="dataItem"  @click="changeShowField(2)">
            <div class="dataLabel">本月预缴额</div>
            <div class="dataValue">
              <!-- <span class="value">{{ monthInfo.allWater }}</span> -->
              <span class="value">{{ monthInfo.currentPayment }}</span>
              <span class="unit">元</span>
            </div>
          </div>
        </div>

        <div class="townData">
          <div class="townValue">{{ currentItem.name }} {{ currentField === 1 ? currentItem.amount : currentItem.payment }}元</div>
          <div class="cardBox">
            <div class="innerItem" v-for="v in currentItem.list" :key="v.name">
              <div style="font-size: 12px;height: 26px;line-height: 26px;">{{ v.name }}</div>
              <div style="padding-top: 6px;">
                <span class="value">{{ currentField === 1 ? v.amount : v.payment }}</span>
                <span style="font-size: 12px;opacity: 0.5;margin-left: 4px;">元</span>
              </div>
            </div>
          </div>
          <!-- <div class="item" v-for="item, index in monthData" :key="index">
            <div class="townValue">{{ item.name}} {{ item.value }}元</div>
            <div class="innerItem" v-for="d, i in item.list" :key="i">
              <div style="font-size: 12px;height: 26px;line-height: 26px;">{{ d.name }}</div>
              <div style="padding-top: 6px;">
                <span class="value">{{ d.value }}</span>
                <span style="font-size: 12px;opacity: 0.5;margin-left: 4px;">元</span>
              </div>
            </div>
          </div> -->
        </div>

      </div>
    </div>

    <div class="bottom card">
      <!-- <div class="title">水文水质监测</div>
      <div class="outerBox">
        <div class="outerBoxItem">
          <div class="smallTitle">水文监测</div>
          <div class="cardBox">
            <div class="card normal" :class="{ warn: item.status }" v-for="item, index in hyData" :key="index">
              <div>{{ item.name }}</div>
              <div class="textBox">
                <span>流量</span>
                <span :class="{ warnText: item.status }">{{ item.flow }}m³/h</span>
              </div>
              <div class="textBox">
                <span>雨量</span>
                <span :class="{ warnText: item.status }">{{ item.rain }}mm</span>
              </div>
            </div>
          </div>
        </div>
        <div class="outerBoxItem">
          <div class="smallTitle">水质监测</div>
          <div class="cardBox">
            <div class="card normal" :class="{ warn: item.status1 || item.status2 }" v-for="item, index in wqData" :key="index">
              <div>{{ item.name }}</div>
              <div class="textBox">
                <span>管网</span>
                <span class="normalText" :class="{ warnText: item.status1 }">{{ item.status1 ? '不达标' : '达标' }}</span>
              </div>
              <div class="textBox">
                <span>用户</span>
                <span class="normalText" :class="{ warnText: item.status2 }">{{ item.status1 ? '不达标' : '达标' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div> -->
      <div class="title">水质监测</div>
      <div class="outer">
        <div class="inner">
          <div class="item">
            <div class="cell">
              <div class="state">达标</div>
              <div class="name">{{ waterFactoryItem.name }}</div>
            </div>
            <!-- <div class="base"></div> -->
          </div>
          <div class="paramBox">
            <div class="pCard">
              <div class="pLabel">浊度</div>
              <div class="pValue">{{ waterFactoryItem.turbidity }}</div>
              <div class="pUnit">NTU</div>
            </div>
            <div class="pCard">
              <div class="pLabel">电导率</div>
              <div class="pValue">{{ waterFactoryItem.powerRate }}</div>
              <div class="pUnit">μs/cm</div>
            </div>
            <div class="pCard">
              <div class="pLabel">PH</div>
              <div class="pValue">{{ waterFactoryItem.ph }}</div>
              <div class="pUnit">--</div>
            </div>
            <div class="pCard">
              <div class="pLabel">温度</div>
              <div class="pValue">{{ waterFactoryItem.temp }}</div>
              <div class="pUnit">°C</div>
            </div>
          </div>
          <!-- <div class="">
            <div class="cell" v-for="pipe in waterFactoryItem.pipeQuality" :key="pipe.name">
              <div class="state">{{ pipe.pass ? '达标' : '未达标' }}</div>
              <div class="name">{{ pipe.name }}</div>
            </div>
            <div class="base"></div>
          </div>
          <div class="">
            <div class="cell" v-for="user in waterFactoryItem.userQuality" :key="user.name">
              <div class="state">{{ user.pass ? '达标' : '未达标' }}</div>
              <div class="name">{{ user.name }}</div>
            </div>
            <div class="base"></div>
          </div> -->
        </div>
        <!-- 箭头 -->
        <i class="el-icon-caret-left arrow" style="left: 20px;" @click="changeItem('prev')"></i>
        <i class="el-icon-caret-right arrow" style="right: 20px;" @click="changeItem('next')"></i>
      </div>
    </div>
  </div>
</template>

<script>
import { factorySupplyData, hourSupplyData } from '@/api/screenData'
import { getCurrentMonthInfo } from '@/api/revenue/screen'
import { getHomeWQ } from '@/api/home/<USER>'
import { getWQLatest } from '@/api/wq/screen'
import { getCurrentMonthRevenueData } from '@/api/revenue/data'

import PieChart from './chart/pieChart'
import SmallBar from './chart/smallBar'
import BarChart from './chart/barChart'


export default {
  // 首页水厂地图左侧面板
  name: 'LeftPanel',
  components: {
    PieChart,
    SmallBar,
    BarChart
  },
  data() {
    return {
      currentValue: 2,
      shortcuts: [
        // {
        //   label: '1小时',
        //   value: 1,
        // },
        {
          label: '当日',
          value: 2,
        },
        {
          label: '当月',
          value: 3,
        },
        {
          label: '当年',
          value: 4,
        }
      ],
      currentValue1: 1,
      shortcuts1: [
        {
          label: '分钟',
          value: 1
        },
        {
          label: '小时',
          value: 2
        },
        {
          label: '当日',
          value: 3
        }
      ],
      total: '--',
      pieData: [
        {
          name: '谭湾',
          value: 0
        },
        {
          name: '大礼',
          value: 0
        },
        {
          name: '吴家坪',
          value: 0
        },
        {
          name: '青华',
          value: 0
        },
        {
          name: '双龙观',
          value: 0
        }
      ],
      pieDataTemp: [],
      hourBarData: [],
      barData: [],

      // 营收数据
      monthInfo: {
        amount: '',
        water: ''
      },
      currentField: 1,
      currentIndex: 0,
      currentItem: {},
      timeId: null,
      // 本月镇村数据
      monthData: [],

      hyData: [
        {
          name: '古洞口水库',
          status: 0,
          flow: 23,
          rain: 50
        },
        {
          name: '琚坪水库',
          status: 0,
          flow: 23,
          rain: 50
        },
        {
          name: '野马洞水库',
          status: 1,
          flow: 23,
          rain: 50
        },
        {
          name: '黄家河水库',
          status: 0,
          flow: 23,
          rain: 50
        },
        {
          name: '喷水洞水库',
          status: 0,
          flow: 23,
          rain: 50
        },
        {
          name: '万佛山水库',
          status: 0,
          flow: 23,
          rain: 50
        }
      ],
      wqData: [
        {
          name: '青华水厂',
          status1: 1,
          status2: 1
        },
        {
          name: '潭湾水厂',
          status1: 0,
          status2: 0
        },
        {
          name: '双龙观水厂',
          status1: 0,
          status2: 0
        },
        {
          name: '大礼水厂',
          status1: 0,
          status2: 0
        },
        {
          name: '吴家坪水厂',
          status1: 0,
          status2: 0
        },
      ],

      // 水厂线水质
      waterFactoryLine: [
        // {
        //   factory: {
        //     name: '吴家坪水厂'
        //   },
        //   pipe: [
        //     {
        //       name: '管网10-10-Y-7'
        //     },
        //     {
        //       name: '管网10-10-Y-8'
        //     }
        //   ],
        //   endUser: [
        //     {
        //       name: '终端用户1'
        //     },
        //     {
        //       name: '终端用户2'
        //     },
        //     {
        //       name: '终端用户3'
        //     }
        //   ]
        // },

      ],
      waterFactoryItem: {
        // factoryQuality: {}
      },
      itemIndex: 0
    }
  },
  mounted() {
    this.getData()
    // this.currentItem = this.monthData[this.currentIndex]
  },
  beforeDestroy() {
    if(this.timeId) clearInterval(this.timeId)
  },
  methods: {
    getData() {
      // 供水统计
      factorySupplyData().then(res => {
        this.pieDataTemp = res.data
        this.setPieData()
        this.setBarData()
      })
      // 营收数据
      // getCurrentMonthInfo().then(res => {
      //   this.monthInfo = res.data
      //   this.monthData = res.data.list || []
      // })
      getCurrentMonthRevenueData().then(res => {
        this.monthInfo = {
          allBillAmount: res.allBillAmount || 0,
          currentPayment: res.currentPayment || 0
        }
        this.monthData = res.list || []
        this.currentItem = this.monthData[this.currentIndex]
        let length = this.monthData.length
        this.timeId = setInterval(() => {
          this.currentIndex++
          if(this.currentIndex >= length) this.currentIndex = 0
          this.currentItem = this.monthData[this.currentIndex]
        }, 3000)
      })
      // 水质监测
      // getHomeWQ().then(res => {
      //   this.waterFactoryLine = res.data
      //   this.waterFactoryItem = this.waterFactoryLine[0]
      // })
      getWQLatest().then(res => {
        this.waterFactoryLine = res.data.filter(item => item.sourceType === 'WATER_FACTORY')
        this.waterFactoryItem = this.waterFactoryLine[0] || {}
      })

      // hourSupplyData().then(res => {
      //   let data = res.data || []
      //   const yesterdayData = data.slice(0, 24)
      //   const todayData = data.slice(25)
      //   this.barData = [
      //     {
      //       color: '#0380FB',
      //       name: '当日',
      //       type: 'bar',
      //       barWidth: 4,
      //       barGap: 0,
      //       data: todayData.map(item => item[1])
      //     },
      //     {
      //       color: '#37EDD1',
      //       name: '昨日',
      //       type: 'bar',
      //       barWidth: 4,
      //       data: yesterdayData.map(item => item[1])
      //     }
      //   ]
      // })
    },
    changeShowField(val) {
      this.currentField = val
    },
    toggelValue(value) {
      this.currentValue = value
      this.setPieData()
    },
    toggelValue1(value) {
      this.currentValue1 = value
    },
    setPieData() {
      // 根据 type 查找
      const target = this.pieDataTemp.find(item => item.type === this.currentValue)
      
      if(target) {
        this.total = target.total
        this.pieData[0].value = target.tanWan || 0
        this.pieData[1].value = target.daLi || 0
        this.pieData[2].value = target.wuJiaPing || 0
        this.pieData[3].value = target.qingHua || 0
        this.pieData[4].value = target.shuangLongGuan || 0
      } else {
        console.log('暂无统计数据')
      }
    },
    setBarData() {
      const target = this.pieDataTemp.find(item => item.type === 1)

      if(target) {
        this.hourBarData = [target.tanWan, target.daLi, target.wuJiaPing, target.qingHua, target.shuangLongGuan]
      }
    },
    changeItem(type) {
      if(type === 'next') {
        this.itemIndex++
      } else {
        this.itemIndex--
      }
      if(this.itemIndex === -1) {
        this.itemIndex = 4
      }
      if(this.itemIndex === 5) {
        this.itemIndex = 0
      }
      this.waterFactoryItem = this.waterFactoryLine[this.itemIndex]
    }
  }
}
</script>

<style lang="scss" scoped>
.leftPanel {
  position: absolute;
  left: 30px;
  height: 100%;
  color: #ffffff;
  .card {
    background-color: rgba(4, 15, 45, 0.3);
    border: 1px solid rgba(23, 110, 217, 0.3);
    backdrop-filter: blur(16px);
  }
  .top {
    width: 440px;
    height: 210px;
    .leftTilte {
      // width: 228px;
      height: 28px;
      padding-left: 16px;
      background-image: url('~@/assets/img/home/<USER>');
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      .shortcut {
        margin-left: 5px;
        font-size: 12px;
        cursor: pointer;
      }
      .activeText {
        color: #58A7ED;
      }
    }
    .infoContent {
      display: flex;
      .item {
        width: 50%;
        position: relative;
      }
    }
    .totalText {
      position: absolute;
      top: 50%;
      left: 23px;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
  .middle {
    margin-top: 20px;
    width: 440px;
    // height: calc((100% - 208px) / 2 - 40px);
    height: calc(100% - 565px);
    overflow: hidden;
    .totalData {
      display: flex;
      justify-content: space-between;
      .dataItem {
        width: 195px;
        height: 72px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100% 100%;
        cursor: pointer;
        .dataLabel {
          font-size: 16px;
          height: 27px;
          text-align: center;
          line-height: 27px;
          color: #50C9E3;
        }
        .dataValue {
          padding-top: 6px;
          text-align: center;
        }
      }
    }
    .value {
      font-size: 24px;
      font-weight: 500;
      text-shadow: 0px 0px 10px #4EC9FF;
    }
    .unit {
      font-size: 12px;
      color: #FFFFFF;
      opacity: 0.5;
      margin-left: 4px;
    }
    .townData {
      // display: flex;
      margin-top: 10px;
      // justify-content: space-between;
      .cardBox {
        display: flex;
        flex-wrap: wrap;
        padding-left: 14px;
      }
      .item {
        width: 134px;
      }
      .townValue {
        height: 30px;
        font-weight: 700;
        color: #FFFFFF;
        font-size: 14px;
        line-height: 30px;
        text-align: center;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100%;
      }
      .innerItem {
        margin-top: 8px;
        margin-right: 10px;
        width: 120px;
        height: 70px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100% 100%;
        color: #FFFFFF;
        div {
          text-align: center;
        }
      }
    }
    .content {
      height: calc(100% - 42px);
      position: relative;
      .yAxisName {
        position: absolute;
        top: 10px;
        left: 10px;
        font-size: 12px;
      }
    }
  }
  .bottom {
    margin-top: 20px;
    overflow: hidden;
    // height: calc((100% - 208px) / 2 - 20px);
    height: 295px;
    .paramBox {
      width: 420px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 0 100px;
      .pCard {
        width: 80px;
        height: 80px;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100% 100%;
        text-align: center;
        .pLabel {
          padding-top: 8px;
        }
        .pValue {
          padding-top: 10px;
          font-size: 18px;
        }
        .pUnit {
          font-size: 12px;
        }
      }
    }
    .outer {
      position: relative;
      height: calc(100% - 42px);
      padding: 10px;
      .inner {
        height: 100%;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100% 100%;
      }
      .item {
        position: relative;
        // height: 33%;
        display: flex;
        align-items: center;
        justify-content: center;
        .cell {
          width: 80px;
          height: 62px;
          background-image: url('~@/assets/img/home/<USER>');
          background-size: 100% 100%;
          .state {
            font-size: 18px;
            font-weight: 500;
            color: #02CCFC;
            text-shadow: 0px 0px 16px #02CCFC;
            text-align: center;
          }
          .name {
            margin-top: 5px;
            font-size: 12px;
            text-align: center;
          }
        }
        .base {
          position: absolute;
          bottom: 0;
          width: 314px;
          height: 14px;
          background-image: url('~@/assets/img/home/<USER>');
          background-size: 100% 100%;
        }
      }
      .arrow {
        position: absolute;
        top: 50%;
        font-size: 26px;
        color: #02CCFC;
        cursor: pointer;
      }
    }
    .outerBox {
      display: flex;
      justify-content: space-between;
      height: calc(100% - 42px);
      overflow: hidden;
      padding: 16px;
      .outerBoxItem {
        width: 190px;
      }
      .smallTitle {
        width: 190px;
        height: 30px;
        color: #FFFFFF;
        line-height: 30px;
        text-align: center;
        background-image: url('~@/assets/img/home/<USER>');
        background-size: 100%;
      }
      .cardBox {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .card {
          width: 90px;
          height: 75px;
          background-size: 100%;
          color: #ffffff;
          font-size: 12px;
          margin-top: 5px;
          background-size: 100% 100%;
          div {
            text-align: center;
            padding-top: 5px;
          }
          .textBox {
            display: flex;
            justify-content: space-around;
          }
        }
      }
      .normal {
        background-image: url('~@/assets/img/home/<USER>');
      }
      .warn {
        color: #F1BB17;
        background-image: url('~@/assets/img/home/<USER>');
      }
      .normalText {
        color: green;
      }
      .warnText {
        color: #FF0000;
      }
    }
  }
  .infoContent {
    padding: 10px;
  }
  .title {
    background: url("../../assets/img/title.png") center no-repeat;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
  }
}
</style>