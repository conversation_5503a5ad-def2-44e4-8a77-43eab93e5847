<template>
  <div class="videoPopup">
    <div class="popupTitle">{{ detaildata.videoName }}</div>
    <div class="videoBox">
      <div class="video">
        <HikPlayer v-if="detaildata.videoCode" :cameraIndexCode="detaildata.videoCode" style="width: 100%;height: 100%;" />
      </div>
    </div>
  </div>
</template>

<script>
import HikPlayer from '@/components/player/hikPlayer2'

export default {
  name: 'VideoPopup',
  props: {
    detaildata: {
      type: Object,
      default: () => {}
    }
  },
  components: { HikPlayer },
}
</script>

<style lang="scss" scoped>
.videoPopup {
  background: url("~@/assets/img/home/<USER>");
  background-size: 100%;
  width: 460px;
  height: 640px;
  position: absolute;
  left: 30px;
  top: 120px;
  .popupTitle {
    height: 51px;
    padding: 13px 0 12px 19px;
    font-size: 22px;
    font-family: 'Microsoft YaHei';
    color: #d8f0ff;
    line-height: 26px;
    letter-spacing: 1px;
    text-shadow: 0px 0px 10px rgba(0, 145, 255, 0.5011), 0px 0px 4px #0091ff;
  }
  .videoBox {
    padding: 20px;
    .video {
      height: 260px;
      background-color: black;
    }
  }
}
</style>