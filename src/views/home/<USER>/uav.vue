<template>
  <div class="uav">
    <div class="default" v-if="showVideo === 'default'"></div>
    <video v-if="showVideo === 'video1'" ref="video1" src="http://223.75.177.151:19000/xsfile/tempVideo1.mp4" class="video" muted autoplay loop @click="handleClick1"></video>
    <video v-if="showVideo === 'video2'" ref="video2" src="http://223.75.177.151:19000/xsfile/tempVideo2.mp4" class="video" muted autoplay loop @click="handleClick2"></video>
    <video v-show="showVideo === 'uav'" ref="videoPlayer" class="video" muted></video>

    <div class="legendBox">
      <div v-for="item in legendlist" :key="item.id">
        <div class="check" @click="change(item)">
          <div class="checked" v-if="item.state"></div>
        </div>
        <!-- <img :src="item.img" style="transform: translate(0, 2px)" alt="" /> -->
        <div class="name">{{ item.label }}</div>
      </div>
    </div>

    <!-- 2个视频按钮 -->
    <div class="videoAction">
      <div class="customButton" @click="videoAction('video1')">河道巡检</div>
      <div class="customButton" style="margin-top: 10px;" @click="videoAction('video2')">管线巡检</div>
    </div>

    <div class="hyDataBox">
      <div
        class="item"
        v-for="item in hyData"
        :key="item.id"
        :style="{ 'background-image': 'url(' + (item.image)  +')' }">
        <div>{{ item.label }}</div>
        <div><span style="font-size: 22px;padding-top: 5px;font-weight: bold;">{{ item.value }}</span>{{ item.unit }}</div>
      </div>
    </div>

    <!-- 罗盘 -->
    <div class="compassBox">
      <img class="arrow" src="@/assets/img/home/<USER>/arrow.png" alt="">
    </div>

    <!-- 顶部数据 -->
    <div class="topDataBox">
      <div class="item" v-for="item in topData" :key="item.id">
        <img :src="item.image" alt="">
        <div class="rightCell">
          <div>{{ item.label }}</div>
          <div><span style="font-size: 24px;font-weight: bold;margin-right: 9px;">{{ item.value }}</span>{{item.unit}}</div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottomButtonBox">
      <div class="outer">
        <!-- <div class="customButton">一键启动</div> -->
        <div class="customButton" @click="playUav">{{ isPlay ? '停止直播' : '远程直播' }}</div>
      </div>
    </div>

    <!-- <div class="uavBox" v-show="showUAV">
      <video ref="videoPlayer" class="video" muted></video>
    </div> -->

    <!-- 右侧识别数据 -->
    <div class="identificationData" v-if="false">
      <div class="title">AI识别 <span class="actionText">查看更多 >></span></div>
      <div class="dataBox">
        <div class="dataItem" v-for="i in 4" :key="i">
          <img src="@/assets/img/home/<USER>/aiPic.png" style="width: 160px;" alt="">
          <div class="rightFiled">
            <div>人员入侵</div>
            <div>智能识别{{ i}}</div>
            <div>2023-08-30 13:24:56</div>
            <div>古夫镇</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Hls from 'hls.js'
import axios from 'axios'

export default {
  name: 'HomeUav',
  data() {
    return {
      showVideo: 'default',
      legendlist: [
        {
          id: 1,
          label: '水质预警',
          state: false
        },
        {
          id: 2,
          label: '河道预警',
          state: false
        },
        {
          id: 3,
          label: '人员入侵',
          state: false
        },
        {
          id: 4,
          label: '漂浮物识别',
          state: false
        }
      ],
      hyData: [
        {
          id: 1,
          label: '流速',
          image: require('@/assets/img/home/<USER>/speed.png'),
          value: '--',
          unit: 'm/s'
        },
        {
          id: 2,
          label: '流量',
          image: require('@/assets/img/home/<USER>/flow.png'),
          value: '--',
          unit: 'm³/h'
        }
      ],
      topData: [
        {
          id: 1,
          label: '作业次数',
          image: require('@/assets/img/home/<USER>/topData1.png'),
          value: 50,
          unit: '次'
        },
        {
          id: 2,
          label: '巡航里程',
          image: require('@/assets/img/home/<USER>/topData2.png'),
          value: 12,
          unit: 'km'
        },
        {
          id: 3,
          label: 'AI识别',
          image: require('@/assets/img/home/<USER>/topData3.png'),
          value: 248,
          unit: 'm²'
        }
      ],
      video1IsPlay: true,
      video2IsPlay: true,
      uavUrl: '',
      showUAV: false,
      uavCanPlay: false,
      isPlay: false,
      hls: null,
      filePerfix: process.env.VUE_APP_FILE_PREFIX 
    }
  },
  mounted() {
    this.uavUrl = process.env.VUE_APP_UAV_URL
    axios.get(this.uavUrl).then(res => {
      if(res.data) {
        this.uavCanPlay = true
      }
    }).catch(error => {
      console.log(error)
      this.uavCanPlay = false
    })
  },
  beforeDestroy() {
    // 销毁 hls
    if(this.hls) {
      this.hls.destroy()
    }
  },
  methods: {
    change(item) {
      item.state = !item.state;
    },
    videoAction(value) {
      this.showVideo = value
    },
    playUav() {
      if(this.uavCanPlay) {

        if(this.isPlay) {
          // 正在播放
          this.isPlay = false
          this.showVideo = 'default'
          // 销毁 hls
          if(this.hls) {
            this.hls.destroy()
          }
        } else {
          // 开始播放
          this.isPlay = true
          this.showVideo = 'uav'

          let video = this.$refs.videoPlayer
          if(Hls.isSupported()) {
            const hls = new Hls()
            this.hls = hls

            hls.loadSource(this.uavUrl)
            hls.attachMedia(video)

            hls.on(Hls.Events.MANIFEST_PARSED, () => {
              console.log('HLS manifest loaded')
              video.play()
            })
            hls.on(Hls.Events.ERROR, (event, data) => {
              console.error('HLS.js error:', data);
            })
          }
        }

      } else {
        console.log('无人机未起飞')
      }
      
    },
    handleClick1() {
      const paused = this.$refs.video1.paused
      if(paused) {
        this.$refs.video1.play()
      } else {
        this.$refs.video1.pause()
      }
    },
    handleClick2() {
      const paused = this.$refs.video2.paused
      if(paused) {
        this.$refs.video2.play()
      } else {
        this.$refs.video2.pause()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.uav {
  height: calc(100vh - 84px);
  // background-image: url('~@/assets/img/home/<USER>/uavBg.png');
  box-shadow: 0 0 100px black inset;
  position: relative;
  .default {
    position: absolute;
    background-image: url('~@/assets/img/home/<USER>/uavBg.png');
    width: 100%;
    height: 100%;
  }
  .uavBox {
    position: absolute;
    width: 100%;
    height: 100%;
  }
  .video {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
  .videoAction {
    position: absolute;
    left: 30px;
    top: 200px;
    .customButton {
      width: 100px;
      height: 40px;
      background-image: url('~@/assets/img/home/<USER>/customButtonBg.png');
      background-size: 100px 40px;
      cursor: pointer;
      font-size: 16px;
      font-family: Microsoft YaHei;
      color: #ffffff;
      text-align: center;
      line-height: 40px;
    }
  }
  .legendBox {
    position: absolute;
    width: 126px;
    height: 141px;
    left: 30px;
    top: 30px;
    background-color: #040F2D;
    display: flex;
    flex-direction: column;
    padding: 15px 0 15px 19px;
    justify-content: space-between;
    .check {
      width: 16px;
      height: 16px;
      border: 1px solid #91d5ff;
      margin-right: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .checked {
      width: 8px;
      height: 8px;
      background: linear-gradient(180deg, #60dad1 0%, #1e9afc 100%);
    }
    div {
      display: flex;
      align-items: center;
      .name {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        margin-left: 5px;
      }
    }
  }
  .hyDataBox {
    position: absolute;
    left: 30px;
    top: 40%;
    .item {
      width: 180px;
      height: 82px;
      background-size: 100%;
      color: #ffffff;
      font-size: 13px;
      padding: 28px 0 0 74px;
    }
  }
  .compassBox {
    width: 160px;
    height: 161px;
    background-image: url('~@/assets/img/home/<USER>/compass.png');
    background-size: 100%;
    position: absolute;
    bottom: 30px;
    &::before {
      display: block;
      content: 'N';
      color: #00FFFF;
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      background-color: #0975AF;
      border-radius: 50%;
      position: absolute;
      top: -10px;
      left: 70px;
    }
    .arrow {
      position: absolute;
      width: 93px;
      height: 93px;
      left: 33.5px;
      transform: rotate(45deg);
      transform-origin: 46.5px 83px;
    }
  }
  .topDataBox {
    position: absolute;
    left: 710px;
    top: 20px;
    width: 500px;
    height: 62px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item {
      display: flex;
      color: #ffffff;
      .rightCell {
        padding: 10px 0 0 17px;
      }
    }
    img {
      width: 60px;
    }
  }
  .bottomButtonBox {
    position: absolute;
    bottom: 65px;
    width: 100%;
    display: flex;
    justify-content: center;
    .outer {
      display: flex;
      // justify-content: center;
    }
    .customButton {
      width: 149px;
      height: 40px;
      background-image: url('~@/assets/img/home/<USER>/customButtonBg.png');
      background-size: 149px 40px;
      cursor: pointer;
      font-size: 18px;
      font-family: Microsoft YaHei;
      color: #ffffff;
      text-align: center;
      line-height: 40px;
      margin-right: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .identificationData {
    position: absolute;
    right: 30px;
    width: 370px;
    height: calc(100% - 60px);
    margin: 30px 0;
    background-color: rgba(11, 25, 46, 0.5);
    
    .actionText {
      color: #00E4FF;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      cursor: pointer;
    }
    .dataBox {
      padding: 0 20px;
    }
    .dataItem {
      width: 330px;
      height: 151px;
      background-image: url("~@/assets/img/home/<USER>/itemBg.png");
      background-size: 100%;
      margin-top: 10px;
      display: flex;
      padding-left: 14px;
      align-items: center;
      .rightFiled {
        flex-grow: 1;
        color: #ffffff;
        font-size: 12px;
        display: flex;
        height: 120px;
        flex-direction: column;
        justify-content: space-between;
        padding-left: 5px;
        div {
          background-color: #0D316F;
          line-height: 24px;
          height: 24px;
          width: 140px;
        }
      }
    }
  }
  .title {
    background: url("~@/assets/img/title.png") center no-repeat;
    background-size: 370px 42px;
    width: 370px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
    padding-right: 30px;
  }
  .titleFlex {
    display: flex;
    justify-content: space-between;
  }
}
</style>