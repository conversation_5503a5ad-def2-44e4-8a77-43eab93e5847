<template>
  <div class="videoWall">
    <div class="navbar">
      <span class="backArrow" @click="backToHome"></span>
      <span style="color: #ACC6EA;">当前位置：首页</span>
      <span><i class="el-icon-arrow-right"></i>视频监控</span>
      <el-tooltip effect="dark" content="无人机值守" placement="left">
        <img src="@/assets/img/uav.png" alt="" @click="jumpToUAV">
      </el-tooltip>
    </div>

    <div class="contentCard">
      <div class="treeBox">
        <div>
          <el-input
            placeholder="搜索视频监控"
            suffix-icon="el-icon-search"
            size="small"
            v-model="search">
          </el-input>
        </div>
        <el-scrollbar class="treeContainer">
          <!-- 滚动条要包裹的tree内容 --> 
          <el-tree 
            :data="monitorVideoTree"
            :props="elTreeProps"
            @node-click="nodeClick"
            :indent="8"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span v-if="node.isLeaf">
                <template v-if="data.cameraType === 1">
                  <img v-if="data.online" src="@/assets/img/home/<USER>" alt="枪机在线">
                  <img v-else src="@/assets/img/home/<USER>" alt="枪机离线">
                </template>
                <template v-else>
                  <img v-if="data.online" src="@/assets/img/home/<USER>" alt="球机在线">
                  <img v-else src="@/assets/img/home/<USER>" alt="球机离线">
                </template>
                
                <span style="font-size: 14px;">{{ node.label }}</span>
              </span>
              <span v-else style="font-size: 14px;">{{ node.label }}<span style="margin-left: 10px;">({{ data.count }})</span></span>
            </span>
          </el-tree>
        </el-scrollbar>
      </div>

      <div class="wallBox">
        <DHPlayerWall :channelInfo="currentChannelInfo" :playList="defaultPlayList" />
      </div>

      <div class="aiData">
        <div class="innerTitle">
          <span>AI识别</span>
          <!-- <img src="@/assets/img/uav.png" alt="" @click="jumpToUAV"> -->
        </div>
        <div class="dataList">
          <div class="item" v-for="item in picList" :key="item.id">
            <div class="type">{{ item.alarmType === 962 ? '人员入侵' : '车辆入侵' }}</div>
            <!-- <el-image style="width: 120px;height: 67.5px;" :src="iccPrefix + item.alarmPicture + '?token=' + token" :preview-src-list="[iccPrefix + item.alarmPicture + '?token=' + token]"></el-image> -->
            <el-image style="width: 120px;height: 67.5px;" :src="item.alarmPicture" :preview-src-list="[item.alarmPicture]"></el-image>
            <!-- <div class="imgBox">
              <img :src="iccPrefix + item.alarmPicture + '?token=' + token" alt="">
            </div> -->
            <div style="font-size: 12px;">{{ item.alarmDate }}</div>
          </div>
        </div>

        <el-pagination
          style="text-align: center;"
          small
          layout="prev, pager, next"
          :current-page.sync="page"
          :page-size="14"
          :pager-count="5"
          @current-change="handlePageChange"
          :total="picTotal">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getMonitorVideoTree } from '@/api/home/<USER>'
import { getICCToken, getCameraPic } from '@/api/camera'

import DHPlayerWall from '@/components/player/DHPlayerWall'

export default {
  name: 'VideoWall',
  components: { DHPlayerWall },
  data() {
    return {
      iccPrefix: 'https://**************:1443/evo-apigw/evo-oss/',
      token: '',
      search: '',
      currentCameraIndexCode: '',
      monitorVideoList: [],
      monitorVideoTree: [],
      elTreeProps: {
        children: 'children',
        label: 'name'
      },
      currentChannelInfo: {},
      defaultPlayList: [
        {
          channelId: '1000004$1$0$39',
          channelName: '峡口吴家坪水厂外球机',
          cameraType: 2
        },
        {
          channelId: '1000004$1$0$0',
          channelName: '昭君镇金乐村二级泵站内球机',
          cameraType: 2
        },
        {
          channelId: '1000004$1$0$29',
          channelName: '昭君镇昭君村景区泵站',
          cameraType: 2
        },
        {
          channelId: '1000004$1$0$22',
          channelName: '昭君镇大礼溪水厂球机',
          cameraType: 2
        }
      ],
      picList: [],
      picTotal: 0,
      page: 1
    }
  },
  created() {
    // getICCToken().then(res => {
    //   this.token = res.data.token.split(' ')[1] || ''
    // })
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      // 获取相机树
      getMonitorVideoTree().then(res => {
        this.monitorVideoTree = this.addLeafCountToTrees(res.data)
      })
      this.getPic()
    },
    // 
    addLeafCountToTrees(trees) {
      // 辅助函数：递归处理单个节点
      function processNode(node) {
        if (!node.children || node.children.length === 0) {
          // 叶子节点，count为1（自己就是叶子节点）
          node.count = 1
          return 1
        }
        
        // 非叶子节点，计算所有子节点的叶子节点数之和
        let leafCount = 0;
        node.children.forEach(child => {
          leafCount += processNode(child);
        });
        
        node.count = leafCount;
        return leafCount;
      }
      
      // 深拷贝原数组以避免修改
      const newTrees = JSON.parse(JSON.stringify(trees))
      
      // 处理每棵树
      newTrees.forEach(tree => {
        processNode(tree)
      })
      
      return newTrees
    },
    backToHome() {
      this.$router.push('/main/home/<USER>')
    },
    // tree 节点点击
    nodeClick(obj, node, self) {
      console.log(obj, node, self)
      if(obj.channelCode) {
        console.log(obj.channelCode)
        // this.currentCameraIndexCode = obj.cameraIndexCode
        this.currentChannelInfo = {
          channelId: obj.channelCode,
          channelName: obj.name,
          cameraType: obj.cameraType
        }
      }
    },
    jumpToUAV() {
      this.$router.push({ name: 'UAV' })
    },
    // 相机图片
    getPic() {
      getCameraPic({
        pageNo: this.page,
        pageSize: 14,
        alarmType: [963, 962]
      }).then(res => {
        this.picList = res.data
        this.picTotal = res.count
      })
    },
    handlePageChange() {
      this.getPic()
    }
  }
}
</script>

<style lang="scss">
.videoWall {
  .treeContainer {
    .el-scrollbar__wrap {
      height: 100%;
      overflow-x: auto;
    }
  }
  .el-tree {
    background: transparent;
    color: #fff;
    .el-tree-node:focus > .el-tree-node__content {
      background-color: #113b7a;
      color: #409eff; //节点的字体颜色
      font-weight: bold;
    }

    .el-tree-node.is-current > .el-tree-node__content {
      background-color: #113b7a;
    }
    .el-tree-node__content:hover {
      background-color: #113b7a;
    }
  }
  .el-image {
    .el-image-viewer__canvas {
      .el-image-viewer__img {
        width: 80%;
      }
    }
  }
  .el-pagination {
    color: #fff;
    button, .el-pager li {
      background-color: transparent;
      height: 22px !important;
      min-width: 22px !important;
      line-height: 22px !important;
      margin: 0 !important;
    }
    .btn-quickprev, .btn-quicknext {
      color: #fff;
    }
    .btn-prev, .btn-next {
      color: #fff;
    }
  }
}
</style>

<style lang="scss" scoped>
.videoWall {
  padding: 0 30px;
  height: calc(100vh - 84px);
  color: #ffffff;
  .navbar {
    font-size: 16px;
    height: 40px;
    display: flex;
    align-items: center;
    .backArrow {
      display: inline-block;
      width: 28px;
      height: 17px;
      background-image: url('../../../assets/img/home/<USER>');
      background-size: 100%;
      cursor: pointer;
      margin-right: 10px;
    }
    img {
      width: 36px;
      cursor: pointer;
      margin-left: auto;
      margin-right: 20px;
    }
  }
  .contentCard {
    height: calc(100% - 70px);
    border: 1px solid #2A5DD2;
    border-radius: 2px;
    background-color: rgba(3 ,30, 73, 0.5);
    padding: 20px;
    display: flex;
    .treeBox {
      width: 280px;
      height: 100%;
      border: 1px solid rgba(42, 93 ,210, 0.5);
      border-radius: 2px;
      padding: 20px;
      background-color: rgba(42 ,93, 210, 0.1);
      .treeContainer {
        margin-top: 10px;
        height: calc(100% - 42px);
      }
    }
    .custom-tree-node {
      display: flex;
      align-items: center;
      img {
        margin-right: 2px;
      }
    }
    .wallBox {
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      // flex-grow: 1;
      flex: 1;
      // width: 1220px;
      padding-left: 20px;
      .videoTemp {
        width: 100%;
        height: 100%;
        position: relative;
        .stationInfo {
          position: absolute;
          height: 42px;
          width: 100%;
          background-color: rgba(17, 115, 204, 0.5);
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          padding: 0 20px;
        }
        img {
          width: 100%;
          height: 100%;
        }
      }
      .videoBox {
        width: 33%;
        height: 230px;
        border: 3px solid #2A5DD2;
        background-color: black;
      }
    }
    .aiData {
      margin-left: 20px;
      // flex-grow: 1;
      width: 296px;
      height: 100%;
      border: 1px solid #2A5DD2;
      border-radius: 2px;
      background-color: rgba(3 ,30, 73, 0.5);
      .innerTitle {
        padding: 0 20px;
        line-height: 44px;
        font-weight: 700;
        font-size: 24px;
        height: 44px;
        background: linear-gradient(270deg, #164F8F 0%, #10243E 100%);
        display: flex;
        align-items: center;
        img {
          width: 36px;
          cursor: pointer;
          margin-left: auto;
        }
      }
      .dataList {
        padding: 20px;
        display: flex;
        flex-wrap: wrap;
        min-height: 638.5px;
        justify-content: space-between;
        .item {
          position: relative;
          .type {
            position: absolute;
            z-index: 1;
            padding: 2px;
            font-size: 13px;
            background-color: #0E9CFF;
          }
          .imgBox {
            width: 120px;
            height: 68px;
          }
          img {
            width: 120px;
          }
        }
      }
    }
  }
}
</style>
