<template>
  <div class="popupbox">
    <!-- top -->
    <div class="closeBtn">
      <i class="el-icon-close close" @click="closepopup" />
    </div>
    <div class="popuptitle">
      {{ detaildata.monitorBaseStationInfo.stationName || "" }}
    </div>
    <div
      style="
        display: flex;
        height: 50px;
        padding: 9px 19px 7px 19px;
        justify-content: space-between;
      "
    >
      <div class="village">
        {{ detaildata.monitorBaseStationInfo.villageName || "" }}
      </div>
      <div style="display: flex; align-items: center">
        <span class="lonlat"> 经度：</span>
        <span class="lonlatdata">{{
          detaildata.coordinate.x.toFixed(2) || ""
        }}</span>
        <div
          style="
            width: 0px;
            height: 17px;
            opacity: 1;
            border: 1px solid #ccecfc;
            margin: 0 20px;
          "
        ></div>
        <span class="lonlat">纬度:</span>
        <span class="lonlatdata">{{
          detaildata.coordinate.y.toFixed(2) || ""
        }}</span>
      </div>
    </div>
    <!-- tab -->
    <div class="infotab">
      <div
        :style="{ borderBottom: activetab === 1 ? '1px solid #83D1FF' : '0' }"
        @click="changetab(1)"
      >
        实时监测
      </div>
      <div
        :style="{ borderBottom: activetab === 2 ? '1px solid #83D1FF' : '0' }"
        @click="changetab(2)"
      >
        历史信息
      </div>
      <div
        :style="{ borderBottom: activetab === 3 ? '1px solid #83D1FF' : '0' }"
        @click="changetab(3)"
      >
        基本信息
      </div>
    </div>
    <div style="margin: 20px" v-if="activetab === 1 || activetab === 3">
      <div v-for="item in infodatalist" :key="item.id" class="infoitem">
        <div>{{ item.label }}</div>
        <div>{{ item.value }}</div>
      </div>
    </div>
    <!-- 历史数据 -->
    <div v-if="activetab === 2" class="historyData">
      <div class="innerTitle">
        <span style="font-size: 14px">历史变化趋势图</span>
        <div class="report" @click="openReport()">报表</div>
      </div>
      <div class="paramList">
        <div
          class="paramItem"
          :class="{ selected: item.value === currentParam }"
          v-for="item in params"
          :key="item.value"
          @click="changeParam(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="timePicker">
        <el-date-picker
          v-model="historyDataTime"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          size="mini"
          style="width: 330px"
        >
        </el-date-picker>
        <div
          class="report"
          style="height: 28px; line-height: 28px; margin-left: 10px"
          @click="handleOk"
        >
          确定
        </div>
      </div>
      <div class="timeShortcuts">
        <div
          class="paramItem"
          :class="{ selected: item.day === currentShortcut }"
          style="width: 48px"
          v-for="item in shortcuts"
          :key="item.value"
          @click="changeShortcut(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <LineChart :seriesData="lineData" height="200px" />
    </div>
    <div v-if="activetab === 3" class="imgbox">
      <div>点位照片</div>
      <img
        v-if="detaildata.monitorBaseStationInfo.pic"
        :src="detaildata.monitorBaseStationInfo.pic"
      />
    </div>
    <!-- 历史信息-报表 -->
    <el-dialog
      title="报表"
      :visible.sync="reportDialogVisible"
      top="5vh"
      width="900px"
      height="750px"
    >
      <div class="contentBox">
        <div class="query">
          <span>选择时间：</span>
          <el-date-picker
            v-model="reportDataTime"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="mini"
            style="width: 330px"
          >
          </el-date-picker>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleReport"
            style="margin-left: 20px"
            >查询</el-button
          >
        </div>
        <el-table
          :data="reportList"
          size="mini"
          height="600px"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column
            label="序号"
            type="index"
            width="55"
          ></el-table-column>
          <el-table-column label="点位名称" prop="">{{ detaildata.monitorBaseStationInfo.stationName }}</el-table-column>
          <!-- <el-table-column label="联网" prop=""></el-table-column> -->
          <el-table-column
            label="监测时间"
            prop="monitorTime"
            width="200"
          ></el-table-column>
          <el-table-column
            label="流量(m³/h)"
            prop="flow"
            width="100"
          ></el-table-column>
          <el-table-column label="压力(Pa)" prop="pressure"></el-table-column>
          <el-table-column label="液位(m)" prop="level"></el-table-column>
          <!-- <el-table-column label="备注" prop=""></el-table-column> -->
        </el-table>
        <el-pagination
          background
          style="padding: 5px 0;"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageInfo.pageNo"
          :page-sizes="[10, 20, 30, 40]"
          :page-size.sync="pageInfo.pageSize"
          layout="total, sizes, ->, prev, pager, next, jumper"
          :total="pageInfo.total"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getShortcutTs } from "@/utils/time";
import { getHistoryData, getReport } from "@/api/home/<USER>";
import LineChart from "../chart/lineChart.vue";

export default {
  name: "Popup1",
  props: ["detaildata", "stationId"],
  components: { LineChart },
  data() {
    return {
      reportList:[],
      multipleSelection: [],
      pageInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
      },
      reportDialogVisible: false,
      reportDataTime: [],
      // 以上为报表
      infodatalist: [],
      activetab: 1,
      historyDataTime: [],
      shortcuts: [
        {
          label: "24h",
          day: 1,
        },
        {
          label: "48h",
          day: 2,
        },
        {
          label: "近一周",
          day: 7,
        },
        {
          label: "近一月",
          day: 30,
        },
      ],
      currentParam: "",
      currentShortcut: 1,
      requestBody: {
        sourceType: "",
        facilityId: null,
        beginTime: "",
        endTime: "",
      },
      lineData: [],
    };
  },
  watch: {
    detaildata: {
      handler(val) {
        this.activetab = 1
        this.setdata();
      },
    },
  },
  computed: {
    params() {
      const arr = [];
      const temp = this.detaildata.monitorBaseStationInfo.monitorType;
      if (temp.includes("流量")) {
        arr.push({
          label: "流量",
          value: "waterFlow",
        });
      }
      if (temp.includes("液位")) {
        arr.push({
          label: "液位",
          value: "waterLevel",
        });
      }
      if (temp.includes("压力")) {
        arr.push({
          label: "压力",
          value: "waterPress",
        });
      }
      if (arr.length) {
        this.currentParam = arr[0].value;
      }
      return arr;
    },
  },
  created() {
    this.init();
  },
  mounted() {
    this.setdata();
  },
  methods: {
    closepopup() {
      this.$emit("closePopup");
    },
    init() {
      this.historyDataTime = getShortcutTs();
    },
    backcenter() {
      this.$emit("back");
    },
    // 处理要展示的数据
    setdata() {
      let infoList = [
        {
          label: "站点名称",
          value: this.detaildata.timeMonitor.stationName,
        },
        {
          label: "监测时间",
          value: this.detaildata.timeMonitor.monitorTime,
        },
      ];
      // 实时监测
      if (this.detaildata.timeMonitor.flow !== "") {
        infoList.push({
          label: "瞬时流量",
          value: this.detaildata.timeMonitor.speed + 'm³/h',
        });
        infoList.push({
          label: "累计流量",
          value: this.detaildata.timeMonitor.flow + 'm³',
        });
      }
      if (this.detaildata.timeMonitor.level !== "") {
        infoList.push({
          label: "液位",
          value: this.detaildata.timeMonitor.level + 'm',
        });
      }
      if (this.detaildata.monitorBaseStationInfo.monitorType === '液位') {
        infoList.push({
          label: '储水量',
          value: (this.detaildata.timeMonitor.currentWater || '--') + 'm³',
        })
      }
      if (this.detaildata.timeMonitor.pressure !== "") {
        infoList.push({
          label: "压力",
          value: this.detaildata.timeMonitor.pressure + 'MPa',
        });
      }
      this.infodatalist = infoList;
    },
    // 处理数据，后续后端处理？
    changetab(val) {
      this.activetab = val;
      if (val === 1) {
        this.setdata();
      } else if (val === 3) {
        this.infodatalist = [
          {
            label: "站点名称",
            value: this.detaildata.monitorBaseStationInfo.stationName,
          },
          {
            label: "所属流域",
            value: this.detaildata.monitorBaseStationInfo.villageName,
          },
          {
            label: "站点类型",
            value: this.detaildata.monitorBaseStationInfo.stationTypeName,
          },
          {
            label: "监测要素",
            value: this.detaildata.monitorBaseStationInfo.monitorType,
          },
        ];
      } else if(val === 2) {
        this.currentShortcut = 1
        const payload = {
          facilityId: this.stationId.facilityId,
          sourceType: this.stationId.stationType,
          type: this.stationId.type,
          beginTime: this.historyDataTime[0],
          endTime: this.historyDataTime[1],
        }
        this.getLineData(payload);
      }
    },
    /* 弹窗历史信息相关方法 */
    // 点击监测参数
    changeParam(item) {
      console.log(item);
      this.currentParam = item.value;
      // 有时间参数才请求
      if (this.requestBody.beginTime && this.requestBody.endTime) {
        this.getLineData(this.requestBody);
      }
    },
    // 历史信息-点击时间
    changeShortcut(item) {
      this.currentShortcut = item.day;
      const ret = getShortcutTs(item.day);
      console.log(ret);
      this.requestBody = {
        facilityId: this.stationId.facilityId,
        sourceType: this.stationId.stationType,
        type: this.stationId.type,
        beginTime: ret[0],
        endTime: ret[1],
      };
      // this.requestBody.beginTime = ret[0];
      // this.requestBody.endTime = ret[1];
      this.historyDataTime = [ret[0], ret[1]];
      this.getLineData(this.requestBody);
    },
    // 历史信息-确定按钮
    handleOk() {
      this.currentShortcut = "";
      if (!this.historyDataTime.length) {
        this.$message.warning("请选择时间");
        return;
      }
      this.requestBody = {
        facilityId: this.stationId.facilityId,
        sourceType: this.stationId.stationType,
        type: this.stationId.type,
        beginTime: this.historyDataTime[0],
        endTime: this.historyDataTime[1],
      };
      // this.requestBody.beginTime = this.historyDataTime[0];
      // this.requestBody.endTime = this.historyDataTime[1];
      this.getLineData(this.requestBody);
    },
    getLineData(payload) {
      getHistoryData(payload).then((res) => {
        console.log(res);
        this.lineData = [
          {
            type: "line",
            symbol: "none",
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#31B3FF", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(0, 89, 84, 0.6)", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
            data: res.data[this.currentParam],
          },
        ];
        console.log(this.lineData);
      });
    },
    /* 报表 */
    // 报表按钮
    openReport() {
      this.reportDialogVisible = true
      this.reportDataTime = getShortcutTs(30)
      this.queryReport()
    },
    // 查询报表数据
    queryReport() {
      let payload = {
        ...this.stationId,
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize
      }
      payload.beginTime = this.reportDataTime[0]
      payload.endTime = this.reportDataTime[1]

      getReport(payload).then(res => {
        console.log('---', res)
        this.reportList = res.data
        this.pageInfo.total = res.count
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleReport() {
      if (!this.reportDataTime.length) {
        this.$message.warning("请选择时间");
        return;
      }
      this.queryReport()
    },
    //分页
    handleSizeChange() {
      this.pageInfo.pageNo = 1
      this.queryReport()
    },
    handleCurrentChange() {
      this.queryReport()
    },
  },
};
</script>

<style lang="scss">
.imgbox {
  margin: 0 20px 20px 20px;
  div {
    font-size: 12px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #d8f0ff;
    line-height: 12px;
    margin-bottom: 10px;
  }
  img {
    width: 420px;
    height: 205px;
  }
}
.historyData {
  color: #ffffff;
  padding: 0 20px;
  .innerTitle {
    color: #d8f0ff;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
  }
  .report {
    width: 48px;
    height: 24px;
    background: linear-gradient(360deg, #043474 0%, #0e4c9a 39%, #1382e6 99%);
    border: 1px solid #2a5dd2;
    border-radius: 4px;
    color: #ffffff;
    font-size: 12px;
    line-height: 22px;
    text-align: center;
    float: right;
    cursor: pointer;
  }
  .paramList {
    display: flex;
  }
  .paramItem {
    width: 68px;
    height: 28px;
    border: 2px solid transparent;
    background: rgba(2, 50, 128, 0.8);
    font-size: 12px;
    text-align: center;
    line-height: 24px;
    margin-right: 10px;
    cursor: pointer;
  }
  .selected {
    border-color: #3ad2e7;
  }
  .timePicker {
    margin: 10px 0;
    display: flex;
  }
  .timeShortcuts {
    display: flex;
    // flex-direction: row-reverse;
    justify-content: flex-end;
  }
}
.popupbox {
  background: url("~@/assets/img/home/<USER>");
  width: 460px;
  height: 640px;
  // background: rgba(13, 40, 71, 0.8);
  border-radius: 0px 0px 0px 0px;
  // border: 1px solid rgba(0, 148, 249, 0.8);
  position: absolute;
  left: 30px;
  top: 120px;
  .closeBtn {
    position: absolute;
    right: 10px;
    top: 10px;
    color: white;
    cursor: pointer;
  }
  .back {
    background: url("~@/assets/img/home/<USER>");
    width: 120px;
    height: 37px;
    position: absolute;
    top: -70px;
  }
  .popuptitle {
    height: 51px;
    padding: 13px 0 12px 19px;
    font-size: 22px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #d8f0ff;
    line-height: 26px;
    letter-spacing: 1px;
    text-shadow: 0px 0px 10px rgba(0, 145, 255, 0.5011), 0px 0px 4px #0091ff;
  }
  .village {
    font-size: 24px;
    font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
    font-weight: 700;
    color: #d8f0ff;
    line-height: 34px;
    letter-spacing: 1px;
  }
  .lonlat {
    font-size: 14px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #cccccc;
    line-height: 17px;
  }
  .lonlatdata {
    font-size: 14px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #0097fd;
    line-height: 16px;
    text-shadow: 0px 0px 4px #00a7ff;
  }
  .infotab {
    display: flex;
    align-items: center;
    justify-content: space-around;
    div {
      font-size: 16px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      color: #d8f0ff;
      line-height: 34px;
      cursor: pointer;
    }
  }
}
.infoitem {
  display: flex;
  div {
    font-size: 12px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
    line-height: 32px;
  }
  :first-child {
    background: rgba(0, 57, 112, 0.5);
    border-radius: 0px 0px 0px 0px;
    width: 110px;
    padding-right: 10px;
    text-align: right;
  }
  :last-child {
    background: rgba(4, 15, 45, 0.2);
    border-radius: 0px 0px 0px 0px;
    padding-left: 10px;
    text-align: left;
  }
}
.el-dialog {
  background: rgba(13, 40, 71, 0.8) !important;
}
.el-table {
  background-color: rgba(13, 40, 71, 0.8);
}
.contentBox {
  color: #fff;
  .query {
    margin: -5px 0 20px 0;
  }
}
</style>