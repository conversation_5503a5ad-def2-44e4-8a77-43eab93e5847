<template>
  <div class="basePopup">
    <span>{{ pointName }}</span>
  </div>
</template>

<script>
export default {
  name: 'BasePopup',
  props: {
    pointName: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.basePopup {
  background-color: rgba(5, 58, 127, 0.4);
  border-radius: 4px;
  border: 1px solid #0b0b88;
  color: #fff;
  padding: 10px;
}
</style>