<template>
  <div class="meterPopup">
    <div class="closeBtn">
      <i class="el-icon-close close" @click="closepopup" />
    </div>
    <div class="popupTitle">{{ detaildata.meterTitle }}</div>
    <div style="margin: 20px" v-if="type === 'meter'">
      <div v-for="item in infodatalist" :key="item.id" class="infoitem">
        <div>{{ item.label }}</div>
        <div>{{ item.value }}</div>
      </div>
    </div>
    <div style="margin: 20px" v-else>
      <div v-for="item in infodatalist1" :key="item.id" class="infoitem">
        <div>{{ item.label }}</div>
        <div>{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getMeterLatestDataById, getMeterLatestDataByIdNew, getHydroStationLatestDataById } from '@/api/home/<USER>'

export default {
  name: 'MeterPopup',
  props: {
    meterId: {
      type: Number
    },
    detaildata: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      infodatalist: [
        {
          label: '用水地址',
          value: '--'
        },
        {
          label: '表编号',
          value: '--'
        },
        {
          label: '读数',
          value: '--'
        },
        {
          label: '电压',
          value: '--'
        },
        {
          label: '电池状态',
          value: '--'
        },
        {
          label: '阀门状态',
          value: '--'
        },
        {
          label: '抄表时间',
          value: '--'
        }
      ],
      infodatalist1: [
        {
          label: '地址',
          value: '--'
        },
        {
          label: '采集时间',
          value: '--'
        },
        {
          label: '表面流速(m/s)',
          value: '--'
        },
        {
          label: '断面流速(m/s)',
          value: '--'
        },
        {
          label: '瞬时流量(m³/s)',
          value: '--'
        },
        {
          label: '小时雨量(mm)',
          value: '--'
        },
        {
          label: '累计水量(m³)',
          value: '--'
        },
        {
          label: '水深(m)',
          value: '--'
        },
        {
          label: '累计水量进位次数(百万m³)',
          value: '--'
        },
        {
          label: '雨量开关',
          value: '--'
        }
      ]
    }
  },
  computed: {
    type() {
      return this.detaildata.type
    }
  },
  watch: {
    meterId() {
      this.getData()
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    closepopup() {
      this.$emit("closePopup");
    },
    getData() {
      if (this.type === 'meter') {
        this.infodatalist.forEach(item => {
          item.value = '--'
        })
        getMeterLatestDataByIdNew(this.meterId).then(res => {
          let { status, data } = res
          if (status === 200 && data !== '') {
            this.infodatalist[0].value = res.data.address
            this.infodatalist[1].value = res.data.meterAddress
            this.infodatalist[2].value = res.data.value
            this.infodatalist[3].value = res.data.batteryVoltage
            this.infodatalist[4].value = res.data.batteryStatus === 0 ? '正常' : '电量低'
            this.infodatalist[5].value = res.data.valveStatus === 0 ? '开阀门' : '关阀门'
            this.infodatalist[6].value = res.data.dataTime
          }
        })
      } else {
        // 一体化水文站
        console.log('一体化水文站')
        this.infodatalist1.forEach(item => {
          item.value = '--'
        })
        getHydroStationLatestDataById(this.meterId).then(res => {
          let { status, data } = res
          if (status === 200 && data !== '') {
            this.infodatalist1[0].value = data.addr
            this.infodatalist1[1].value = data.dataTime
            this.infodatalist1[2].value = data.bmls
            this.infodatalist1[3].value = data.dmls
            this.infodatalist1[4].value = data.ssll
            this.infodatalist1[5].value = data.xsyl
            this.infodatalist1[6].value = data.ljsl
            this.infodatalist1[7].value = data.ss
            this.infodatalist1[8].value = data.ljsljwcs
            this.infodatalist1[9].value = data.ylkg == 'True' ? '开状态' : '关状态'
          }
        })
      }
      
    }
  }
}
</script>

<style lang="scss" scoped>
.meterPopup {
  background: url("~@/assets/img/home/<USER>");
  background-size: 100%;
  width: 460px;
  height: 640px;
  position: absolute;
  left: 30px;
  top: 120px;
  .closeBtn {
    position: absolute;
    right: 10px;
    top: 10px;
    color: white;
    cursor: pointer;
  }
  .popupTitle {
    height: 51px;
    padding: 13px 0 12px 19px;
    font-size: 22px;
    font-family: 'Microsoft YaHei';
    color: #d8f0ff;
    line-height: 26px;
    letter-spacing: 1px;
    text-shadow: 0px 0px 10px rgba(0, 145, 255, 0.5011), 0px 0px 4px #0091ff;
  }
  .infoitem {
    display: flex;
    div {
      font-size: 12px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 32px;
    }
    :first-child {
      background: rgba(0, 57, 112, 0.5);
      border-radius: 0px 0px 0px 0px;
      width: 160px;
      padding-right: 10px;
      text-align: right;
    }
    :last-child {
      background: rgba(4, 15, 45, 0.2);
      border-radius: 0px 0px 0px 0px;
      padding-left: 10px;
      text-align: left;
    }
  }
}
</style>