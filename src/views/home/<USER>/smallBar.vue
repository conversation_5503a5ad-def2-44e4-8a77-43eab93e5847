<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from '@/utils/chartResize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    barData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    yAxisName: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    barData: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption(
        {
          color: ['#02F470'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            backgroundColor: 'rgba(2, 50, 128, 0.6)'
          },
          grid: {
            top: 4,
            right: 0,
            bottom: 4,
            left: 0,
            containLabel: true
          },
          xAxis: {
            type: 'value',
            axisLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
          },
          // 类目轴
          yAxis: [
            {
              type: 'category',
              inverse: true,
              axisLine: {
                show: false,
              },
              axisLabel: {
                color: '#FFFFFF',
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              },
              data: ['谭湾', '大礼', '吴家坪', '青华', '双龙观']
            },
            // 第二个y轴，用于显示数值
            {
              type: 'category',
              inverse: true,
              axisLine: {
                show: false,
              },
              axisLabel: {
                color: '#00CCFF',
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              },
              data: this.barData
            }
          ],
          series: [
            {
              name: '实时水量',
              type: 'bar',
              showBackground: true,
              backgroundStyle: {
                color: '#1c253b'
              },
              barWidth: 8,
              data: this.barData
            }
          ]
        },
        {
          notMerge: true
        }
      )
    }
  }
}
</script>
