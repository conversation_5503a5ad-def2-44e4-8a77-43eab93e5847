<template>
  <div>
    <table border="1">
      <thead>
        <tr>
          <th>内容名称</th>
          <th>指标名称</th>
          <th>评估指标</th>
          <th>评估依据</th>
          <th>细则</th>
        </tr>
      </thead>
      <tbody>
        <template v-for="(content, contentIndex) in data">
          <template
            v-for="(indicator, indicatorIndex) in content.indicatorList"
          >
            <template
              v-for="(standard, standardIndex) in indicator.standardList"
            >
              <tr v-if="indicatorIndex === 0 && standardIndex === 0">
                <td :rowspan="getRowCount(contentIndex)">
                  {{ content.contentName }}
                </td>
                <td
                  :rowspan="getIndicatorRowCount(contentIndex, indicatorIndex)"
                >
                  {{ indicator.indicatorName }}
                </td>
                <td>{{ standard.name }}</td>
                <td>{{ standard.assessmentBasic }}</td>
                <td>
                  <ul>
                    <li
                      v-for="(detail, detailIndex) in standard.detail"
                      :key="detailIndex"
                    >
                      {{ detail }}
                    </li>
                  </ul>
                </td>
              </tr>
              <template v-else>
                <tr>
                  <td
                    v-if="standardIndex === 0"
                    :rowspan="
                      getIndicatorRowCount(contentIndex, indicatorIndex)
                    "
                  >
                    {{ indicator.indicatorName }}
                  </td>
                  <td>{{ standard.name }}</td>
                  <td>{{ standard.assessmentBasic }}</td>
                  <td>
                    <ul>
                      <li
                        v-for="(detail, detailIndex) in standard.detail"
                        :key="detailIndex"
                      >
                        {{ detail }}
                      </li>
                    </ul>
                  </td>
                </tr>
              </template>
            </template>
          </template>
        </template>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: [
        {
          contentName: "组织领导",
          indicatorList: [
            {
              indicatorName: "责任落实情况",
              standardList: [
                {
                  name: "设立农村供水服务电话和监督电话并进行公开，并将服务卡张贴到灶台水池水龙头",
                  assessmentBasic: "档案资料,现场查看",
                  detail: [
                    "未设立农村供水服务监督电话的扣2分",
                    "未张贴供水服务卡的扣2分",
                  ],
                },
                {
                  name: "制订镇、村农村供水应急预案，针对各种突发情况，组织处理及时",
                  assessmentBasic: "档案资料,入户走访",
                  detail: [
                    "未制订镇、村农村供水应急预案的扣2分",
                    "突发情况，因处理不及时导致群众投诉举报或通过抖音等媒体曝光造成不良影响的扣2分",
                  ],
                },
                {
                  name: "制订乡镇农村供水保障工作管理办法，明确管理模式或管护主体，建立健全管护制度，签订规范的管护合同（协议）",
                  assessmentBasic: "档案资料",
                  detail: [
                    "未制订乡镇农村供水保障工作管理办法扣2分",
                    "未明确管理模式或管护主体责任的扣2分",
                    "没有管护制度的扣1分",
                    "未与供水公司签订管护合同（协议）的扣1分",
                    "未与管水员签订管护合同（协议）的扣1分",
                  ],
                },
              ],
            },
          ],
        },
        {
          contentName: "供水保障",
          indicatorList: [
            {
              indicatorName: "供水设施管理情况",
              standardList: [
                {
                  assessmentIndicator: "供水设施管理情况",
                  name: "取水工程管护到位能够确保正常运行",
                  assessmentBasic: "现场查看",
                  detail: [
                    "水源地有污染源的扣3分",
                    "水源地设置的水源保护措施损坏的扣2分",
                    "取水设施堵塞有枯枝落叶淤泥的扣2分",
                    "引水管道破裂、堵塞的扣2分",
                  ],
                },
              ],
            },
            {
              indicatorName: "工程设施外部形象",
              standardList: [
                {
                  assessmentIndicator: "工程设施外部形象",
                  name: "管道安装规范、无裸露现象",
                  assessmentBasic: "现场查看",
                  detail: ["管道安装不规范扣2分", "管道裸露的扣2分"],
                },
                {
                  assessmentIndicator: "工程设施外部形象",
                  name: "确保农村集中供水工程设施外部环境干净、整洁，无安全隐患",
                  assessmentBasic: "现场查看",
                  detail: [
                    "凡是发现影响集中供水工程外部形象问题或存在安全隐患的1处扣1分，扣完为止",
                  ],
                },
              ],
            },
          ],
        },
      ],
    };
  },
  methods: {
    getRowCount(contentIndex) {
      let count = 0;
      this.data[contentIndex].indicatorList.forEach((indicator) => {
        count += indicator.standardList.length;
      });
      return count;
    },
    getIndicatorRowCount(contentIndex, indicatorIndex) {
      return this.data[contentIndex].indicatorList[indicatorIndex].standardList
        .length;
    },
  },
};
</script>
