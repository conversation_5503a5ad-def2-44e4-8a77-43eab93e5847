import { parseTime } from './index'

import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import weekday from 'dayjs/plugin/weekday'
import weekOfYear from 'dayjs/plugin/weekOfYear'


dayjs.locale('zh-cn')
dayjs.extend(weekday)
dayjs.extend(weekOfYear)

/* 时间处理 */

// 获取近一周
export function getWeekTs() {
  let now = new Date().getTime()
  let beginTs = now - 60 * 60 * 1000 * 24 * 7
  let endTime = parseTime(now)
  let beginTime = parseTime(beginTs)
  return [beginTime, endTime]
}
// 获取近一周 yyyy-MM-dd
export function getWeekDate() {
  let now = new Date().getTime()
  let beginTs = now - 60 * 60 * 1000 * 24 * 7
  let endTime = parseTime(now, '{y}-{m}-{d}')
  let beginTime = parseTime(beginTs, '{y}-{m}-{d}')
  return [beginTime, endTime]
}

// 获取周
export function getWeekOfYear(year, week = 0) {
  return dayjs(year).week(week).format('YYYY-MM-DD') + ' 00:00:00'
}

// 获取月初始时间
export function getMonthOfYear(year, month = 0) {
  return dayjs(year).month(month).format('YYYY-MM-DD') + ' 00:00:00'
}

// 获取周几
const array = ['周日', '周一', '周二', '周三', '周四', '周五' ,'周六']
export function getWeekday() {
  return array[dayjs().day()]
}

// 获取以前的时间

export function getShortcutTs(day = 1, cFormat) {
  let now = new Date().getTime()
  let beginTs = now - 60 * 60 * 1000 * 24 * day
  let endTime = parseTime(now, cFormat)
  let beginTime = parseTime(beginTs, cFormat)
  return [beginTime, endTime]
}

export function getTodayTs() {
  let now = new Date().getTime()
  let endTime = parseTime(now)
  let beginTime = parseTime(now, '{y}-{m}-{d}') + ' 00:00:00'
  return [beginTime, endTime]
}