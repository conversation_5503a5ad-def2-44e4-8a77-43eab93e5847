/** 网络请求工具类 **/
import axios from 'axios'
import qs from 'qs'
import {
    MessageBox,
    Notification,
    Loading
} from 'element-ui';
import store from '@/store'

// 创建 axios 实例
const service = axios.create({
    baseURL: process.env.VUE_APP_BASE_API,
    //超时时间
    timeout: 60 * 1000,
    //跨域请求时是否需要使用凭证
    withCredentials: true,
    //参数序列化
    paramsSerializer: params => {
        return qs.stringify(params, {
            indices: false
        })
    }
});

//request请求拦截器
service.interceptors.request.use(config => {
    config.headers['Content-Type'] = 'application/json;charset=UTF-8';
    if (config.method === 'get') {
        config.headers['If-Modified-Since'] = 'Mon, 26 Jul 1997 05:00:00 GMT';
        config.headers['Cache-Control'] = 'no-cache';
        config.headers['Pragma'] = 'no-cache';
    }
    if (config.needLoading === undefined) {
        config.needLoading = true
    }
    if (config.needLoading) {
        startLoading();
    }
    return config;
}, error => {
    //请求失败时，在这里做点什
    return Promise.reject(error);
});

let toLoginCount = 0;

// response 响应拦截器
service.interceptors.response.use(response => {
    // 显示loading
    if (response.config.needLoading) {
        stopLoading();
    }
    if (response.status !== 200) {
        return Promise.reject(response);
    }
    let resData = response.data;
    // 是否显示错误信息提示，默认 true
    let showErrorTip = response.config.showErrorTip !== undefined ? response.config.showErrorTip : true;
    // 是否显示正确信息提示，默认 false
    let showSuccessTip = response.config.showSuccessTip !== undefined ? response.config.showSuccessTip : false;
    if (resData.code !== 200) {
        // 拦截未登录请求
        if (resData.code === 401) {
            MessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
                confirmButtonText: '重新登录',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                store.dispatch('LogOut').then(() => {
                    location.href = '/index';
                }).catch(() => {})
            }).catch(() => {})
            return Promise.reject(resData);
        }

        // 需要显示错误信息提示时
        if (showErrorTip) {
            Notification.closeAll();
            Notification({
                title: '错误',
                dangerouslyUseHTMLString: true,
                message: '错误信息：' + resData.msg,
                type: 'error',
                duration: 3000
            });
        }
        return Promise.reject(resData);
    }
    // 需要显示成功信息提示时
    if (showSuccessTip) {
        Notification.closeAll();
        Notification({
            title: '成功',
            message: '操作成功',
            type: 'success',
            duration: 3000
        });
    }
    return Promise.resolve(resData);
}, error => {
    if (error.config.needLoading) {
        stopLoading();
    }
    let errRes = error.response;
    let normalRrrMsg = '出错了，请联系管理员';
    let errorMsg = {
        400: '错误请求',
        403: '无权限访问',
        404: '接口无法访问，请检查URL是否正确',
        500: '服务器内部错误'
    };
    Notification({
        title: '错误',
        dangerouslyUseHTMLString: true,
        message: errorMsg[errRes.status] || normalRrrMsg,
        type: 'error',
        duration: 3000
    });
    return Promise.reject(errRes);
});

let loading;
// 有loading的请求个数
let loadingRequestCount = 0;
// 延迟300ms显示loading，防止响应速度过快出现闪屏现象
let loadingTime;

//显示Loading
const startLoading = () => {
    if (loadingRequestCount === 0) {
        loadingTime = setTimeout(() => {
            loading = Loading.service({
                lock: true,
                text: '加载中，请稍候……',
                background: 'rgba(0, 0, 0, 0.7)'
            });
        }, 500);
    }
    loadingRequestCount++;
};

//隐藏Loading
const stopLoading = () => {
    if (loadingRequestCount <= 0) return;
    loadingRequestCount--;
    if (loadingTime) {
        clearTimeout(loadingTime);
        loadingTime = null;
    }
    if (loadingRequestCount === 0 && loading) {
        loading.close();
    }
};

export default service;