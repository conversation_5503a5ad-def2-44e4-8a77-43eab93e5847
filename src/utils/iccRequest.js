import axios from 'axios'

const ICCTOKEN = 'icc_token'
const getICCToken = () => localStorage.getItem(ICCTOKEN)
const setICCToken = (token) => localStorage.setItem(ICCTOKEN, token)

const fetchNewToken = async () => {
  try {
    // 获取token
    
    const res = await axios.get('/api/token')
    const { token } = res.data
    setICCToken(token)
  } catch (error) {

  }
}

let isRefreshing = false
let failedQueue = []

const axiosInstance = axios.create({
  baseURL: '/icc',
  timeout: 10000
})

axiosInstance.interceptors.request.use(
  (config) => {
    const token = getICCToken()
    if(token) {
      config.headers['Authorization'] = `bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

axiosInstance.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const { config, response } = error
    // 如果是 401 错误并且没有正在刷新 token
    if(response && response.status === 401 && !isRefreshing) {
      isRefreshing = true
      try {
        const newToken = await fetchNewToken()
        // 更新 token
        setICCToken(newToken)

        // 重新请求失败的请求
        failedQueue.forEach((req) => req())
        failedQueue = []

        // 将原来的请求重新发起
        config.headers['Authorization'] = `bearer ${newToken}`
        return axiosInstance(config)

      } catch (err) {
        return  Promise.reject(err)
      } finally {
        isRefreshing = false
      }
    }

     // 如果是 401 错误并且已经在刷新 token，则将请求加入队列，等待 token 刷新完成
    if(response && response.status === 401 && isRefreshing) {
      return new Promise((resolve, reject) => {
        failedQueue.push(() => {
          config.headers['Authorization'] = `Bearer ${getICCToken()}`
          resolve(axiosInstance(config))
        })
      })
    }

    // const originalRequest = error.config
    // if(error.response && error.response.status === 401 && !originalRequest._retry) {
    //   if(isRefreshing) {
    //     return new Promise((resolve, reject) => {
    //       failedQueue.push({ resolve, reject })
    //     }).then((accessToken) => {
    //       originalRequest.headers['Authorization'] = `bearer ${accessToken}`
    //       return axiosInstance(originalRequest)
    //     }).catch((err) => Promise.reject(err))
    //   }

    //   originalRequest._retry = true
    //   isRefreshing = true

    //   try {
    //     const newToken = await fetchNewToken()

    //     axiosInstance.defaults.headers['Authorization'] = `bearer ${accessToken}`

    //     failedQueue.forEach((req) => req.resolve(newToken))
    //     failedQueue = []

    //     return axiosInstance(originalRequest)
    //   } catch (error) {
        

    //   } finally {
    //     isRefreshing = false
    //   }

    // }

    return Promise.reject(error)
  }
)