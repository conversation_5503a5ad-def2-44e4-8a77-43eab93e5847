/*
 * @Author: your name
 * @Date: 2020-10-13 14:01:27
 * @LastEditTime: 2020-10-13 14:02:22
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \vue-demo\src\utils\filters\index.js
 */
import {FILTER_OBJ} from "./filterVariable";

export function globalFilter(value, filter, format) {
    if (value === null || value === undefined) {
        return (filter ? '-' : value);
    }
    if (filter) {
        if (format) {
            return eval(`${filter}(${JSON.stringify(value)},${JSON.stringify(format)})`);
        } else {
            return (FILTER_OBJ[filter] && FILTER_OBJ[filter]) ? FILTER_OBJ[filter][value] : value;
        }
    } else {
        return value;
    }
}

function addZero(val) {
    if (val < 10) {
        return "0" + val;
    } else {
        return val;
    }
}

export function timeFilter(timestamp, format) {
    timestamp = Number(timestamp);
    const dateTime = new Date(timestamp);
    const year = dateTime.getFullYear();
    const month = addZero(dateTime.getMonth() + 1);
    const day = addZero(dateTime.getDate());
    const hour = addZero(dateTime.getHours());
    const minute = addZero(dateTime.getMinutes());
    const second = addZero(dateTime.getSeconds());
    const formatMap = {
        'yyyy': 0,
        'yyyy-MM': 1,
        'yyyy-MM-dd': 2,
        'HH:mm': 3,
        'MM-dd HH:mm': 4,
        'yyyy-MM-dd HH:mm': 5,
        'yyyy-MM-dd HH:mm:ss': 6,
        'yyyy/MM/dd': 7,
        'MM-dd': 8
    };
    const formatType = formatMap[format];
    let result = '';
    switch (formatType) {
        case 0:
            result = `${year}`;
            break;
        case 1:
            result = `${year}-${month}`;
            break;
        case 2:
            result = `${year}-${month}-${day}`;
            break;
        case 3:
            result = `${hour}:${minute}`;
            break;
        case 4:
            result = `${month}-${day} ${hour}:${minute}`;
            break;
        case 5:
            result = `${year}-${month}-${day} ${hour}:${minute}`;
            break;
        case 6:
            result = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
            break;
        case 7:
            result = `${year}年${month}月${day}日`;
            break;
        case 8:
            result = `${month}-${day}`;
            break;
        default:
            result = `${year}-${month}-${day}`;
            break;
    }
    return result;
}

export function formatInt(num) {
    return num && num.toString().replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','));
}