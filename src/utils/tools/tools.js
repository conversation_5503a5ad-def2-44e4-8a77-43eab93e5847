// 设置精确到几位小数
export function fnDecimalPlaces(val, places) {
    let m = Math.pow(10, places);
    return Math.floor(val * m) / m;
}

// 转换字符串为数组
export function fnStrToArr(data) {
    if (!data) {
        return [];
    }
    return data.trim().split(/\s+/);
}

// 获取XXX分钟后的时间
export function fnMinutesLater(time, minutes) {
    let curTime = new Date(time);
    return curTime.setMinutes(curTime.getMinutes() + minutes, 0, 0);
}

// 获取XXX天前的时间
export function fnDaysLater(days, curDate) {
    let curTime = new Date().getTime();
    let startDate = curTime - (days * 3600 * 24 * 1000);
    return new Date(startDate);
}

/**
 * 转换对象为列表
 * 用于转换 枚举对象 为 下拉选项
 **/
export function fnMapperToArray(data, numberType) {
    if (!data) {
        return [];
    }
    const list = [];
    Object.keys(data).forEach((key) => {
        let val = numberType ? Number(key) : key;
        if (val === "true") {
            val = true
        }
        if (val === "false") {
            val = false
        }
        list.push({
            value: val,
            label: data[key]
        });
    });
    return list;
}

/**
 * 设置查询条件
 * 过滤 空值
 **/
export function fnSetCondition(conditionTemp) {
    const condition = Object.assign({}, conditionTemp);
    for (let key in condition) {
        if (!condition.hasOwnProperty(key)) {
            return;
        }
        if (typeof condition[key] === 'string') {
            condition[key] = condition[key].trim();
        }
        if (condition[key] === null || condition[key] === "" || condition[key] === undefined) {
            delete condition[key];
        }
    }
    return condition;
}

/**
 * 获取列表选中行的KEY集合
 **/
export function fnGetKeyList(list, key) {
    let keyList = [];
    list.forEach(item => {
        item[key] && keyList.push(item[key]);
    });
    return keyList;
}

/**
 * 验证是否是JSON串
 **/
export function fnIsJsonStr(str) {
    try {
        let obj = JSON.parse(str);
        return typeof obj === 'object'
    } catch (e) {
        return false;
    }
}

// 保留几位小数
export function fnKeepDecimal(val, places) {
    let f = Math.round(val * Math.pow(10, places)) / Math.pow(10, places);
    let s = f.toString();
    let rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + places) {
        s += '0';
    }
    return s;
}

// 设置文件表单
export function getFileFormData(file) {
    let fd = new FormData();
    fd.append(file.name, file);
    return fd;
}