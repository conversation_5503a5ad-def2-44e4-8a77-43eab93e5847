// 加
let fnPlus = (val1, val2) => {
    let r1 = 0, r2 = 0, m;
    try {
        r1 = val1.toString().split(".")[1].length
    } catch (e) {
        r1 = 0
    }
    try {
        r2 = val2.toString().split(".")[1].length
    } catch (e) {
        r2 = 0
    }
    m = Math.pow(10, Math.max(r1, r2));
    return (fnTimes(val1, m) + fnTimes(val2, m)) / m
};

// 减
let fnMinus = (val1, val2) => {
    let r1, r2, m;
    try {
        r1 = val1.toString().split(".")[1].length;
    } catch (e) {
        r1 = 0
    }
    try {
        r2 = val2.toString().split(".")[1].length;
    } catch (e) {
        r2 = 0
    }
    m = Math.pow(10, Math.max(r1, r2));
    return (fnTimes(val1, m) - fnTimes(val2, m)) / m;
};

// 乘
let fnTimes = (arg1, arg2) => {
    let m = 0, s1 = arg1.toString(), s2 = arg2.toString();
    try {
        m += s1.split(".")[1].length
    } catch (e) {
        // console.log(e);
    }
    try {
        m += s2.split(".")[1].length
    } catch (e) {
        // console.log(e);
    }
    return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m)
};

// 除
let fnDivide = (arg1, arg2) => {
    let t1 = 0, t2 = 0, r1, r2;
    try {
        t1 = arg1.toString().split(".")[1].length
    } catch (e) {
        // console.log(e);
    }
    try {
        t2 = arg2.toString().split(".")[1].length
    } catch (e) {
        // console.log(e);
    }
    r1 = Number(arg1.toString().replace(".", ""));
    r2 = Number(arg2.toString().replace(".", ""));
    return fnTimes((r1 / r2), Math.pow(10, t2 - t1));
};

export {
    fnPlus,
    fnMinus,
    fnTimes,
    fnDivide
} 