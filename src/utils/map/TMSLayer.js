import BaseTileLayer from '@arcgis/core/layers/BaseTileLayer'

let TMSLayer = BaseTileLayer.createSubclass({
  properties: {
    urlTemplate: null
  },
  
  getTileUrl: function(level, col, row) {
    // 这里对y轴瓦片编号进行转置
    let col2 = Math.pow(2, level) - col - 1;
    return this.urlTemplate.replace("{z}", level).replace("{x}", row).replace("{y}", col2)
  }
})

export default JLImageLayer = BaseTileLayer.createSubclass({
  properties: {
    urlTemplate: null
  },

  getTileUrl: function(z, y, x) {
    return this.urlTemplate.replace("{z}", z).replace("{x}", x).replace("{y}", y)
  }
})