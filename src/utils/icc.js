import axios from 'axios'

function fetchNewToken() {
  console.log('重新请求 token')
  return axios.get('/getIccToken')
}
const ICCTOKEN = 'icc_token'
const getICCToken = () => localStorage.getItem(ICCTOKEN)
const setICCToken = (token) => localStorage.setItem(ICCTOKEN, token)

const axiosInstance = axios.create({
  baseURL: '/icc',
  timeout: 10000
})

axiosInstance.interceptors.request.use(
  (config) => {
    const token = getICCToken()
    if(token) {
      config.headers['Authorization'] = `bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

axiosInstance.interceptors.response.use(
  async (res) => {
    // if(res.data.code === 401) {
    //   const token = await fetchNewToken()
    //   setICCToken(token)

    //   console.log('重新请求')
    //   res.config.headers['Authorization'] =  `bearer ${token}`
    //   const resp = await axiosInstance.request(res.config)
    //   return resp
      
    // }
    return res.data
  },
  async error => {
    if(error.response && error.response.status === 401) {

      try {
        const newToken = await fetchNewToken()
        setICCToken(newToken)

        error.config.headers['Authorization'] = `bearer ${newToken}`

        return axiosInstance(error.config)
      } catch (error) {
        console.log('获取新 token 失败')
      }
    }
  }
)

export default axiosInstance