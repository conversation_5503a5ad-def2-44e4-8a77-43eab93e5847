import axios from 'axios'

export function handleDownload(filename, res) {
  let elink = document.createElement('a')
  elink.download = filename
  elink.style.display = 'none'
  let blob = new Blob([res])
  const href = URL.createObjectURL(blob)
  elink.href = href
  document.body.appendChild(elink)
  elink.click()
  URL.revokeObjectURL(href)
  document.body.removeChild(elink)
}

// 去除文件后缀名
export function removeFileSuffix(url) {
  const index = url.lastIndexOf('.')
  return url.slice(0, index)
}

// 根据文件url获取blob
export function getBlobByUrl(url) {
  return axios({
    method: 'get',
    url: url,
    responseType: 'blob'
  })
}