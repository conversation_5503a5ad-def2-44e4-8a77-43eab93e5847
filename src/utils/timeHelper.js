import { parseTime } from './index'

// 获取近 day 天开始和结束时间
export function getDaysDate(day = 7) {
  const now = new Date()
  const start = now.getTime() - 60 * 60 * 24 * day * 1000
  return [parseTime(start, '{y}-{m}-{d}'), parseTime(now, '{y}-{m}-{d}')]
}

// 开始时间为 00:00:00 结束时间为 23:59:59
export function getDaysDateWithDefault(day = 7) {
  const now = new Date()
  const start = now.getTime()  - 60 * 60 * 24 * day * 1000
  return [parseTime(start, '{y}-{m}-{d}') + ' 00:00:00', parseTime(now, '{y}-{m}-{d}') + ' 23:59:59']
}