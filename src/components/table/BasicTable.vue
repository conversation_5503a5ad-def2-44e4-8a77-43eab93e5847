<template>
    <div>
        <el-table :data="data" ref="baseTable"
                  border
                  highlight-current-row
                  v-loading="loading"
                  tooltip-effect="dark"
                  element-loading-text="加载中..."
                  element-loading-spinner="el-icon-loading"
                  @selection-change="selectionChange"
                  @current-change="selectedRowChange"
                  :row-class-name="rowClassName"
                  :height="tBodyHeight"
                  :class="{'tb-Scroll': tableScrolling}"
                  style="width: 100%">
            <!-- 选择 -->
            <el-table-column v-if="showSelection" type="selection" width="45"
                             class-name="text-center" fixed="left"></el-table-column>
            <!-- 序号 -->
            <el-table-column v-if="showIndex" type="index" label="序号" width="60" fixed="left"></el-table-column>
            <!-- 数据源 -->
            <template v-for="(column, index) in columns">
                <!-- 操作列 type：'operate' -->
                <!-- power 是否有权限 true：有权限 false: 无权限 -->
                <!-- limitProp 限制是否可操作的自定义字段（获取数据时处理） 字段对应的值 true：限制 false：不限制 -->
                <el-table-column
                        :key="index"
                        :label="column.label"
                        :fixed="column.fixed"
                        :class-name="column.className ? column.className : ''"
                        :width="column.width ? column.width : ''"
                        :min-width="column.minWidth ? column.minWidth : ''"
                        v-if="column.show !== false && column.type==='operate'">
                    <template slot-scope="scope">
                        <template v-for="(item, btnIndex) in handleColumn">
                            <span class="tb-row-btn" :key="btnIndex"
                                  v-if="setBtnShown(scope.row, item.hideProp, item.hideValue)">
                                <el-link :type="item.btnType || 'primary'" :underline="false"
                                         :disabled="setBtnDisabled(scope.row, item.power, item.disableProp, item.disableValue)"
                                         @click.stop="handleEvent(item.type, scope.row, scope.$index, $event, item)"
                                         :title="item.label">
                                    <i v-if="item.icon" :class="'icoFont ico-' + item.icon"></i>
                                    {{item.label}}
                                </el-link>
                                <span class="row-btn-interval"></span>
                            </span>
                        </template>
                    </template>
                </el-table-column>
                <!-- 链接列 type：'link' -->
                <el-table-column
                        :key="index"
                        :label="column.label"
                        :fixed="column.fixed"
                        show-overflow-tooltip
                        :class-name="column.className ? column.className : ''"
                        :width="column.width ? column.width : ''"
                        :min-width="column.minWidth ? column.minWidth : ''"
                        v-else-if="column.show !== false && column.type ==='link'">
                    <template slot-scope="scope">
                        <el-link type="primary" class="td-link" :underline="false" :title="column.title"
                                 @click.stop="handleLink(column.fnType, scope.row, scope.$index)">
                            {{scope.row[column.field] | globalFilter(column.prop, column.format)}}
                        </el-link>
                    </template>
                </el-table-column>
                <!-- 外部链接列 type：'outLink' -->
                <el-table-column
                        :key="index"
                        :label="column.label"
                        :fixed="column.fixed"
                        show-overflow-tooltip
                        :class-name="column.className ? column.className : ''"
                        :width="column.width ? column.width : ''"
                        :min-width="column.minWidth ? column.minWidth : ''"
                        v-else-if="column.show !== false && column.type ==='outLink'">
                    <template slot-scope="scope">
                        <a class="out-link" :title="column.title" :href="scope.row.linkUrl" target="_blank"
                           v-if="scope.row.linkUrl">
                            {{scope.row[column.field] | globalFilter(column.prop, column.format)}}
                        </a>
                        <span v-else>
                            {{scope.row[column.field] | globalFilter(column.prop, column.format)}}
                        </span>
                    </template>
                </el-table-column>
                <!-- 排序列 type：'sort' -->
                <el-table-column
                        :key="index"
                        :label="column.label"
                        :fixed="column.fixed"
                        show-overflow-tooltip
                        :class-name="column.className ? column.className : ''"
                        :width="column.width ? column.width : ''"
                        :min-width="column.minWidth ? column.minWidth : ''"
                        v-else-if="column.show !== false && column.type ==='sort'">
                    <template slot-scope="scope">
                        <el-link type="primary" class="td-link m-r-5" :underline="false" title="上移"
                                 :disabled="scope.$index === 0 || (column.power !== undefined ? !$store.state.actionAuth.actionAuth[column.power] : false)"
                                 @click.stop="handleSort(column.fnType, scope.row, 'up', scope.$index)">
                            <i class="ico-font ico-arrow-to-up"></i>
                        </el-link>
                        <el-link type="primary" class="td-link" :underline="false" title="下移"
                                 :disabled="scope.$index === (data.length - 1) || (column.power !== undefined ? !$store.state.actionAuth.actionAuth[column.power] : false)"
                                 @click.stop="handleSort(column.fnType, scope.row, 'down',  scope.$index)">
                            <i class="ico-font ico-arrow-to-down"></i>
                        </el-link>
                    </template>
                </el-table-column>
                <!-- 状态列 type：'state' 需要根据样式模板设计样式 -->
                <el-table-column
                        :key="index"
                        :label="column.label"
                        :fixed="column.fixed"
                        :class-name="column.className ? column.className : ''"
                        :width="column.width ? column.width : ''"
                        :min-width="column.minWidth ? column.minWidth : ''"
                        v-else-if="column.show !== false && column.type ==='state'">
                    <template slot-scope="scope">
                        <span class="tb-row-state" :class="{'success': scope.row[column.field] === column.successVal,
                        'disabled': scope.row[column.field] === column.disabledVal,
                        'error': scope.row[column.field] === column.errorVal}">
                            {{scope.row[column.field] | globalFilter(column.prop, column.format)}}
                        </span>
                    </template>
                </el-table-column>
                <!-- 开关列 type：'switch' 点击切换状态 -->
                <el-table-column
                        :key="index"
                        :label="column.label"
                        :fixed="column.fixed"
                        :class-name="column.className ? column.className : ''"
                        :width="column.width ? column.width : ''"
                        :min-width="column.minWidth ? column.minWidth : ''"
                        v-else-if="column.show !== false && column.type ==='switch'">
                    <template slot-scope="scope">
                        <el-tooltip :content="scope.row[column.field] | globalFilter(column.prop, column.format)"
                                    placement="top">
                            <div class="switch-wrap">
                                <el-switch v-model="scope.row[column.field]"
                                           active-color="#007cff"
                                           inactive-color="#ccc"
                                           :disabled="column.power !== undefined ? !$store.state.actionAuth.actionAuth[column.power] : false">
                                </el-switch>
                                <div class="switch-btn"
                                     @click.stop="handleEvent(column.fnType, scope.row, scope.$index, $event, column)"
                                     v-show="column.power !== undefined ? $store.state.actionAuth.actionAuth[column.power] : true"></div>
                            </div>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <!-- 图片 type：'image' 显示图片 -->
                <el-table-column
                        :key="index"
                        :label="column.label"
                        :fixed="column.fixed"
                        :class-name="column.className ? column.className : ''"
                        :width="column.width ? column.width : ''"
                        :min-width="column.minWidth ? column.minWidth : ''"
                        v-else-if="column.show !== false && column.type ==='image'">
                    <template slot-scope="scope">
                        <img class="td-img" v-show="scope.row[column.field]" :src="scope.row[column.field]"/>
                    </template>
                </el-table-column>
                <!-- 文字列 -->
                <el-table-column
                        :key="index"
                        :label="column.label"
                        :fixed="column.fixed"
                        show-overflow-tooltip
                        :class-name="column.className ? column.className : ''"
                        :width="column.width ? column.width : ''"
                        :min-width="column.minWidth ? column.minWidth : ''"
                        v-else-if="column.show !== false">
                    <template slot-scope="scope">
                        {{scope.row[column.field] | globalFilter(column.prop, column.format)}}
                    </template>
                </el-table-column>
            </template>
        </el-table>

        <div class="clear-fix">
            <el-pagination style="margin-top: 10px;"
                           v-if="showPagination"
                           class="pull-right"
                           name="fenYe"
                           background
                           @size-change="pageSizeChange"
                           @current-change="pageNoChange"
                           :page-sizes="pageSizes"
                           layout="total, prev, pager, next"
                           :total='totalCount'
                           :page-size="pageSize"
                           :current-page="pageNo">
            </el-pagination>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'hawk-basic-table',
        props: {
            // 表格名称
            listName: {
                type: String,
                default: ''
            },
            // 列表数据
            data: {
                type: Array,
                default: () => {
                }
            },
            // 显示加载中
            loading: {
                type: Boolean,
                default: false
            },
            // 表格列 现三种类型[默认：不设置type，链接：type='link'，操作：type='handle']
            columns: {
                type: Array,
                default: () => []
            },
            // 静态表格操作列 - 需要表格列配置时使用
            staticColumns: {
                type: Array,
                default: () => []
            },
            // 表格操作列
            handleColumn: {
                type: Array,
                default: () => []
            },
            // 显示选择列
            showSelection: {
                type: Boolean,
                default: true
            },
            // 显示索引列
            showIndex: {
                type: Boolean,
                default: true
            },
            // 显示分页
            showPagination: {
                type: Boolean,
                default: false
            },
            // 分页大小列表
            pageSizes: {
                type: Array,
                default: () => [10, 20, 30, 50]
            },
            // 当前页码
            pageNo: {
                type: Number,
                default: 1
            },
            // 当前分页大小
            pageSize: {
                type: Number,
                default: 10
            },
            // 数据总数
            totalCount: {
                type: Number,
                default: 0
            },
            // 是否有标签栏
            hasTabBar: {
                type: Boolean,
                default: false
            }
        },
        data() {
            return {
                selectedIndex: [],
                tBodyHeight: 0,
                tableScrolling: true
            }
        },
        methods: {
            // 多选高亮选中行
            rowClassName({row, rowIndex}) {
                if (this.selectedIndex.indexOf(rowIndex) > -1) {
                    return 'checked'
                }
            },
            // 选中项改变
            selectionChange(val) {
                this.selectedIndex = [];
                val.forEach((item, index) => {
                    this.selectedIndex.push(this.data.indexOf(item));
                });
                this.$emit('handleSelectionChange', val);
            },
            // 选中行改变
            selectedRowChange(val) {
                this.$emit('handleSelectedRowChange', val);
            },
            // 当前页码改变
            pageNoChange(val) {
                let pageInfo = {
                    pageNo: val,
                    pageSize: this.pageSize
                };
                this.$emit('handlePageChange', pageInfo);
            },
            // 分页大小改变
            pageSizeChange(val) {
                let pageInfo = {
                    pageNo: this.pageNo,
                    pageSize: val
                };
                this.$emit('handlePageChange', pageInfo);
            },
            // 判断是否隐藏按钮
            setBtnShown(dataRow, prop, value) {
                // 未设置按钮失效条件，或者失效条件是空
                if (prop === undefined || prop.length === 0) {
                    return true;
                }
                let btnDisable = true;  // 按钮是否失效
                for (let i = 0; i < prop.length; i++) {
                    let hideValue = value[i];
                    if (!Array.isArray(hideValue)) {
                        // 隐藏字段对应的值不是数组
                        if (dataRow[prop[i]] === value[i]) {
                            btnDisable = false;
                            break;
                        }
                    } else {
                        // 隐藏字段对应的值是数组
                        for (let j = 0; j < hideValue.length; j++) {
                            if (dataRow[prop[i]] === hideValue[j]) {
                                btnDisable = false;
                                break;
                            }
                        }
                    }
                }
                return btnDisable;
            },
            // 判断是否使按钮失效；返回值 true：按钮失效、false：按钮有效
            setBtnDisabled(dataRow, power, prop, value) {
                // 先判断按钮是否有权限
                if (power && !this.$store.state.actionAuth.actionAuth[power]) {
                    return true;
                }
                // 未设置按钮失效条件，或者失效条件是空
                if (prop === undefined || prop.length === 0) {
                    return false;
                }
                let btnDisable = false;  // 按钮是否失效
                for (let i = 0; i < prop.length; i++) {
                    let disableValue = value[i];
                    if (!Array.isArray(disableValue)) {
                        // 隐藏字段对应的值不是数组
                        if (dataRow[prop[i]] === value[i]) {
                            btnDisable = true;
                            break;
                        }
                    } else {
                        // 隐藏字段对应的值是数组
                        for (let j = 0; j < disableValue.length; j++) {
                            if (dataRow[prop[i]] === disableValue[j]) {
                                btnDisable = true;
                                break;
                            }
                        }
                    }
                }
                return btnDisable;
            },
            // 表格操作列操作函数
            handleEvent(type, row, index, e, item) {
                this.$emit(`handle${type}`, row, index, item);
            },
            // 表格链接列操作函数
            handleLink(fnType, row, index) {
                this.$emit(`handle${fnType}`, row, index);
            },
            // 排序
            handleSort(fnType, row, direction, index) {
                this.$emit(`handleSort`, row, direction, index);
            },
            setTBodyHeight() {
                // todo 动态获取父组件搜索栏高度，然后根据浏览器高度设置列表滚动区域高度
                let winHeight = document.body.offsetHeight;
                // 顶栏 + 主体部分上下间隔(固定，根据设计效果定)
                let topBottomInterval = 125;
                // 搜索栏高度
                let searchBarHeight = this.$parent.$refs['searchBar'] ? this.$parent.$refs['searchBar'].offsetHeight : 0;
                // 操作按钮栏高度
                let operateBarHeight = this.$parent.$refs['operateBar'] ? this.$parent.$refs['operateBar'].offsetHeight : (searchBarHeight === 0 ? 0 : 15);
                // 分页栏高度
                let pageHeight = this.showPagination ? 47 : 0;
                // 标签栏高度 带入参数 提示是否有标签栏
                let tabBarHeight = this.hasTabBar ? 50 : 0;
                // 待删除的高度
                let toSubtractHeight = topBottomInterval + searchBarHeight + operateBarHeight + pageHeight + tabBarHeight;
                let tbHeight = winHeight - toSubtractHeight;
                this.$nextTick(() => {
                    this.tBodyHeight = tbHeight;
                    let tableBodyClass = this.$refs['baseTable'].bodyWrapper.className;
                    this.tableScrolling = tableBodyClass.indexOf('is-scrolling-none') === -1;
                });
            }
        },
        mounted() {
            this.setTBodyHeight();
            window.addEventListener('resize', this.setTBodyHeight);
        },
        destroyed() {
            window.removeEventListener('resize', this.setTBodyHeight)
        }
    }
</script>

<style lang="scss" scoped>
    .td-img {
        max-width: 100px;
        max-height: 50px;
        display: block;
    }

    .td-link {
        display: inline;
    }

    .out-link {
        color: #007cff;
        text-decoration: none;
    }

    .tb-row-btn {
        .row-btn-interval {
            display: inline-block;
            width: 1px;
            height: 8px;
            background: #d9dce2;
            vertical-align: middle;
            margin: 0 8px;
        }
        &:last-child .row-btn-interval {
            display: none;
        }
    }

    .switch-wrap {
        position: relative;
        display: inline-block;
        outline: none;
        .switch-btn {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            cursor: pointer;
        }
    }
</style>