/*
 * @Author: your name
 * @Date: 2020-10-13 09:22:51
 * @LastEditTime: 2020-10-13 10:08:18
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \vue-demo\src\components\index.js
 */
import Vue from 'vue';

// import LoginModel from './login/LoginModel.vue';
import BasicTable from './table/BasicTable.vue';


const install = (Vue)=>{
    // 在install方法里注册全局组件
    // Vue.component(LoginModel.name,LoginModel) // 我们可以把名字在组件文件里用name定义好,这样就取的是组件文件的名字
    Vue.component(BasicTable.name,BasicTable)
}

if (typeof Vue !== 'undefined') {
    install(Vue)
}

export default {
    install
}

