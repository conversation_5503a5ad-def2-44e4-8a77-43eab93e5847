<template>
  <div class="demo">
    <div class="map" id="viewDiv"></div>
  </div>
</template>

<script>
import Map from '@arcgis/core/Map'
import MapView from '@arcgis/core/views/MapView'
import Ground from '@arcgis/core/Ground'
import WMTSLayer from '@arcgis/core/layers/WMTSLayer'
import WebTileLayer from '@arcgis/core/layers/WebTileLayer'

export default {
  name: 'BaseMap',
  data() {
    return {
      map: null,
      view: null
    }
  },
  mounted() {
    this.createMap()
  },
  methods: {
    createMap() {
      this.map = new Map({
        ground: new Ground({
          layers: [],
          surfaceColor: "transparent",
          opacity: 0,
        }),
      })
      this.view = new MapView({
        container: 'viewDiv',
        map: this.map,
        center: [110.80, 31.2],
        zoom: 10
      })
      const tdtBase = new WebTileLayer({
        urlTemplate: 'http://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=9fdd5478fbb1d40ef3ebe83882c0fda6',
      })
      
      this.view.map.add(tdtBase)
    }
  }
}
</script>

<style lang="scss" scoped>
.demo {
  height: calc(100vh - 84px);
  .map {
    height: 100%;
  }
}
</style>