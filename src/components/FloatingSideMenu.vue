<template>
  <div class="floating-side-menu">
    <!-- 浮动切换按钮（当菜单隐藏时显示） -->
    <div 
      class="floating-toggle" 
      v-show="isCollapsed"
      @click="toggleMenu"
    >
      <i class="el-icon-menu"></i>
    </div>

    <!-- 侧边栏菜单（完全隐藏或完全显示） -->
    <div 
      class="side-menu" 
      :class="{ hidden: isCollapsed, visible: !isCollapsed }"
      v-show="!isCollapsed"
    >
      <!-- 菜单内部的切换按钮 -->
      <div class="internal-toggle-btn" @click="toggleMenu">
        <i class="el-icon-close"></i>
      </div>

      <!-- 模块标题 -->
      <div class="module-title">
        <h3>{{ moduleTitle }}</h3>
      </div>

      <!-- 菜单列表 -->
      <div class="menu-list">
        <div 
          v-for="item in menuItems" 
          :key="item.id"
          class="menu-item"
          :class="{ active: item.name === $route.name }"
          @click="selectMenu(item)"
        >
          <span class="menu-label">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FloatingSideMenu',
  props: {
    moduleType: {
      type: String,
      required: true,
      validator: value => ['dispatch', 'wq', 'leakage', 'pipenetwork'].includes(value)
    },
    show: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isCollapsed: true
    }
  },
  computed: {
    moduleTitle() {
      const titleMap = {
        'dispatch': '供水调度',
        'wq': '水质监测',
        'leakage': '管网漏损',
        'pipenetwork': '管网GIS'
      }
      return titleMap[this.moduleType] || '系统菜单'
    },
    menuItems() {
      // 根据模块类型返回对应的菜单项
      const menuMap = {
        'dispatch': [
          { id: 5, name: 'OverviewPage', label: '调度总览' },
          { id: 1, name: 'RealTimeMonitor', label: '运行一张图' },
          { id: 2, name: 'Warn', label: '风险预警' },
          { id: 4, name: 'StroeRegulate', label: '错峰调度' },
          { id: 7, name: 'RiskManage', label: '风险管控' },
          { id: 3, name: 'RiskProfile', label: '风险预测' }
        ],
        'wq': [
          { id: 5, name: 'WqMonitor', label: '实时数据' },
          { id: 1, name: 'WqAnalyze', label: '数据分析' },
          { id: 2, name: 'WqWarn', label: '水质预警' },
          { id: 3, name: 'WqReport', label: '水质报告' },
          { id: 4, name: 'WqProgramme', label: '应急方案' }
        ],
        'leakage': [
          { id: 2, name: 'Subarea', label: 'DMA概览' },
          { id: 3, name: 'Special', label: '专题分析' },
          { id: 4, name: 'RealTime', label: '实时数据' },
          { id: 5, name: 'NightMinFlow', label: '夜间最小流量' },
          { id: 6, name: 'PressureArea', label: '压力分布' },
          { id: 10, name: 'DispatchPage', label: '调度管理' },
          { id: 11, name: 'PerformanceScore', label: '绩效管理' },
          { id: 12, name: 'Platform', label: '平台管理' }
        ],
        'pipenetwork': [
          { id: 1, value: 1, name: 'Business', label: '查询统计' },
          { id: 2, value: 2, name: 'Business', label: '管网分析' },
          { id: 3, value: 3, name: 'Business', label: '专题展示' },
          { id: 4, value: 4, name: 'Business', label: '管网编辑' }
        ]
      }
      return menuMap[this.moduleType] || []
    }
  },
  methods: {
    toggleMenu() {
      this.isCollapsed = !this.isCollapsed
    },
    selectMenu(item) {
      // 对于管网GIS特殊处理
      if (this.moduleType === 'pipenetwork') {
        this.$router.push({ name: 'Business', query: { type: item.value } })
      } else {
        this.$router.push({ name: item.name })
      }
      // 跳转后收起菜单
      this.isCollapsed = true
    }
  }
}
</script>

<style lang="scss" scoped>
.floating-side-menu {
  pointer-events: none; // 允许点击穿透到后面的内容
  
  > * {
    pointer-events: auto; // 恢复子元素的点击事件
  }
}

// 浮动按钮样式
.floating-toggle {
  position: fixed;
  left: 20px;
  top: 20px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, rgba(4, 15, 45, 0.9) 0%, rgba(2, 50, 128, 0.9) 100%);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(23, 110, 217, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2000;

  &:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    background: linear-gradient(135deg, rgba(19, 130, 230, 0.9) 0%, rgba(2, 50, 128, 0.9) 100%);
  }

  i {
    font-size: 20px;
    color: #fff;
  }
}

// 侧边栏样式
.side-menu {
  position: fixed;
  left: 0;
  top: 0;
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, rgba(4, 15, 45, 0.95) 0%, rgba(2, 50, 128, 0.95) 100%);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(23, 110, 217, 0.3);
  z-index: 1500;
  color: #fff;
  overflow: hidden;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.visible {
    transform: translateX(0);
  }

  &.hidden {
    transform: translateX(-100%);
  }

  .internal-toggle-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.1);
    }

    i {
      font-size: 18px;
      color: #fff;
    }
  }

  // 菜单内容区域
  padding: 80px 0 20px;
  height: 100%;
  overflow-y: auto;

  .module-title {
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: bold;
      color: #fff;
      text-align: center;
    }
  }

  .menu-list {
    .menu-item {
      padding: 16px 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      border-left: 3px solid transparent;
      position: relative;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-left-color: #1382E6;
      }

      &.active {
        background: linear-gradient(90deg, rgba(19, 130, 230, 0.3) 0%, rgba(19, 130, 230, 0.1) 100%);
        border-left-color: #1382E6;

        &::before {
          content: '';
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translateY(-50%);
          width: 8px;
          height: 8px;
          background: #1382E6;
          border-radius: 50%;
        }
      }

      .menu-label {
        font-size: 15px;
        color: #fff;
        display: block;
        font-weight: 500;
      }
    }
  }

  // 滚动条样式
  ::-webkit-scrollbar {
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.side-menu.visible {
  animation: slideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  .module-title,
  .menu-list {
    animation: fadeIn 0.4s ease 0.1s both;
  }
}
</style>