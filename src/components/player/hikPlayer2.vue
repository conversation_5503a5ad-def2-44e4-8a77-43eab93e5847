<template>
  <div class="webPlayerBox" ref="webPlayerBox">
    <div id="playWnd" class="playWnd"></div>
  </div>
</template>

<script>
import { parseTime } from '@/utils'

export default {
  name: 'WebPlayer',
  props: {
    cameraIndexCode: {
      require: true,
      type: String
    },
    playMode: {
      type: Number,
      // 0 预览 1 回放
      default: 0
    },
    playBackTime: {
      type: Object,
      default: () => {
        const now = new Date()
        const date = parseTime(now, '{y}-{m}-{d}')
        const startTs = new Date(date + ' 00:00:00').getTime()
        const endTs = new Date(date + ' 23:59:59').getTime()
        return {
          startTimeStamp: Math.floor(startTs / 1000).toString(),
          endTimeStamp: Math.floor(endTs / 1000).toString()
        }
      }
    }
  },
  data() {
    return {
      oWebControl: null,
      pubKey: null,
      initCount: 0
    }
  },
  computed: {
    webPlayerBoxSize() {
      const element = this.$refs.webPlayerBox
      return {
        width: element.offsetWidth,
        height: element.offsetHeight
      }
    }
  },
  watch: {
    cameraIndexCode: {
      handler(val) {
        this.startPreview(val)
      }
    }
  },
  async mounted() {
    const result =  await this.initPlugin()
    console.log(result)
    // let layout = await this.oWebControl.JS_RequestInterface({
    //   funcName: 'getLayout'
    // })
    // console.log(JSON.parse(layout.responseMsg.data))
    this.setWndSize()
  },
  beforeDestroy() {
    if(this.oWebControl) {
      this.oWebControl.JS_HideWnd()
      this.oWebControl.JS_DestroyWnd()
      this.oWebControl.JS_Disconnect()
      this.oWebControl = null
    }
  },
  methods: {
    // 创建播放器插件实例
    initPlugin() {
      return new Promise((resolve, reject) => {
        this.oWebControl = new WebControl({
          szPluginContainer: 'playWnd',
          iServicePortStart: 15900,
          iServicePortEnd: 15909,
          szClassId:"23BF3B0A-2C56-4D97-9C03-0CB103AA8F11",
          // 创建成功
          cbConnectSuccess: () => {	
            // 启动插件服务									
            this.oWebControl.JS_StartService("window", {
              dllPath: "./VideoPluginConnect.dll"
            }).then(() => {
              this.oWebControl.JS_SetWindowControlCallback({
                cbIntegrationCallBack: this.cbIntegrationCallBack
              })
              // 创建插件窗口
              this.oWebControl.JS_CreateWnd("playWnd", this.webPlayerBoxSize.width, this.webPlayerBoxSize.height).then(async () => {
                await this.init()  // 创建播放实例成功后初始化
                if(this.playMode === 0) {
                  await this.startPreview(this.cameraIndexCode)
                } else {
                  // await this.startPlayBack()
                }
                resolve('启动插件服务成功-->init-->play')
              })
            }, function () {
              reject('启动插件服务失败')
            })
          },
          // 创建失败
          cbConnectError: () => {
            this.oWebControl = null;
            document.getElementById('playWnd').innerHTML("插件未启动，正在尝试启动，请稍候...");
            this.oWebControl.JS_WakeUp("VideoWebPlugin://"); // 程序未启动时执行error函数，采用wakeup来启动程序
            this.initCount ++;
            if (initCount < 3) {                             
              setTimeout(function () {
                initPlugin();
              }, 3000)
            } else {
              document.getElementById('playWnd').innerHTML("插件启动失败，请检查插件是否安装！");
            }
          },
          cbConnectClose: (bNormalClose) => {            
            // 异常断开：bNormalClose = false
            // JS_Disconnect正常断开：bNormalClose = true	
            console.log("cbConnectClose", bNormalClose);
            this.oWebControl = null;
          }
        })
        window.oWebControl = this.oWebControl
      })
    },

    // 设置窗口控制回调
    setCallbacks() {
      this.oWebControl.JS_SetWindowControlCallback({
        cbIntegrationCallBack: this.cbIntegrationCallBack
      })
    },

    // 推送消息
    cbIntegrationCallBack(oData) {
      console.log('消息回调', oData)
    },

    // 插件初始化
    async init() {
      await this.getPubkey()
      let initParams = {
         appkey: "22138259",                           //综合安防管理平台提供的appkey，必填
         secret: this.setEncrypt("kKBxeuMT9pTBC0Tr9d8z"),   //综合安防管理平台提供的secret，必填
         ip: "**************",                           //综合安防管理平台IP地址，必填
         playMode: this.playMode,                                  //初始播放模式：0-预览，1-回放
         port: 1443,                                    //综合安防管理平台端口，若启用HTTPS协议，默认443
         snapDir: "D:\\SnapDir",                       //抓图存储路径
         videoDir: "D:\\VideoDir",                     //紧急录像或录像剪辑存储路径
         layout: "1x1",                                //playMode指定模式的布局
         enableHTTPS: 1,                               //是否启用HTTPS协议与综合安防管理平台交互，这里总是填1
         encryptedFields: 'secret',					   //加密字段，默认加密领域为secret
         showToolbar: 1,                               //是否显示工具栏，0-不显示，非0-显示
         showSmart: 1,                                 //是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示
         buttonIDs: "0,16,256,257,258,259,260,512,513,514,515,516,517,768,769"  //自定义工具条按钮
        ////////////////////////////////// 请自行修改以上变量值	////////////////////////////////////
      }
      await this.oWebControl.JS_RequestInterface({
        funcName: 'init',
        argument: JSON.stringify(initParams)
      })
      this.oWebControl.JS_Resize(this.webPlayerBoxSize.width, this.webPlayerBoxSize.height)
    },

    // 获取公钥
    async getPubkey() {
      console.log('getPubkey')
      const { responseMsg } = await this.oWebControl.JS_RequestInterface({
        funcName: 'getRSAPubKey',
        argument: JSON.stringify({
          keyLength: 1024
        })
      })
      this.pubKey = responseMsg.data
    },

    // RSA加密 使用公钥对sk加密
    setEncrypt(value) {
      let encrypt = new JSEncrypt()
      encrypt.setPublicKey(this.pubKey)
      return encrypt.encrypt(value)
    },

    setWndSize() {
      window.addEventListener('resize', () => {
        if(this.oWebControl != null) {
          this.oWebControl.JS_Resize(this.webPlayerBoxSize.width, this.webPlayerBoxSize.height)
          this.setWndCover()
        }
      })
      window.addEventListener('scroll', () => {
        if(this.oWebControl != null) {
          this.oWebControl.JS_Resize(this.webPlayerBoxSize.width, this.webPlayerBoxSize.height)
          this.setWndCover()
        }
      })
    },

    setWndCover() {
      let iWidth = window.innerWidth
      let iHeight = window.innerHeight
      let oDivRect = document.getElementById('playWnd').getBoundingClientRect()

      let iCoverLeft = (oDivRect.left < 0) ? Math.abs(oDivRect.left): 0
      let iCoverTop = (oDivRect.top < 0) ? Math.abs(oDivRect.top): 0
      let iCoverRight = (oDivRect.right - iWidth > 0) ? Math.round(oDivRect.right - iWidth) : 0
      let iCoverBottom = (oDivRect.bottom - iHeight > 0) ? Math.round(oDivRect.bottom - iHeight) : 0

      // 多1个像素点防止还原后边界缺失一个像素条
      this.oWebControl.JS_RepairPartWindow(0, 0, this.webPlayerBoxSize.width + 1, this.webPlayerBoxSize.height);
      if (iCoverLeft != 0) {
        this.oWebControl.JS_CuttingPartWindow(0, 0, iCoverLeft, this.webPlayerBoxSize.height);
      }
      if (iCoverTop != 0) {
        // 多剪掉一个像素条，防止出现剪掉一部分窗口后出现一个像素条
        this.oWebControl.JS_CuttingPartWindow(0, 0, this.webPlayerBoxSize.width + 1, iCoverTop);
      }
      if (iCoverRight != 0) {
        this.oWebControl.JS_CuttingPartWindow(this.webPlayerBoxSize.width - iCoverRight, 0, iCoverRight, this.webPlayerBoxSize.height);
      }
      if (iCoverBottom != 0) {
        this.oWebControl.JS_CuttingPartWindow(0, this.webPlayerBoxSize.height - iCoverBottom, this.webPlayerBoxSize.width, iCoverBottom);
      }
    },

    async startPreview(cameraIndexCode, wndId) {
      console.log('预览')
      await this.oWebControl.JS_RequestInterface({
        funcName: 'startPreview',
        argument: JSON.stringify({
          cameraIndexCode: cameraIndexCode,                //监控点编号
          streamMode: 0,                         //主子码流标识 0 主 1 子
          transMode: 1,                           //传输协议
          gpuMode: 0,                               //是否开启GPU硬解
          // wndId: 1                                     //可指定播放窗口
        })
      })
    },
    async startPlayBack(wndId) {
      console.log('回放')
      const ret = await this.oWebControl.JS_RequestInterface({
        funcName: 'startPlayback',
        argument: JSON.stringify({
          cameraIndexCode: this.cameraIndexCode,                //监控点编号
          // streamMode: 0,                         //主子码流标识
          transMode: 1,                           //传输协议
          gpuMode: 0,                               //是否开启GPU硬解
          wndId: 0,                                     //可指定播放窗口
          recordLocation: 0,
          ...this.playBackTime
        })
      })
      console.log(ret)
    },

    stopAllPreview() {
      this.oWebControl.JS_RequestInterface({
          funcName: "stopAllPreview"
      })
    }

  }
}
</script>

<style scpped>
.playWnd {
  width: 100%;
  height: 100%;
}
</style>
