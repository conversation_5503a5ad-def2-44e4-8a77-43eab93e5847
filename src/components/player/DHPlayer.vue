<template>
  <div class="dhplayer" id="dhplayer-dom"></div>
</template>

<script>
export default {
  name: 'DHPlayer',
  props: {
    channelId: {
      type: String
    }
  },
  data() {
    return {
      
    }
  },
  mounted() {
    this.initDHPlayer()
  },
  beforeD<PERSON>roy() {
    this.destroyPlayer()
  },
  watch: {
    channelId(newVal) {
      if(newVal) this.realPlay()
    }
  },
  methods: {
    // 初始化并登录插件
    initDHPlayer() {
      this.myVideoPlayer = new VideoPlayer({
        videoId: 'dhplayer-dom',
        windowType: 0, // 0-实时预览  3-录像回放  7-录像回放(支持倒放)
        usePluginLogin: true,
        pluginLoginInfo: { // 传入 icc 平台的登录信息
          host: '**************',
          port: '1443',
          username: 'main',
          password: 'Abc!@#123'
        },
        division: 1, // 默认展示的窗口数量， 必传
        draggable: false,
        showBar: false,  // 底部操作栏
        shieldClass: ['menuList'],
        coverShieldClass: [],
        parentIframeShieldClass: [],

        createSuccess: (versionInfo) => {
          console.log('创建成功' + versionInfo.message)
          // this.setTabControlBtn([])
          if(this.channelId) this.realPlay()
        },
        createError: (error) => {
          console.log('创建失败' + JSON.stringify(error))
        },
        realSuccess: (info) => {
          console.log('预览成功' + JSON.stringify(info))
        }
      })
    },


    // 预览
    realPlay() {
      console.log('realPlay', this.channelId)
      if(!this.myVideoPlayer) {
        console.log('不能播放，请先初始化插件')
        return
      }
      this.myVideoPlayer.startReal([
        {
          channelId: this.channelId,
          channelName: `通道号${this.channelId}`,
          snum: 0,
          streamType: 1,
          deviceType: 5,
          // cameraType: '1',
          // capability: '00000000000000000000000000000001'
        }
      ])
    },

    // 销毁
    destroyPlayer() {
      if(this.myVideoPlayer) this.myVideoPlayer.destroy()
      this.myVideoPlayer = null
    }
  }
}
</script>

<style lang="scss" scoped>
.dhplayer {
  height: 100%;
  width: 100%;
}
</style>