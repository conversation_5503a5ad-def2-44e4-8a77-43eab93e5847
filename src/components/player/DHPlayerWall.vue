<template>
  <div class="dhplayer" id="dhplayerwall-dom"></div>
</template>

<script>
export default {
  name: 'DHPlayerWall',
  props: {
    // 单个信息
    channelInfo: {
      type: Object
    },
    // 需要播放的列表，目前为4个
    playList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      cellIndex: 0,
    }
  },
  mounted() {
    this.initDHPlayer()
  },
  beforeDestroy() {
    this.destroyPlayer()
  },
  watch: {
    channelInfo: {
      handler(newVal) {
        if(newVal) console.log('channelId->' + JSON.stringify(newVal))
        this.realPlay()
      },
      deep: true
    },
    // playList: {
    //   handler(newVal) {
    //     if(newVal.length) this.realPlayList()
    //   },
    //   deep: true
    // }
  },
  methods: {
    // 初始化并登录插件
    initDHPlayer() {
      this.myVideoPlayer = new VideoPlayer({
        videoId: 'dhplayerwall-dom',
        windowType: 0, // 0-实时预览  3-录像回放  7-录像回放(支持倒放)
        usePluginLogin: true,
        pluginLoginInfo: { // 传入 icc 平台的登录信息
          host: '**************',
          port: '1443',
          username: 'main',
          password: 'Abc!@#123'
        },
        division: 4, // 默认展示的窗口数量，必传 目前可划分窗口数为  1，4，6，8，9，13，16，20，25，36，64
        draggable: false,
        showBar: true, 
        shieldClass: ['menuList', 'el-image-viewer__wrapper', 'menuBox'], // 需要出现在播放器上的DOM元素的类名
        coverShieldClass: [],
        parentIframeShieldClass: [],
        /* 事件监听 */
        createSuccess: (versionInfo) => {
          console.log('创建成功' + JSON.stringify(versionInfo))
          if(this.playList.length > 0) {
            this.realPlayList()
          }
        },
        createError: (error) => {
          console.log('创建失败' + JSON.stringify(error))
        },
        // 插件公共回调方法
        dhPlayerMessage: (info, error) => {
          console.log('插件公共回调方法->' + JSON.stringify(info) + JSON.stringify(error))
        },
        // 实时预览，成功回调
        realSuccess: (info) => {
          console.log('预览成功' + JSON.stringify(info))
        },
        // 实时预览，错误回调
        realError: (info, error) => {
          console.log('预览失败' + JSON.stringify(info) + JSON.stringify(error))
        },
        // 单击窗口回调
        clickWindow: (snum) => {
          console.log('窗口点击->', snum)
          this.cellIndex = snum
        },
        // 关闭视频窗口回调
        closeWindowSuccess: ({ isAll, snum, channelList }) => {},
        // 切换窗口数量回调
        changeDivision: (division) => {
          console.log('切换窗口数量->' + division)
        },
      })
    },

    // 更新参数
    /* 
      videoPlayer._update({
        division: 4
      })
    */
    updatePlayer(options) {
      if(this.myVideoPlayer) this.myVideoPlayer._update(options)
    },

    // 预览
    realPlay() {
      console.log('预览')
      if(!this.myVideoPlayer) {
        console.log('不能播放，请先初始化插件')
        return
      }
      this.myVideoPlayer.startReal(
        [
          {
            channelId: this.channelInfo.channelId,
            channelName: this.channelInfo.channelName,
            snum: this.cellIndex,
            streamType: 1, // 码流类型 1 主码流 2 辅码流 （默认主码流）
            deviceType: 5, // 设备类别(用于对讲)
            cameraType: this.channelInfo.cameraType, // 摄像头类型(用于云台) 1 枪机 2 球机 3 半球 5 本地采集输入
            // capability: '00000000000000000000000000000001' // 能力集(用于云台)
          }
        ]
      )
    },
    // 播放多个
    realPlayList() {
      console.log('播放多个')
      if(!this.myVideoPlayer) {
        console.log('不能播放，请先初始化插件')
        return
      }
      const list = this.playList.map((item, index) => {
        return {
          channelId: item.channelId,
          channelName: item.channelName,
          snum: index,
          streamType: 1, // 码流类型 1 主码流 2 辅码流 （默认主码流）
          deviceType: 5, // 设备类别(用于对讲)
          cameraType: item.cameraType, // 摄像头类型(用于云台) 1 枪机 2 球机 3 半球 5 本地采集输入
          // capability: '00000000000000000000000000000001' // 能力集(用于云台)
        }
      })
      this.myVideoPlayer.startReal(list)
    },

    // 选中窗口
    chooseWindow(snum) {
      this.myVideoPlayer.chooseWindow(snum, (info) => {
        console.log('选中窗口信息->' + info)
      })
    },

    // 关闭播放
    closeOneOrAll(snum) {
      if(snum !== undefined) {
        this.myVideoPlayer.closeVideo(snum)
      } else {
        this.myVideoPlayer.closeVideo()
      }
    },

    // 播放器全屏
    setFullScreen() {
      this.myVideoPlayer.setFullScreen()
    },

    // 销毁
    destroyPlayer() {
      if(this.myVideoPlayer) {
        this.myVideoPlayer.destroy().then(() => {
          console.log("销毁成功")
        })
      }
      // this.myVideoPlayer = null
    },

    // 改变窗口数，目前可划分窗口数为  1，4，6，8，9，13，16，20，25，36，64
    changeDivision(divison) {
      this.myVideoPlayer.changeDivision(divison)
    },

    /** 
     * 设置水印
     * @param { Object } option 参数
     * @param { Number } snum 窗口数量
     * @param { String } item.color 水印颜色
     * @param { Number } item.fontSize 水印尺寸
     * @param { Number } item.fontWeight 字体粗细
     * @param { String } item.position 水印位置
     * @param { Number } item.text 文本
     */
    setWaterMark(option) {
      this.myVideoPlaye.waterMark(
        [
          {
            ...option
          }
        ]
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.dhplayer {
  height: 100%;
  width: 100%;
}
</style>