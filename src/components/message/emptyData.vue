<template>
  <div class="emptyData">
    <span class="message">{{ message }}</span>
  </div>
</template>

<script>
export default {
  name: 'EmptyData',
  props: {
    message: {
      type: String,
      default: '暂无数据'
    }
  }
}
</script>

<style slang="scss" scoped>
.emptyData {
  width: 100%;
  height: 100%;
  display: flex;
  color: #ffffff;
  align-items: center;
  justify-content: center;
}
</style>
