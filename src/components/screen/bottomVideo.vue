<template>
  <div class="bottomVideo">
    <div class="actionIcon">
      <div class="iconBox" :class="{ isOpen: show }" @click="changeShow"></div>
    </div>
    <div class="content" v-show="show">
      <div class="title">
        <span style="font-size: 20px;line-height: 1;">视频监控</span>
        <div class="moreButton" @click="toVideoWall">查看更多</div>
      </div>
      <div class="videoBox">
        <div class="videoItem" v-for="item, index in videoList" :key="index">
          <div class="videoName">
            <span style="padding-left: 27px;">{{ item.name }}</span>
            <span>2023-08-30 14:25:49</span>
          </div>
          <video src="#"></video>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        show: false,
        videoList: [
          {
            name: '摄像头01'
          },
          {
            name: '摄像头02'
          },
          {
            name: '摄像头03'
          }
        ]
      }
    },
    methods: {
      changeShow() {
        this.show = !this.show
      },
      toVideoWall() {
        this.$router.push('/main/videoWall')
      }
    }
  }
</script>

<style lang="scss" scoped>
.bottomVideo {
  position: absolute;
  width: calc(100% - 940px);
  left: 470px;
  bottom: 20px;
  .actionIcon {
    display: flex;
    justify-content: center;
    .iconBox {
      width: 48px;
      height: 24px;
      cursor: pointer;
      background-image: url('../../assets/img/home/<USER>');
      background-size: 100%;
    }
    .isOpen {
      background-image: url('../../assets/img/home/<USER>');
    }
  }
  .content {
    padding: 0 20px;
    color: #ffffff;
    .title {
      height: 40px;
      padding-left: 49px;
      padding-right: 20px;
      background-image: url('../../assets/img/home/<USER>');
      background-size: 100% 40px;

      display: flex;
      justify-content: space-between;
      align-items: center;
      .moreButton {
        width: 100px;
        height: 30px;
        background-color: #0F57AA;
        border-radius: 4px;
        text-align: center;
        line-height: 30px;
        cursor: pointer;
      }
    }
    .videoBox {
      background-color: rgba(4, 15, 45, 0.3);
      padding: 10px;
      display: flex;
      justify-content: space-between;
      .videoItem {
        width: 300px;
        height: 200px;
        border: 1px solid #1173CC;
        background-color: black;
        position: relative;
        .videoName {
          position: absolute;
          width: 100%;
          height: 30px;
          font-size: 14px;
          padding: 0 10px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: rgba(17, 115, 204, 0.8);
          // opacity: 0.8;
        }
        .videoName::before {
          position: absolute;
          display: block;
          content: '1';
          width: 16px;
          height: 18px;
          background-image: url('../../assets/img/home/<USER>');
          background-size: 100%;
        }
        video {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
