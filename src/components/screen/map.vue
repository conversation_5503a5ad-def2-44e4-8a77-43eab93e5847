<template>
  <div id="map">
    <div class="legend">
      <div v-for="item in legendlist" :key="item.id">
        <div class="check" @click="changelayer(item)">
          <div class="checked" v-if="item.state"></div>
        </div>
        <img :src="item.img" style="transform: translate(0, 2px)" alt="" />
        <div class="name">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import Ground from "@arcgis/core/Ground";
import WMSLayer from "@arcgis/core/layers/WMSLayer";
import WFSLayer from "@arcgis/core/layers/WFSLayer";
import Graphic from "@arcgis/core/Graphic";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import * as clusterLabelCreator from "@arcgis/core/smartMapping/labels/clusters";
export default {
  data() {
    return {
      map: null,
      view: null,
      imglayer: null,
      levellayer: null,
      falllayer: null,
      flowlayer: null,
      pressurelayer: null,
      qualitylayer: null,
      monitorlayer: null,
      legendlist: [
        {
          id: 1,
          type: "flow",
          img: require("../../assets/img/home/<USER>"),
          label: "流量监测",
          state: true,
        },
        {
          id: 2,
          type: "level",
          img: require("../../assets/img/home/<USER>"),
          label: "水位监测",
          state: true,
        },
        {
          id: 3,
          type: "pressure",
          img: require("../../assets/img/home/<USER>"),
          label: "压力监测",
          state: true,
        },
        {
          id: 4,
          type: "quality",
          img: require("../../assets/img/home/<USER>"),
          label: "水质监测",
          state: true,
        },
        {
          id: 5,
          type: "fall",
          img: require("../../assets/img/home/<USER>"),
          label: "雨量监测",
          state: true,
        },
        {
          id: 6,
          type: "monitor",
          img: require("../../assets/img/home/<USER>"),
          label: "视频监控",
          state: true,
        },
      ],
      geourl:""
    };
  },
  mounted() {
    this.geourl = process.env.VUE_APP_GEO_URL
    this.createmap();
  },
  methods: {
    createmap() {
      this.imglayer = new WMSLayer({
        url: this.geourl + "/geoserver/img/wms",
      });
      this.map = new Map({
        ground: new Ground({
          layers: [],
          surfaceColor: "transparent",
          opacity: 0,
        }),
        layers: [],
      });
      let view = new MapView({
        container: "map",
        map: this.map,
        background: {
          type: "color",
          color: [255, 252, 244, 0],
        },
        extent: {
          xmax: 111.15191207080001,
          xmin: 110.39483293,
          ymax: 31.591080100800003,
          ymin: 30.9630324254,
        },
        spatialReference: {
          wkid: 4326,
        },
      });
      view.ui.remove("attribution");
      view.ui.empty("top-left");
      view.map.add(this.imglayer);
      view.on("click", async (e) => {
        let point = [e.mapPoint.x, e.mapPoint.y];
        console.log(point);
        // view.hitTest(e).then((res) => {
        //   console.log(res);
        // });
      });
      this.levellayer = new WFSLayer({
        url: this.geourl + "/geoserver/water_level/wms",
        outFields: "*",
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require("../../assets/img/home/<USER>"),
            width: "25px",
            height: "33px",
          },
        },
      });
      this.falllayer = new WFSLayer({
        url: this.geourl + "/geoserver/rain_fall/wms",
        outFields: "*",
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require("../../assets/img/home/<USER>"),
            width: "25px",
            height: "33px",
          },
        },
      });
      this.flowlayer = new WFSLayer({
        url: this.geourl + "/geoserver/water_flow/wms",
        outFields: "*",
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require("../../assets/img/home/<USER>"),
            width: "25px",
            height: "33px",
          },
        },
      });
      this.pressurelayer = new WFSLayer({
        url: this.geourl + "/geoserver/pressure/wms",
        outFields: "*",
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require("../../assets/img/home/<USER>"),
            width: "25px",
            height: "33px",
          },
        },
      });
      this.qualitylayer = new WFSLayer({
        url: this.geourl + "/geoserver/water_quality/wms",
        outFields: "*",
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require("../../assets/img/home/<USER>"),
            width: "25px",
            height: "33px",
          },
        },
      });
      this.monitorlayer = new WFSLayer({
        url: this.geourl + "/geoserver/video_monitor/wms",
        outFields: "*",
        renderer: {
          type: "simple",
          symbol: {
            type: "picture-marker",
            url: require("../../assets/img/home/<USER>"),
            width: "25px",
            height: "33px",
          },
        },
      });
      view.map.add(this.levellayer);
      view.map.add(this.falllayer);
      view.map.add(this.flowlayer);
      view.map.add(this.pressurelayer);
      view.map.add(this.qualitylayer);
      view.map.add(this.monitorlayer);
    },
    changelayer(item) {
      item.state = !item.state;
      switch (item.type) {
        case "level":
          this.levellayer.visible = item.state;
          break;
        case "fall":
          this.falllayer.visible = item.state;
          break;
        case "pressure":
          this.pressurelayer.visible = item.state;
          break;
        case "flow":
          this.flowlayer.visible = item.state;
          break;
        case "quality":
          this.qualitylayer.visible = item.state;
          break;
        case "monitor":
          this.monitorlayer.visible = item.state;
          break;
      }
    },
  },
};
</script>
<style lang="scss">
.esri-view-width-medium .esri-popup__main-container {
  background-color: rgba(4, 15, 45, 0.3);
}
.esri-popup__header {
  background-color: rgba(4, 15, 45, 0.3) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  background-color: rgba(4, 15, 45, 0) !important;
}
.esri-popup__icon,
.esri-icon-close {
  color: #fff !important;
}
.esri-popup__content {
  margin: 0;
}
.esri-feature {
  background-color: rgba(4, 15, 45, 0.3);
  padding: 0 15px 12px;
}
.esri-widget__table {
  color: #fff;
}
.esri-popup__header-title {
  background-color: rgba(4, 15, 45, 0) !important;
  color: #fff;
}
.esri-popup__pointer-direction {
  background-color: rgba(4, 15, 45, 0.3);
}

.esri-popup__footer {
  display: none;
}
.esri-popup__feature-menu {
  display: none;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}
.esri-view-surface {
  outline: none !important;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: auto 0px Highlight !important;
  outline: auto 0px -webkit-focus-ring-color !important;
}
[class*="esri-popup--is-docked-top-"] .esri-popup__footer,
[class*="esri-popup--aligned-bottom-"] .esri-popup__footer {
  border-bottom: solid 1px #6e6e6e4d;
}
.esri-popup__header {
  border-top-left-radius: 5px !important;
  border-top-right-radius: 5px;
  background-color: #fff;
  color: #fff;
}
.esri-popup--shadow {
  box-shadow: 0 0 0 0 rgb(155, 155, 155) !important;
}
.esri-popup__header-container,
.esri-popup__header-container--button {
  outline: none !important;
}
.esri-ui .esri-popup {
  border-radius: 5px !important;
}
.esri-popup {
  position: absolute !important;
}
.esri-popup__button {
  background-color: transparent !important;
  outline: none;
}
</style>
<style scoped lang="scss">
#map {
  position: absolute;
  left: 500px;
  top: 0;
  width: 910px;
  height: 820px;
  .legend {
    width: 140px;
    height: 191px;
    background: rgba(4, 15, 45, 0.5);
    position: absolute;
    // left: 0;
    // top: 0;
    right: 0;
    bottom: 0;
    padding: 18px 14px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .check {
      width: 16px;
      height: 16px;
      border: 1px solid #91d5ff;
      margin-right: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .checked {
      width: 8px;
      height: 8px;
      background: linear-gradient(180deg, #60dad1 0%, #1e9afc 100%);
    }
    div {
      display: flex;
      align-items: center;
      .name {
        font-size: 12px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        margin-left: 5px;
      }
    }
  }
}
</style>