<template>
  <div class="left">
    <div class="warn">
      <div class="title videoToggle">
        <div class="type" :class="{ active: videoType == 'type1' }" @click="toggle('type1')">视频监控</div>
        <div style="margin-left: 40px;" class="type" :class="{ active: videoType == 'type2' }" @click="toggle('type2')">无人机值守</div>
      </div>
      <div class="overviewData">
        <div class="videoBox">
          <img src="../../assets/img/home/<USER>" alt="" style="width: 100%;height: 100%;" v-if="videoType == 'type1'">
          <img src="../../assets/img/home/<USER>" alt="" style="width: 100%;height: 100%;" v-else>
        </div>
        <div class="videoListBox" v-if="videoType == 'type1'">
          <div v-for="v in videoList" :key="v.id" class="videoSmallCard" :class="{ select: currentVideoStation == v.id }" @click="change('currentVideoStation', v.id, v)">
            <img :src="v.pic" alt="" style="width: 100%;height: 100%;">
            <div class="stationName">{{ v.name }}</div>
          </div>
        </div>
        <div class="videoListBox" v-else>
          <div v-for="v in videoListOther" :key="v.id" class="videoSmallCard" :class="{ select: currentUAV == v.id }" @click="change('currentUAV', v.id, v)">
            <img :src="v.pic" alt="" style="width: 100%;height: 100%;">
            <div class="stationName">{{ v.name }}</div>
          </div>
        </div>
      </div>
      <!-- <div class="overviewData">
        <div class="item">
          <span class="label">水源地</span>
          <span class="value">{{ overviewData.waterSource }}</span>
        </div>
        <div class="item">
          <span class="label">水厂</span>
          <span class="value">{{ overviewData.factory }}</span>
        </div>
        <div class="item">
          <span class="label">蓄水池</span>
          <span class="value">{{ overviewData.waterStorage }}</span>
        </div>
        <div class="item">
          <span class="label">压力测点数</span>
          <span class="value">{{ overviewData.pressure }}</span>
        </div>
        <div class="item">
          <span class="label">流量测点数</span>
          <span class="value">{{ overviewData.flow }}</span>
        </div>
        <div class="item">
          <span class="label">水质测点数</span>
          <span class="value">{{ overviewData.quality }}</span>
        </div>
        <div class="item">
          <span class="label">服务居民</span>
          <span class="value">{{ overviewData.waterSource }}W</span>
        </div>
        <div class="item">
          <span class="label">企业用户</span>
          <span class="value">{{ overviewData.firms }}</span>
        </div>
        <div class="item">
          <span class="label">管网长度</span>
          <span class="value">{{ overviewData.pipeLength }}KM</span>
        </div>
      </div> -->
      <!-- <div class="title">实时预警</div> -->
      <!-- <div class="calcHeight">
        <div class="listHeader" style="text-align: center;">
          <span>预警等级</span>
          <span>预警类型</span>
          <span style="width: 122px">预警站点</span>
          <span>预警时间</span>
        </div>
        <div class="listItem" v-for="item in areaList" :key="item.id" @click="toWarnPage">
          <div class="small">
            <div class="num">{{ item.alarmGrade == "1" && "Ⅰ级预警" || item.alarmGrade == "2" && "Ⅱ级预警" || item.alarmGrade == "3" && "Ⅲ级预警" }}</div>
          </div>
          <div class="small">{{ item.warnType | warnTypeFilter }}</div>
          <div class="small" style="width: 122px">{{ item.name }}</div>
          <div class="big">{{ item.dataTime }}</div>
        </div>
      </div> -->
    </div>
    
    <div class="data">
      <div class="title">营收数据</div>
      <div class="content">
        <div class="name">
          <img
            src="../../assets/img/icon.png"
            style="margin-right: 7px"
            alt=""
          />
          用水性质分布
        </div>
        <div class="chart" id="chart"></div>
        <div class="name">
          <img
            src="../../assets/img/icon.png"
            style="margin-right: 7px"
            alt=""
          />
          营业情况统计
        </div>
        <div class="chart2" id="chart2"></div>
        <div class="name">
          <img
            src="../../assets/img/icon.png"
            style="margin-right: 7px"
            alt=""
          />
          抄表情况统计
        </div>
        <div class="datadetail">
          <div class="item">
            <img src="../../assets/img/icon3.png" alt="" />
            <div class="itemtitle">新增用户数</div>
            <div class="num">201</div>
            <div class="per">1.2%</div>
            <div class="total">总用户：104535</div>
          </div>
          <div class="item">
            <img src="../../assets/img/icon2.png" alt="" />
            <div class="itemtitle">总水表数</div>
            <div class="num">104492</div>
            <div class="per">1.83%</div>
            <div class="total">计费表数：104428</div>
          </div>
          <div class="item">
            <img src="../../assets/img/icon1.png" alt="" />
            <div class="itemtitle">户表户均水量</div>
            <div class="num">837.69</div>
            <div class="per">0.3%</div>
            <div class="total">户表数：104394</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getHomepageOverviewData } from '@/api/screenData'
import { getWqWarn } from '@/api/wq/warn'

import echarts from "echarts";
import { debounce } from '@/utils'
export default {
  data() {
    return {
      overviewData: {},
      // 左上角视频，目前是为了静态展示
      videoType: 'type1',
      currentVideoStation: 1,
      currentUAV: 1,
      videoList: [
        {
          id: 1,
          name: '水源地1',
          pic: require('@/assets/img/home/<USER>')
        },
        {
          id: 2,
          name: '水源地2',
          pic: require('@/assets/img/home/<USER>')
        }
      ],
      videoListOther: [
        {
          id: 1,
          name: '无人机1',
          pic: require('@/assets/img/home/<USER>')
        },
        {
          id: 2,
          name: '无人机2',
          pic: require('@/assets/img/home/<USER>')
        }
      ],
      areaList: [],
      centerChart: null,
      centerChart2: null,
    };
  },
  filters: {
    warnTypeFilter(value) {
      const labelMap = {
        'UN_KNOWN': '未知',
        'WATER_QUALITY': '水质预警',
        'EQUIPMENT': '设备预警',
        'WATER_LEVEL': '水位预警',
        'WATER_TRAFFIC': '流量预警',
        'WATER_PRESSURE': '压力预警'
      }
      if(!value) return '--'
      return labelMap[value]
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initchart();
      this.initchart2();
    });
    this.getOverviewData()
    // this.getTableData()
    const resizeHandler = debounce(() => {
      this.chartResize()
    }, 100)
    window.addEventListener('resize', resizeHandler)
    this.$once('hook:beforeDestroy', () => {
      console.log('remove')
      window.removeEventListener('resize', resizeHandler)
    })
  },
  methods: {
    // 切换视频
    toggle(type) {
      this.videoType = type
    },
    // 切换站点
    change(param, value) {
      this[param] = value
    },
    toWarnPage() {
      this.$router.push({
        name: "WqWarn"
      })
    },
    getOverviewData() {
      getHomepageOverviewData().then(res => {
        this.overviewData = res.data
      })
    },
    getTableData() {
      getWqWarn({
        pageNo: 1,
        pageSize: 5,
      }).then(res => {
        if (res.status === 200) {
          this.areaList = res.data
        }
      })
    },
    initchart() {
      this.centerChart = echarts.init(document.getElementById("chart"));
      let option = {
        color: ["#05CFF7", "#0269E9", "#C6ECDF", "#01F871"],
        tooltip: {
          trigger: "item",
        },
        legend: {
          icon: "circle",
          orient: "vertical",
          right: 20,
          top: 'center',
          // bottom: 20,
          textStyle: {
            fontSize: 14,
            fontWeight: "normal",
            color: "#ACC6EA",
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: ["60%", "80%"],
            center: ["30%", "50%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 0,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 15,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 0.442, name: "生活用水" },
              { value: 0.221, name: "商业用水" },
              { value: 0.202, name: "工业用水" },
              { value: 0.155, name: "其他用水" },
            ],
          },
        ],
      };
      this.centerChart.setOption(option, true);
    },
    initchart2() {
      this.centerChart2 = echarts.init(document.getElementById("chart2"));
      let option = {
        legend: {
          right: 20,
          top: 10,
          textStyle: {
            fontSize: 12,
            fontWeight: "normal",
            color: "#FFFFFF",
          },
        },
        grid: {
          // 让图表占满容器
          top: "40px",
          left: "40px",
          right: "20px",
          bottom: "20px",
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0,
              show: true, //这行代码控制着坐标轴x轴的文字是否显示
              textStyle: {
                color: "#DEEBFF", //x轴上的字体颜色
                fontSize: "12", // x轴字体大小
              },
            },
            data: [
              "2023-01",
              "2023-02",
              "2023-03",
              "2023-04",
              "2023-05",
              "2023-06",
            ],
          },
          // 第2个x轴，用于背景
          {
            type: "category",
            axisLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: {
          type: "value",
          axisLabel: {
            show: true, //这行代码控制着坐标轴x轴的文字是否显示
            textStyle: {
              color: "#DEEBFF", //x轴上的字体颜色
              fontSize: "12", // x轴字体大小
            },
          },
        },
        series: [
          {
            data: [3500, 5800, 1900, 3200, 1980, 1980],
            name: "欠费金额",
            type: "bar",
            // showBackground: true,
            // backgroundStyle: {
            //   color: "rgba(172,198,234, 0.1)",
            // },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#D7B44E",
                  },
                  {
                    offset: 0.5,
                    color: "rgba(155,116,0,0.5)",
                  },
                  {
                    offset: 1,
                    color: "rgba(155,116,0,0.1)",
                  },
                ]),
              },
            },
          },
          {
            data: [7500, 7500, 7000, 6800, 7500, 6900],
            name: "应收金额",
            type: "bar",
            // showBackground: true,
            // backgroundStyle: {
            //   color: "rgba(172,198,234, 0.1)",
            // },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#00B4FF",
                  },
                  {
                    offset: 0.5,
                    color: "rgba(45,72,173,0.5)",
                  },
                  {
                    offset: 1,
                    color: "rgba(45,72,173,0.1)",
                  },
                ]),
              },
            },
          },
          {
            data: [5200, 6300, 5600, 5600, 5600, 5600],
            name: "实收金额",
            type: "bar",
            // showBackground: true,
            // backgroundStyle: {
            //   color: "rgba(172,198,234, 0.1)",
            // },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#42FB97",
                  },
                  {
                    offset: 0.5,
                    color: "rgba(29,39,115,0.5)",
                  },
                  {
                    offset: 1,
                    color: "rgba(29,39,115,0.1)",
                  },
                ]),
              },
            },
          },
          {
            data: [2900, 4800, 3200, 3700, 3500, 3300],
            name: "售水量",
            type: "bar",
            // showBackground: true,
            // backgroundStyle: {
            //   color: "rgba(172,198,234, 0.1)",
            // },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#91D5FF",
                  },
                  {
                    offset: 0.5,
                    color: "rgba(32,74,100,0.5)",
                  },
                  {
                    offset: 1,
                    color: "rgba(32,74,100,0.1)",
                  },
                ]),
              },
            },
          },
          {
            type: 'bar',
            xAxisIndex: 1,
            // silent: false,
            itemStyle: {
              color: 'rgb(172,198,234)'
            },
            barWidth: 100,
            tooltip: {
              show: false
            },
            data: [1, 1, 10]
          },
        ],
      };
      this.centerChart2.setOption(option, true);
    },
    chartResize() {
      this.centerChart && this.centerChart.resize()
      this.centerChart2 && this.centerChart2.resize()
      console.log('1111')
    }
  },
};
</script>

<style scoped lang="scss">
.left {
  position: absolute;
  left: 30px;
  height: 100%;
  .warn {
    width: 440px;
  }
  .overviewData {
    background-color: rgba(4, 15, 45, 0.3);
    padding: 10px;
    display: flex;
    // flex-wrap: wrap;
    justify-content: space-between;

    color: #ffffff;
    .item {
      width: 140px;
      height: 30px;
      font-size: 14px;
      line-height: 30px;
      .label {
        display: inline-block;
        width: 83px;
      }
    }
  }
  .videoBox {
    width: 300px;
    height: 200px;
    padding: 14px;
    background-image: url('~@/assets/img/home/<USER>');
    background-size: 100%;
  }
  .videoListBox {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .videoSmallCard {
      width: 100px;
      height: 94px;
      padding-left: 6px;
      position: relative;
      cursor: pointer;
      img {
        border: 2px solid #023280;
      }
      .stationName {
        font-size: 12px;
        color: #FFFFFF;
        background-color: rgba(42, 93, 210, 0.7);
        position: absolute;
        text-align: center;
        line-height: 20px;
        width: 90px;
        height: 20px;
        left: 8px;
        bottom: 2px;        
      }
    }
    .select {
      background-image: url('~@/assets/img/home/<USER>');
      background-size: 100%;
      img {
        border-color: transparent;
      }
    }
  }
  .data {
    margin-top: 20px;
    width: 440px;
    height: calc(100% - 262px);
    .content {
      padding: 0 20px;
      height: calc(100% - 42px);
      background-color: rgba(4, 15, 45, 0.3);
      .name {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 24px;
        padding-top: 10px;
      }
      .chart {
        height: 22.5%;
      }
      .chart2 {
        height: 27.3%;
      }
      .datadetail {
        display: flex;
        justify-content: space-around;
        .item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .itemtitle {
            font-size: 12px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #acc6ea;
          }
          .num {
            font-size: 18px;
            font-family: DIN;
            font-weight: bold;
            color: #ffffff;
          }
          .per {
            font-size: 12px;
            font-family: DIN;
            font-weight: bold;
            color: #ffffff;
          }
          .total {
            font-size: 12px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #acc6ea;
          }
        }
      }
    }
  }
  .videoToggle {
    display: flex;
    .type {
      cursor: pointer;
      font-size: 20px;
      border-bottom: 2px solid transparent;
    }
    .active {
      border-bottom-color: #83D1FF;
    }
  }
  .title {
    background: url("../../assets/img/title.png") center no-repeat;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
  }
  .calcHeight {
    height: 210px;
    padding-top: 5px;
    .listHeader {
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 34px;
      background: url("../../assets/img/table.png") center no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 34px;
      span {
        display: block;
        width: 80px;
      }
      span:first-child {
        width: 93px;
        padding-left: 30px;
      }
      span:last-child {
        flex-grow: 1;
      }
    }
    .listItem {
      text-align: center;
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 34px;
      .small {
        width: 80px;
        // font-size: 14px;
      }
      .small:first-child {
        width: 93px;
        padding-left: 30px;
      }
      .big {
        display: flex;
        align-items: center;
        max-width: 195px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
</style>