<template>
  <div class="right">
    <div class="analysis">
      <div class="title">水质分析</div>
      <div class="content">
        <div class="guageBox">
          <div id="mychart" style="width: 200px; height: 160px;"></div>
          <div class="guageBg"></div>
        </div>
        <div class="num">
          <div>
            <span>标本数</span>
            <span>1000</span>
          </div>
          <div>
            <span>达标率</span>
            <span style="color: #01F871;">96%</span>
          </div>
          <div>
            <span>有效数据</span>
            <span style="color: #FAAD14;">96%</span>
          </div>
        </div>
      </div>
    </div>
    <div class="monitor">
      <div class="title">设备监测预警</div>
      <div class="eqInfoBox">
        <div class="imgBox">
          <img style="" src="../../assets/img/eqIcon.png" alt="">
        </div>
        <div class="item" style="padding-left: 10px; ">
          <div class="label">设备台数(台)</div>
          <div class="value">245</div>
        </div>
        <div class="item item1">
          <div class="label"><img src="../../assets/img/eqOnline.png" alt="">在线(台)</div>
          <div class="value">172</div>
        </div>
        <div class="item item1">
          <div class="label"><img src="../../assets/img/eqOffline.png" alt="">离线(台)</div>
          <div class="value">83</div>
        </div>
      </div>
      <div class="calcHeight">
        <div class="listHeader" style="text-align: center;">
          <span>设备名称</span>
          <span style="width: 122px">所在区域</span>
          <span style="width: 122px">是否处理</span>
          <span>故障时间</span>
        </div>
        <div class="listItem" v-for="item in areaList" :key="item.id" @click="toWarnPage">
          <div class="small">{{ item.eqName }}</div>
          <div class="small" style="width: 122px">{{ item.location }}</div>
          <div class="small" :style="{ width: '122px', color: item.isHandle ? '#01F871' : '#F76300' }">{{ item.isHandle ? '是' : '否' }}</div>
          <div class="big">{{ item.dataTime }}</div>
        </div>
      </div>
    </div>

    <div class="situation">
      <div class="title">管网漏损率</div>
      <div class="rateBox">
        <div class="item"><i class="el-icon-bottom" style="color: #F76300;"></i><span>同比 1.2%</span></div>
        <div class="item"><i class="el-icon-top" style="color: #01F871;"></i><span>环比 2.4%</span></div>
      </div>
      <!-- <div id="mychart2"></div> -->
      <div class="lineChartBox">
        <CommonLine :xAxisData="xAxisData" :seriesData="seriesData" />
      </div>
    </div>
  </div>
</template>

<script>
import echarts from "echarts";
import CommonLine from './lineChart'

import { getWqRealtimeDataPage } from '@/api/wq/monitor'
import { getWqWarn } from '@/api/wq/warn'
export default {
  components: {
    CommonLine
  },
  data() {
    return {
      areaList: [
        {
          eqName: '设备1',
          location: '兴山第一水厂',
          isHandle: false,
          dataTime: '08-30 13:00'
        },
        {
          eqName: '设备2',
          location: '兴山小河里蓄水池',
          isHandle: false,
          dataTime: '08-30 13:00'
        },
        {
          eqName: 'X设备1X',
          location: 'A区域供水管道',
          isHandle: false,
          dataTime: '08-30 13:00'
        },
        {
          eqName: '设备3',
          location: 'B区域供水管道',
          isHandle: true,
          dataTime: '08-30 13:00'
        },
        {
          eqName: '设备5',
          location: '古夫镇东北部',
          isHandle: true,
          dataTime: '08-30 13:00'
        }
      ],
      xAxisData: [
        "2023-01",
        "2023-02",
        "2023-03",
        "2023-04",
        "2023-05",
        "2023-06",
      ],
      seriesData: [
        {
          type: "line",
          symbol: 'circle',
          markLine: {
            symbol: ['none', 'none'],
            data: [
              {
                name: '漏损红线',
                yAxis: 80,
                lineStyle: {
                  color: '#D23904',
                  type: 'solid',
                },
                label: {
                  position: 'insideEndTop',
                  formatter: '{b}'
                }
              }
            ]
          },
          data: [85, 80, 88, 76, 90, 84, 77],
        }
      ]
    };
  },
  filters: {
    warnTypeFilter(value) {
      const labelMap = {
        'UN_KNOWN': '未知',
        'WATER_QUALITY': '水质预警',
        'EQUIPMENT': '设备预警',
        'WATER_LEVEL': '水位预警',
        'WATER_TRAFFIC': '流量预警',
        'WATER_PRESSURE': '压力预警'
      }
      if(!value) return '--'
      return labelMap[value]
    }
  },
  mounted() {
    this.getData()
    this.$nextTick(() => {
      this.initchart();
      // this.initchart2();
    });
  },
  methods: {
    toWarnPage() {
      this.$router.push({
        name: "WqWarn"
      })
    },
    initchart() {
      let chart = echarts.init(document.getElementById("mychart"));
      let option = {
        series: [
          {
            type: "gauge",
            radius: '84%',
            // progress: {
            //   show: true,
            //   width: 50,
            // },
            splitNumber: 5,
            axisLine: {
              lineStyle: {
                width: 13,
                color: [
                  [
                    0.96,
                    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                      {
                        offset: 0,
                        color: "#2898FA",
                      },
                      {
                        offset: 1,
                        color: "#02F9E7",
                      },
                    ]),
                  ],
                  [1, '#2A92FB']
                ]
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            pointer: {
              width: 3
            },
            title: {
              textStyle: {
                color: '#ACC6EA',
                fontSize: 14,
              },
              offsetCenter: [0, "102%"]
            },
            detail: {
              fontSize: 22,
              fontWeight: 'bold',
              color: '#02F9E7',
              padding: [50, 0, 0, 0],
              formatter: function(value) {
                return value + '%'
              }
            },
            data: [
              {
                name: '水质监测结果',
                value: 96,
              },
            ],
          },
          {
            type: 'gauge',
            radius: '65%',
            splitNumber: 5,
            axisLine: {
              show: false
            },
            axisTick: {
              length: 6
            },
            axisLabel: {
              distance: -20,
              color: '#ffffff'
            },
            splitLine: {
              show: false
            },
            pointer: {
              show: false
            },
            title: {
              show: false
            },
            detail: {
              show: false
            }
          }
        ],
      };
      chart.setOption(option, true);
    },
    getData() {
      // getWqRealtimeDataPage({
      //   pageNo: 1,
      //   pageSize: 7
      // }).then(res => {
      //   const { status, data } = res
      //   if(status === 200) {
      //     this.areaList = data
      //   }
      // })
      getWqWarn({
        pageNo: 1,
        pageSize: 5
      }).then(res => {
        const { status, data } = res
        if(status === 200) {
          // this.areaList = data
        }
      })
    },
    initchart2() {
      let chart = echarts.init(document.getElementById("mychart2"));
      let option = {
        tooltip: {
          trigger: "axis",
        },
        color: ["#00E4FF", "#18D565", "#EBA827"],
        grid: {
          top: "40px",
          left: "40px",
          right: "30px",
          bottom: "20px",
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [
            "2023-01",
            "2023-02",
            "2023-03",
            "2023-04",
            "2023-05",
            "2023-06",
          ],
          axisLabel: {
            interval: 0,
            show: true, //这行代码控制着坐标轴x轴的文字是否显示
            textStyle: {
              color: "#DEEBFF", //x轴上的字体颜色
              fontSize: "12", // x轴字体大小
            },
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            show: true, //这行代码控制着坐标轴x轴的文字是否显示
            textStyle: {
              color: "#DEEBFF", //x轴上的字体颜色
              fontSize: "12", // x轴字体大小
            },
          },
        },
        series: [
          {
            name: "蓄水池",
            type: "line",
            symbol: 'circle',
            data: [85, 80, 88, 76, 90, 84, 77],
          }
        ],
      };
      chart.setOption(option, true);
    },
  },
};
</script>

<style scoped lang="scss">
.right {
  position: absolute;
  right: 30px;
  height: 100%;
  .calcHeight {
    height: 210px;
    padding: 5px 10px 0;
    background-color: rgba(4, 15, 45, 0.3);
    .listHeader {
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 34px;
      background: url("../../assets/img/table.png") center no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 34px;
      span {
        display: block;
        width: 80px;
      }
      span:first-child {
        width: 93px;
        padding-left: 30px;
      }
      span:last-child {
        flex-grow: 1;
      }
    }
    .listItem {
      text-align: center;
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 34px;
      .small {
        width: 80px;
        // font-size: 14px;
      }
      .small:first-child {
        width: 93px;
        padding-left: 30px;
      }
      .big {
        display: flex;
        align-items: center;
        max-width: 195px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  .analysis {
    width: 440px;
    height: 24.2%;
    // height: 252px;
    .content {
      height: calc(100% - 42px);
      display: flex;
      background-color: rgba(4, 15, 45, 0.3);
      .guageBox {
        position: relative;
        overflow: hidden;
        .guageBg {
          position: absolute;
          top: -10px;
          left: 10px;
          width: 180px;
          height: 180px;
          background-image: url("../../assets/img/home/<USER>");
          background-size: 100%;
        }
      }
      .num {
        // padding: 24px 0;
        div {
          background: url("../../assets/img/ana.png");
          width: 195px;
          height: 49px;
          display: flex;
          align-items: center;
          justify-content: space-around;
          span:first-child {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #ffffff;
          }
          span:last-child {
            font-size: 18px;
            font-family: DIN;
            font-weight: bold;
            color: #11cdff;
          }
        }
      }
    }
  }
  .monitor {
    width: 440px;
    // height: 326px;
    margin-top: 20px;
    .eqInfoBox {
      padding: 10px;
      display: flex;
      // justify-content: space-between;
      color: #ffffff;
      background-color: rgba(4, 15, 45, 0.3);
      .imgBox {
        width: 76px;
        height: 82px;
        img {
          width: 100%;
        }
      }
      .item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 10px;
        flex-grow: 1;
        .label {
          font-size: 14px;
          color: #ACC6EA;
          font-weight: 400;
          margin-bottom: 13px;
          display: flex;
          align-items: center;
          img {
            width: 16px;
            margin-right: 8px;
          }
        }
        .value {
          font-size: 20px;
          font-weight: bold;
        }
      }
      .item1 {
        align-items: center;
      }
    }
  }
  .situation {
    width: 440px;
    margin-top: 20px;
    height: calc(73.8% - 394px);
    background-color: rgba(4, 15, 45, 0.3);
    .rateBox {
      color: #ffffff;
      display: flex;
      justify-content: center;
      .item {
        width: 120px;
        height: 24px;
        background-image: url('../../assets/img/pericon.png');
        background-size: 120px 24px;
        display: flex;
        align-items: center;
        justify-content: space-around;
      }
    }
    .lineChartBox {
      height: calc(100% - 66px);
    }
    #mychart2 {
      width: 440px;
      height: 284px;
      height: calc(100% - 42px);      
    }
  }
  .title {
    background: url("../../assets/img/title.png") center no-repeat;
    width: 440px;
    height: 42px;
    font-size: 24px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    padding-left: 48px;
  }
}
</style>