const state = {
    actionAuth: {}
};

const mutations = {
    SET_ACTION_AUTH(state, data) {
        state.actionAuth = data;
    }
};

const actions = {
    setActionAuth({commit}, data) {
        commit('SET_ACTION_AUTH', data);
    }
};

const getters = {
    getActionAuth(state) {
        return state.actionAuth
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
}