const state = {
  pipeNetworkMenuActive: Number(localStorage.getItem('activePipeNetWorkMenu')) || -1,
  homeActive: 1,
  DMAShowType: 'chart'
};

const mutations = {
  SET_pipeNetworkMenuActive: (state, value) => {
    state.pipeNetworkMenuActive = value
  },
  SET_homeActive: (state, value) => {
    state.homeActive = value
  },
  SET_DMAShowType: (state, value) => {
    state.DMAShowType = value
  },
};

const actions = {
  setPipeNetworkMenuActive({ commit }, value) {
    commit('SET_pipeNetworkMenuActive', value)
  },
  setHomeActive({ commit }, value) {
    commit('SET_homeActive', value)
  },
  setDMAShowType({ commit }, value) {
    commit('SET_DMAShowType', value)
  }
};

const getters = {
  getPipeNetworkMenuActive: state => state.pipeNetworkMenuActive,
  getHomeAction: state => state.homeActive,
  DMAShowType: state => state.DMAShowType
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
}