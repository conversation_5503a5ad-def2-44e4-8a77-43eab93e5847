/**
 * Minified by jsDelivr using Terser v5.19.2.
 * Original file: /npm/mars3d-heatmap@3.1.22/dist/mars3d-heatmap.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
/*!
 * Mars3D平台插件,结合heatmap可视化功能插件
 * 版本信息：v3.1.21
 * 编译日期：2022-01-08 18:18:09
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 */
!function(t){let e=t.Cesium;e||"object"!=typeof exports||(e=require("mars3d-cesium"),require("mars3d-cesium/Build/Cesium/Widgets/widgets.css"),t.Cesium=e),e||console.error("请引入Cesium库，可参考教程：http://mars3d.cn/dev/guide/basics/import.html");let n=t.mars3d;n||"object"!=typeof exports||(n=require("mars3d"),t.mars3d=n),n||console.error("请引入mars3d库，可参考教程：http://mars3d.cn/dev/guide/basics/import.html");let r=t.h337;function i(t,e){var n=a();return(i=function(t,e){return n[t-=220]})(t,e)}function a(){var t=["diffHeight","max","getOwnPropertyDescriptor","concat","lng","push","_renderer","show","mars3d","yellow","blue","_map","uniform sampler2D image;\nczm_material czm_getMaterial(czm_materialInput materialInput){\n  czm_material material = czm_getDefaultMaterial(materialInput);\n  vec2 st = materialInput.st;\n  vec4 colorImage = texture2D(image,st);\n  if(colorImage.rgb == vec3(1.0) || colorImage.rgb == vec3(0.0)){\n    discard;\n  }\n  material.diffuse = colorImage.rgb;\n  material.alpha = colorImage.a;\n  return material;\n}\n","removeChild","KEEP","positions","getOwnPropertyDescriptors","radius","green","clientWidth","Util","flyTo","formatRectangle","default","__esModule","style","LatLngPoint","992XIRbRJ","defaultValue","configure","RenderState","addPosition","granularity","defineProperties","addGraphic","floor","maxCanvasSize","prototype","div","constructor","6957174EYPTGn","forEach","writable","5851504iExxUd",";\n            p +=vec4(disPos,0.0);\n            gl_Position = czm_modelViewProjectionRelativeToEye * p;\n          }\n        ","value","parse","heat","keys","defineProperty","Cannot call a class as a function","px;","PointTrans","ALWAYS","BaseLayer","133070ISiBqX","_createArcGraphic","abs","body","_heat","Super expression must either be null or a function","getPrototypeOf","xmax","BlendingState","_addedHook","fromCache","GraphicLayer","addLayer","iterator","mercator2lonlat","StencilOperation","Derived constructors may only return object or undefined","configurable","4642332HMGyhC","\n        margin:0;\n        width:","Rectangle","lat","apply","_last_mBounds","999335aMkSLl","VERTEX_FORMAT","min","REPLACE","arcRadiusScale","arcBlurScale","mars3d-heatmap","getRectangle","register","xmin","_graphic","container","coordinates","canvas","Material","layer","px;\n        height:","round","PolyUtil","DomUtil","1017ZCjqfE","rgb(255,255,255)","Module","HeatLayer","exports","options","_removedHook","toStringTag","_last_heatData","create","setPrototypeOf","7PjvIGM","filter","Color","removeGraphic","_mountedHook","_createGraphic","1087ldowkJ","_updatePositionsHook","undefined","merge","1768026wDbWod","Cesium","RectanglePrimitive","StencilFunction","heatStyle","removeLayer","gradientArc","splitNum","_layer","valueOf","EllipsoidSurfaceAppearance","call","object","function","rgb(140,140,140)","height","image/png","enumerable","h337","string","symbol","clientHeight","_rectangle","this hasn't been initialised - super() hasn't been called","ymax","clear","_positions","arc","minCanvasSize","construct","key","fromDegrees","getOwnPropertySymbols","ymin","length"];return(a=function(){return t})()}r||"object"!=typeof exports||(r=require("heatmap.js"),t.h337=r),function(t,e){for(var n=i,r=t();;)try{if(905714===-parseInt(n(262))/1*(parseInt(n(328))/2)+-parseInt(n(266))/3+-parseInt(n(374))/4+-parseInt(n(225))/5+parseInt(n(341))/6+-parseInt(n(256))/7*(-parseInt(n(344))/8)+parseInt(n(245))/9*(parseInt(n(356))/10))break;r.push(r.shift())}catch(t){r.push(r.shift())}}(a),t.mars3dHeatmap=function(t){var e={};function n(r){var a=i;if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r][a(277)](o[a(249)],o,o.exports,n),o.l=!0,o[a(249)]}return n.m=t,n.c=e,n.d=function(t,e,r){var a=i;n.o(t,e)||Object[a(350)](t,e,{enumerable:!0,get:r})},n.r=function(t){var e=i;"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object[e(350)](t,Symbol[e(252)],{value:e(247)}),Object[e(350)](t,"__esModule",{value:!0})},n.t=function(t,e){var r=i;if(1&e&&(t=n(t)),8&e)return t;if(4&e&&r(278)==typeof t&&t&&t[r(325)])return t;var a=Object[r(254)](null);if(n.r(a),Object[r(350)](a,r(324),{enumerable:!0,value:t}),2&e&&r(285)!=typeof t)for(var o in t)n.d(a,o,function(e){return t[e]}.bind(null,o));return a},n.n=function(t){var e=i,r=t&&t[e(325)]?function(){return t[e(324)]}:function(){return t};return n.d(r,"a",r),r},n.o=function(t,e){var n=i;return Object[n(338)].hasOwnProperty[n(277)](t,e)},n.p="",n(n.s=13)}([function(e,n){var r=i;e[r(249)]=t[r(267)]},function(e,n){var r=i;e[r(249)]=t[r(309)]},function(t,e){var n=i;function r(e){var n=i;return t[n(249)]=r=Object[n(255)]?Object[n(362)]:function(t){var e=n;return t.__proto__||Object[e(362)](t)},t[n(249)][n(324)]=t[n(249)],t[n(249)][n(325)]=!0,r(e)}t[n(249)]=r,t.exports[n(324)]=t[n(249)],t.exports.__esModule=!0},function(t,e){var n=i;t[n(249)]=n(313)},function(t,e){var n=i;t[n(249)]=function(t,e,r){return e in t?Object[n(350)](t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t},t.exports[n(324)]=t[n(249)],t[n(249)][n(325)]=!0},function(t,e){var n=i;t[n(249)]=function(t,e){if(!(t instanceof e))throw new TypeError(n(351))},t[n(249)][n(324)]=t[n(249)],t.exports[n(325)]=!0},function(t,e){var n=i;function r(t,e){for(var n=i,r=0;r<e[n(300)];r++){var a=e[r];a.enumerable=a[n(283)]||!1,a[n(373)]=!0,"value"in a&&(a[n(343)]=!0),Object.defineProperty(t,a[n(296)],a)}}t[n(249)]=function(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t},t[n(249)][n(324)]=t[n(249)],t[n(249)].__esModule=!0},function(t,e,n){var r=i,a=n(9);t[r(249)]=function(t,e){var n=r;if("function"!=typeof e&&null!==e)throw new TypeError(n(361));t[n(338)]=Object[n(254)](e&&e[n(338)],{constructor:{value:t,writable:!0,configurable:!0}}),e&&a(t,e)},t[r(249)].default=t[r(249)],t[r(249)].__esModule=!0},function(t,e,n){var r=i,a=n(10)[r(324)],o=n(11);t.exports=function(t,e){var n=r;if(e&&(n(278)===a(e)||n(279)==typeof e))return e;if(void 0!==e)throw new TypeError(n(372));return o(t)},t[r(249)].default=t[r(249)],t[r(249)][r(325)]=!0},function(t,e){var n=i;function r(e,n){var a=i;return t[a(249)]=r=Object[a(255)]||function(t,e){return t.__proto__=e,t},t.exports[a(324)]=t.exports,t[a(249)][a(325)]=!0,r(e,n)}t[n(249)]=r,t[n(249)][n(324)]=t[n(249)],t.exports[n(325)]=!0},function(t,e){var n=i;function r(e){var n=i;return"function"==typeof Symbol&&n(286)==typeof Symbol[n(369)]?(t[n(249)]=r=function(t){return typeof t},t.exports.default=t[n(249)],t[n(249)][n(325)]=!0):(t[n(249)]=r=function(t){var e=n;return t&&e(279)==typeof Symbol&&t[e(340)]===Symbol&&t!==Symbol.prototype?e(286):typeof t},t[n(249)][n(324)]=t[n(249)],t[n(249)][n(325)]=!0),r(e)}t[n(249)]=r,t.exports[n(324)]=t.exports,t[n(249)].__esModule=!0},function(t,e){var n=i;t[n(249)]=function(t){if(void 0===t)throw new ReferenceError(n(289));return t},t.exports[n(324)]=t[n(249)],t[n(249)].__esModule=!0},function(e,n){var r=i;e[r(249)]=t[r(284)]},function(t,e,n){"use strict";var r=i;n.r(e),n.d(e,r(248),(function(){return j}));var a=n(4),o=n.n(a),s=n(5),c=n.n(s),u=n(6),l=n.n(u),h=n(7),f=n.n(h),p=n(8),m=n.n(p),v=n(2),d=n.n(v),y=n(0),g=n(1),b=n(3),_=n.n(b);function x(t,e){var n=r,i=Object[n(349)](t);if(Object[n(298)]){var a=Object.getOwnPropertySymbols(t);e&&(a=a[n(257)]((function(e){var r=n;return Object.getOwnPropertyDescriptor(t,e)[r(283)]}))),i[n(306)][n(223)](i,a)}return i}function S(t){for(var e=r,n=1;n<arguments[e(300)];n++){var i=null!=arguments[n]?arguments[n]:{};n%2?x(Object(i),!0)[e(342)]((function(e){o()(t,e,i[e])})):Object[e(317)]?Object[e(334)](t,Object[e(317)](i)):x(Object(i))[e(342)]((function(n){var r=e;Object[r(350)](t,n,Object[r(303)](i,n))}))}return t}var O=n(12),M=g[r(240)][r(355)],w={maxOpacity:.8,minOpacity:.1,blur:.85,radius:25,gradient:{.4:r(311),.6:r(319),.8:r(310),.9:"red"}},P={arcRadiusScale:1.5,arcBlurScale:1.5,vertexFormat:y[r(276)][r(226)]},j=function(t){var e=r;f()(s,t);var n,a,o=(n=s,a=function(){var t=i;if(t(264)==typeof Reflect||!Reflect[t(295)])return!1;if(Reflect[t(295)].sham)return!1;if(t(279)==typeof Proxy)return!0;try{return Boolean[t(338)][t(275)].call(Reflect[t(295)](Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=i,r=d()(n);if(a){var o=d()(this).constructor;t=Reflect[e(295)](r,arguments,o)}else t=r[e(223)](this,arguments);return m()(this,t)});function s(){var t,e=i,n=arguments[e(300)]>0&&void 0!==arguments[0]?arguments[0]:{};return c()(this,s),(t=o[e(277)](this,n)).options[e(337)]=y[e(329)](t.options[e(337)],document[e(359)][e(320)]),t[e(250)][e(337)]=Math[e(227)](t.options[e(337)],5e3),t[e(250)][e(294)]=y[e(329)](t[e(250)][e(294)],document[e(359)][e(287)]),t[e(250)].minCanvasSize=Math[e(302)](t[e(250)].minCanvasSize,700),t[e(250)][e(270)]=S(S({},w),t.options[e(270)]||{}),t.options.style=S(S({},P),t[e(250)].style||{}),t}return l()(s,[{key:"layer",get:function(){return this[i(274)]}},{key:e(270),get:function(){return this[e(250)].heatStyle},set:function(t){var n=e;this[n(250)].heatStyle=g.Util.merge(this[n(250)][n(270)],t),this._heat&&this[n(360)][n(330)](this[n(250)][n(270)])}},{key:e(326),get:function(){return this[e(250)].style},set:function(t){var n=e;this[n(250)][n(326)]=g[n(321)][n(265)](this[n(250)][n(326)],t)}},{key:"positions",get:function(){return this[e(292)]},set:function(t){var n=e;this._positions=t,this[n(263)]()}},{key:e(237),get:function(){var t=e,n=[];return this.points[t(342)]((function(e){n[t(306)](e.toArray())})),n}},{key:"rectangle",get:function(){return this[e(288)]}},{key:"_setOptionsHook",value:function(t){var n=e;t.positions&&(this.positions=t[n(316)])}},{key:e(260),value:function(){var t=e;this[t(274)]=new(g.layer[t(367)])({noLayerManage:!0}),this[t(274)]._mars3d_private=!0}},{key:e(365),value:function(){var t=e;this[t(312)][t(368)](this[t(274)]),this[t(250)][t(316)]&&(this[t(316)]=this.options[t(316)]),this[t(250)][t(322)]&&this.flyToByAnimationEnd()}},{key:e(251),value:function(){var t=e;this.heatStyle[t(236)]&&(this[t(312)][t(236)][t(314)](this[t(270)][t(236)]),delete this[t(270)].container),this[t(291)](),this[t(312)][t(271)](this._layer)}},{key:e(332),value:function(t){var n=e;this[n(292)]=this._positions||[],this._positions[n(306)](t),this[n(263)]()}},{key:"clear",value:function(){var t=e;this[t(235)]&&(this[t(274)][t(259)](this[t(235)],!0),delete this[t(235)])}},{key:e(263),value:function(){var t=e;if(!this[t(308)]||!this[t(312)]||!this.positions||0===this[t(316)][t(300)])return this;var n=this._getHeatCanvasImg();return this[t(326)][t(293)]?this[t(357)](n):this[t(261)](n),this}},{key:"_getHeatCanvasImg",value:function(){var t,n,r,i,a,o,s=e,c=this[s(292)],u=[];c.forEach((function(t){var e=s,n=g[e(327)][e(347)](t);n&&(n[e(346)]=t[e(346)]||1,null==r?(r=n[e(305)],i=n.lng,a=n[e(222)],o=n[e(222)]):(r=Math.min(r,n[e(305)]),i=Math[e(302)](i,n[e(305)]),a=Math[e(227)](a,n[e(222)]),o=Math[e(302)](o,n.lat)),u.push(n))}));var l,h,f,p=1.5*this.heatStyle.radius,m={xmin:r,xmax:i,ymin:a,ymax:o},v=(l=m,h=g[s(353)].lonlat2mercator([l[s(234)],l[s(299)]]),f=g[s(353)].lonlat2mercator([l.xmax,l.ymax]),{xmin:h[0],ymin:h[1],xmax:f[0],ymax:f[1]}),d=Math[s(358)](v[s(363)]-v[s(234)]),b=Math[s(358)](v[s(290)]-v.ymin),_=Math[s(302)](d,b),x=Math[s(227)](d,b);this._mBoundsMax=_;var S=1;_>this[s(250)][s(337)]?x/(S=_/this[s(250)][s(337)])<this[s(250)].minCanvasSize&&(S=x/this.options[s(294)]):x<this.options[s(294)]&&_/(S=x/this[s(250)][s(294)])>this.options[s(337)]&&(S=_/this[s(250)].maxCanvasSize);var M,w,P,j,E=d/S+2*p,k=b/S+2*p,C=p*S;v[s(234)]-=C,v[s(299)]-=C,v[s(363)]+=C,v[s(290)]+=C,M=v,P=g[(w=s)(353)][w(370)]([M[w(234)],M.ymin]),j=g[w(353)][w(370)]([M.xmax,M.ymax]),m={xmin:P[0],ymin:P[1],xmax:j[0],ymax:j[1]},this[s(288)]=y[s(221)][s(297)](m[s(234)],m[s(299)],m[s(363)],m[s(290)]);var I=null!==(t=u[0][s(346)])&&void 0!==t?t:1,R=null!==(n=u[0].value)&&void 0!==n?n:0,D=[];u.forEach((function(t){var e=s,n=g.PointTrans.lonlat2mercator([t[e(305)],t[e(222)]]),r=t.value||1,i=Math[e(242)]((n[0]-v[e(234)])/S),a=Math[e(242)]((v[e(290)]-n[1])/S);I=Math[e(302)](I,r),R=Math.min(R,r),D[e(306)]({x:i,y:a,value:r})}));var z={min:R,max:I,data:D};this[s(253)]=z,this[s(224)]&&v[s(234)]===this._last_mBounds.xmin&&v[s(299)]===this[s(224)][s(299)]&&v[s(363)]===this[s(224)][s(363)]&&v[s(290)]===this[s(224)][s(290)]||(this._last_mBounds=v,this[s(270)][s(236)]||(this.heatStyle[s(236)]=g[s(244)][s(254)](s(339),s(231),this._map[s(236)])),this[s(270)][s(236)][s(326)].cssText=s(220)[s(304)](E,s(241)).concat(k,s(352)),this[s(360)]?this[s(360)].configure(this[s(270)]):this[s(360)]=O[s(254)](this[s(270)])),this[s(360)].setData(z);var L=this[s(360)][s(307)][s(238)].toDataURL(s(282));return this[s(270)][s(236)]&&(this[s(312)].container[s(314)](this.heatStyle[s(236)]),delete this[s(270)].container),L}},{key:e(261),value:function(t){var n=e;this[n(291)](),this[n(235)]=new g.graphic.RectanglePrimitive(S(S({},this[n(250)]),{},{rectangle:this._rectangle,appearance:new y.EllipsoidSurfaceAppearance({material:new(y[n(239)])({fabric:{uniforms:{image:t},source:_.a},translucent:!0}),flat:!0})})),this._layer[n(335)](this[n(235)])}},{key:e(357),value:function(t){var n=e;this.clear(),this[n(360)].configure(S(S({},this.heatStyle),{},{radius:this[n(270)][n(318)]*this[n(326)][n(229)],blur:this[n(270)].blur*this[n(326)][n(230)],gradient:this.heatStyle[n(272)]||{.25:"rgb(0,0,0)",.55:n(280),.85:"rgb(216,216,216)",1:n(246)}}));var r=this[n(360)][n(307)][n(238)].toDataURL(n(282)),i=y[n(331)][n(366)]({cull:{enabled:!0},depthTest:{enabled:!0},stencilTest:{enabled:!0,frontFunction:y[n(269)][n(354)],frontOperation:{fail:y.StencilOperation.KEEP,zFail:y[n(371)].KEEP,zPass:y[n(371)].REPLACE},backFunction:y[n(269)][n(354)],backOperation:{fail:y.StencilOperation.KEEP,zFail:y[n(371)][n(315)],zPass:y[n(371)][n(228)]},reference:2,mask:2},blending:y[n(364)].ALPHA_BLEND}),a=Math[n(336)](y[n(329)](this[n(326)][n(301)],.02*this._mBoundsMax))+.1,o=y.defaultValue(this.style[n(273)],100),s=Math[n(302)](this._rectangle[n(281)],this._rectangle.width);this[n(326)][n(333)]=s/=o,this[n(235)]=new(g.graphic[n(268)])(S(S({},this[n(250)]),{},{rectangle:this._rectangle,appearance:new(y[n(276)])({aboveGround:!0,renderState:i,material:new(y[n(239)])({fabric:{uniforms:{image:t,repeat:new y.Cartesian2(1,1),color:new(y[n(258)])(1,1,1,.01),bumpMap:r},source:_.a},translucent:!0}),vertexShaderSource:"attribute vec3 position3DHigh;\n          attribute vec3 position3DLow;\n          attribute vec2 st;\n          attribute float batchId;\n          uniform sampler2D bumpMap_3;\n          varying vec3 v_positionMC;\n          varying vec3 v_positionEC;\n          varying vec2 v_st;\n          void main()\n          {\n            vec4 p = czm_computePosition();\n            v_positionMC = position3DHigh + position3DLow;\n            v_positionEC = (czm_modelViewRelativeToEye * p).xyz;\n            v_st = st;\n            vec4 color = texture2D(bumpMap_3, v_st);\n            float centerBump = distance(vec3(0.0),color.rgb);\n            vec3 upDir = normalize(v_positionMC.xyz);\n            vec3 disPos = upDir * centerBump * "[n(304)](a,n(345)),flat:!0})})),this[n(274)][n(335)](this._graphic)}},{key:e(232),value:function(t){var n=e;return null!=t&&t.isFormat&&this[n(288)]?g[n(243)][n(323)](this._rectangle):this[n(288)]}}]),s}(M);g[r(240)][r(248)]=j,g.LayerUtil[r(233)](r(348),j)}])}(window);
//# sourceMappingURL=/sm/1dd2f337f52cbcda15b46855c7c272ec34ed367600dd02a2210c24e4209b3aaf.map