/**
 * Mars3D平台插件,结合mapv可视化功能插件  mars3d-mapv
 *
 * 版本信息：v3.7.5
 * 编译日期：2024-03-05 20:02:24
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2024-01-15
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mapv || require('mapv')), (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mapv', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-mapv"] = {}, global.mapv, global.mars3d));
})(this, (function (exports, mapv, mars3d) { 
'use strict';const _0x48fb8f=_0x5138;(function(_0x29fcb8,_0x4503e7){const _0x3b41ed={_0xb85902:0xf7,_0x5e6ca7:0xad,_0x1fdd80:0xb3,_0x20ee67:0xbc,_0x10d626:0xc4,_0x1f7f92:0xcc,_0x3d8f45:0x93,_0x5a7040:0xef},_0x1b569d=_0x5138,_0x431aa5=_0x29fcb8();while(!![]){try{const _0x2e7406=-parseInt(_0x1b569d(_0x3b41ed._0xb85902))/0x1+-parseInt(_0x1b569d(_0x3b41ed._0x5e6ca7))/0x2*(-parseInt(_0x1b569d(_0x3b41ed._0x1fdd80))/0x3)+-parseInt(_0x1b569d(0xe2))/0x4+-parseInt(_0x1b569d(_0x3b41ed._0x20ee67))/0x5*(parseInt(_0x1b569d(_0x3b41ed._0x10d626))/0x6)+-parseInt(_0x1b569d(_0x3b41ed._0x1f7f92))/0x7+parseInt(_0x1b569d(_0x3b41ed._0x3d8f45))/0x8+-parseInt(_0x1b569d(_0x3b41ed._0x5a7040))/0x9*(-parseInt(_0x1b569d(0xbd))/0xa);if(_0x2e7406===_0x4503e7)break;else _0x431aa5['push'](_0x431aa5['shift']());}catch(_0xe23669){_0x431aa5['push'](_0x431aa5['shift']());}}}(_0x2407,0xcbf43));function _interopNamespace(_0x529550){const _0x2b4016={_0x46ea53:0xb9},_0x55a453={_0x401a22:0xc5},_0x8e211c=_0x5138;if(_0x529550&&_0x529550[_0x8e211c(_0x2b4016._0x46ea53)])return _0x529550;var _0x223f1b=Object['create'](null);return _0x529550&&Object['keys'](_0x529550)['forEach'](function(_0x4f4d0a){const _0x2ecca7=_0x8e211c;if(_0x4f4d0a!=='default'){var _0x5a6d59=Object['getOwnPropertyDescriptor'](_0x529550,_0x4f4d0a);Object[_0x2ecca7(_0x55a453._0x401a22)](_0x223f1b,_0x4f4d0a,_0x5a6d59['get']?_0x5a6d59:{'enumerable':!![],'get':function(){return _0x529550[_0x4f4d0a];}});}}),_0x223f1b['default']=_0x529550,_0x223f1b;}var mapv__namespace=_interopNamespace(mapv),mars3d__namespace=_interopNamespace(mars3d);const Cesium$1=mars3d__namespace['Cesium'],baiduMapLayer=mapv__namespace?mapv__namespace['baiduMapLayer']:null,BaseLayer$1=baiduMapLayer?baiduMapLayer['__proto__']:Function;function _0x5138(_0x20025e,_0x3017ba){const _0x2407c2=_0x2407();return _0x5138=function(_0x5138b8,_0x3a5936){_0x5138b8=_0x5138b8-0x91;let _0x2c6f0f=_0x2407c2[_0x5138b8];return _0x2c6f0f;},_0x5138(_0x20025e,_0x3017ba);}class MapVRenderer extends BaseLayer$1{constructor(_0x476255,_0x1ed9b0,_0x2759c2,_0x16961a){const _0x23433d={_0x30e9aa:0xa1},_0x456368=_0x5138;super(_0x476255,_0x1ed9b0,_0x2759c2);if(!BaseLayer$1)return;this['map']=_0x476255,this['scene']=_0x476255['scene'],this[_0x456368(_0x23433d._0x30e9aa)]=_0x1ed9b0,_0x2759c2=_0x2759c2||{},this['init'](_0x2759c2),this['argCheck'](_0x2759c2),this['initDevicePixelRatio'](),this['canvasLayer']=_0x16961a,this[_0x456368(0xc0)]=!0x1,this['animation']=_0x2759c2['animation'];}['initDevicePixelRatio'](){this['devicePixelRatio']=window['devicePixelRatio']||0x1;}[_0x48fb8f(0x9a)](){}[_0x48fb8f(0xe5)](){const _0x5c67b4={_0x16abf1:0xa6},_0xecfa83=_0x48fb8f,_0x1f28f0=this[_0xecfa83(0xa7)][_0xecfa83(0xe9)];this[_0xecfa83(0xc8)]()&&this['animator']&&(this['steps'][_0xecfa83(_0x5c67b4._0x16abf1)]=_0x1f28f0['stepsRange']['start']);}['animatorMoveendEvent'](){this['isEnabledTime']()&&this['animator'];}[_0x48fb8f(0xba)](){return this['canvasLayer']['canvas']['getContext'](this['context']);}[_0x48fb8f(0xdd)](_0x515489){const _0x5ce1d5={_0x1df426:0xf2,_0x306758:0xb4},_0x346a43=_0x48fb8f;this['options']=_0x515489,this[_0x346a43(_0x5ce1d5._0x1df426)](_0x515489),this['context']=this['options']['context']||'2d',Cesium$1[_0x346a43(0xc2)](this['options'][_0x346a43(_0x5ce1d5._0x306758)])&&this['canvasLayer']&&this['canvasLayer']['setZIndex']&&this['canvasLayer']['setZIndex'](this['options'][_0x346a43(0xb4)]),this['initAnimator']();}['_canvasUpdate'](_0x5022ad){const _0x55d118={_0x48fbbc:0xb5,_0x52f805:0xa7,_0x339d1a:0xa7,_0x319721:0x96},_0x45110b={_0x2779a4:0xd0,_0x40060e:0xd0},_0x44b504={_0x40cf28:0xd5,_0x30a1ac:0x95,_0x1022c1:0xc9},_0x568445=_0x48fb8f,_0x4d1896=this['scene'];if(this['canvasLayer']&&!this['stopAniamation']){const _0x4d4e4e=this['options']['animation'],_0x551a03=this['getContext']();if(this['isEnabledTime']()){if(void 0x0===_0x5022ad)return void this[_0x568445(0xbb)](_0x551a03);this['context']==='2d'&&(_0x551a03['save'](),_0x551a03['globalCompositeOperation']='destination-out',_0x551a03[_0x568445(0xd3)]=_0x568445(0xf9),_0x551a03['fillRect'](0x0,0x0,_0x551a03['canvas']['width'],_0x551a03['canvas'][_0x568445(0xa9)]),_0x551a03['restore']());}else this[_0x568445(0xbb)](_0x551a03);if(this['context']==='2d')for(const _0x46ff10 in this['options']){_0x551a03[_0x46ff10]=this[_0x568445(0xa7)][_0x46ff10];}else _0x551a03['clear'](_0x551a03[_0x568445(0xdb)]);const _0x4658e7={'transferCoordinate':function(_0x4040c0){const _0x171faf=_0x568445,_0x4900d0=null;let _0x40d905=_0x4d1896[_0x171faf(_0x44b504._0x40cf28)];_0x4d1896['mapvAutoHeight']&&(_0x40d905=_0x4d1896['getHeight'](Cesium$1['Cartographic']['fromDegrees'](_0x4040c0[0x0],_0x4040c0[0x1])));const _0x521155=Cesium$1['Cartesian3']['fromDegrees'](_0x4040c0[0x0],_0x4040c0[0x1],_0x40d905);if(!_0x521155)return _0x4900d0;const _0x103fa8=_0x4d1896['cartesianToCanvasCoordinates'](_0x521155);if(!_0x103fa8)return _0x4900d0;if(_0x4d1896['mapvDepthTest']&&_0x4d1896[_0x171faf(_0x44b504._0x30a1ac)]===Cesium$1[_0x171faf(0xc7)]['SCENE3D']){const _0x17ec46=new Cesium$1[(_0x171faf(0xcf))](_0x4d1896['globe']['ellipsoid'],_0x4d1896['camera'][_0x171faf(_0x44b504._0x1022c1)]),_0x4baf0e=_0x17ec46['isPointVisible'](_0x521155);if(!_0x4baf0e)return _0x4900d0;}return[_0x103fa8['x'],_0x103fa8['y']];}};void 0x0!==_0x5022ad&&(_0x4658e7['filter']=function(_0x4e47fa){const _0x16a685=_0x568445,_0x263073=_0x4d4e4e['trails']||0xa;return!!(_0x5022ad&&_0x4e47fa[_0x16a685(_0x45110b._0x2779a4)]>_0x5022ad-_0x263073&&_0x4e47fa[_0x16a685(_0x45110b._0x40060e)]<_0x5022ad);});const _0x2046cc=this['dataSet']['get'](_0x4658e7);this[_0x568445(_0x55d118._0x48fbbc)](_0x2046cc),this[_0x568445(_0x55d118._0x52f805)]['unit']==='m'&&this[_0x568445(_0x55d118._0x339d1a)]['size'],this['options']['_size']=this['options']['size'];const _0x7c3bb3=_0x4d1896[_0x568445(_0x55d118._0x319721)](Cesium$1['Cartesian3']['fromDegrees'](0x0,0x0));if(!_0x7c3bb3)return;this['drawContext'](_0x551a03,new mapv__namespace[(_0x568445(0x94))](_0x2046cc),this['options'],_0x7c3bb3),this[_0x568445(_0x55d118._0x339d1a)]['updateCallback']&&this['options']['updateCallback'](_0x5022ad);}}[_0x48fb8f(0xf0)](_0x4a67ba,_0x5a4fec){const _0x330878={_0x4cd39e:0xf3},_0x5421e9=_0x48fb8f;let _0x5253bb=_0x4a67ba;_0x5253bb&&_0x5253bb['get']&&(_0x5253bb=_0x5253bb['get']()),void 0x0!==_0x5253bb&&this['dataSet']['set'](_0x5253bb),super[_0x5421e9(_0x330878._0x4cd39e)]({'options':_0x5a4fec});}['addData'](_0x142fee,_0x3784ce){let _0x59d146=_0x142fee;_0x142fee&&_0x142fee['get']&&(_0x59d146=_0x142fee['get']()),this['dataSet']['add'](_0x59d146),this['update']({'options':_0x3784ce});}['getData'](){return this['dataSet'];}['removeData'](_0x224db2){const _0x326783={_0xfbbe7f:0xaa},_0x14b010=_0x48fb8f;if(this['dataSet']){const _0xfca36e=this['dataSet']['get']({'filter':function(_0x59d797){return _0x224db2==null||typeof _0x224db2!=='function'||!_0x224db2(_0x59d797);}});this['dataSet'][_0x14b010(_0x326783._0xfbbe7f)](_0xfca36e),this['update']({'options':null});}}['clearData'](){const _0x55279d={_0x2d434b:0xbb},_0x1c6be4=_0x48fb8f;this['dataSet']&&this['dataSet'][_0x1c6be4(_0x55279d._0x2d434b)](),this['update']({'options':null});}[_0x48fb8f(0xc1)](){this['canvasLayer']['draw']();}['clear'](_0x2b9800){const _0x5b5dc5={_0x3fc6ef:0xab,_0x3cbd3a:0xa9},_0xc70517=_0x48fb8f;_0x2b9800&&_0x2b9800[_0xc70517(0xdc)]&&_0x2b9800[_0xc70517(0xdc)](0x0,0x0,_0x2b9800[_0xc70517(0xa0)][_0xc70517(_0x5b5dc5._0x3fc6ef)],_0x2b9800['canvas'][_0xc70517(_0x5b5dc5._0x3cbd3a)]);}['destroy'](){const _0x2f2e20={_0xc2f9d0:0xba,_0x3d2fa7:0xed,_0x5d9201:0xed},_0x58ecdb=_0x48fb8f;this['clear'](this[_0x58ecdb(_0x2f2e20._0xc2f9d0)]()),this['clearData'](),this[_0x58ecdb(0xed)]&&this[_0x58ecdb(_0x2f2e20._0x3d2fa7)]['stop'](),this[_0x58ecdb(_0x2f2e20._0x5d9201)]=null,this['canvasLayer']=null;}}if(mapv__namespace!==null&&mapv__namespace!==void 0x0&&mapv__namespace['DataSet'])mapv__namespace[_0x48fb8f(0x94)][_0x48fb8f(0xec)]['transferCoordinate']=function(_0x5b1247,_0x23b14f,_0x2d2e82,_0x247792){const _0x1009c4={_0x19697f:0xd1,_0x1d6663:0xe0,_0x3041e5:0xda,_0xbee84a:0xeb},_0x364a86={_0x2cc868:0xeb},_0xb1c9cf=_0x48fb8f;_0x247792=_0x247792||_0xb1c9cf(0xea),_0x2d2e82=_0x2d2e82||_0xb1c9cf(_0x1009c4._0x19697f);for(let _0xf2493=0x0;_0xf2493<_0x5b1247[_0xb1c9cf(_0x1009c4._0x1d6663)];_0xf2493++){const _0x4d740e=_0x5b1247[_0xf2493]['geometry'],_0x5d5a16=_0x4d740e[_0x2d2e82];switch(_0x4d740e['type']){case'Point':{const _0x3b67cc=_0x23b14f(_0x5d5a16);_0x3b67cc?_0x4d740e[_0x247792]=_0x3b67cc:_0x4d740e[_0x247792]=[-0x3e7,-0x3e7];}break;case _0xb1c9cf(_0x1009c4._0x3041e5):{const _0xb1a50b=[];for(let _0x402b4e=0x0;_0x402b4e<_0x5d5a16['length'];_0x402b4e++){const _0x1a43cc=_0x23b14f(_0x5d5a16[_0x402b4e]);_0x1a43cc&&_0xb1a50b[_0xb1c9cf(_0x1009c4._0xbee84a)](_0x1a43cc);}_0x4d740e[_0x247792]=_0xb1a50b;}break;case'MultiLineString':case'Polygon':{const _0x2a413e=_0x2e5c66(_0x5d5a16);_0x4d740e[_0x247792]=_0x2a413e;}break;case'MultiPolygon':{const _0x56a060=[];for(let _0x44384e=0x0;_0x44384e<_0x5d5a16['length'];_0x44384e++){const _0x177700=_0x2e5c66(_0x5d5a16[_0x44384e]);_0x177700['length']>0x0&&_0x56a060['push'](_0x177700);}_0x4d740e[_0x247792]=_0x56a060;}break;}}function _0x2e5c66(_0x27f9ea){const _0x18ade1=_0xb1c9cf,_0x2244ea=[];for(let _0xed44f4=0x0;_0xed44f4<_0x27f9ea['length'];_0xed44f4++){const _0x2ec0c8=_0x27f9ea[_0xed44f4],_0xa2930d=[];for(let _0x57727a=0x0;_0x57727a<_0x2ec0c8['length'];_0x57727a++){const _0x5c0050=_0x23b14f(_0x2ec0c8[_0x57727a]);_0x5c0050&&_0xa2930d[_0x18ade1(_0x364a86._0x2cc868)](_0x5c0050);}_0xa2930d['length']>0x0&&_0x2244ea[_0x18ade1(_0x364a86._0x2cc868)](_0xa2930d);}return _0x2244ea;}return _0x5b1247;};else throw new Error(_0x48fb8f(0xe4));function _0x2407(){const _0x3f0216=['rgba(0,\x200,\x200,\x20.1)','_onMoveStartEvent','_mapVRenderer','4824496wPVDpX','DataSet','mode','cartesianToCanvasCoordinates','methods','_reset','getData','addAnimatorEvent','postRender','EventType','concat','_data','_onMapClick','canvas','dataSet','_pointerEvents','mouseMove','mapvAutoHeight','0px','step','options','_onMapMouseMove','height','set','width','scale','116KjomaS','keys','register','Util','clampToGround','addData','19059MELchg','zIndex','processData','absolute','none','style','__esModule','getContext','clear','285235YyQrRl','470BAdnQS','pointerEvents','mousemove','stopAniamation','draw','defined','mousemoveEvent','168ddOYED','defineProperty','parentElement','SceneMode','isEnabledTime','positionWC','ymax','removeChild','7501494UiOscN','resize','display','EllipsoidalOccluder','time','coordinates','click','fillStyle','_canvasUpdate','mapvFixedHeight','bindEvent','scene','cameraMoveStart','Cesium','LineString','COLOR_BUFFER_BIT','clearRect','init','remove','cameraMoveEnd','length','mouseDown','574408BPXCFe','layer','请引入\x20mapv\x20库\x20','animatorMovestartEvent','BaseLayer','_addedHook','off','animation','_coordinates','push','prototype','animator','_setOptionsHook','779913Qtvtiq','updateData','all','initDataRange','update','_map','_onMoveEndEvent','_cache_event','1396460LHknmG','render'];_0x2407=function(){return _0x3f0216;};return _0x2407();}const Cesium=mars3d__namespace[_0x48fb8f(0xd9)],BaseLayer=mars3d__namespace['layer'][_0x48fb8f(0xe6)];class MapVLayer extends BaseLayer{constructor(_0x1346df,_0x4d9ab6){const _0x1898bd=_0x48fb8f;super(_0x1346df),this['_pointerEvents']=this['options']['pointerEvents'],this['dataSet']=_0x4d9ab6||new mapv__namespace['DataSet'](_0x1346df['data']),this[_0x1898bd(0xa0)]=null;}get['pointerEvents'](){return this['_pointerEvents'];}set['pointerEvents'](_0x2dd579){const _0x206f3c={_0x33ad7b:0xb8,_0xae724d:0xb7},_0x16ccd0=_0x48fb8f;this[_0x16ccd0(0xa2)]=_0x2dd579,this['canvas']&&(_0x2dd579?this['canvas']['style'][_0x16ccd0(0xbe)]=_0x16ccd0(0xf1):this['canvas'][_0x16ccd0(_0x206f3c._0x33ad7b)][_0x16ccd0(0xbe)]=_0x16ccd0(_0x206f3c._0xae724d));}['_showHook'](_0x4ac918){const _0x3713da={_0x4d2120:0xb8,_0x644fc2:0xce},_0x21d366=_0x48fb8f;_0x4ac918?this['canvas'][_0x21d366(_0x3713da._0x4d2120)][_0x21d366(_0x3713da._0x644fc2)]='block':this['canvas']['style']['display']='none';}['_mountedHook'](){const _0x5d0b04={_0x2d1963:0xa7},_0x375f7e=_0x48fb8f;this['_map']['scene']['mapvDepthTest']=this[_0x375f7e(_0x5d0b04._0x2d1963)]['depthTest']??!![],this[_0x375f7e(0xf4)][_0x375f7e(0xd7)][_0x375f7e(0xa4)]=this['options'][_0x375f7e(0xb1)]??![],this['_map']['scene']['mapvFixedHeight']=this['options']['fixedHeight']??0x0;}['_addedHook'](){const _0x4e1215={_0x365e60:0xa1,_0x77a3dd:0xa1,_0x129d74:0x92,_0x39f39e:0xa1},_0x5e0dde=_0x48fb8f;this['dataSet']&&(!this[_0x5e0dde(_0x4e1215._0x365e60)]['_data']||this['dataSet']['_data']['length']===0x0)&&(this[_0x5e0dde(_0x4e1215._0x77a3dd)]['_data']=[][_0x5e0dde(0x9d)](this['dataSet']['_dataCache'])),this[_0x5e0dde(_0x4e1215._0x129d74)]=new MapVRenderer(this['_map'],this[_0x5e0dde(_0x4e1215._0x39f39e)],this[_0x5e0dde(0xa7)],this),this['initDevicePixelRatio'](),this['canvas']=this['_createCanvas'](),this['render']=this[_0x5e0dde(0xf8)]['bind'](this),this[_0x5e0dde(0xd6)](),this['_reset']();}['_removedHook'](){const _0x2643ec=_0x48fb8f;this['unbindEvent'](),this['_mapVRenderer']&&(this['_mapVRenderer']['destroy'](),this[_0x2643ec(0x92)]=null),this['canvas']['parentElement'][_0x2643ec(0xcb)](this[_0x2643ec(0xa0)]);}['initDevicePixelRatio'](){this['devicePixelRatio']=window['devicePixelRatio']||0x1;}[_0x48fb8f(0xd6)](){const _0x3689f4={_0x853175:0x91,_0x28656a:0xd8,_0x319285:0x9c,_0x3c659b:0xf5,_0x59fcc7:0xa7,_0x5ef1a5:0x9f},_0x5e0b8d=_0x48fb8f;var _0x10d223,_0x12b876;this['_map']['on'](mars3d__namespace[_0x5e0b8d(0x9c)]['mouseDown'],this[_0x5e0b8d(_0x3689f4._0x853175)],this),this['_map']['on'](mars3d__namespace['EventType'][_0x5e0b8d(_0x3689f4._0x28656a)],this['_onMoveStartEvent'],this),this['_map']['on'](mars3d__namespace[_0x5e0b8d(_0x3689f4._0x319285)]['cameraMoveEnd'],this[_0x5e0b8d(_0x3689f4._0x3c659b)],this),(_0x10d223=this[_0x5e0b8d(_0x3689f4._0x59fcc7)])!==null&&_0x10d223!==void 0x0&&(_0x10d223=_0x10d223['methods'])!==null&&_0x10d223!==void 0x0&&_0x10d223['click']&&this['_map']['on'](mars3d__namespace['EventType']['click'],this[_0x5e0b8d(_0x3689f4._0x5ef1a5)],this),(_0x12b876=this[_0x5e0b8d(0xa7)])!==null&&_0x12b876!==void 0x0&&(_0x12b876=_0x12b876[_0x5e0b8d(0x97)])!==null&&_0x12b876!==void 0x0&&_0x12b876[_0x5e0b8d(0xbf)]&&this['_map']['on'](mars3d__namespace['EventType']['mouseMove'],this['_onMapMouseMove'],this);}['unbindEvent'](){const _0x4f33c8={_0x58e722:0xf4,_0x871ee3:0xe1,_0x335c31:0x91,_0x504cef:0xe8,_0x313498:0x9b,_0x1e99d8:0xd2,_0x533bf9:0xa8},_0x3dfb00=_0x48fb8f;var _0x361626,_0x2b0a94;this[_0x3dfb00(_0x4f33c8._0x58e722)]['off'](mars3d__namespace['EventType'][_0x3dfb00(_0x4f33c8._0x871ee3)],this['_onMoveStartEvent'],this),this[_0x3dfb00(0xf4)]['off'](mars3d__namespace['EventType']['cameraMoveStart'],this[_0x3dfb00(_0x4f33c8._0x335c31)],this),this['_map'][_0x3dfb00(0xe8)](mars3d__namespace['EventType'][_0x3dfb00(0xdf)],this[_0x3dfb00(0xf5)],this),this[_0x3dfb00(0xf4)][_0x3dfb00(_0x4f33c8._0x504cef)](mars3d__namespace[_0x3dfb00(0x9c)][_0x3dfb00(_0x4f33c8._0x313498)],this['_reset'],this),(_0x361626=this['options'])!==null&&_0x361626!==void 0x0&&(_0x361626=_0x361626['methods'])!==null&&_0x361626!==void 0x0&&_0x361626['click']&&this['_map']['off'](mars3d__namespace['EventType'][_0x3dfb00(_0x4f33c8._0x1e99d8)],this['_onMapClick'],this),(_0x2b0a94=this['options'])!==null&&_0x2b0a94!==void 0x0&&(_0x2b0a94=_0x2b0a94['methods'])!==null&&_0x2b0a94!==void 0x0&&_0x2b0a94['mousemove']&&this['_map']['off'](mars3d__namespace['EventType']['mouseMove'],this[_0x3dfb00(_0x4f33c8._0x533bf9)],this);}['_onMoveStartEvent'](){const _0x2d9898={_0x5dde91:0x9c},_0x4a770d=_0x48fb8f;this['_mapVRenderer']&&(this['_mapVRenderer']['animatorMovestartEvent'](),this['_map']['off'](mars3d__namespace['EventType'][_0x4a770d(0x9b)],this['_reset'],this),this['_map']['on'](mars3d__namespace[_0x4a770d(_0x2d9898._0x5dde91)]['postRender'],this[_0x4a770d(0x98)],this));}['_onMoveEndEvent'](){const _0x353d54={_0x5e644b:0x92,_0x4b6180:0x98},_0x2f6c67=_0x48fb8f;this[_0x2f6c67(_0x353d54._0x5e644b)]&&(this['_map'][_0x2f6c67(0xe8)](mars3d__namespace['EventType']['postRender'],this['_reset'],this),this[_0x2f6c67(0x92)]['animatorMoveendEvent'](),this[_0x2f6c67(_0x353d54._0x4b6180)]());}[_0x48fb8f(0xee)](_0x2ec722,_0x51965e){const _0x592e95={_0x59db3f:0xe7},_0x35d4f1=_0x48fb8f;this['_removedHook'](),this[_0x35d4f1(_0x592e95._0x59db3f)]();}[_0x48fb8f(0xb2)](_0x546d8a){const _0x3c719d=_0x48fb8f;this['_mapVRenderer']&&this[_0x3c719d(0x92)]['addData'](_0x546d8a,this['options']);}['updateData'](_0x1b4c83){const _0x17f9a8={_0x294509:0x92},_0x29b646=_0x48fb8f;this['_mapVRenderer']&&this[_0x29b646(_0x17f9a8._0x294509)]['updateData'](_0x1b4c83,this['options']);}[_0x48fb8f(0x99)](){const _0x4e6336=_0x48fb8f;return this['_mapVRenderer']&&(this['dataSet']=this['_mapVRenderer'][_0x4e6336(0x99)]()),this['dataSet'];}['removeData'](_0x2a9ace){this['_mapVRenderer']&&this['_mapVRenderer']['removeData'](_0x2a9ace);}['removeAllData'](){const _0x4e0141={_0x496c9a:0x92},_0x5e0bc5=_0x48fb8f;this[_0x5e0bc5(_0x4e0141._0x496c9a)]&&this['_mapVRenderer']['clearData']();}['_createCanvas'](){const _0x693c56={_0x48dee4:0xf4,_0x4ad575:0xb8,_0x283b85:0xab,_0x176191:0xab},_0x150fba=_0x48fb8f,_0x3eac0c=mars3d__namespace['DomUtil']['create']('canvas','mars3d-mapv',this[_0x150fba(_0x693c56._0x48dee4)]['container']);_0x3eac0c['id']=this['id'],_0x3eac0c[_0x150fba(_0x693c56._0x4ad575)]['position']=_0x150fba(0xb6),_0x3eac0c[_0x150fba(0xb8)]['top']=_0x150fba(0xa5),_0x3eac0c[_0x150fba(0xb8)]['left']='0px',_0x3eac0c['width']=parseInt(this['_map']['canvas'][_0x150fba(_0x693c56._0x283b85)]),_0x3eac0c['height']=parseInt(this['_map']['canvas']['height']),_0x3eac0c['style']['width']=this[_0x150fba(_0x693c56._0x48dee4)]['canvas']['style'][_0x150fba(_0x693c56._0x176191)],_0x3eac0c['style']['height']=this['_map']['canvas']['style']['height'],_0x3eac0c['style']['pointerEvents']=this[_0x150fba(0xa2)]?'auto':'none',_0x3eac0c['style']['zIndex']=this['options']['zIndex']??0x9;if(this['options']['context']==='2d'){const _0x4ce580=this['devicePixelRatio'];_0x3eac0c['getContext'](this['options']['context'])[_0x150fba(0xac)](_0x4ce580,_0x4ce580);}return _0x3eac0c;}['_reset'](){const _0x4032d8=_0x48fb8f;this[_0x4032d8(0xcd)](),this['render']();}['draw'](){const _0x1db0b9={_0x5bb845:0x98},_0x168a1f=_0x48fb8f;this[_0x168a1f(_0x1db0b9._0x5bb845)]();}[_0x48fb8f(0xde)](){const _0x2d6216=_0x48fb8f;this[_0x2d6216(0x92)]&&(this['_mapVRenderer']['destroy'](),this['_mapVRenderer']=null),this['canvas'][_0x2d6216(0xc6)][_0x2d6216(0xcb)](this['canvas']);}['render'](){const _0x47cf60={_0x5c3727:0xd4},_0x286031=_0x48fb8f;this[_0x286031(0x92)][_0x286031(_0x47cf60._0x5c3727)]();}['resize'](){const _0x400f35={_0x541b39:0xab,_0x15b125:0xb8,_0x4e2199:0xa9},_0x2c61fa=_0x48fb8f;if(this['canvas']){const _0x5ab266=this['canvas'];_0x5ab266['style']['position']='absolute',_0x5ab266['style']['top']='0px',_0x5ab266['style']['left']='0px',_0x5ab266['width']=parseInt(this['_map']['canvas'][_0x2c61fa(_0x400f35._0x541b39)]),_0x5ab266['height']=parseInt(this['_map']['canvas']['height']),_0x5ab266[_0x2c61fa(0xb8)][_0x2c61fa(0xab)]=this['_map']['canvas'][_0x2c61fa(_0x400f35._0x15b125)]['width'],_0x5ab266[_0x2c61fa(_0x400f35._0x15b125)][_0x2c61fa(_0x400f35._0x4e2199)]=this['_map']['canvas']['style']['height'];}}['getRectangle'](_0x248649){const _0x36e20b={_0x3b57f6:0xb0,_0x383fc1:0xca},_0xa493ff=_0x48fb8f;if(!this[_0xa493ff(0xa1)]||!this['dataSet']['_data'])return;const _0x4c3ddf=mars3d__namespace[_0xa493ff(_0x36e20b._0x3b57f6)]['getExtentByGeoJSON']({'type':'FeatureCollection','features':this[_0xa493ff(0xa1)][_0xa493ff(0x9e)]});if(!_0x4c3ddf)return;return _0x248649!==null&&_0x248649!==void 0x0&&_0x248649['isFormat']?_0x4c3ddf:Cesium['Rectangle']['fromDegrees'](_0x4c3ddf['xmin'],_0x4c3ddf['ymin'],_0x4c3ddf['xmax'],_0x4c3ddf[_0xa493ff(_0x36e20b._0x383fc1)]);}['_onMapClick'](_0x35df6f){const _0x16af0d={_0x2914f8:0xf6},_0x4fa546=_0x48fb8f;this[_0x4fa546(_0x16af0d._0x2914f8)]=_0x35df6f,this['_mapVRenderer']&&this['_mapVRenderer']['clickEvent'](_0x35df6f['windowPosition'],_0x35df6f);}[_0x48fb8f(0xa8)](_0x497e5d){const _0x475262=_0x48fb8f;this['_cache_event']=_0x497e5d,this['_mapVRenderer']&&this['_mapVRenderer'][_0x475262(0xc3)](_0x497e5d['windowPosition'],_0x497e5d);}['on'](_0x329e95,_0x33ecc7,_0x4135f0){const _0xa1df7b={_0x5c9745:0x97,_0x31816f:0xd2,_0x41b3e5:0xd2,_0x132b7e:0x9f},_0x2967bd=_0x48fb8f;this['options']['methods']=this['options'][_0x2967bd(_0xa1df7b._0x5c9745)]||{};if(_0x329e95===mars3d__namespace['EventType'][_0x2967bd(_0xa1df7b._0x31816f)])this[_0x2967bd(0xa7)]['methods'][_0x2967bd(_0xa1df7b._0x31816f)]=_0x330b06=>{_0x330b06&&_0x33ecc7['bind'](_0x4135f0)({...this['_cache_event'],'layer':this,'data':_0x330b06});},this['_map']['on'](mars3d__namespace['EventType'][_0x2967bd(_0xa1df7b._0x41b3e5)],this[_0x2967bd(_0xa1df7b._0x132b7e)],this);else _0x329e95===mars3d__namespace[_0x2967bd(0x9c)]['mouseMove']&&(this['options']['methods'][_0x2967bd(0xbf)]=_0x1e4977=>{_0x1e4977&&_0x33ecc7['bind'](_0x4135f0)({...this['_cache_event'],'layer':this,'data':_0x1e4977});},this['_map']['on'](mars3d__namespace['EventType']['mouseMove'],this['_onMapMouseMove'],this));return this;}['off'](_0x24a07f,_0x49208c){const _0x267d30={_0x3be146:0xa3},_0x164019=_0x48fb8f;if(_0x24a07f==='click'){var _0x55b1fc;this[_0x164019(0xf4)][_0x164019(0xe8)](_0x24a07f,this['_onMapClick'],this),(_0x55b1fc=this['options']['methods'])!==null&&_0x55b1fc!==void 0x0&&_0x55b1fc['mousemove']&&delete this['options']['methods']['click'];}else{if(_0x24a07f===_0x164019(_0x267d30._0x3be146)){var _0x45e94e;this['_map']['off'](_0x24a07f,this['_onMapMouseMove'],this),(_0x45e94e=this['options']['methods'])!==null&&_0x45e94e!==void 0x0&&_0x45e94e['mousemove']&&delete this[_0x164019(0xa7)]['methods']['mousemove'];}}return this;}}mars3d__namespace['LayerUtil'][_0x48fb8f(0xaf)]('mapv',MapVLayer),mars3d__namespace[_0x48fb8f(0xe3)]['MapVLayer']=MapVLayer,mars3d__namespace['mapv']=mapv__namespace,exports['MapVLayer']=MapVLayer,Object[_0x48fb8f(0xae)](mapv)['forEach'](function(_0x2fa486){if(_0x2fa486!=='default'&&!exports['hasOwnProperty'](_0x2fa486))Object['defineProperty'](exports,_0x2fa486,{'enumerable':!![],'get':function(){return mapv[_0x2fa486];}});}),Object['defineProperty'](exports,'__esModule',{'value':!![]});
}));
