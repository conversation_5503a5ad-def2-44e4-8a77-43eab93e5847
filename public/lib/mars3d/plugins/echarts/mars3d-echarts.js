/**
 * Mars3D平台插件,结合echarts可视化功能插件  mars3d-echarts
 *
 * 版本信息：v3.7.5
 * 编译日期：2024-03-05 20:02:24
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2024-01-15
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.echarts || require('echarts')), (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'echarts', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-echarts"] = {}, global.echarts, global.mars3d));
})(this, (function (exports, echarts, mars3d) { 
'use strict';const _0x4478b4=_0x23e3;function _0x23e3(_0x54ac3d,_0x5f0f6a){const _0x553c72=_0x553c();return _0x23e3=function(_0x23e323,_0x5359e7){_0x23e323=_0x23e323-0x16e;let _0x4cac80=_0x553c72[_0x23e323];return _0x4cac80;},_0x23e3(_0x54ac3d,_0x5f0f6a);}(function(_0x3a969a,_0x319b9b){const _0x5626fc={_0x487a74:0x194,_0x507b3:0x187,_0x43ebe3:0x176,_0x3d42e9:0x19f,_0x508299:0x18d,_0x1735ac:0x18f},_0x3a885d=_0x23e3,_0xf3b45c=_0x3a969a();while(!![]){try{const _0x25b581=parseInt(_0x3a885d(_0x5626fc._0x487a74))/0x1+-parseInt(_0x3a885d(0x193))/0x2*(-parseInt(_0x3a885d(_0x5626fc._0x507b3))/0x3)+parseInt(_0x3a885d(_0x5626fc._0x43ebe3))/0x4+-parseInt(_0x3a885d(_0x5626fc._0x3d42e9))/0x5+-parseInt(_0x3a885d(_0x5626fc._0x508299))/0x6+parseInt(_0x3a885d(0x188))/0x7*(-parseInt(_0x3a885d(_0x5626fc._0x1735ac))/0x8)+parseInt(_0x3a885d(0x16f))/0x9;if(_0x25b581===_0x319b9b)break;else _0xf3b45c['push'](_0xf3b45c['shift']());}catch(_0x1cbe52){_0xf3b45c['push'](_0xf3b45c['shift']());}}}(_0x553c,0xa6153));function _interopNamespace(_0x2732f0){const _0x399396={_0x5e4261:0x1a4},_0x4dc180=_0x23e3;if(_0x2732f0&&_0x2732f0['__esModule'])return _0x2732f0;var _0x202741=Object['create'](null);return _0x2732f0&&Object[_0x4dc180(0x182)](_0x2732f0)['forEach'](function(_0x510938){const _0x56f245=_0x4dc180;if(_0x510938!==_0x56f245(_0x399396._0x5e4261)){var _0x51dca9=Object['getOwnPropertyDescriptor'](_0x2732f0,_0x510938);Object[_0x56f245(0x1a0)](_0x202741,_0x510938,_0x51dca9['get']?_0x51dca9:{'enumerable':!![],'get':function(){return _0x2732f0[_0x510938];}});}}),_0x202741['default']=_0x2732f0,_0x202741;}var echarts__namespace=_interopNamespace(echarts),mars3d__namespace=_interopNamespace(mars3d);const Cesium$1=mars3d__namespace['Cesium'];class CompositeCoordinateSystem{constructor(_0x54ad17,_0x44d6d8){const _0x1ae34a={_0x1250dc:0x186},_0x369e2d=_0x23e3;this['_mars3d_scene']=_0x54ad17,this['dimensions']=['lng','lat'],this[_0x369e2d(_0x1ae34a._0x1250dc)]=[0x0,0x0],this['_api']=_0x44d6d8;}['setMapOffset'](_0x4da30a){this['_mapOffset']=_0x4da30a;}['getBMap'](){const _0x486cbe=_0x23e3;return this[_0x486cbe(0x1a7)];}['dataToPoint'](_0x252d80){const _0x107ef6={_0x6f1dd3:0x1a1},_0x1f26ba=_0x23e3,_0x2bb85b=this['_mars3d_scene'],_0x5d058b=[NaN,NaN];let _0x40098b=_0x2bb85b['echartsFixedHeight'];_0x2bb85b['echartsAutoHeight']&&(_0x40098b=_0x2bb85b[_0x1f26ba(0x195)](Cesium$1['Cartographic']['fromDegrees'](_0x252d80[0x0],_0x252d80[0x1])));const _0x4bfb31=Cesium$1['Cartesian3']['fromDegrees'](_0x252d80[0x0],_0x252d80[0x1],_0x40098b);if(!_0x4bfb31)return _0x5d058b;const _0xd5b5dd=Cesium$1['SceneTransforms']['wgs84ToWindowCoordinates'](_0x2bb85b,_0x4bfb31);if(!_0xd5b5dd)return _0x5d058b;if(_0x2bb85b['echartsDepthTest']&&_0x2bb85b['mode']===Cesium$1['SceneMode'][_0x1f26ba(0x1a6)]){const _0x119d5d=new Cesium$1['EllipsoidalOccluder'](_0x2bb85b['globe'][_0x1f26ba(_0x107ef6._0x6f1dd3)],_0x2bb85b['camera']['positionWC']),_0x3182ab=_0x119d5d['isPointVisible'](_0x4bfb31);if(!_0x3182ab)return _0x5d058b;}return[_0xd5b5dd['x']-this['_mapOffset'][0x0],_0xd5b5dd['y']-this['_mapOffset'][0x1]];}['getViewRect'](){const _0x113efc={_0xb43160:0x17f},_0x3c31dc=_0x23e3,_0x389230=this['_api'];return new echarts__namespace['graphic']['BoundingRect'](0x0,0x0,_0x389230[_0x3c31dc(_0x113efc._0xb43160)](),_0x389230['getHeight']());}[_0x4478b4(0x17e)](){return echarts__namespace['matrix']['create']();}}CompositeCoordinateSystem['dimensions']=['lng','lat'],CompositeCoordinateSystem['create']=function(_0x418143,_0x55fe71){const _0x2c4eec=_0x4478b4;let _0x4b11cf;const _0x49d750=_0x418143[_0x2c4eec(0x199)][_0x2c4eec(0x172)]['_mars3d_scene'];_0x418143['eachComponent']('mars3dMap',function(_0x4a2786){const _0x2682a1=_0x2c4eec,_0x1cd61c=_0x55fe71['getZr']()['painter'];if(!_0x1cd61c)return;!_0x4b11cf&&(_0x4b11cf=new CompositeCoordinateSystem(_0x49d750,_0x55fe71)),_0x4a2786['coordinateSystem']=_0x4b11cf,_0x4b11cf[_0x2682a1(0x192)](_0x4a2786[_0x2682a1(0x1a9)]||[0x0,0x0]);}),_0x418143['eachSeries'](function(_0xf65e16){const _0x351c54=_0x2c4eec;_0xf65e16['get']('coordinateSystem')==='mars3dMap'&&(!_0x4b11cf&&(_0x4b11cf=new CompositeCoordinateSystem(_0x49d750,_0x55fe71)),_0xf65e16[_0x351c54(0x170)]=_0x4b11cf);});};function _0x553c(){const _0x1b03ed=['canvas','clientHeight','getRoamTransform','getWidth','style','_mountedHook','keys','addEventListener','value','coords','_mapOffset','1954023kbUnvQ','80689FKAXVq','_echartsInstance','pointerEvents','_echartsContainer','_createChartOverlay','7056804KLtKWa','options','56gmLvOS','registerCoordinateSystem','clear','setMapOffset','2UGYjuH','1351630gyokhC','getHeight','updateLayout','0px','scene','scheduler','init','clampToGround','resize','clientWidth','fromDegrees','4525920ayUGRl','defineProperty','ellipsoid','_addedHook','min','default','__esModule','SCENE3D','_mars3d_scene','forEach','__mapOffset','dispose','top','1561473azyRDk','coordinateSystem','_map','ecInstance','register','height','mars3dMapRoam','2663256PtHYEc','echartsAutoHeight','_pointerEvents','onByQuery','container','setOption'];_0x553c=function(){return _0x1b03ed;};return _0x553c();}if(echarts__namespace!==null&&echarts__namespace!==void 0x0&&echarts__namespace[_0x4478b4(0x19a)])echarts__namespace[_0x4478b4(0x190)]('mars3dMap',CompositeCoordinateSystem),echarts__namespace['registerAction']({'type':_0x4478b4(0x175),'event':'mars3dMapRoam','update':_0x4478b4(0x196)},function(_0x38d60d,_0x2439b1){}),echarts__namespace['extendComponentModel']({'type':'mars3dMap','getBMap':function(){const _0x1e125f={_0x3349c0:0x1a7},_0x2c30ce=_0x4478b4;return this[_0x2c30ce(_0x1e125f._0x3349c0)];},'defaultOption':{'roam':![]}}),echarts__namespace['extendComponentView']({'type':'mars3dMap','init':function(_0x2ea52c,_0x4d1924){const _0x590998={_0x40cabe:0x199,_0x517866:0x183},_0x53063c=_0x4478b4;this['api']=_0x4d1924,this[_0x53063c(0x198)]=_0x2ea52c[_0x53063c(_0x590998._0x40cabe)]['ecInstance']['_mars3d_scene'],this['scene']['postRender'][_0x53063c(_0x590998._0x517866)](this['moveHandler'],this);},'moveHandler':function(_0x57b5be,_0x4c05da){this['api']['dispatchAction']({'type':'mars3dMapRoam'});},'render':function(_0x45406f,_0x49e0cf,_0xd47a62){},'dispose':function(_0x7a1a3f){const _0x21a07c={_0xe3f5af:0x198},_0x1a8fe2=_0x4478b4;this[_0x1a8fe2(_0x21a07c._0xe3f5af)]['postRender']['removeEventListener'](this['moveHandler'],this);}});else throw new Error('请引入\x20echarts\x20库\x20');const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer']['BaseLayer'];class EchartsLayer extends BaseLayer{constructor(_0xacb60d={}){const _0x32ee5a={_0x3f1968:0x178},_0x7d5820=_0x4478b4;super(_0xacb60d),this[_0x7d5820(_0x32ee5a._0x3f1968)]=this['options']['pointerEvents'];}get['layer'](){return this['_echartsInstance'];}get['pointerEvents'](){const _0x354b0c=_0x4478b4;return this[_0x354b0c(0x178)];}set['pointerEvents'](_0x408058){const _0x230eb7={_0x5b89b7:0x18b,_0x2596b2:0x18a},_0x358f7c=_0x4478b4;this['_pointerEvents']=_0x408058,this[_0x358f7c(_0x230eb7._0x5b89b7)]&&(_0x408058?this['_echartsContainer']['style'][_0x358f7c(_0x230eb7._0x2596b2)]='all':this['_echartsContainer']['style'][_0x358f7c(0x18a)]='none');}['_setOptionsHook'](_0x132c4f,_0x289928){this['setEchartsOption'](_0x132c4f);}['_showHook'](_0x43f9ea){_0x43f9ea?this['_echartsContainer']['style']['visibility']='visible':this['_echartsContainer']['style']['visibility']='hidden';}[_0x4478b4(0x181)](){const _0x392f5b={_0x1134a5:0x171,_0x496bde:0x19b},_0x593987=_0x4478b4;this['_map']['scene']['echartsDepthTest']=this['options']['depthTest']??!![],this[_0x593987(_0x392f5b._0x1134a5)]['scene'][_0x593987(0x177)]=this['options'][_0x593987(_0x392f5b._0x496bde)]??![],this['_map']['scene']['echartsFixedHeight']=this[_0x593987(0x18e)]['fixedHeight']??0x0;}[_0x4478b4(0x1a2)](){const _0x44115a=_0x4478b4;this['_echartsContainer']=this[_0x44115a(0x18c)](),this[_0x44115a(0x189)]=echarts__namespace['init'](this['_echartsContainer']),this['_echartsInstance']['_mars3d_scene']=this['_map']['scene'],this['setEchartsOption'](this['options']);}['_removedHook'](){const _0xaef78={_0x316502:0x189,_0x295bfb:0x191,_0x5a76a8:0x171,_0xe35fe4:0x18b},_0x2276a2=_0x4478b4;this[_0x2276a2(_0xaef78._0x316502)]&&(this['_echartsInstance'][_0x2276a2(_0xaef78._0x295bfb)](),this['_echartsInstance'][_0x2276a2(0x1aa)](),delete this['_echartsInstance']),this['_echartsContainer']&&(this[_0x2276a2(_0xaef78._0x5a76a8)][_0x2276a2(0x17a)]['removeChild'](this[_0x2276a2(_0xaef78._0xe35fe4)]),delete this['_echartsContainer']);}['_createChartOverlay'](){const _0x37412e={_0xfe1b77:0x16e,_0x2151f2:0x198,_0x120f57:0x180},_0xd63a51=_0x4478b4,_0x48ecb9=mars3d__namespace['DomUtil']['create']('div','mars3d-echarts',this['_map']['container']);return _0x48ecb9['id']=this['id'],_0x48ecb9[_0xd63a51(0x180)]['position']='absolute',_0x48ecb9[_0xd63a51(0x180)][_0xd63a51(_0x37412e._0xfe1b77)]='0px',_0x48ecb9[_0xd63a51(0x180)]['left']=_0xd63a51(0x197),_0x48ecb9['style']['width']=this['_map'][_0xd63a51(_0x37412e._0x2151f2)]['canvas']['clientWidth']+'px',_0x48ecb9['style']['height']=this['_map'][_0xd63a51(_0x37412e._0x2151f2)]['canvas'][_0xd63a51(0x17d)]+'px',_0x48ecb9['style']['pointerEvents']=this['_pointerEvents']?'all':'none',_0x48ecb9[_0xd63a51(_0x37412e._0x120f57)]['zIndex']=this['options']['zIndex']??0x9,_0x48ecb9;}[_0x4478b4(0x19c)](){const _0x1e2090={_0x159311:0x19d,_0x25e8b7:0x174},_0x3a3317=_0x4478b4;if(!this[_0x3a3317(0x189)])return;this['_echartsContainer']['style']['width']=this['_map']['scene']['canvas'][_0x3a3317(_0x1e2090._0x159311)]+'px',this['_echartsContainer']['style'][_0x3a3317(_0x1e2090._0x25e8b7)]=this[_0x3a3317(0x171)]['scene'][_0x3a3317(0x17c)]['clientHeight']+'px',this['_echartsInstance']['resize']();}['setEchartsOption'](_0x32a8c6,_0x5a2db4,_0x46af1f){const _0x233290={_0x326397:0x189},_0x56d74d=_0x4478b4;this[_0x56d74d(_0x233290._0x326397)]&&(_0x32a8c6={'mars3dMap':{},...mars3d__namespace['Util']['getAttrVal'](_0x32a8c6,{'onlySimpleType':!![]})},delete _0x32a8c6['eventParent'],this['_echartsInstance'][_0x56d74d(0x17b)](_0x32a8c6,_0x5a2db4,_0x46af1f));}['getRectangle'](_0x4a69eb){const _0x4e2a04={_0x56c311:0x19e},_0x2552e3={_0x39e188:0x1a3},_0x1a6e9c=_0x4478b4;let _0x286a2b,_0x3b9a75,_0x3bdee0,_0x14747a;function _0x4fc337(_0x476cbf){const _0x487755=_0x23e3;if(!Array['isArray'](_0x476cbf))return;const _0x1f9214=_0x476cbf[0x0]||0x0,_0x2041ed=_0x476cbf[0x1]||0x0;_0x1f9214!==0x0&&_0x2041ed!==0x0&&(_0x286a2b===undefined?(_0x286a2b=_0x1f9214,_0x3b9a75=_0x1f9214,_0x3bdee0=_0x2041ed,_0x14747a=_0x2041ed):(_0x286a2b=Math[_0x487755(_0x2552e3._0x39e188)](_0x286a2b,_0x1f9214),_0x3b9a75=Math['max'](_0x3b9a75,_0x1f9214),_0x3bdee0=Math[_0x487755(0x1a3)](_0x3bdee0,_0x2041ed),_0x14747a=Math['max'](_0x14747a,_0x2041ed)));}const _0x223898=this['options']['series'];_0x223898&&_0x223898['forEach'](_0xafba22=>{_0xafba22['data']&&_0xafba22['data']['forEach'](_0x122340=>{const _0x353e7e=_0x23e3;if(_0x122340[_0x353e7e(0x184)])_0x4fc337(_0x122340['value']);else _0x122340[_0x353e7e(0x185)]&&_0x122340['coords'][_0x353e7e(0x1a8)](_0x493816=>{_0x4fc337(_0x493816);});});});if(_0x286a2b===0x0&&_0x3bdee0===0x0&&_0x3b9a75===0x0&&_0x14747a===0x0)return null;return _0x4a69eb!==null&&_0x4a69eb!==void 0x0&&_0x4a69eb['isFormat']?{'xmin':_0x286a2b,'xmax':_0x3b9a75,'ymin':_0x3bdee0,'ymax':_0x14747a}:Cesium['Rectangle'][_0x1a6e9c(_0x4e2a04._0x56c311)](_0x286a2b,_0x3bdee0,_0x3b9a75,_0x14747a);}['on'](_0x47dfc8,_0x4cfe24,_0x1fab7b){return this['_echartsInstance']['on'](_0x47dfc8,_0x4cfe24,_0x1fab7b||this),this;}[_0x4478b4(0x179)](_0x398ce9,_0x22a27b,_0x2daa37,_0x29207f){return this['_echartsInstance']['on'](_0x398ce9,_0x22a27b,_0x2daa37,_0x29207f||this),this;}['off'](_0x42442b,_0x181a57,_0x5459e2){return this['_echartsInstance']['off'](_0x42442b,_0x181a57,_0x5459e2||this),this;}}mars3d__namespace['LayerUtil'][_0x4478b4(0x173)]('echarts',EchartsLayer),mars3d__namespace['layer']['EchartsLayer']=EchartsLayer,mars3d__namespace['echarts']=echarts__namespace,exports['EchartsLayer']=EchartsLayer,Object[_0x4478b4(0x182)](echarts)['forEach'](function(_0x16d2d5){const _0x3d7c05={_0x3b1efd:0x1a4,_0x5466f2:0x1a0},_0x42b12f=_0x4478b4;if(_0x16d2d5!==_0x42b12f(_0x3d7c05._0x3b1efd)&&!exports['hasOwnProperty'](_0x16d2d5))Object[_0x42b12f(_0x3d7c05._0x5466f2)](exports,_0x16d2d5,{'enumerable':!![],'get':function(){return echarts[_0x16d2d5];}});}),Object['defineProperty'](exports,_0x4478b4(0x1a5),{'value':!![]});
}));
