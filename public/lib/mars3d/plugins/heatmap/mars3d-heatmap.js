/**
 * Mars3D平台插件,结合heatmap可视化功能插件  mars3d-heatmap
 *
 * 版本信息：v3.7.4
 * 编译日期：2024-02-20 13:37:33
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2024-01-15
 */
(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
	typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-heatmap"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';var _0x3a8dc4=_0x256d;(function(_0x3913e0,_0x54feed){var _0x2d3dce={_0xebef33:0x135,_0x1d1d9c:0x1ba,_0x1cea6e:0x163,_0x32d22b:0x18a,_0x1dc6c0:0x1c7,_0x4ea975:0x16f},_0x44a37c=_0x256d,_0x2c19dc=_0x3913e0();while(!![]){try{var _0x792b7c=parseInt(_0x44a37c(_0x2d3dce._0xebef33))/0x1+parseInt(_0x44a37c(_0x2d3dce._0x1d1d9c))/0x2*(parseInt(_0x44a37c(_0x2d3dce._0x1cea6e))/0x3)+-parseInt(_0x44a37c(0x13e))/0x4*(parseInt(_0x44a37c(_0x2d3dce._0x32d22b))/0x5)+-parseInt(_0x44a37c(_0x2d3dce._0x1dc6c0))/0x6+-parseInt(_0x44a37c(_0x2d3dce._0x4ea975))/0x7+-parseInt(_0x44a37c(0x1a8))/0x8*(-parseInt(_0x44a37c(0x12f))/0x9)+parseInt(_0x44a37c(0x199))/0xa;if(_0x792b7c===_0x54feed)break;else _0x2c19dc['push'](_0x2c19dc['shift']());}catch(_0x398b04){_0x2c19dc['push'](_0x2c19dc['shift']());}}}(_0x8dd4,0xe68bb));function _interopNamespace(_0x559b5e){var _0x421eda={_0xd082ad:0x1b4,_0x2c396d:0x142},_0x13dcdd=_0x256d;if(_0x559b5e&&_0x559b5e[_0x13dcdd(_0x421eda._0xd082ad)])return _0x559b5e;var _0x2191a8=Object['create'](null);return _0x559b5e&&Object[_0x13dcdd(_0x421eda._0x2c396d)](_0x559b5e)['forEach'](function(_0x571b05){if(_0x571b05!=='default'){var _0x4b4b2d=Object['getOwnPropertyDescriptor'](_0x559b5e,_0x571b05);Object['defineProperty'](_0x2191a8,_0x571b05,_0x4b4b2d['get']?_0x4b4b2d:{'enumerable':!![],'get':function(){return _0x559b5e[_0x571b05];}});}}),_0x2191a8['default']=_0x559b5e,_0x2191a8;}function _mergeNamespaces(_0x10318e,_0x4d00e8){var _0x58518e={_0x4ba74e:0x1c8},_0x18ff3e=_0x256d;return _0x4d00e8[_0x18ff3e(_0x58518e._0x4ba74e)](function(_0x50e746){_0x50e746&&typeof _0x50e746!=='string'&&!Array['isArray'](_0x50e746)&&Object['keys'](_0x50e746)['forEach'](function(_0x5d6ef3){if(_0x5d6ef3!=='default'&&!(_0x5d6ef3 in _0x10318e)){var _0x3396be=Object['getOwnPropertyDescriptor'](_0x50e746,_0x5d6ef3);Object['defineProperty'](_0x10318e,_0x5d6ef3,_0x3396be['get']?_0x3396be:{'enumerable':!![],'get':function(){return _0x50e746[_0x5d6ef3];}});}});}),_0x10318e;}var mars3d__namespace=_interopNamespace(mars3d),commonjsGlobal=typeof globalThis!=='undefined'?globalThis:typeof window!=='undefined'?window:typeof global!==_0x3a8dc4(0x1c5)?global:typeof self!=='undefined'?self:{},heatmap$1={'exports':{}};(function(_0x546580){var _0x1b8eb3={_0x5a7f42:0x195},_0x4afbc7={_0x55f533:0x191},_0x524c07=_0x3a8dc4;(function(_0x2ffaee,_0x19f032,_0xf1e772){_0x546580['exports']?_0x546580['exports']=_0xf1e772():_0x19f032[_0x2ffaee]=_0xf1e772();}(_0x524c07(_0x1b8eb3._0x5a7f42),commonjsGlobal,function(){var _0x54bb52={_0x5f3330:0x157},_0x39312e={_0x1e271e:0x17e},_0x18f913={_0x3920e0:0x12d},_0x521fd3={_0x35acf0:0x1bb},_0x3e2a62={_0x3ba04b:0x1a4,_0x1ff199:0x177,_0xe23098:0x165,_0x12cb59:0x139,_0x318b3:0x166},_0x3787a3={_0x4b852f:0x144},_0x36c70a={_0x3c3447:0x13d,_0xf45726:0x14c,_0x37e3c3:0x134,_0x57ed92:0x1b2,_0x46fef9:0x1c1,_0x1fde6d:0x15b},_0x2feade={_0x11d026:0x19a,_0x393448:0x1b2},_0x149ec4={_0x5c949a:0x16e},_0xa611d9={_0x227b30:0x1bc},_0x37e021={_0x2ef682:0x1ab,_0x31bae5:0x184,_0x12b9c3:0x14f,_0x5dae07:0x168,_0x5516f8:0x157},_0xdc7a1a={_0x5a605f:0x184,_0x566aa5:0x138},_0x50b142=_0x524c07,_0x527aca={'defaultRadius':0x28,'defaultRenderer':_0x50b142(0x161),'defaultGradient':{0.25:'rgb(0,0,255)',0.55:'rgb(0,255,0)',0.85:_0x50b142(_0x4afbc7._0x55f533),0x1:_0x50b142(0x151)},'defaultMaxOpacity':0x1,'defaultMinOpacity':0x0,'defaultBlur':0.85,'defaultXField':'x','defaultYField':'y','defaultValueField':'value','plugins':{}},_0x2b031e=function _0x1ef43d(){var _0x20f454={_0x35a7c6:0x12e},_0x3f6e60={_0x48612b:0x174,_0xce2ad9:0x1b5,_0x527a23:0x1b9},_0x1c2f41=function _0x5496b9(_0x239653){var _0x2191d1=_0x256d;this['_coordinator']={},this['_data']=[],this['_radi']=[],this['_min']=0xa,this[_0x2191d1(_0x3f6e60._0x48612b)]=0x1,this['_xField']=_0x239653['xField']||_0x239653['defaultXField'],this['_yField']=_0x239653['yField']||_0x239653['defaultYField'],this['_valueField']=_0x239653['valueField']||_0x239653[_0x2191d1(_0x3f6e60._0xce2ad9)],_0x239653['radius']&&(this[_0x2191d1(_0x3f6e60._0x527a23)]=_0x239653['radius']);},_0x2cf572=_0x527aca['defaultRadius'];return _0x1c2f41['prototype']={'_organiseData':function(_0x31122e,_0x24b984){var _0x158e88=_0x256d,_0x1d7f46=_0x31122e[this['_xField']],_0x4f0e60=_0x31122e[this[_0x158e88(0x1b1)]],_0x5ce6ce=this['_radi'],_0x3ae160=this[_0x158e88(0x184)],_0x286f07=this['_max'],_0x6f44df=this[_0x158e88(0x138)],_0x591292=_0x31122e[this['_valueField']]||0x1,_0x4eba98=_0x31122e[_0x158e88(_0x20f454._0x35a7c6)]||this['_cfgRadius']||_0x2cf572;!_0x3ae160[_0x1d7f46]&&(_0x3ae160[_0x1d7f46]=[],_0x5ce6ce[_0x1d7f46]=[]);!_0x3ae160[_0x1d7f46][_0x4f0e60]?(_0x3ae160[_0x1d7f46][_0x4f0e60]=_0x591292,_0x5ce6ce[_0x1d7f46][_0x4f0e60]=_0x4eba98):_0x3ae160[_0x1d7f46][_0x4f0e60]+=_0x591292;var _0x849f52=_0x3ae160[_0x1d7f46][_0x4f0e60];if(_0x849f52>_0x286f07)return!_0x24b984?this['_max']=_0x849f52:this['setDataMax'](_0x849f52),![];else return _0x849f52<_0x6f44df?(!_0x24b984?this[_0x158e88(0x138)]=_0x849f52:this['setDataMin'](_0x849f52),![]):{'x':_0x1d7f46,'y':_0x4f0e60,'value':_0x591292,'radius':_0x4eba98,'min':_0x6f44df,'max':_0x286f07};},'_unOrganizeData':function(){var _0xb01c3c=_0x256d,_0x3820aa=[],_0x3775af=this[_0xb01c3c(0x184)],_0x27e794=this['_radi'];for(var _0x21316e in _0x3775af){for(var _0x328ef2 in _0x3775af[_0x21316e]){_0x3820aa['push']({'x':_0x21316e,'y':_0x328ef2,'radius':_0x27e794[_0x21316e][_0x328ef2],'value':_0x3775af[_0x21316e][_0x328ef2]});}}return{'min':this['_min'],'max':this['_max'],'data':_0x3820aa};},'_onExtremaChange':function(){var _0x398bfb=_0x256d;this['_coordinator']['emit']('extremachange',{'min':this[_0x398bfb(0x138)],'max':this['_max']});},'addData':function(){var _0x3ac579=_0x256d;if(arguments[0x0]['length']>0x0){var _0xd18ac7=arguments[0x0],_0x1fbab5=_0xd18ac7['length'];while(_0x1fbab5--){this['addData']['call'](this,_0xd18ac7[_0x1fbab5]);}}else{var _0x19a916=this['_organiseData'](arguments[0x0],!![]);_0x19a916&&(this[_0x3ac579(_0xdc7a1a._0x5a605f)]['length']===0x0&&(this[_0x3ac579(0x138)]=this['_max']=_0x19a916['value']),this['_coordinator']['emit'](_0x3ac579(0x1b3),{'min':this[_0x3ac579(_0xdc7a1a._0x566aa5)],'max':this[_0x3ac579(0x174)],'data':[_0x19a916]}));}return this;},'setData':function(_0x3c9ae6){var _0x79ab3f=_0x256d,_0x2e94fd=_0x3c9ae6['data'],_0x354af4=_0x2e94fd[_0x79ab3f(_0x37e021._0x2ef682)];this[_0x79ab3f(_0x37e021._0x31bae5)]=[],this[_0x79ab3f(_0x37e021._0x12b9c3)]=[];for(var _0x541c25=0x0;_0x541c25<_0x354af4;_0x541c25++){this[_0x79ab3f(0x1c2)](_0x2e94fd[_0x541c25],![]);}return this['_max']=_0x3c9ae6['max'],this['_min']=_0x3c9ae6['min']||0x0,this[_0x79ab3f(_0x37e021._0x5dae07)](),this[_0x79ab3f(_0x37e021._0x5516f8)]['emit']('renderall',this['_getInternalData']()),this;},'removeData':function(){},'setDataMax':function(_0x278736){var _0x516adb=_0x256d;return this['_max']=_0x278736,this['_onExtremaChange'](),this['_coordinator'][_0x516adb(_0xa611d9._0x227b30)]('renderall',this[_0x516adb(0x1a0)]()),this;},'setDataMin':function(_0x1429a0){var _0x412e39=_0x256d;return this['_min']=_0x1429a0,this['_onExtremaChange'](),this['_coordinator']['emit'](_0x412e39(_0x149ec4._0x5c949a),this[_0x412e39(0x1a0)]()),this;},'setCoordinator':function(_0x49efb0){this['_coordinator']=_0x49efb0;},'_getInternalData':function(){return{'max':this['_max'],'min':this['_min'],'data':this['_data'],'radi':this['_radi']};},'getData':function(){return this['_unOrganizeData']();}},_0x1c2f41;}(),_0x390115=function _0x196272(){var _0x35e934={_0x32c673:0x13d,_0x41fe62:0x17d},_0x1e4ba7={_0x5e878c:0x134,_0x31e7f7:0x178},_0x5cb6c3={_0x41e46d:0x1c6,_0x379abe:0x139,_0x4d5ac6:0x12a},_0x35b776={_0x4e618d:0x1bb,_0x10638d:0x160},_0x287908={_0x2497dc:0x17a,_0x2183b3:0x1ab,_0x59ea5f:0x1ab},_0x351419={_0x11ad1f:0x13d,_0x2865f3:0x193,_0x1a478b:0x146,_0x5acfbd:0x1bf,_0x38245f:0x18e},_0x19ba00=function(_0x3eff0a){var _0x1210e4=_0x256d,_0x1655e4=_0x3eff0a[_0x1210e4(0x187)]||_0x3eff0a['defaultGradient'],_0x29962=document[_0x1210e4(_0x2feade._0x11d026)]('canvas'),_0x3607e6=_0x29962[_0x1210e4(_0x2feade._0x393448)]('2d',{'willReadFrequently':!![]});_0x29962[_0x1210e4(0x165)]=0x100,_0x29962['height']=0x1;var _0x54a39b=_0x3607e6[_0x1210e4(0x192)](0x0,0x0,0x100,0x1);for(var _0x32299b in _0x1655e4){_0x54a39b['addColorStop'](_0x32299b,_0x1655e4[_0x32299b]);}return _0x3607e6['fillStyle']=_0x54a39b,_0x3607e6[_0x1210e4(0x15a)](0x0,0x0,0x100,0x1),_0x3607e6[_0x1210e4(0x1be)](0x0,0x0,0x100,0x1)['data'];},_0x13a1ac=function(_0x1b4db3,_0x290624){var _0x554f42=_0x256d,_0x4aa0b0=document['createElement'](_0x554f42(_0x351419._0x11ad1f)),_0x272a0d=_0x4aa0b0['getContext']('2d',{'willReadFrequently':!![]}),_0x96487a=_0x1b4db3,_0x37380c=_0x1b4db3;_0x4aa0b0[_0x554f42(0x165)]=_0x4aa0b0['height']=_0x1b4db3*0x2;if(_0x290624==0x1)_0x272a0d['beginPath'](),_0x272a0d[_0x554f42(0x1bd)](_0x96487a,_0x37380c,_0x1b4db3,0x0,0x2*Math['PI'],![]),_0x272a0d['fillStyle']=_0x554f42(_0x351419._0x2865f3),_0x272a0d[_0x554f42(_0x351419._0x1a478b)]();else{var _0x3dd589=_0x272a0d['createRadialGradient'](_0x96487a,_0x37380c,_0x1b4db3*_0x290624,_0x96487a,_0x37380c,_0x1b4db3);_0x3dd589[_0x554f42(_0x351419._0x5acfbd)](0x0,'rgba(0,0,0,1)'),_0x3dd589['addColorStop'](0x1,'rgba(0,0,0,0)'),_0x272a0d[_0x554f42(_0x351419._0x38245f)]=_0x3dd589,_0x272a0d['fillRect'](0x0,0x0,0x2*_0x1b4db3,0x2*_0x1b4db3);}return _0x4aa0b0;},_0x2fc554=function(_0x495700){var _0x441a52=_0x256d,_0x4cc0e9=[],_0x4d88cc=_0x495700[_0x441a52(0x15c)],_0x49ee6d=_0x495700[_0x441a52(_0x287908._0x2497dc)],_0x3cfc42=_0x495700['radi'],_0x495700=_0x495700['data'],_0x2ea52a=Object[_0x441a52(0x142)](_0x495700),_0x2e6145=_0x2ea52a[_0x441a52(_0x287908._0x2183b3)];while(_0x2e6145--){var _0x58434a=_0x2ea52a[_0x2e6145],_0x57d004=Object['keys'](_0x495700[_0x58434a]),_0x154af7=_0x57d004[_0x441a52(_0x287908._0x59ea5f)];while(_0x154af7--){var _0x39c03b=_0x57d004[_0x154af7],_0x4cda4a=_0x495700[_0x58434a][_0x39c03b],_0x4204b6=_0x3cfc42[_0x58434a][_0x39c03b];_0x4cc0e9['push']({'x':_0x58434a,'y':_0x39c03b,'value':_0x4cda4a,'radius':_0x4204b6});}}return{'min':_0x4d88cc,'max':_0x49ee6d,'data':_0x4cc0e9};};function _0x13e7f1(_0x3aca3a){var _0x4887ae=_0x256d,_0x4f019a=_0x3aca3a[_0x4887ae(0x1a9)],_0x57ace1=this['shadowCanvas']=document['createElement']('canvas'),_0x22f2bb=this['canvas']=_0x3aca3a['canvas']||document['createElement'](_0x4887ae(_0x36c70a._0x3c3447));this['_renderBoundaries']=[0x2710,0x2710,0x0,0x0];var _0xc02bcc=getComputedStyle(_0x3aca3a['container'])||{};_0x22f2bb[_0x4887ae(_0x36c70a._0xf45726)]='heatmap-canvas',this['_width']=_0x22f2bb['width']=_0x57ace1['width']=_0x3aca3a['width']||+_0xc02bcc['width']['replace'](/px/,''),this['_height']=_0x22f2bb['height']=_0x57ace1['height']=_0x3aca3a['height']||+_0xc02bcc['height']['replace'](/px/,''),this[_0x4887ae(_0x36c70a._0x37e3c3)]=_0x57ace1[_0x4887ae(_0x36c70a._0x57ed92)]('2d',{'willReadFrequently':!![]}),this['ctx']=_0x22f2bb['getContext']('2d',{'willReadFrequently':!![]}),_0x22f2bb['style'][_0x4887ae(_0x36c70a._0x46fef9)]=_0x57ace1['style']['cssText']=_0x4887ae(_0x36c70a._0x1fde6d),_0x4f019a['style']['position']='relative',_0x4f019a['appendChild'](_0x22f2bb),this['_palette']=_0x19ba00(_0x3aca3a),this['_templates']={},this['_setStyles'](_0x3aca3a);}return _0x13e7f1['prototype']={'renderPartial':function(_0x2b7e9c){var _0x4f2b77=_0x256d;_0x2b7e9c['data']['length']>0x0&&(this[_0x4f2b77(0x160)](_0x2b7e9c),this[_0x4f2b77(_0x3787a3._0x4b852f)]());},'renderAll':function(_0x48a3e2){var _0x5a856f=_0x256d;this['_clear'](),_0x48a3e2[_0x5a856f(_0x35b776._0x4e618d)]['length']>0x0&&(this[_0x5a856f(_0x35b776._0x10638d)](_0x2fc554(_0x48a3e2)),this['_colorize']());},'_updateGradient':function(_0x43e265){var _0x2b8c6a=_0x256d;this[_0x2b8c6a(0x173)]=_0x19ba00(_0x43e265);},'updateConfig':function(_0x3b117b){var _0x3ee3a7=_0x256d;_0x3b117b[_0x3ee3a7(0x187)]&&this['_updateGradient'](_0x3b117b),this['_setStyles'](_0x3b117b);},'setDimensions':function(_0x2468e6,_0x17254e){var _0x48d0c2=_0x256d;this[_0x48d0c2(0x177)]=_0x2468e6,this[_0x48d0c2(_0x5cb6c3._0x41e46d)]=_0x17254e,this['canvas'][_0x48d0c2(0x165)]=this[_0x48d0c2(_0x5cb6c3._0x379abe)]['width']=_0x2468e6,this['canvas'][_0x48d0c2(0x12a)]=this[_0x48d0c2(0x139)][_0x48d0c2(_0x5cb6c3._0x4d5ac6)]=_0x17254e;},'_clear':function(){var _0x264a1d=_0x256d;this['shadowCtx']['clearRect'](0x0,0x0,this['_width'],this[_0x264a1d(0x1c6)]),this['ctx']['clearRect'](0x0,0x0,this['_width'],this[_0x264a1d(0x1c6)]);},'_setStyles':function(_0xc64d3b){var _0x48a7ee=_0x256d;this['_blur']=_0xc64d3b[_0x48a7ee(_0x3e2a62._0x3ba04b)]==0x0?0x0:_0xc64d3b['blur']||_0xc64d3b[_0x48a7ee(0x1af)],_0xc64d3b['backgroundColor']&&(this['canvas']['style'][_0x48a7ee(0x17b)]=_0xc64d3b['backgroundColor']),this[_0x48a7ee(_0x3e2a62._0x1ff199)]=this['canvas']['width']=this['shadowCanvas']['width']=_0xc64d3b[_0x48a7ee(_0x3e2a62._0xe23098)]||this[_0x48a7ee(_0x3e2a62._0x1ff199)],this['_height']=this['canvas']['height']=this[_0x48a7ee(_0x3e2a62._0x12cb59)]['height']=_0xc64d3b['height']||this['_height'],this['_opacity']=(_0xc64d3b['opacity']||0x0)*0xff,this['_maxOpacity']=(_0xc64d3b['maxOpacity']||_0xc64d3b['defaultMaxOpacity'])*0xff,this['_minOpacity']=(_0xc64d3b['minOpacity']||_0xc64d3b['defaultMinOpacity'])*0xff,this[_0x48a7ee(_0x3e2a62._0x318b3)]=!!_0xc64d3b[_0x48a7ee(0x1c0)];},'_drawAlpha':function(_0x3965ba){var _0x5307ec=_0x256d,_0x29c782=this['_min']=_0x3965ba[_0x5307ec(0x15c)],_0x3a0305=this['_max']=_0x3965ba['max'],_0x3965ba=_0x3965ba['data']||[],_0x23680d=_0x3965ba['length'],_0x5dafbb=0x1-this[_0x5307ec(0x169)];while(_0x23680d--){var _0x1d9f7e=_0x3965ba[_0x23680d],_0x9c6fda=_0x1d9f7e['x'],_0x41c151=_0x1d9f7e['y'],_0x5104f7=_0x1d9f7e['radius'],_0x48debc=Math['min'](_0x1d9f7e['value'],_0x3a0305),_0x30040d=_0x9c6fda-_0x5104f7,_0x156b94=_0x41c151-_0x5104f7,_0x2940ec=this[_0x5307ec(_0x1e4ba7._0x5e878c)],_0x2507d0;!this['_templates'][_0x5104f7]?this['_templates'][_0x5104f7]=_0x2507d0=_0x13a1ac(_0x5104f7,_0x5dafbb):_0x2507d0=this['_templates'][_0x5104f7];var _0x273775=(_0x48debc-_0x29c782)/(_0x3a0305-_0x29c782);_0x2940ec['globalAlpha']=_0x273775<0.01?0.01:_0x273775,_0x2940ec['drawImage'](_0x2507d0,_0x30040d,_0x156b94),_0x30040d<this['_renderBoundaries'][0x0]&&(this['_renderBoundaries'][0x0]=_0x30040d),_0x156b94<this['_renderBoundaries'][0x1]&&(this[_0x5307ec(_0x1e4ba7._0x31e7f7)][0x1]=_0x156b94),_0x30040d+0x2*_0x5104f7>this['_renderBoundaries'][0x2]&&(this['_renderBoundaries'][0x2]=_0x30040d+0x2*_0x5104f7),_0x156b94+0x2*_0x5104f7>this[_0x5307ec(_0x1e4ba7._0x31e7f7)][0x3]&&(this[_0x5307ec(0x178)][0x3]=_0x156b94+0x2*_0x5104f7);}},'_colorize':function(){var _0xf1b3c3=_0x256d,_0x254151=this['_renderBoundaries'][0x0],_0x1156b7=this[_0xf1b3c3(0x178)][0x1],_0x5d6e5d=this['_renderBoundaries'][0x2]-_0x254151,_0x3c48bf=this['_renderBoundaries'][0x3]-_0x1156b7,_0x3345ea=this['_width'],_0x11c7ee=this[_0xf1b3c3(0x1c6)],_0x31d3ad=this[_0xf1b3c3(0x15d)],_0x34c263=this['_maxOpacity'],_0x2457e0=this['_minOpacity'],_0x1778a5=this['_useGradientOpacity'];_0x254151<0x0&&(_0x254151=0x0);_0x1156b7<0x0&&(_0x1156b7=0x0);_0x254151+_0x5d6e5d>_0x3345ea&&(_0x5d6e5d=_0x3345ea-_0x254151);_0x1156b7+_0x3c48bf>_0x11c7ee&&(_0x3c48bf=_0x11c7ee-_0x1156b7);var _0x58b0f1=this['shadowCtx']['getImageData'](_0x254151,_0x1156b7,_0x5d6e5d,_0x3c48bf),_0x136058=_0x58b0f1[_0xf1b3c3(_0x521fd3._0x35acf0)],_0x408cb8=_0x136058[_0xf1b3c3(0x1ab)],_0x4b338b=this['_palette'];for(var _0x20c539=0x3;_0x20c539<_0x408cb8;_0x20c539+=0x4){var _0x158a50=_0x136058[_0x20c539],_0x4c4471=_0x158a50*0x4;if(!_0x4c4471)continue;var _0x7a192a;_0x31d3ad>0x0?_0x7a192a=_0x31d3ad:_0x158a50<_0x34c263?_0x158a50<_0x2457e0?_0x7a192a=_0x2457e0:_0x7a192a=_0x158a50:_0x7a192a=_0x34c263,_0x136058[_0x20c539-0x3]=_0x4b338b[_0x4c4471],_0x136058[_0x20c539-0x2]=_0x4b338b[_0x4c4471+0x1],_0x136058[_0x20c539-0x1]=_0x4b338b[_0x4c4471+0x2],_0x136058[_0x20c539]=_0x1778a5?_0x4b338b[_0x4c4471+0x3]:_0x7a192a;}this['ctx'][_0xf1b3c3(0x153)](_0x58b0f1,_0x254151,_0x1156b7),this['_renderBoundaries']=[0x3e8,0x3e8,0x0,0x0];},'getValueAt':function(_0x33d8a2){var _0x48811b=_0x256d,_0x3a1737,_0x2e1dbf=this['shadowCtx'],_0x13702d=_0x2e1dbf['getImageData'](_0x33d8a2['x'],_0x33d8a2['y'],0x1,0x1),_0x272dda=_0x13702d['data'][0x3],_0x178c0d=this['_max'],_0x53a20f=this['_min'];return _0x3a1737=Math[_0x48811b(_0x18f913._0x3920e0)](_0x178c0d-_0x53a20f)*(_0x272dda/0xff)>>0x0,_0x3a1737;},'getDataURL':function(){var _0x38ad0e=_0x256d;return this[_0x38ad0e(_0x35e934._0x32c673)][_0x38ad0e(_0x35e934._0x41fe62)]();}},_0x13e7f1;}(),_0x25994a=function _0x3feb91(){var _0x3b2e71=_0x50b142,_0x4634a9=![];return _0x527aca[_0x3b2e71(0x186)]===_0x3b2e71(0x161)&&(_0x4634a9=_0x390115),_0x4634a9;}(),_0x48aa28={'merge':function(){var _0x4d61cc={},_0x4e07e2=arguments['length'];for(var _0x146740=0x0;_0x146740<_0x4e07e2;_0x146740++){var _0x5c92fc=arguments[_0x146740];for(var _0x423c9c in _0x5c92fc){_0x4d61cc[_0x423c9c]=_0x5c92fc[_0x423c9c];}}return _0x4d61cc;}},_0x445982=function _0x57af89(){var _0xf119e7={_0x2fe455:0x194},_0x4d8adf={_0x5aed24:0x1ae},_0x4e573b=function _0x516a09(){var _0x2afdc2={_0x310f66:0x185};function _0x26fd42(){var _0x427180=_0x256d;this[_0x427180(0x147)]={};}return _0x26fd42['prototype']={'on':function(_0xcd3c39,_0x5b3d98,_0x4f6cc2){var _0x1afdc5=_0x256d,_0x4f5dba=this['cStore'];!_0x4f5dba[_0xcd3c39]&&(_0x4f5dba[_0xcd3c39]=[]),_0x4f5dba[_0xcd3c39][_0x1afdc5(_0x2afdc2._0x310f66)](function(_0x5ae99d){var _0x1bec58=_0x1afdc5;return _0x5b3d98[_0x1bec58(0x129)](_0x4f6cc2,_0x5ae99d);});},'emit':function(_0x1d063d,_0x1897d8){var _0x44f556=this['cStore'];if(_0x44f556[_0x1d063d]){var _0x5d57e8=_0x44f556[_0x1d063d]['length'];for(var _0x1b8f18=0x0;_0x1b8f18<_0x5d57e8;_0x1b8f18++){var _0x948706=_0x44f556[_0x1d063d][_0x1b8f18];_0x948706(_0x1897d8);}}}},_0x26fd42;}(),_0x47a4ab=function(_0x1a8219){var _0xa69d7f={_0x18312c:0x15f},_0xa2af66=_0x256d,_0x174113=_0x1a8219[_0xa2af66(_0x4d8adf._0x5aed24)],_0x339a3d=_0x1a8219[_0xa2af66(0x157)],_0x54c31f=_0x1a8219['_store'];_0x339a3d['on'](_0xa2af66(0x1b3),_0x174113['renderPartial'],_0x174113),_0x339a3d['on'](_0xa2af66(0x16e),_0x174113[_0xa2af66(0x190)],_0x174113),_0x339a3d['on']('extremachange',function(_0x150021){var _0x56b835=_0xa2af66;_0x1a8219['_config'][_0x56b835(_0xa69d7f._0x18312c)]&&_0x1a8219['_config'][_0x56b835(_0xa69d7f._0x18312c)]({'min':_0x150021['min'],'max':_0x150021['max'],'gradient':_0x1a8219['_config']['gradient']||_0x1a8219['_config']['defaultGradient']});}),_0x54c31f[_0xa2af66(0x179)](_0x339a3d);};function _0x59a380(){var _0x2248ae=_0x256d,_0x234dcb=this['_config']=_0x48aa28['merge'](_0x527aca,arguments[0x0]||{});this['_coordinator']=new _0x4e573b();if(_0x234dcb[_0x2248ae(0x1a6)]){var _0x4ef745=_0x234dcb['plugin'];if(!_0x527aca[_0x2248ae(_0x39312e._0x1e271e)][_0x4ef745])throw new Error('Plugin\x20\x27'+_0x4ef745+'\x27\x20not\x20found.\x20Maybe\x20it\x20was\x20not\x20registered.');else{var _0x5aca82=_0x527aca['plugins'][_0x4ef745];this['_renderer']=new _0x5aca82['renderer'](_0x234dcb),this['_store']=new _0x5aca82[(_0x2248ae(0x14a))](_0x234dcb);}}else this['_renderer']=new _0x25994a(_0x234dcb),this['_store']=new _0x2b031e(_0x234dcb);_0x47a4ab(this);}return _0x59a380['prototype']={'addData':function(){var _0x2ba247=_0x256d;return this['_store']['addData'][_0x2ba247(0x197)](this['_store'],arguments),this;},'removeData':function(){var _0x5ec898=_0x256d;return this['_store'][_0x5ec898(0x140)]&&this['_store']['removeData']['apply'](this['_store'],arguments),this;},'setData':function(){var _0x40bd36=_0x256d;return this['_store'][_0x40bd36(0x18c)][_0x40bd36(0x197)](this[_0x40bd36(0x194)],arguments),this;},'setDataMax':function(){return this['_store']['setDataMax']['apply'](this['_store'],arguments),this;},'setDataMin':function(){var _0x1db59c=_0x256d;return this['_store']['setDataMin'][_0x1db59c(0x197)](this['_store'],arguments),this;},'configure':function(_0x2a259f){return this['_config']=_0x48aa28['merge'](this['_config'],_0x2a259f),this['_renderer']['updateConfig'](this['_config']),this['_coordinator']['emit']('renderall',this['_store']['_getInternalData']()),this;},'repaint':function(){var _0x29c5b1=_0x256d;return this[_0x29c5b1(_0x54bb52._0x5f3330)]['emit']('renderall',this['_store'][_0x29c5b1(0x1a0)]()),this;},'getData':function(){var _0x1df5fe=_0x256d;return this['_store'][_0x1df5fe(0x175)]();},'getDataURL':function(){return this['_renderer']['getDataURL']();},'getValueAt':function(_0x5d97aa){var _0x376bad=_0x256d;if(this[_0x376bad(_0xf119e7._0x2fe455)]['getValueAt'])return this['_store']['getValueAt'](_0x5d97aa);else return this['_renderer']['getValueAt']?this['_renderer']['getValueAt'](_0x5d97aa):null;}},_0x59a380;}(),_0x10f531={'create':function(_0x46dcd9){return new _0x445982(_0x46dcd9);},'register':function(_0x259bc3,_0x1e5cde){var _0x266633=_0x50b142;_0x527aca[_0x266633(0x17e)][_0x259bc3]=_0x1e5cde;}};return _0x10f531;}));}(heatmap$1));var heatmap=heatmap$1['exports'],h337=_mergeNamespaces({'__proto__':null,'default':heatmap},[heatmap$1[_0x3a8dc4(0x16c)]]),HeatMaterial='uniform\x20sampler2D\x20image;\x0a\x0aczm_material\x20czm_getMaterial(czm_materialInput\x20materialInput)\x20{\x0a\x20\x20czm_material\x20material\x20=\x20czm_getDefaultMaterial(materialInput);\x0a\x20\x20vec2\x20st\x20=\x20materialInput.st;\x0a\x20\x20vec4\x20colorImage\x20=\x20texture(image,\x20st);\x0a\x20\x20if(colorImage.rgb\x20==\x20vec3(1.0)\x20||\x20colorImage.rgb\x20==\x20vec3(0.0))\x20{\x0a\x20\x20\x20\x20discard;\x0a\x20\x20}\x0a\x20\x20material.diffuse\x20=\x20colorImage.rgb;\x0a\x20\x20material.alpha\x20=\x20colorImage.a;\x0a\x20\x20return\x20material;\x0a}\x0a';if(!heatmap$1['exports'][_0x3a8dc4(0x14b)])throw new Error('请引入\x20heatmap.js\x20库\x20');const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace[_0x3a8dc4(0x18d)]['BaseLayer'],DEF_HEATSTYLE={'maxOpacity':0.8,'minOpacity':0.1,'blur':0.85,'radius':0x19,'gradient':{0.4:'blue',0.6:_0x3a8dc4(0x1b6),0.8:'yellow',0.9:'red'}},DEF_STYLE={'arcRadiusScale':1.5,'arcBlurScale':1.5,'vertexFormat':Cesium['EllipsoidSurfaceAppearance'][_0x3a8dc4(0x132)]};class HeatLayer extends BaseLayer{constructor(_0x4fdfed={}){var _0x5c3b4f={_0x249d66:0x15c,_0x193479:0x180,_0xa4a7fc:0x149},_0x427262=_0x3a8dc4;super(_0x4fdfed),this['options']['maxCanvasSize']=this['options']['maxCanvasSize']??document['body'][_0x427262(0x1b0)],this[_0x427262(0x167)]['maxCanvasSize']=Math[_0x427262(_0x5c3b4f._0x249d66)](this['options'][_0x427262(_0x5c3b4f._0x193479)],0x1388),this['options']['minCanvasSize']=this[_0x427262(0x167)]['minCanvasSize']??document['body']['clientHeight'],this['options']['minCanvasSize']=Math['max'](this['options']['minCanvasSize'],0x2bc),this[_0x427262(0x167)][_0x427262(0x188)]={...DEF_HEATSTYLE,...this['options']['heatStyle']},this['options']['style']={...DEF_STYLE,...this['options'][_0x427262(_0x5c3b4f._0xa4a7fc)]};}get['layer'](){return this['_layer'];}get['heatStyle'](){var _0x39e61d=_0x3a8dc4;return this['options'][_0x39e61d(0x188)];}set[_0x3a8dc4(0x188)](_0x1b05a4){var _0xaa4aaa={_0x182600:0x188,_0x25cb9b:0x188},_0x3a0d23=_0x3a8dc4;this['options']['heatStyle']=mars3d__namespace['Util']['merge'](this['options'][_0x3a0d23(_0xaa4aaa._0x182600)],_0x1b05a4),this['_heat']&&(this['_heat']['configure'](this['options'][_0x3a0d23(_0xaa4aaa._0x25cb9b)]),this['_updatePositionsHook'](!![]));}get['style'](){return this['options']['style'];}set[_0x3a8dc4(0x149)](_0x4851ce){var _0x32d462={_0x29f968:0x181},_0x3fe93a=_0x3a8dc4;this['options'][_0x3fe93a(0x149)]=mars3d__namespace[_0x3fe93a(_0x32d462._0x29f968)]['merge'](this['options']['style'],_0x4851ce);}get[_0x3a8dc4(0x164)](){return this['_positions'];}set['positions'](_0x14596b){this['setPositions'](_0x14596b);}get['coordinates'](){var _0x26157b=_0x3a8dc4;const _0x57c6e8=[];return this[_0x26157b(0x156)]['forEach'](_0x1f95bb=>{_0x57c6e8['push'](_0x1f95bb['toArray']());}),_0x57c6e8;}get['rectangle'](){return this['_rectangle'];}['_setOptionsHook'](_0x449356,_0x17697e){_0x449356['positions']&&(this['positions']=_0x449356['positions']);}['_mountedHook'](){var _0x5a60dd={_0x443612:0x1c9},_0x268d1a=_0x3a8dc4;this['style']['type']==='image'?this[_0x268d1a(_0x5a60dd._0x443612)]=new mars3d__namespace['layer']['ImageLayer']({'crs':'EPSG:3857','private':!![]}):this['_layer']=new mars3d__namespace['layer']['GraphicLayer']({'private':!![]});}[_0x3a8dc4(0x19d)](){var _0x556fa7={_0x478ec8:0x176,_0x4ffbd8:0x131,_0x1cd6bd:0x167,_0x45a056:0x13b},_0x5bf2c3=_0x3a8dc4;this['_map'][_0x5bf2c3(_0x556fa7._0x478ec8)](this['_layer']),this['_container']=mars3d__namespace[_0x5bf2c3(0x19b)]['create']('div',_0x5bf2c3(_0x556fa7._0x4ffbd8),this['_map'][_0x5bf2c3(0x1a9)]),this['options']['positions']&&(this['positions']=this[_0x5bf2c3(_0x556fa7._0x1cd6bd)]['positions']),this['options']['flyTo']&&this[_0x5bf2c3(_0x556fa7._0x45a056)]();}[_0x3a8dc4(0x1b7)](){var _0x5707d6={_0x271d76:0x182},_0x58f467=_0x3a8dc4;this['_container']&&(mars3d__namespace['DomUtil']['remove'](this[_0x58f467(_0x5707d6._0x271d76)]),delete this['_container']),this[_0x58f467(0x162)](),this['_map']['removeLayer'](this['_layer']);}['addPosition'](_0xe5096e,_0x1cb436){var _0x19c634={_0x2f97d1:0x12c},_0x52c90c=_0x3a8dc4;this['_positions']=this['_positions']||[],this[_0x52c90c(_0x19c634._0x2f97d1)]['push'](_0xe5096e),this['_updatePositionsHook'](_0x1cb436);}['setPositions'](_0x45a195,_0x40e8f3){var _0x3802dd={_0x1ddd56:0x12c},_0x10de1c=_0x3a8dc4;this[_0x10de1c(_0x3802dd._0x1ddd56)]=_0x45a195,this['_updatePositionsHook'](_0x40e8f3);}['clear'](){var _0x27c9dc={_0x1de65f:0x1a3,_0x4d57c2:0x1c9},_0xbd9ee6=_0x3a8dc4;this['_graphic']&&(this['_layer']['removeGraphic'](this['_graphic'],!![]),delete this[_0xbd9ee6(0x189)]),this[_0xbd9ee6(_0x27c9dc._0x1de65f)]&&(this[_0xbd9ee6(_0x27c9dc._0x4d57c2)][_0xbd9ee6(0x148)](this['_graphic2'],!![]),delete this['_graphic2']);}['_updatePositionsHook'](_0x21f754){var _0x3ccb41={_0x206886:0x149,_0x34b8ff:0x1a3,_0x3e29b8:0x189,_0x131e00:0x16a},_0x1778db=_0x3a8dc4;if(!this['show']||!this[_0x1778db(0x130)]||!this['positions']||this[_0x1778db(0x164)][_0x1778db(0x1ab)]===0x0)return this;const _0x5098e8=this['_getHeatCanvas']();if(this[_0x1778db(_0x3ccb41._0x206886)]['type']==='image')this[_0x1778db(0x1c9)]['setOptions']({'url':_0x5098e8['toDataURL']('image/png',0x1),'rectangle':this['_rectangle'],'opacity':this['style'][_0x1778db(0x127)]});else this['style'][_0x1778db(0x1bd)]?this['_graphic']&&_0x21f754?(this['_graphic'][_0x1778db(0x1c4)]['image']=_0x5098e8,this[_0x1778db(0x189)]['uniforms'][_0x1778db(0x16a)]=this['_getArcHeatCanvas'](),this[_0x1778db(_0x3ccb41._0x34b8ff)]&&(this['_graphic2']['uniforms']['image']=_0x5098e8,this[_0x1778db(0x1a3)]['uniforms']['bumpMap']=this[_0x1778db(_0x3ccb41._0x3e29b8)]['uniforms'][_0x1778db(_0x3ccb41._0x131e00)])):this['_createArcGraphic'](_0x5098e8):this[_0x1778db(_0x3ccb41._0x3e29b8)]&&_0x21f754?this['_graphic']['uniforms']['image']=_0x5098e8:this['_createGraphic'](_0x5098e8);return this;}['_createGraphic'](_0x2cfb8e){var _0x48e97c={_0x538030:0x162},_0x3b47d3=_0x3a8dc4;this[_0x3b47d3(_0x48e97c._0x538030)](),this['_graphic']=new mars3d__namespace['graphic']['RectanglePrimitive']({...this['options'],'rectangle':this['_rectangle'],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'material':mars3d__namespace['MaterialUtil']['createMaterial'](mars3d__namespace[_0x3b47d3(0x1a2)]['Image2'],{'image':_0x2cfb8e}),'flat':!![]})}),this[_0x3b47d3(0x1c9)]['addGraphic'](this[_0x3b47d3(0x189)]);}['_createArcGraphic'](_0x1a62ea){var _0x47be82={_0x3cd857:0x12b,_0x33d9a7:0x19f,_0x2bfe06:0x1c3,_0x173685:0x170,_0x30b953:0x137,_0x78d92a:0x145,_0x3260e5:0x149,_0x267e16:0x12a,_0x22d34b:0x17f,_0x104ccd:0x167,_0x39975a:0x17f},_0x501938=_0x3a8dc4;this['clear']();const _0x2bbdaf=Cesium['RenderState']['fromCache']({'cull':{'enabled':!![]},'depthTest':{'enabled':!![]},'stencilTest':{'enabled':!![],'frontFunction':Cesium[_0x501938(_0x47be82._0x3cd857)][_0x501938(0x150)],'frontOperation':{'fail':Cesium['StencilOperation']['KEEP'],'zFail':Cesium[_0x501938(0x170)][_0x501938(0x1c3)],'zPass':Cesium['StencilOperation'][_0x501938(_0x47be82._0x33d9a7)]},'backFunction':Cesium[_0x501938(0x12b)]['ALWAYS'],'backOperation':{'fail':Cesium['StencilOperation']['KEEP'],'zFail':Cesium['StencilOperation'][_0x501938(_0x47be82._0x2bfe06)],'zPass':Cesium[_0x501938(_0x47be82._0x173685)]['REPLACE']},'reference':0x2,'mask':0x2},'blending':Cesium[_0x501938(_0x47be82._0x30b953)]['ALPHA_BLEND']}),_0x325686=Math['floor'](this['style'][_0x501938(_0x47be82._0x78d92a)]??this['_mBoundsMax']*0.02)+0.1;this['style']['diffHeight']&&delete this[_0x501938(_0x47be82._0x3260e5)]['diffHeight'];const _0x16f200=(this['style']['splitNum'],0x64);let _0x254df1=Math[_0x501938(0x17a)](this[_0x501938(0x17f)][_0x501938(_0x47be82._0x267e16)],this[_0x501938(_0x47be82._0x22d34b)]['width']);this[_0x501938(_0x47be82._0x3260e5)]['granularity']=_0x254df1/=_0x16f200;const _0x2673f8=new Cesium['Material']({'fabric':{'uniforms':{'image':_0x1a62ea,'repeat':new Cesium['Cartesian2'](0x1,0x1),'color':new Cesium['Color'](0x1,0x1,0x1,0x0),'bumpMap':this[_0x501938(0x1ca)]()},'source':HeatMaterial},'translucent':!![]}),_0x40e466=this[_0x501938(0x149)][_0x501938(0x19e)]||0x1;this['_graphic']=new mars3d__namespace['graphic']['RectanglePrimitive']({...this[_0x501938(0x167)],'rectangle':this['_rectangle'],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'flat':!![],'aboveGround':!![],'renderState':_0x2bbdaf,'material':_0x2673f8,'vertexShaderSource':getVertexShaderSource(_0x325686*_0x40e466)})}),this['_layer']['addGraphic'](this['_graphic']),this['style']['arcDirection']===0x0&&(this['_graphic2']=new mars3d__namespace[(_0x501938(0x133))][(_0x501938(0x198))]({...this[_0x501938(_0x47be82._0x104ccd)],'rectangle':this[_0x501938(_0x47be82._0x39975a)],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'flat':!![],'aboveGround':!![],'renderState':_0x2bbdaf,'material':_0x2673f8,'vertexShaderSource':getVertexShaderSource(-_0x325686)})}),this['_layer'][_0x501938(0x171)](this['_graphic2']));}['getRectangle'](_0x30ab3f){var _0x45ec82={_0x5bc1d0:0x1b8},_0x11677c=_0x3a8dc4;return _0x30ab3f!==null&&_0x30ab3f!==void 0x0&&_0x30ab3f['isFormat']&&this['_rectangle']?mars3d__namespace[_0x11677c(0x1ad)][_0x11677c(_0x45ec82._0x5bc1d0)](this['_rectangle']):this['_rectangle'];}[_0x3a8dc4(0x128)](){var _0x61a742={_0x45ad17:0x167,_0x3728db:0x152,_0x2153a9:0x14e,_0xa2ceb9:0x152,_0x1d3e8a:0x13c,_0x386756:0x17a,_0x1ded8d:0x155,_0x5e468e:0x1a1,_0x2cd47c:0x13c,_0x40a5c9:0x14d},_0x4e2cbf={_0x4588b4:0x159,_0x1d3ef8:0x16d,_0x28cf0b:0x136,_0x4cbd81:0x15c},_0x46f2e5=_0x3a8dc4;const _0xeca01c=this[_0x46f2e5(0x12c)],_0x369b25=[];let _0x58d230,_0x4da403,_0x895611,_0x170a2f;_0xeca01c['forEach'](_0x21322a=>{var _0x1ce3d5=_0x46f2e5;const _0x2c38ff=mars3d__namespace[_0x1ce3d5(_0x4e2cbf._0x4588b4)]['parse'](_0x21322a);if(!_0x2c38ff)return;_0x2c38ff['value']=_0x21322a['value']||0x1,!this[_0x1ce3d5(0x167)]['rectangle']&&(_0x58d230===undefined?(_0x58d230=_0x2c38ff['lng'],_0x4da403=_0x2c38ff['lng'],_0x895611=_0x2c38ff[_0x1ce3d5(0x16d)],_0x170a2f=_0x2c38ff[_0x1ce3d5(_0x4e2cbf._0x1d3ef8)]):(_0x58d230=Math[_0x1ce3d5(0x15c)](_0x58d230,_0x2c38ff[_0x1ce3d5(_0x4e2cbf._0x28cf0b)]),_0x4da403=Math['max'](_0x4da403,_0x2c38ff['lng']),_0x895611=Math[_0x1ce3d5(_0x4e2cbf._0x4cbd81)](_0x895611,_0x2c38ff[_0x1ce3d5(0x16d)]),_0x170a2f=Math[_0x1ce3d5(0x17a)](_0x170a2f,_0x2c38ff['lat']))),_0x369b25['push'](_0x2c38ff);});_0x58d230===_0x4da403&&(_0x58d230-=0.000001,_0x4da403+=0.000001);_0x895611===_0x170a2f&&(_0x895611-=0.000001,_0x170a2f+=0.000001);let _0x37c396=this[_0x46f2e5(_0x61a742._0x45ad17)][_0x46f2e5(0x154)]||{'xmin':_0x58d230,'xmax':_0x4da403,'ymin':_0x895611,'ymax':_0x170a2f};const _0x4a3d96=getMercatorBounds(_0x37c396),_0x587631=Math['abs'](_0x4a3d96['xmax']-_0x4a3d96[_0x46f2e5(_0x61a742._0x3728db)]),_0x15ef68=Math['abs'](_0x4a3d96['ymax']-_0x4a3d96['ymin']),_0x1a094a=Math['max'](_0x587631,_0x15ef68),_0x3c1aa1=Math['min'](_0x587631,_0x15ef68);this[_0x46f2e5(0x1ac)]=_0x1a094a;let _0x412f37=0x1;if(_0x1a094a>this['options']['maxCanvasSize'])_0x412f37=_0x1a094a/this['options']['maxCanvasSize'],_0x3c1aa1/_0x412f37<this['options']['minCanvasSize']&&(_0x412f37=_0x3c1aa1/this[_0x46f2e5(0x167)][_0x46f2e5(_0x61a742._0x2153a9)]);else _0x3c1aa1<this['options'][_0x46f2e5(0x14e)]&&(_0x412f37=_0x3c1aa1/this['options'][_0x46f2e5(0x14e)],_0x1a094a/_0x412f37>this['options'][_0x46f2e5(0x180)]&&(_0x412f37=_0x1a094a/this['options']['maxCanvasSize']));const _0x888a80=this['heatStyle'][_0x46f2e5(0x12e)]*1.5,_0x21258d=_0x587631/_0x412f37+_0x888a80*0x2,_0xb90f69=_0x15ef68/_0x412f37+_0x888a80*0x2,_0x15fbdb=_0x888a80*_0x412f37;_0x4a3d96[_0x46f2e5(_0x61a742._0xa2ceb9)]-=_0x15fbdb,_0x4a3d96['ymin']-=_0x15fbdb,_0x4a3d96[_0x46f2e5(0x1a5)]+=_0x15fbdb,_0x4a3d96[_0x46f2e5(0x155)]+=_0x15fbdb,_0x4a3d96['xmin']=Math['max'](_0x4a3d96['xmin'],-20037508.34),_0x4a3d96['xmax']=Math['min'](_0x4a3d96['xmax'],20037508.34),_0x4a3d96[_0x46f2e5(_0x61a742._0x1d3e8a)]=Math[_0x46f2e5(_0x61a742._0x386756)](_0x4a3d96[_0x46f2e5(0x13c)],-20037508.34),_0x4a3d96[_0x46f2e5(_0x61a742._0x1ded8d)]=Math['min'](_0x4a3d96['ymax'],20037508.34),this[_0x46f2e5(_0x61a742._0x5e468e)]=_0x412f37,_0x37c396=geLatLngBounds(_0x4a3d96),this[_0x46f2e5(0x17f)]=Cesium['Rectangle'][_0x46f2e5(0x17c)](_0x37c396['xmin'],_0x37c396[_0x46f2e5(0x13c)],_0x37c396['xmax'],_0x37c396['ymax']);let _0x7683dd=_0x369b25[0x0]['value']??0x1,_0x5ee104=_0x369b25[0x0][_0x46f2e5(0x1aa)]??0x0;const _0x5cc730=[];_0x369b25['forEach'](_0xc9c0a1=>{var _0x3d007b=_0x46f2e5;const _0x3ecfb8=mars3d__namespace['PointTrans']['lonlat2mercator']([_0xc9c0a1['lng'],_0xc9c0a1['lat']]),_0x230de0=_0xc9c0a1['value']||0x1,_0x5f3c59=Math[_0x3d007b(0x15e)]((_0x3ecfb8[0x0]-_0x4a3d96[_0x3d007b(0x152)])/_0x412f37),_0x35d480=Math['round']((_0x4a3d96['ymax']-_0x3ecfb8[0x1])/_0x412f37);_0x7683dd=Math['max'](_0x7683dd,_0x230de0),_0x5ee104=Math['min'](_0x5ee104,_0x230de0),_0x5cc730[_0x3d007b(0x185)]({'x':_0x5f3c59,'y':_0x35d480,'value':_0x230de0});});const _0x3476e9={'min':this['options']['min']??_0x5ee104,'max':this[_0x46f2e5(0x167)]['max']??_0x7683dd,'data':_0x5cc730};this[_0x46f2e5(0x13a)]=_0x3476e9;if(!this['_last_mBounds']||_0x4a3d96[_0x46f2e5(_0x61a742._0x3728db)]!==this['_last_mBounds']['xmin']||_0x4a3d96['ymin']!==this[_0x46f2e5(0x18f)][_0x46f2e5(_0x61a742._0x2cd47c)]||_0x4a3d96['xmax']!==this['_last_mBounds'][_0x46f2e5(0x1a5)]||_0x4a3d96['ymax']!==this['_last_mBounds']['ymax']){this['_last_mBounds']=_0x4a3d96,this['_container'][_0x46f2e5(0x149)][_0x46f2e5(0x1c1)]=_0x46f2e5(_0x61a742._0x40a5c9)+_0x21258d+'px;height:'+_0xb90f69+'px;';const _0x4798bc={...this['heatStyle'],'container':this['_container']};!this['_heat']?this['_heat']=heatmap$1['exports']['create'](_0x4798bc):this['_heat']['configure'](_0x4798bc);}this['_heat']['setData'](_0x3476e9);const _0x14c92f=mars3d__namespace['DomUtil']['copyCanvas'](this['_heat']['_renderer']['canvas']);return _0x14c92f;}[_0x3a8dc4(0x1ca)](){var _0x23e023={_0x3c8672:0x141,_0xdbf652:0x16b,_0x514a5a:0x1a7,_0x38f1b3:0x19b,_0x5d82d3:0x1ae},_0x14964a=_0x3a8dc4;this[_0x14964a(_0x23e023._0x3c8672)][_0x14964a(_0x23e023._0xdbf652)]({'radius':this['heatStyle'][_0x14964a(0x12e)]*this['style'][_0x14964a(0x143)],'blur':this['heatStyle']['blur']*this[_0x14964a(0x149)][_0x14964a(0x172)],'gradient':this['heatStyle']['gradientArc']||{0.25:_0x14964a(0x13f),0.55:_0x14964a(_0x23e023._0x514a5a),0.85:'rgb(216,216,216)',0x1:_0x14964a(0x183)}});const _0x19561b=mars3d__namespace[_0x14964a(_0x23e023._0x38f1b3)]['copyCanvas'](this['_heat'][_0x14964a(_0x23e023._0x5d82d3)][_0x14964a(0x13d)]);return this[_0x14964a(0x141)]['configure'](this['options']['heatStyle']),_0x19561b;}['getPointData'](_0x532c78){var _0x13a565={_0xac70df:0x158,_0x5e733f:0x136,_0x43a74d:0x1ae,_0x24a8bb:0x18b},_0x272ae5=_0x3a8dc4;const _0x5e0777=mars3d__namespace['LngLatPoint']['parse'](_0x532c78);if(!_0x5e0777)return{};const _0x1fe929=mars3d__namespace[_0x272ae5(_0x13a565._0xac70df)]['lonlat2mercator']([_0x5e0777[_0x272ae5(_0x13a565._0x5e733f)],_0x5e0777[_0x272ae5(0x16d)]]),_0x463d16=this['_last_mBounds'],_0x30753b=Math['round']((_0x1fe929[0x0]-_0x463d16[_0x272ae5(0x152)])/this['_scale']),_0x4702fe=Math['round']((_0x463d16['ymax']-_0x1fe929[0x1])/this['_scale']),_0xa6611=this['_heat']['getValueAt']({'x':_0x30753b,'y':_0x4702fe}),_0x5c8b87=this[_0x272ae5(0x141)][_0x272ae5(_0x13a565._0x43a74d)]['ctx']['getImageData'](_0x30753b-0x1,_0x4702fe-0x1,0x1,0x1)['data'];return{'x':_0x30753b,'y':_0x4702fe,'value':_0xa6611,'color':_0x272ae5(_0x13a565._0x24a8bb)+_0x5c8b87[0x0]+','+_0x5c8b87[0x1]+','+_0x5c8b87[0x2]+','+_0x5c8b87[0x3]+')'};}}function _0x256d(_0xc4e94e,_0x3b4713){var _0x8dd452=_0x8dd4();return _0x256d=function(_0x256d1c,_0x4b5f70){_0x256d1c=_0x256d1c-0x127;var _0x5b30ee=_0x8dd452[_0x256d1c];return _0x5b30ee;},_0x256d(_0xc4e94e,_0x3b4713);}mars3d__namespace['LayerUtil']['register']('heat',HeatLayer),mars3d__namespace[_0x3a8dc4(0x18d)]['HeatLayer']=HeatLayer,mars3d__namespace['h337']=h337;function _0x8dd4(){var _0x196d8b=['apply','RectanglePrimitive','5126450LstXJv','createElement','DomUtil','mercator2lonlat','_addedHook','arcDirection','REPLACE','_getInternalData','_scale','MaterialType','_graphic2','blur','xmax','plugin','rgb(140,140,140)','152nBteiB','container','value','length','_mBoundsMax','PolyUtil','_renderer','defaultBlur','clientWidth','_yField','getContext','renderpartial','__esModule','defaultValueField','green','_removedHook','formatRectangle','_cfgRadius','968MGWjYO','data','emit','arc','getImageData','addColorStop','useGradientOpacity','cssText','_organiseData','KEEP','uniforms','undefined','_height','4084620FffqBy','forEach','_layer','_getArcHeatCanvas','opacity','_getHeatCanvas','call','height','StencilFunction','_positions','abs','radius','372267nGpDQB','_map','mars3d-heatmap\x20mars3d-hideDiv','VERTEX_FORMAT','graphic','shadowCtx','1390200zKFsIu','lng','BlendingState','_min','shadowCanvas','_last_heatData','flyToByAnimationEnd','ymin','canvas','5074044dslxHg','rgb(0,0,0)','removeData','_heat','keys','arcRadiusScale','_colorize','diffHeight','fill','cStore','removeGraphic','style','store','create','className','width:','minCanvasSize','_radi','ALWAYS','rgb(255,0,0)','xmin','putImageData','rectangle','ymax','points','_coordinator','PointTrans','LngLatPoint','fillRect','position:absolute;left:0;top:0;','min','_opacity','round','onExtremaChange','_drawAlpha','canvas2d','clear','5424YsEiSA','positions','width','_useGradientOpacity','options','_onExtremaChange','_blur','bumpMap','configure','exports','lat','renderall','4691526acWDNF','StencilOperation','addGraphic','arcBlurScale','_palette','_max','getData','addLayer','_width','_renderBoundaries','setCoordinator','max','backgroundColor','fromDegrees','toDataURL','plugins','_rectangle','maxCanvasSize','Util','_container','rgb(255,255,255)','_data','push','defaultRenderer','gradient','heatStyle','_graphic','5NWZxIP','rgba(','setData','layer','fillStyle','_last_mBounds','renderAll','yellow','createLinearGradient','rgba(0,0,0,1)','_store','h337','lonlat2mercator'];_0x8dd4=function(){return _0x196d8b;};return _0x8dd4();}function getMercatorBounds(_0x145dfe){var _0x104138=_0x3a8dc4;const _0x580769=mars3d__namespace['PointTrans']['lonlat2mercator']([_0x145dfe['xmin'],_0x145dfe['ymin']]),_0x542f9d=mars3d__namespace['PointTrans'][_0x104138(0x196)]([_0x145dfe['xmax'],_0x145dfe['ymax']]);return{'xmin':_0x580769[0x0],'ymin':_0x580769[0x1],'xmax':_0x542f9d[0x0],'ymax':_0x542f9d[0x1]};}function geLatLngBounds(_0x1b8097){var _0x18bdc1={_0x39ed49:0x158,_0x5c11fd:0x19c},_0x5f38d8=_0x3a8dc4;const _0x2b4556=mars3d__namespace[_0x5f38d8(_0x18bdc1._0x39ed49)]['mercator2lonlat']([_0x1b8097['xmin'],_0x1b8097['ymin']]),_0x21b4ca=mars3d__namespace['PointTrans'][_0x5f38d8(_0x18bdc1._0x5c11fd)]([_0x1b8097['xmax'],_0x1b8097['ymax']]);return{'xmin':_0x2b4556[0x0],'ymin':_0x2b4556[0x1],'xmax':_0x21b4ca[0x0],'ymax':_0x21b4ca[0x1]};}function getVertexShaderSource(_0x1e35f9){return'in\x20vec3\x20position3DHigh;\x0a\x20\x20in\x20vec3\x20position3DLow;\x0a\x20\x20in\x20vec2\x20st;\x0a\x20\x20in\x20float\x20batchId;\x0a\x20\x20uniform\x20sampler2D\x20bumpMap_3;\x0a\x20\x20out\x20vec3\x20v_positionMC;\x0a\x20\x20out\x20vec3\x20v_positionEC;\x0a\x20\x20out\x20vec2\x20v_st;\x0a\x0a\x20\x20void\x20main()\x0a\x20\x20{\x0a\x20\x20\x20\x20vec4\x20p\x20=\x20czm_computePosition();\x0a\x20\x20\x20\x20v_positionMC\x20=\x20position3DHigh\x20+\x20position3DLow;\x0a\x20\x20\x20\x20v_positionEC\x20=\x20(czm_modelViewRelativeToEye\x20*\x20p).xyz;\x0a\x20\x20\x20\x20v_st\x20=\x20st;\x0a\x20\x20\x20\x20vec4\x20color\x20=\x20texture(bumpMap_3,\x20v_st);\x0a\x20\x20\x20\x20float\x20centerBump\x20=\x20distance(vec3(0.0),color.rgb);\x0a\x20\x20\x20\x20vec3\x20upDir\x20=\x20normalize(v_positionMC.xyz);\x0a\x20\x20\x20\x20vec3\x20disPos\x20=\x20upDir\x20*\x20centerBump\x20*\x20'+_0x1e35f9+';\x0a\x20\x20\x20\x20p\x20+=vec4(disPos,0.0);\x0a\x20\x20\x20\x20gl_Position\x20=\x20czm_modelViewProjectionRelativeToEye\x20*\x20p;\x0a\x20\x20}\x0a';}exports['HeatLayer']=HeatLayer,Object['defineProperty'](exports,'__esModule',{'value':!![]});
}));
