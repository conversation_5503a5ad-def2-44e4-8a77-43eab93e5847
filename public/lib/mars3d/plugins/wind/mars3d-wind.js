/**
 * Mars3D平台插件,支持气象 风向图 功能插件  mars3d-wind
 *
 * 版本信息：v3.7.5
 * 编译日期：2024-03-05 20:02:19
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2024-01-15
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-wind"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';function _0x3bd3(){const _0x2cf9bd=['push','globe','execute','create','commandToExecute','RGBA','PI_OVER_THREE','windTextures','_tomap','getPixelSize','auto','removeAll','canvasWidth','particlesTextureSize','commandList','Draw','_calcUV','TRIANGLES','_calc_speedRate','drawingBufferHeight','createComputingPrimitives','6265760XcFjIO','SCENE3D','clientHeight','Geometry','bindEvent','OPAQUE','context','mouse_move','FLOAT','udata','getWind','canvasContext','fragmentShaderSource','#ffffff','colors','clearCommand','preRender','nextTrails','data','RenderState','10022940jqFAdZ','clear','DISABLE_LOG_DEPTH_FRAGMENT_WRITE','ymax','show','609212ioBpvr','refreshTimer','nextParticlesSpeed','getContext','speed','_updateIng','mode','scene','animateFrame','currentParticlesSpeed','ColorRamp','lineWidth','_onMouseDownEvent','6536REMJNI','_map','primitives','setOptions','random','cols','sqrt','east','blending','wheel','BoundingSphere','framebuffer','beginPath','framebuffers','removeChild','rawRenderState','mouseUp','GeometryAttributes','xmax','Sampler','xmin','87842KZwyXe','nextParticlesPosition','PrimitiveType','Cesium','tlng','canvas','array','TextureMagnificationFilter','TextureMinificationFilter','color','IDENTITY','createCommand','nextTrailsDepth','latRange','particlesWind','Cartesian3','resize','defined','WindLayer','fixedHeight','createWindTextures','SceneMode','12105gAbrBR','ceil','updateSpeed','createTexture','depthTexture','off','createSegmentsGeometry','floor','getUVByXY','2964930TLAbfY','textures','reverseY','wind','visibility','currentParticlesPosition','globeBoundingSphere','postMessage','setData','min','addEventListener','mouseMove','windField','rows','speedRate','lat','3564jftKUP','primitiveType','nextTrailsColor','stroke','pow','lev','attributeLocations','getOptions','fromCache','EventType','mars3d-canvasWind','globalAlpha','dimensions','ymin','camera','maxAge','depthMask','_pointerEvents','addPrimitives','levmin','STATIC_DRAW','outputTexture','Cartesian2','length','layer','WindUtil','BufferUsage','mouseDown','Framebuffer','_canrefresh','commandType','_maxAge','currentTrailsColor','destroy','updatePosition','abs','mod','particlesComputing','ComponentDatatype','Math','PI_OVER_TWO','_onMap_preRenderEvent','viewerParameters','visible','options','55URdune','forEach','postProcessingSpeed','ZERO','_removedHook','worker','_createCanvas','strokeStyle','keys','LayerUtil','NEAREST','particlesTextures','PixelDatatype','2qoLhPA','pixelSize','lon','computeViewRectangle','TWO_PI','bind','toRadians','windData','clearFramebuffers','left','uniformMap','_onMapWhellEvent','add','Compute','particleSystem','particlesNumber','style','randomizeParticles','_updateIng2','age','shaderProgram','trails','segments','max','fromGeometry','init','postProcessingPosition','redraw','createFramebuffer','8582fCEDVj','geometry','now','zIndex','maxParticles','vertexShaderSource','autoClear','lonRange','once','toDegrees','updateViewerParameters','lng','height','clientWidth','colorTable','_onMouseMoveEvent','default','particlesRendering','createRenderingFramebuffers','particles','pointer-events','ShaderSource','cos','frameRate','tlat','CanvasWindField','ALWAYS','mouse_down','currentTrails','createRawRenderState','frameTime','vdata','_onMouseUpEvent','createParticlesTextures'];_0x3bd3=function(){return _0x2cf9bd;};return _0x3bd3();}const _0x3a1149=_0x2c91;(function(_0x417b4c,_0x5179e2){const _0x57c8b1=_0x2c91,_0x52f6d9=_0x417b4c();while(!![]){try{const _0x101900=parseInt(_0x57c8b1(0x215))/0x1*(parseInt(_0x57c8b1(0x1ac))/0x2)+-parseInt(_0x57c8b1(0x1cb))/0x3+parseInt(_0x57c8b1(0x18a))/0x4+-parseInt(_0x57c8b1(0x1c2))/0x5*(parseInt(_0x57c8b1(0x1db))/0x6)+-parseInt(_0x57c8b1(0x232))/0x7*(-parseInt(_0x57c8b1(0x197))/0x8)+-parseInt(_0x57c8b1(0x185))/0x9+parseInt(_0x57c8b1(0x171))/0xa*(parseInt(_0x57c8b1(0x208))/0xb);if(_0x101900===_0x5179e2)break;else _0x52f6d9['push'](_0x52f6d9['shift']());}catch(_0x211709){_0x52f6d9['push'](_0x52f6d9['shift']());}}}(_0x3bd3,0xcbc3f));function _interopNamespace(_0x5adadf){const _0x322f41=_0x2c91;if(_0x5adadf&&_0x5adadf['__esModule'])return _0x5adadf;var _0x46efc5=Object['create'](null);return _0x5adadf&&Object[_0x322f41(0x210)](_0x5adadf)['forEach'](function(_0x112f81){if(_0x112f81!=='default'){var _0x455962=Object['getOwnPropertyDescriptor'](_0x5adadf,_0x112f81);Object['defineProperty'](_0x46efc5,_0x112f81,_0x455962['get']?_0x455962:{'enumerable':!![],'get':function(){return _0x5adadf[_0x112f81];}});}}),_0x46efc5[_0x322f41(0x14a)]=_0x5adadf,_0x46efc5;}var mars3d__namespace=_interopNamespace(mars3d);const Cesium$7=mars3d__namespace['Cesium'];function getU(_0x2ec255,_0x2815ec){const _0x252db3=_0x2c91,_0x18ff29=_0x2ec255*Math[_0x252db3(0x150)](Cesium$7[_0x252db3(0x202)][_0x252db3(0x21b)](_0x2815ec));return _0x18ff29;}function getV(_0x8c3afe,_0x42b3d6){const _0x1af040=_0x8c3afe*Math['sin'](Cesium$7['Math']['toRadians'](_0x42b3d6));return _0x1af040;}function getSpeed(_0x2d0d0d,_0x538781){const _0x48bb14=_0x2c91,_0x5b9332=Math[_0x48bb14(0x19d)](Math[_0x48bb14(0x1df)](_0x2d0d0d,0x2)+Math['pow'](_0x538781,0x2));return _0x5b9332;}function getDirection(_0x2858aa,_0x5224a0){const _0x20bc5b=_0x2c91;let _0x5ccd8e=Cesium$7[_0x20bc5b(0x202)]['toDegrees'](Math['atan2'](_0x5224a0,_0x2858aa));return _0x5ccd8e+=_0x5ccd8e<0x0?0x168:0x0,_0x5ccd8e;}var WindUtil={'__proto__':null,'getU':getU,'getV':getV,'getSpeed':getSpeed,'getDirection':getDirection};const Cesium$6=mars3d__namespace[_0x3a1149(0x1af)];class CustomPrimitive{constructor(_0x59d50f){const _0x35d253=_0x3a1149;this['commandType']=_0x59d50f[_0x35d253(0x1f9)],this[_0x35d253(0x233)]=_0x59d50f['geometry'],this['attributeLocations']=_0x59d50f['attributeLocations'],this['primitiveType']=_0x59d50f[_0x35d253(0x1dc)],this[_0x35d253(0x21f)]=_0x59d50f[_0x35d253(0x21f)],this[_0x35d253(0x237)]=_0x59d50f['vertexShaderSource'],this['fragmentShaderSource']=_0x59d50f[_0x35d253(0x17d)],this[_0x35d253(0x1a6)]=_0x59d50f['rawRenderState'],this['framebuffer']=_0x59d50f['framebuffer'],this[_0x35d253(0x1f0)]=_0x59d50f['outputTexture'],this['autoClear']=_0x59d50f[_0x35d253(0x238)]??![],this['preExecute']=_0x59d50f['preExecute'],this['show']=!![],this['commandToExecute']=undefined,this['clearCommand']=undefined,this['autoClear']&&(this[_0x35d253(0x180)]=new Cesium$6['ClearCommand']({'color':new Cesium$6['Color'](0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':this['framebuffer'],'pass':Cesium$6['Pass'][_0x35d253(0x176)]}));}[_0x3a1149(0x1b7)](_0x1d1915){const _0x5772f3=_0x3a1149;switch(this['commandType']){case _0x5772f3(0x16b):{const _0x148375=Cesium$6['VertexArray'][_0x5772f3(0x22d)]({'context':_0x1d1915,'geometry':this[_0x5772f3(0x233)],'attributeLocations':this[_0x5772f3(0x1e1)],'bufferUsage':Cesium$6[_0x5772f3(0x1f5)]['STATIC_DRAW']}),_0x1e66d4=Cesium$6['ShaderProgram'][_0x5772f3(0x1e3)]({'context':_0x1d1915,'attributeLocations':this['attributeLocations'],'vertexShaderSource':this[_0x5772f3(0x237)],'fragmentShaderSource':this['fragmentShaderSource']}),_0x4583b5=Cesium$6[_0x5772f3(0x184)]['fromCache'](this[_0x5772f3(0x1a6)]);return new Cesium$6['DrawCommand']({'primitiveType':this['primitiveType'],'shaderProgram':_0x1e66d4,'vertexArray':_0x148375,'modelMatrix':Cesium$6['Matrix4'][_0x5772f3(0x1b6)],'renderState':_0x4583b5,'uniformMap':this['uniformMap'],'castShadows':![],'receiveShadows':![],'framebuffer':this['framebuffer'],'pass':Cesium$6['Pass']['OPAQUE'],'pickOnly':!![],'owner':this});}case'Compute':{return new Cesium$6['ComputeCommand']({'owner':this,'fragmentShaderSource':this['fragmentShaderSource'],'uniformMap':this[_0x5772f3(0x21f)],'outputTexture':this['outputTexture'],'persists':!![]});}}}['setGeometry'](_0x416409,_0x2cf88b){const _0xd3b95c=_0x3a1149;this[_0xd3b95c(0x233)]=_0x2cf88b;const _0x2a9119=Cesium$6['VertexArray']['fromGeometry']({'context':_0x416409,'geometry':this['geometry'],'attributeLocations':this['attributeLocations'],'bufferUsage':Cesium$6['BufferUsage']['STATIC_DRAW']});this[_0xd3b95c(0x160)]['vertexArray']=_0x2a9119;}['update'](_0x39071d){const _0x31ff2d=_0x3a1149;if(!this['show'])return;if(_0x39071d[_0x31ff2d(0x190)]!==Cesium$6['SceneMode']['SCENE3D'])return;!Cesium$6[_0x31ff2d(0x1bd)](this['commandToExecute'])&&(this[_0x31ff2d(0x160)]=this['createCommand'](_0x39071d['context'])),Cesium$6['defined'](this['preExecute'])&&this['preExecute'](),Cesium$6['defined'](this['clearCommand'])&&_0x39071d['commandList']['push'](this['clearCommand']),_0x39071d[_0x31ff2d(0x16a)]['push'](this['commandToExecute']);}['isDestroyed'](){return![];}[_0x3a1149(0x1fc)](){const _0x158f1f=_0x3a1149;if(this['clearCommand']){var _0x37f25e,_0x7c2e7a;(_0x37f25e=this['clearCommand'])!==null&&_0x37f25e!==void 0x0&&_0x37f25e['vertexArray']&&this['clearCommand']['vertexArray']['destroy'](),(_0x7c2e7a=this[_0x158f1f(0x180)])!==null&&_0x7c2e7a!==void 0x0&&_0x7c2e7a[_0x158f1f(0x229)]&&this['clearCommand']['shaderProgram']['destroy'](),delete this['clearCommand'];}return this[_0x158f1f(0x160)]&&(this[_0x158f1f(0x160)]['vertexArray']&&this[_0x158f1f(0x160)]['vertexArray']['destroy'](),this['commandToExecute']['shaderProgram']&&this['commandToExecute'][_0x158f1f(0x229)]['destroy'](),delete this['commandToExecute']),Cesium$6['destroyObject'](this);}}const Cesium$5=mars3d__namespace[_0x3a1149(0x1af)],Util=(function(){const _0x5e79f7=function(){const _0x2611c6=_0x2c91,_0x3108d6=new Cesium$5[(_0x2611c6(0x174))]({'attributes':new Cesium$5[(_0x2611c6(0x1a8))]({'position':new Cesium$5['GeometryAttribute']({'componentDatatype':Cesium$5['ComponentDatatype']['FLOAT'],'componentsPerAttribute':0x3,'values':new Float32Array([-0x1,-0x1,0x0,0x1,-0x1,0x0,0x1,0x1,0x0,-0x1,0x1,0x0])}),'st':new Cesium$5['GeometryAttribute']({'componentDatatype':Cesium$5['ComponentDatatype'][_0x2611c6(0x179)],'componentsPerAttribute':0x2,'values':new Float32Array([0x0,0x0,0x1,0x0,0x1,0x1,0x0,0x1])})}),'indices':new Uint32Array([0x3,0x2,0x0,0x0,0x2,0x1])});return _0x3108d6;},_0x12a074=function(_0x8e7dc9,_0x21b020){if(Cesium$5['defined'](_0x21b020)){const _0x35977b={};_0x35977b['arrayBufferView']=_0x21b020,_0x8e7dc9['source']=_0x35977b;}const _0x409482=new Cesium$5['Texture'](_0x8e7dc9);return _0x409482;},_0x49c37e=function(_0x974cae,_0x26d117,_0x5109df){const _0x374a86=_0x2c91,_0x19fe7e=new Cesium$5[(_0x374a86(0x1f7))]({'context':_0x974cae,'colorTextures':[_0x26d117],'depthTexture':_0x5109df});return _0x19fe7e;},_0x4ebc2e=function(_0x3af8fd){const _0x4c85fd=_0x2c91,_0x4178df=!![],_0x1cde48=![],_0x122615={'viewport':_0x3af8fd['viewport'],'depthTest':_0x3af8fd['depthTest'],'depthMask':_0x3af8fd[_0x4c85fd(0x1eb)],'blending':_0x3af8fd[_0x4c85fd(0x19f)]},_0x10a45e=Cesium$5['Appearance']['getDefaultRenderState'](_0x4178df,_0x1cde48,_0x122615);return _0x10a45e;},_0x5e8a7e=function(_0x3dfaae){const _0x18fbbd=_0x2c91,_0x151b52={},_0x2b62cc=Cesium$5[_0x18fbbd(0x202)]['mod'](_0x3dfaae['west'],Cesium$5['Math'][_0x18fbbd(0x219)]),_0x36ab62=Cesium$5['Math'][_0x18fbbd(0x1ff)](_0x3dfaae[_0x18fbbd(0x19e)],Cesium$5['Math'][_0x18fbbd(0x219)]),_0x158da0=_0x3dfaae['width'];let _0x3b02cb,_0x54f7ed;_0x158da0>Cesium$5[_0x18fbbd(0x202)]['THREE_PI_OVER_TWO']?(_0x3b02cb=0x0,_0x54f7ed=Cesium$5['Math'][_0x18fbbd(0x219)]):_0x36ab62-_0x2b62cc<_0x158da0?(_0x3b02cb=_0x2b62cc,_0x54f7ed=_0x2b62cc+_0x158da0):(_0x3b02cb=_0x2b62cc,_0x54f7ed=_0x36ab62);_0x151b52['lon']={'min':Cesium$5['Math'][_0x18fbbd(0x23b)](_0x3b02cb),'max':Cesium$5['Math']['toDegrees'](_0x54f7ed)};const _0x3f76d2=_0x3dfaae['south'],_0x19bf0a=_0x3dfaae['north'],_0x2271a1=_0x3dfaae['height'],_0x4dd61a=_0x2271a1>Cesium$5['Math']['PI']/0xc?_0x2271a1/0x2:0x0;let _0x13f14f=Cesium$5[_0x18fbbd(0x202)]['clampToLatitudeRange'](_0x3f76d2-_0x4dd61a),_0x573f8e=Cesium$5['Math']['clampToLatitudeRange'](_0x19bf0a+_0x4dd61a);return _0x13f14f<-Cesium$5['Math']['PI_OVER_THREE']&&(_0x13f14f=-Cesium$5['Math']['PI_OVER_TWO']),_0x573f8e>Cesium$5['Math'][_0x18fbbd(0x162)]&&(_0x573f8e=Cesium$5[_0x18fbbd(0x202)][_0x18fbbd(0x203)]),_0x151b52['lat']={'min':Cesium$5[_0x18fbbd(0x202)][_0x18fbbd(0x23b)](_0x13f14f),'max':Cesium$5['Math']['toDegrees'](_0x573f8e)},_0x151b52;};return{'getFullscreenQuad':_0x5e79f7,'createTexture':_0x12a074,'createFramebuffer':_0x49c37e,'createRawRenderState':_0x4ebc2e,'viewRectangleToLonLatRange':_0x5e8a7e};}());var segmentDraw_vert='in\x20vec2\x20st;\x0a//\x20it\x20is\x20not\x20normal\x20itself,\x20but\x20used\x20to\x20control\x20normal\x0ain\x20vec3\x20normal;\x20//\x20(point\x20to\x20use,\x20offset\x20sign,\x20not\x20used\x20component)\x0a\x0auniform\x20sampler2D\x20currentParticlesPosition;\x0auniform\x20sampler2D\x20postProcessingPosition;\x0auniform\x20sampler2D\x20postProcessingSpeed;\x0a\x0auniform\x20float\x20particleHeight;\x0a\x0auniform\x20float\x20aspect;\x0auniform\x20float\x20pixelSize;\x0auniform\x20float\x20lineWidth;\x0a\x0aout\x20float\x20speedNormalization;\x0a\x0avec3\x20convertCoordinate(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20WGS84\x20(lon,\x20lat,\x20lev)\x20->\x20ECEF\x20(x,\x20y,\x20z)\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_conversion#From_geodetic_to_ECEF_coordinates\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20WGS\x2084\x20geometric\x20constants\x0a\x20\x20\x20\x20float\x20a\x20=\x206378137.0;\x20//\x20Semi-major\x20axis\x0a\x20\x20\x20\x20float\x20b\x20=\x206356752.3142;\x20//\x20Semi-minor\x20axis\x0a\x20\x20\x20\x20float\x20e2\x20=\x206.69437999014e-3;\x20//\x20First\x20eccentricity\x20squared\x0a\x0a\x20\x20\x20\x20float\x20latitude\x20=\x20radians(lonLatLev.y);\x0a\x20\x20\x20\x20float\x20longitude\x20=\x20radians(lonLatLev.x);\x0a\x0a\x20\x20\x20\x20float\x20cosLat\x20=\x20cos(latitude);\x0a\x20\x20\x20\x20float\x20sinLat\x20=\x20sin(latitude);\x0a\x20\x20\x20\x20float\x20cosLon\x20=\x20cos(longitude);\x0a\x20\x20\x20\x20float\x20sinLon\x20=\x20sin(longitude);\x0a\x0a\x20\x20\x20\x20float\x20N_Phi\x20=\x20a\x20/\x20sqrt(1.0\x20-\x20e2\x20*\x20sinLat\x20*\x20sinLat);\x0a\x20\x20\x20\x20float\x20h\x20=\x20particleHeight;\x20//\x20it\x20should\x20be\x20high\x20enough\x20otherwise\x20the\x20particle\x20may\x20not\x20pass\x20the\x20terrain\x20depth\x20test\x0a\x0a\x20\x20\x20\x20vec3\x20cartesian\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20cartesian.x\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20cosLon;\x0a\x20\x20\x20\x20cartesian.y\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20sinLon;\x0a\x20\x20\x20\x20cartesian.z\x20=\x20((b\x20*\x20b)\x20/\x20(a\x20*\x20a)\x20*\x20N_Phi\x20+\x20h)\x20*\x20sinLat;\x0a\x20\x20\x20\x20return\x20cartesian;\x0a}\x0a\x0avec4\x20calcProjectedCoordinate(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20the\x20range\x20of\x20longitude\x20in\x20Cesium\x20is\x20[-180,\x20180]\x20but\x20the\x20range\x20of\x20longitude\x20in\x20the\x20NetCDF\x20file\x20is\x20[0,\x20360]\x0a\x20\x20\x20\x20//\x20[0,\x20180]\x20is\x20corresponding\x20to\x20[0,\x20180]\x20and\x20[180,\x20360]\x20is\x20corresponding\x20to\x20[-180,\x200]\x0a\x20\x20\x20\x20lonLatLev.x\x20=\x20mod(lonLatLev.x\x20+\x20180.0,\x20360.0)\x20-\x20180.0;\x0a\x20\x20\x20\x20vec3\x20particlePosition\x20=\x20convertCoordinate(lonLatLev);\x0a\x20\x20\x20\x20vec4\x20projectedCoordinate\x20=\x20czm_modelViewProjection\x20*\x20vec4(particlePosition,\x201.0);\x0a\x20\x20\x20\x20return\x20projectedCoordinate;\x0a}\x0a\x0avec4\x20calcOffset(vec4\x20currentProjectedCoordinate,\x20vec4\x20nextProjectedCoordinate,\x20float\x20offsetSign)\x20{\x0a\x20\x20\x20\x20vec2\x20aspectVec2\x20=\x20vec2(aspect,\x201.0);\x0a\x20\x20\x20\x20vec2\x20currentXY\x20=\x20(currentProjectedCoordinate.xy\x20/\x20currentProjectedCoordinate.w)\x20*\x20aspectVec2;\x0a\x20\x20\x20\x20vec2\x20nextXY\x20=\x20(nextProjectedCoordinate.xy\x20/\x20nextProjectedCoordinate.w)\x20*\x20aspectVec2;\x0a\x0a\x20\x20\x20\x20float\x20offsetLength\x20=\x20lineWidth\x20/\x202.0;\x0a\x20\x20\x20\x20vec2\x20direction\x20=\x20normalize(nextXY\x20-\x20currentXY);\x0a\x20\x20\x20\x20vec2\x20normalVector\x20=\x20vec2(-direction.y,\x20direction.x);\x0a\x20\x20\x20\x20normalVector.x\x20=\x20normalVector.x\x20/\x20aspect;\x0a\x20\x20\x20\x20normalVector\x20=\x20offsetLength\x20*\x20normalVector;\x0a\x0a\x20\x20\x20\x20vec4\x20offset\x20=\x20vec4(offsetSign\x20*\x20normalVector,\x200.0,\x200.0);\x0a\x20\x20\x20\x20return\x20offset;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec2\x20particleIndex\x20=\x20st;\x0a\x0a\x20\x20\x20\x20vec3\x20currentPosition\x20=\x20texture(currentParticlesPosition,\x20particleIndex).rgb;\x0a\x20\x20\x20\x20vec4\x20nextPosition\x20=\x20texture(postProcessingPosition,\x20particleIndex);\x0a\x0a\x20\x20\x20\x20vec4\x20currentProjectedCoordinate\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20vec4\x20nextProjectedCoordinate\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20if\x20(nextPosition.w\x20>\x200.0)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20currentProjectedCoordinate\x20=\x20calcProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20\x20\x20\x20\x20nextProjectedCoordinate\x20=\x20calcProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20currentProjectedCoordinate\x20=\x20calcProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20\x20\x20\x20\x20nextProjectedCoordinate\x20=\x20calcProjectedCoordinate(nextPosition.xyz);\x0a\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20float\x20pointToUse\x20=\x20normal.x;\x20//\x20-1\x20is\x20currentProjectedCoordinate\x20and\x20+1\x20is\x20nextProjectedCoordinate\x0a\x20\x20\x20\x20float\x20offsetSign\x20=\x20normal.y;\x0a\x0a\x20\x20\x20\x20vec4\x20offset\x20=\x20pixelSize\x20*\x20calcOffset(currentProjectedCoordinate,\x20nextProjectedCoordinate,\x20offsetSign);\x0a\x20\x20\x20\x20if\x20(pointToUse\x20<\x200.0)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20gl_Position\x20=\x20currentProjectedCoordinate\x20+\x20offset;\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20gl_Position\x20=\x20nextProjectedCoordinate\x20+\x20offset;\x0a\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20speedNormalization\x20=\x20texture(postProcessingSpeed,\x20particleIndex).a;\x0a}\x0a',segmentDraw_frag='uniform\x20sampler2D\x20colorTable;\x0a\x0ain\x20float\x20speedNormalization;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20out_FragColor\x20=\x20texture(colorTable,\x20vec2(speedNormalization,\x200.0));\x0a}\x0a',fullscreen_vert='in\x20vec3\x20position;\x0ain\x20vec2\x20st;\x0a\x0aout\x20vec2\x20textureCoordinate;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20textureCoordinate\x20=\x20st;\x0a\x20\x20\x20\x20gl_Position\x20=\x20vec4(position,\x201.0);\x0a}\x0a',trailDraw_frag='uniform\x20sampler2D\x20segmentsColorTexture;\x0auniform\x20sampler2D\x20segmentsDepthTexture;\x0a\x0auniform\x20sampler2D\x20currentTrailsColor;\x0auniform\x20sampler2D\x20trailsDepthTexture;\x0a\x0auniform\x20float\x20fadeOpacity;\x0a\x0ain\x20vec2\x20textureCoordinate;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec4\x20pointsColor\x20=\x20texture(segmentsColorTexture,\x20textureCoordinate);\x0a\x20\x20\x20\x20vec4\x20trailsColor\x20=\x20texture(currentTrailsColor,\x20textureCoordinate);\x0a\x0a\x20\x20\x20\x20trailsColor\x20=\x20floor(fadeOpacity\x20*\x20255.0\x20*\x20trailsColor)\x20/\x20255.0;\x20//\x20make\x20sure\x20the\x20trailsColor\x20will\x20be\x20strictly\x20decreased\x0a\x0a\x20\x20\x20\x20float\x20pointsDepth\x20=\x20texture(segmentsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20\x20\x20float\x20trailsDepth\x20=\x20texture(trailsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20\x20\x20float\x20globeDepth\x20=\x20czm_unpackDepth(texture(czm_globeDepthTexture,\x20textureCoordinate));\x0a\x0a\x20\x20\x20\x20out_FragColor\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20if\x20(pointsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20out_FragColor\x20+\x20pointsColor;\x0a\x20\x20\x20\x20}\x0a\x20\x20\x20\x20if\x20(trailsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20out_FragColor\x20+\x20trailsColor;\x0a\x20\x20\x20\x20}\x0a\x20\x20\x20\x20gl_FragDepth\x20=\x20min(pointsDepth,\x20trailsDepth);\x0a}\x0a',screenDraw_frag='uniform\x20sampler2D\x20trailsColorTexture;\x0auniform\x20sampler2D\x20trailsDepthTexture;\x0a\x0ain\x20vec2\x20textureCoordinate;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec4\x20trailsColor\x20=\x20texture(trailsColorTexture,\x20textureCoordinate);\x0a\x20\x20\x20\x20float\x20trailsDepth\x20=\x20texture(trailsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20\x20\x20float\x20globeDepth\x20=\x20czm_unpackDepth(texture(czm_globeDepthTexture,\x20textureCoordinate));\x0a\x0a\x20\x20\x20\x20if\x20(trailsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20trailsColor;\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20}\x0a}\x0a';const Cesium$4=mars3d__namespace['Cesium'];class ParticlesRendering{constructor(_0x31ff68,_0x247f17,_0x152d05,_0x35ee4d,_0xd16aca){const _0x318030=_0x3a1149;this['createRenderingTextures'](_0x31ff68,_0x247f17,_0x152d05[_0x318030(0x17f)]),this[_0x318030(0x14c)](_0x31ff68),this['createRenderingPrimitives'](_0x31ff68,_0x152d05,_0x35ee4d,_0xd16aca);}['createRenderingTextures'](_0xb9f14f,_0x167ade,_0x261694){const _0x238589=_0x3a1149,_0x107d69={'context':_0xb9f14f,'width':_0xb9f14f['drawingBufferWidth'],'height':_0xb9f14f[_0x238589(0x16f)],'pixelFormat':Cesium$4['PixelFormat'][_0x238589(0x161)],'pixelDatatype':Cesium$4['PixelDatatype']['UNSIGNED_BYTE']},_0x51885d={'context':_0xb9f14f,'width':_0xb9f14f['drawingBufferWidth'],'height':_0xb9f14f['drawingBufferHeight'],'pixelFormat':Cesium$4['PixelFormat']['DEPTH_COMPONENT'],'pixelDatatype':Cesium$4[_0x238589(0x214)]['UNSIGNED_INT']},_0x1c0026=_0x261694['length'],_0x432d85=new Float32Array(_0x1c0026*0x3);for(let _0xb43418=0x0;_0xb43418<_0x1c0026;_0xb43418++){const _0x2e4696=Cesium$4['Color']['fromCssColorString'](_0x261694[_0xb43418]);_0x432d85[0x3*_0xb43418]=_0x2e4696['red'],_0x432d85[0x3*_0xb43418+0x1]=_0x2e4696['green'],_0x432d85[0x3*_0xb43418+0x2]=_0x2e4696['blue'];}const _0x4fdf3a={'context':_0xb9f14f,'width':_0x1c0026,'height':0x1,'pixelFormat':Cesium$4['PixelFormat']['RGB'],'pixelDatatype':Cesium$4[_0x238589(0x214)]['FLOAT'],'sampler':new Cesium$4[(_0x238589(0x1aa))]({'minificationFilter':Cesium$4[_0x238589(0x1b4)]['LINEAR'],'magnificationFilter':Cesium$4[_0x238589(0x1b3)]['LINEAR']})};this['textures']={'segmentsColor':Util[_0x238589(0x1c5)](_0x107d69),'segmentsDepth':Util['createTexture'](_0x51885d),'currentTrailsColor':Util['createTexture'](_0x107d69),'currentTrailsDepth':Util['createTexture'](_0x51885d),'nextTrailsColor':Util['createTexture'](_0x107d69),'nextTrailsDepth':Util[_0x238589(0x1c5)](_0x51885d),'colorTable':Util['createTexture'](_0x4fdf3a,_0x432d85)};}['createRenderingFramebuffers'](_0x4bb4ab){const _0x256808=_0x3a1149;this['framebuffers']={'segments':Util[_0x256808(0x231)](_0x4bb4ab,this['textures']['segmentsColor'],this['textures']['segmentsDepth']),'currentTrails':Util[_0x256808(0x231)](_0x4bb4ab,this['textures'][_0x256808(0x1fb)],this[_0x256808(0x1cc)]['currentTrailsDepth']),'nextTrails':Util[_0x256808(0x231)](_0x4bb4ab,this['textures'][_0x256808(0x1dd)],this['textures'][_0x256808(0x1b8)])};}[_0x3a1149(0x1c8)](_0x6e5661){const _0x1c172d=_0x3a1149,_0x2dc73a=0x4;let _0x53777e=[];for(let _0x376999=0x0;_0x376999<_0x6e5661['particlesTextureSize'];_0x376999++){for(let _0x4fb7fe=0x0;_0x4fb7fe<_0x6e5661[_0x1c172d(0x169)];_0x4fb7fe++){for(let _0x220ea1=0x0;_0x220ea1<_0x2dc73a;_0x220ea1++){_0x53777e['push'](_0x376999/_0x6e5661[_0x1c172d(0x169)]),_0x53777e['push'](_0x4fb7fe/_0x6e5661[_0x1c172d(0x169)]);}}}_0x53777e=new Float32Array(_0x53777e);let _0x1ceea2=[];const _0x312ba9=[-0x1,0x1],_0x2215cb=[-0x1,0x1];for(let _0x8e4286=0x0;_0x8e4286<_0x6e5661['maxParticles'];_0x8e4286++){for(let _0x452f85=0x0;_0x452f85<_0x2dc73a/0x2;_0x452f85++){for(let _0xc9ec5a=0x0;_0xc9ec5a<_0x2dc73a/0x2;_0xc9ec5a++){_0x1ceea2['push'](_0x312ba9[_0x452f85]),_0x1ceea2['push'](_0x2215cb[_0xc9ec5a]),_0x1ceea2['push'](0x0);}}}_0x1ceea2=new Float32Array(_0x1ceea2);const _0x2d4da5=0x6*_0x6e5661['maxParticles'],_0xe69d1b=new Uint32Array(_0x2d4da5);for(let _0x36fd7b=0x0,_0x544ff4=0x0,_0xdcd3ae=0x0;_0x36fd7b<_0x6e5661['maxParticles'];_0x36fd7b++){_0xe69d1b[_0x544ff4++]=_0xdcd3ae+0x0,_0xe69d1b[_0x544ff4++]=_0xdcd3ae+0x1,_0xe69d1b[_0x544ff4++]=_0xdcd3ae+0x2,_0xe69d1b[_0x544ff4++]=_0xdcd3ae+0x2,_0xe69d1b[_0x544ff4++]=_0xdcd3ae+0x1,_0xe69d1b[_0x544ff4++]=_0xdcd3ae+0x3,_0xdcd3ae+=0x4;}const _0x15e8a3=new Cesium$4['Geometry']({'attributes':new Cesium$4['GeometryAttributes']({'st':new Cesium$4['GeometryAttribute']({'componentDatatype':Cesium$4[_0x1c172d(0x201)]['FLOAT'],'componentsPerAttribute':0x2,'values':_0x53777e}),'normal':new Cesium$4['GeometryAttribute']({'componentDatatype':Cesium$4['ComponentDatatype']['FLOAT'],'componentsPerAttribute':0x3,'values':_0x1ceea2})}),'indices':_0xe69d1b});return _0x15e8a3;}['createRenderingPrimitives'](_0x4aeca2,_0x5aa6ec,_0x10a5ee,_0x206333){const _0x38a4e0=_0x3a1149,_0x42be3d=this;this[_0x38a4e0(0x199)]={'segments':new CustomPrimitive({'commandType':'Draw','attributeLocations':{'st':0x0,'normal':0x1},'geometry':this['createSegmentsGeometry'](_0x5aa6ec),'primitiveType':Cesium$4['PrimitiveType']['TRIANGLES'],'uniformMap':{'currentParticlesPosition':function(){const _0x2b3ead=_0x38a4e0;return _0x206333['particlesTextures'][_0x2b3ead(0x1d0)];},'postProcessingPosition':function(){return _0x206333['particlesTextures']['postProcessingPosition'];},'postProcessingSpeed':function(){const _0x14f994=_0x38a4e0;return _0x206333[_0x14f994(0x213)]['postProcessingSpeed'];},'colorTable':function(){const _0x18adf1=_0x38a4e0;return _0x42be3d[_0x18adf1(0x1cc)]['colorTable'];},'aspect':function(){return _0x4aeca2['drawingBufferWidth']/_0x4aeca2['drawingBufferHeight'];},'pixelSize':function(){return _0x10a5ee['pixelSize'];},'lineWidth':function(){return _0x5aa6ec['lineWidth'];},'particleHeight':function(){return _0x5aa6ec['particleHeight'];}},'vertexShaderSource':new Cesium$4['ShaderSource']({'sources':[segmentDraw_vert]}),'fragmentShaderSource':new Cesium$4[(_0x38a4e0(0x14f))]({'sources':[segmentDraw_frag]}),'rawRenderState':Util['createRawRenderState']({'viewport':undefined,'depthTest':{'enabled':!![]},'depthMask':!![]}),'framebuffer':this[_0x38a4e0(0x1a4)]['segments'],'autoClear':!![]}),'trails':new CustomPrimitive({'commandType':'Draw','attributeLocations':{'position':0x0,'st':0x1},'geometry':Util['getFullscreenQuad'](),'primitiveType':Cesium$4[_0x38a4e0(0x1ae)]['TRIANGLES'],'uniformMap':{'segmentsColorTexture':function(){const _0x47fd89=_0x38a4e0;return _0x42be3d[_0x47fd89(0x1cc)]['segmentsColor'];},'segmentsDepthTexture':function(){const _0xcc1dd9=_0x38a4e0;return _0x42be3d[_0xcc1dd9(0x1cc)]['segmentsDepth'];},'currentTrailsColor':function(){const _0xcc3b73=_0x38a4e0;return _0x42be3d[_0xcc3b73(0x1a4)][_0xcc3b73(0x156)]['getColorTexture'](0x0);},'trailsDepthTexture':function(){const _0x390dbf=_0x38a4e0;return _0x42be3d[_0x390dbf(0x1a4)]['currentTrails']['depthTexture'];},'fadeOpacity':function(){return _0x5aa6ec['fadeOpacity'];}},'vertexShaderSource':new Cesium$4['ShaderSource']({'defines':['DISABLE_GL_POSITION_LOG_DEPTH'],'sources':[fullscreen_vert]}),'fragmentShaderSource':new Cesium$4['ShaderSource']({'defines':[_0x38a4e0(0x187)],'sources':[trailDraw_frag]}),'rawRenderState':Util['createRawRenderState']({'viewport':undefined,'depthTest':{'enabled':!![],'func':Cesium$4['DepthFunction'][_0x38a4e0(0x154)]},'depthMask':!![]}),'framebuffer':this['framebuffers']['nextTrails'],'autoClear':!![],'preExecute':function(){const _0x4256ac=_0x38a4e0,_0x332d43=_0x42be3d[_0x4256ac(0x1a4)]['currentTrails'];_0x42be3d['framebuffers']['currentTrails']=_0x42be3d['framebuffers'][_0x4256ac(0x182)],_0x42be3d[_0x4256ac(0x1a4)]['nextTrails']=_0x332d43,_0x42be3d[_0x4256ac(0x199)][_0x4256ac(0x22a)]['commandToExecute']['framebuffer']=_0x42be3d['framebuffers'][_0x4256ac(0x182)],_0x42be3d['primitives']['trails'][_0x4256ac(0x180)]['framebuffer']=_0x42be3d['framebuffers']['nextTrails'];}}),'screen':new CustomPrimitive({'commandType':_0x38a4e0(0x16b),'attributeLocations':{'position':0x0,'st':0x1},'geometry':Util['getFullscreenQuad'](),'primitiveType':Cesium$4['PrimitiveType'][_0x38a4e0(0x16d)],'uniformMap':{'trailsColorTexture':function(){return _0x42be3d['framebuffers']['nextTrails']['getColorTexture'](0x0);},'trailsDepthTexture':function(){const _0x4d6321=_0x38a4e0;return _0x42be3d[_0x4d6321(0x1a4)][_0x4d6321(0x182)][_0x4d6321(0x1c6)];}},'vertexShaderSource':new Cesium$4[(_0x38a4e0(0x14f))]({'defines':['DISABLE_GL_POSITION_LOG_DEPTH'],'sources':[fullscreen_vert]}),'fragmentShaderSource':new Cesium$4['ShaderSource']({'defines':['DISABLE_LOG_DEPTH_FRAGMENT_WRITE'],'sources':[screenDraw_frag]}),'rawRenderState':Util[_0x38a4e0(0x157)]({'viewport':undefined,'depthTest':{'enabled':![]},'depthMask':!![],'blending':{'enabled':!![]}}),'framebuffer':undefined})};}}var getWind_frag='//\x20the\x20size\x20of\x20UV\x20textures:\x20width\x20=\x20lon,\x20height\x20=\x20lat*lev\x0auniform\x20sampler2D\x20U;\x20//\x20eastward\x20wind\x0auniform\x20sampler2D\x20V;\x20//\x20northward\x20wind\x0a\x0auniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0a\x0auniform\x20vec3\x20dimension;\x20//\x20(lon,\x20lat,\x20lev)\x0auniform\x20vec3\x20minimum;\x20//\x20minimum\x20of\x20each\x20dimension\x0auniform\x20vec3\x20maximum;\x20//\x20maximum\x20of\x20each\x20dimension\x0auniform\x20vec3\x20interval;\x20//\x20interval\x20of\x20each\x20dimension\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20mapPositionToNormalizedIndex2D(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20ensure\x20the\x20range\x20of\x20longitude\x20and\x20latitude\x0a\x20\x20\x20\x20lonLatLev.x\x20=\x20mod(lonLatLev.x,\x20360.0);\x0a\x20\x20\x20\x20lonLatLev.y\x20=\x20clamp(lonLatLev.y,\x20-90.0,\x2090.0);\x0a\x0a\x20\x20\x20\x20vec3\x20index3D\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20index3D.x\x20=\x20(lonLatLev.x\x20-\x20minimum.x)\x20/\x20interval.x;\x0a\x20\x20\x20\x20index3D.y\x20=\x20(lonLatLev.y\x20-\x20minimum.y)\x20/\x20interval.y;\x0a\x20\x20\x20\x20index3D.z\x20=\x20(lonLatLev.z\x20-\x20minimum.z)\x20/\x20interval.z;\x0a\x0a\x20\x20\x20\x20//\x20the\x20st\x20texture\x20coordinate\x20corresponding\x20to\x20(col,\x20row)\x20index\x0a\x20\x20\x20\x20//\x20example\x0a\x20\x20\x20\x20//\x20data\x20array\x20is\x20[0,\x201,\x202,\x203,\x204,\x205],\x20width\x20=\x203,\x20height\x20=\x202\x0a\x20\x20\x20\x20//\x20the\x20content\x20of\x20texture\x20will\x20be\x0a\x20\x20\x20\x20//\x20t\x201.0\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x20\x203\x204\x205\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x20\x200\x201\x202\x0a\x20\x20\x20\x20//\x20\x20\x200.0------1.0\x20s\x0a\x0a\x20\x20\x20\x20vec2\x20index2D\x20=\x20vec2(index3D.x,\x20index3D.z\x20*\x20dimension.y\x20+\x20index3D.y);\x0a\x20\x20\x20\x20vec2\x20normalizedIndex2D\x20=\x20vec2(index2D.x\x20/\x20dimension.x,\x20index2D.y\x20/\x20(dimension.y\x20*\x20dimension.z));\x0a\x20\x20\x20\x20return\x20normalizedIndex2D;\x0a}\x0a\x0afloat\x20getWind(sampler2D\x20windTexture,\x20vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20vec2\x20normalizedIndex2D\x20=\x20mapPositionToNormalizedIndex2D(lonLatLev);\x0a\x20\x20\x20\x20float\x20result\x20=\x20texture(windTexture,\x20normalizedIndex2D).r;\x0a\x20\x20\x20\x20return\x20result;\x0a}\x0a\x0aconst\x20mat4\x20kernelMatrix\x20=\x20mat4(\x0a\x20\x20\x20\x200.0,\x20-1.0,\x202.0,\x20-1.0,\x20//\x20first\x20column\x0a\x20\x20\x20\x202.0,\x200.0,\x20-5.0,\x203.0,\x20//\x20second\x20column\x0a\x20\x20\x20\x200.0,\x201.0,\x204.0,\x20-3.0,\x20//\x20third\x20column\x0a\x20\x20\x20\x200.0,\x200.0,\x20-1.0,\x201.0\x20//\x20fourth\x20column\x0a);\x0afloat\x20oneDimensionInterpolation(float\x20t,\x20float\x20p0,\x20float\x20p1,\x20float\x20p2,\x20float\x20p3)\x20{\x0a\x20\x20\x20\x20vec4\x20tVec4\x20=\x20vec4(1.0,\x20t,\x20t\x20*\x20t,\x20t\x20*\x20t\x20*\x20t);\x0a\x20\x20\x20\x20tVec4\x20=\x20tVec4\x20/\x202.0;\x0a\x20\x20\x20\x20vec4\x20pVec4\x20=\x20vec4(p0,\x20p1,\x20p2,\x20p3);\x0a\x20\x20\x20\x20return\x20dot((tVec4\x20*\x20kernelMatrix),\x20pVec4);\x0a}\x0a\x0afloat\x20calculateB(sampler2D\x20windTexture,\x20float\x20t,\x20float\x20lon,\x20float\x20lat,\x20float\x20lev)\x20{\x0a\x20\x20\x20\x20float\x20lon0\x20=\x20floor(lon)\x20-\x201.0\x20*\x20interval.x;\x0a\x20\x20\x20\x20float\x20lon1\x20=\x20floor(lon);\x0a\x20\x20\x20\x20float\x20lon2\x20=\x20floor(lon)\x20+\x201.0\x20*\x20interval.x;\x0a\x20\x20\x20\x20float\x20lon3\x20=\x20floor(lon)\x20+\x202.0\x20*\x20interval.x;\x0a\x0a\x20\x20\x20\x20float\x20p0\x20=\x20getWind(windTexture,\x20vec3(lon0,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p1\x20=\x20getWind(windTexture,\x20vec3(lon1,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p2\x20=\x20getWind(windTexture,\x20vec3(lon2,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p3\x20=\x20getWind(windTexture,\x20vec3(lon3,\x20lat,\x20lev));\x0a\x0a\x20\x20\x20\x20return\x20oneDimensionInterpolation(t,\x20p0,\x20p1,\x20p2,\x20p3);\x0a}\x0a\x0afloat\x20interpolateOneTexture(sampler2D\x20windTexture,\x20vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20float\x20lon\x20=\x20lonLatLev.x;\x0a\x20\x20\x20\x20float\x20lat\x20=\x20lonLatLev.y;\x0a\x20\x20\x20\x20float\x20lev\x20=\x20lonLatLev.z;\x0a\x0a\x20\x20\x20\x20float\x20lat0\x20=\x20floor(lat)\x20-\x201.0\x20*\x20interval.y;\x0a\x20\x20\x20\x20float\x20lat1\x20=\x20floor(lat);\x0a\x20\x20\x20\x20float\x20lat2\x20=\x20floor(lat)\x20+\x201.0\x20*\x20interval.y;\x0a\x20\x20\x20\x20float\x20lat3\x20=\x20floor(lat)\x20+\x202.0\x20*\x20interval.y;\x0a\x0a\x20\x20\x20\x20vec2\x20coefficient\x20=\x20lonLatLev.xy\x20-\x20floor(lonLatLev.xy);\x0a\x20\x20\x20\x20float\x20b0\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat0,\x20lev);\x0a\x20\x20\x20\x20float\x20b1\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat1,\x20lev);\x0a\x20\x20\x20\x20float\x20b2\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat2,\x20lev);\x0a\x20\x20\x20\x20float\x20b3\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat3,\x20lev);\x0a\x0a\x20\x20\x20\x20return\x20oneDimensionInterpolation(coefficient.y,\x20b0,\x20b1,\x20b2,\x20b3);\x0a}\x0a\x0avec3\x20bicubic(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20https://en.wikipedia.org/wiki/Bicubic_interpolation#Bicubic_convolution_algorithm\x0a\x20\x20\x20\x20float\x20u\x20=\x20interpolateOneTexture(U,\x20lonLatLev);\x0a\x20\x20\x20\x20float\x20v\x20=\x20interpolateOneTexture(V,\x20lonLatLev);\x0a\x20\x20\x20\x20float\x20w\x20=\x200.0;\x0a\x20\x20\x20\x20return\x20vec3(u,\x20v,\x20w);\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20vec3\x20lonLatLev\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20windVector\x20=\x20bicubic(lonLatLev);\x0a\x20\x20\x20\x20out_FragColor\x20=\x20vec4(windVector,\x200.0);\x0a}\x0a',updateSpeed_frag='uniform\x20sampler2D\x20currentParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0auniform\x20sampler2D\x20particlesWind;\x0a\x0a//\x20used\x20to\x20calculate\x20the\x20wind\x20norm\x0auniform\x20vec2\x20uSpeedRange;\x20//\x20(min,\x20max);\x0auniform\x20vec2\x20vSpeedRange;\x0auniform\x20float\x20pixelSize;\x0auniform\x20float\x20speedFactor;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0afloat\x20calculateWindNorm(vec3\x20speed)\x20{\x0a\x20\x20\x20\x20vec3\x20percent\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20percent.x\x20=\x20(speed.x\x20-\x20uSpeedRange.x)\x20/\x20(uSpeedRange.y\x20-\x20uSpeedRange.x);\x0a\x20\x20\x20\x20percent.y\x20=\x20(speed.y\x20-\x20vSpeedRange.x)\x20/\x20(vSpeedRange.y\x20-\x20vSpeedRange.x);\x0a\x20\x20\x20\x20float\x20normalization\x20=\x20length(percent);\x0a\x0a\x20\x20\x20\x20return\x20normalization;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20//\x20vec3\x20currentSpeed\x20=\x20texture(currentParticlesSpeed,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20windVector\x20=\x20texture(particlesWind,\x20v_textureCoordinates).rgb;\x0a\x0a\x20\x20\x20\x20vec4\x20nextSpeed\x20=\x20vec4(speedFactor\x20*\x20pixelSize\x20*\x20windVector,\x20calculateWindNorm(windVector));\x0a\x20\x20\x20\x20out_FragColor\x20=\x20nextSpeed;\x0a}\x0a',updatePosition_frag='uniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0auniform\x20sampler2D\x20currentParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20lengthOfLonLat(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20unit\x20conversion:\x20meters\x20->\x20longitude\x20latitude\x20degrees\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_system#Length_of_a_degree\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20the\x20length\x20of\x20a\x20degree\x20of\x20latitude\x20and\x20longitude\x20in\x20meters\x0a\x20\x20\x20\x20float\x20latitude\x20=\x20radians(lonLatLev.y);\x0a\x0a\x20\x20\x20\x20float\x20term1\x20=\x20111132.92;\x0a\x20\x20\x20\x20float\x20term2\x20=\x20559.82\x20*\x20cos(2.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term3\x20=\x201.175\x20*\x20cos(4.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term4\x20=\x200.0023\x20*\x20cos(6.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20latLength\x20=\x20term1\x20-\x20term2\x20+\x20term3\x20-\x20term4;\x0a\x0a\x20\x20\x20\x20float\x20term5\x20=\x20111412.84\x20*\x20cos(latitude);\x0a\x20\x20\x20\x20float\x20term6\x20=\x2093.5\x20*\x20cos(3.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term7\x20=\x200.118\x20*\x20cos(5.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20longLength\x20=\x20term5\x20-\x20term6\x20+\x20term7;\x0a\x0a\x20\x20\x20\x20return\x20vec2(longLength,\x20latLength);\x0a}\x0a\x0avoid\x20updatePosition(vec3\x20lonLatLev,\x20vec3\x20speed)\x20{\x0a\x20\x20\x20\x20vec2\x20lonLatLength\x20=\x20lengthOfLonLat(lonLatLev);\x0a\x20\x20\x20\x20float\x20u\x20=\x20speed.x\x20/\x20lonLatLength.x;\x0a\x20\x20\x20\x20float\x20v\x20=\x20speed.y\x20/\x20lonLatLength.y;\x0a\x20\x20\x20\x20float\x20w\x20=\x200.0;\x0a\x20\x20\x20\x20vec3\x20windVectorInLonLatLev\x20=\x20vec3(u,\x20v,\x20w);\x0a\x0a\x20\x20\x20\x20vec3\x20nextParticle\x20=\x20lonLatLev\x20+\x20windVectorInLonLatLev;\x0a\x0a\x20\x20\x20\x20out_FragColor\x20=\x20vec4(nextParticle,\x200.0);\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20vec3\x20lonLatLev\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20speed\x20=\x20texture(currentParticlesSpeed,\x20v_textureCoordinates).rgb;\x0a\x0a\x20\x20\x20\x20updatePosition(lonLatLev,\x20speed);\x0a}\x0a',postProcessingPosition_frag='uniform\x20sampler2D\x20nextParticlesPosition;\x0auniform\x20sampler2D\x20nextParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0a\x0a//\x20range\x20(min,\x20max)\x0auniform\x20vec2\x20lonRange;\x0auniform\x20vec2\x20latRange;\x0a\x0auniform\x20float\x20randomCoefficient;\x20//\x20use\x20to\x20improve\x20the\x20pseudo-random\x20generator\x0auniform\x20float\x20dropRate;\x20//\x20drop\x20rate\x20is\x20a\x20chance\x20a\x20particle\x20will\x20restart\x20at\x20random\x20position\x20to\x20avoid\x20degeneration\x0auniform\x20float\x20dropRateBump;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0a//\x20pseudo-random\x20generator\x0aconst\x20vec3\x20randomConstants\x20=\x20vec3(12.9898,\x2078.233,\x204375.85453);\x0aconst\x20vec2\x20normalRange\x20=\x20vec2(0.0,\x201.0);\x0afloat\x20rand(vec2\x20seed,\x20vec2\x20range)\x20{\x0a\x20\x20\x20\x20vec2\x20randomSeed\x20=\x20randomCoefficient\x20*\x20seed;\x0a\x20\x20\x20\x20float\x20temp\x20=\x20dot(randomConstants.xy,\x20randomSeed);\x0a\x20\x20\x20\x20temp\x20=\x20fract(sin(temp)\x20*\x20(randomConstants.z\x20+\x20temp));\x0a\x20\x20\x20\x20return\x20temp\x20*\x20(range.y\x20-\x20range.x)\x20+\x20range.x;\x0a}\x0a\x0avec3\x20generateRandomParticle(vec2\x20seed,\x20float\x20lev)\x20{\x0a\x20\x20\x20\x20//\x20ensure\x20the\x20longitude\x20is\x20in\x20[0,\x20360]\x0a\x20\x20\x20\x20float\x20randomLon\x20=\x20mod(rand(seed,\x20lonRange),\x20360.0);\x0a\x20\x20\x20\x20float\x20randomLat\x20=\x20rand(-seed,\x20latRange);\x0a\x0a\x20\x20\x20\x20return\x20vec3(randomLon,\x20randomLat,\x20lev);\x0a}\x0a\x0abool\x20particleOutbound(vec3\x20particle)\x20{\x0a\x20\x20\x20\x20return\x20particle.y\x20<\x20-90.0\x20||\x20particle.y\x20>\x2090.0;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec3\x20nextParticle\x20=\x20texture(nextParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec4\x20nextSpeed\x20=\x20texture(nextParticlesSpeed,\x20v_textureCoordinates);\x0a\x20\x20\x20\x20float\x20particleDropRate\x20=\x20dropRate\x20+\x20dropRateBump\x20*\x20nextSpeed.a;\x0a\x0a\x20\x20\x20\x20vec2\x20seed1\x20=\x20nextParticle.xy\x20+\x20v_textureCoordinates;\x0a\x20\x20\x20\x20vec2\x20seed2\x20=\x20nextSpeed.xy\x20+\x20v_textureCoordinates;\x0a\x20\x20\x20\x20vec3\x20randomParticle\x20=\x20generateRandomParticle(seed1,\x20nextParticle.z);\x0a\x20\x20\x20\x20float\x20randomNumber\x20=\x20rand(seed2,\x20normalRange);\x0a\x0a\x20\x20\x20\x20if\x20(randomNumber\x20<\x20particleDropRate\x20||\x20particleOutbound(nextParticle))\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(randomParticle,\x201.0);\x20//\x201.0\x20means\x20this\x20is\x20a\x20random\x20particle\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(nextParticle,\x200.0);\x0a\x20\x20\x20\x20}\x0a}\x0a',postProcessingSpeed_frag='uniform\x20sampler2D\x20postProcessingPosition;\x0auniform\x20sampler2D\x20nextParticlesSpeed;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec4\x20randomParticle\x20=\x20texture(postProcessingPosition,\x20v_textureCoordinates);\x0a\x20\x20\x20\x20vec4\x20particleSpeed\x20=\x20texture(nextParticlesSpeed,\x20v_textureCoordinates);\x0a\x0a\x20\x20\x20\x20if\x20(randomParticle.a\x20>\x200.0)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20particleSpeed;\x0a\x20\x20\x20\x20}\x0a}\x0a';const Cesium$3=mars3d__namespace['Cesium'];class ParticlesComputing{constructor(_0x63527a,_0x4dd9fe,_0x4ee0c5,_0xec352d){const _0x1d6f8a=_0x3a1149;this[_0x1d6f8a(0x183)]=_0x4dd9fe,this['createWindTextures'](_0x63527a,_0x4dd9fe),this['createParticlesTextures'](_0x63527a,_0x4ee0c5,_0xec352d),this['createComputingPrimitives'](_0x4dd9fe,_0x4ee0c5,_0xec352d);}[_0x3a1149(0x1c0)](_0xb8daa7,_0x1443ed){const _0x5dc413=_0x3a1149,_0x34f81c={'context':_0xb8daa7,'width':_0x1443ed[_0x5dc413(0x1e7)]['lon'],'height':_0x1443ed['dimensions']['lat']*(_0x1443ed['dimensions']['lev']||0x1),'pixelFormat':Cesium$3['PixelFormat']['LUMINANCE'],'pixelDatatype':Cesium$3['PixelDatatype']['FLOAT'],'flipY':![],'sampler':new Cesium$3['Sampler']({'minificationFilter':Cesium$3['TextureMinificationFilter'][_0x5dc413(0x212)],'magnificationFilter':Cesium$3[_0x5dc413(0x1b3)][_0x5dc413(0x212)]})};this['windTextures']={'U':Util[_0x5dc413(0x1c5)](_0x34f81c,_0x1443ed['U'][_0x5dc413(0x1b2)]),'V':Util['createTexture'](_0x34f81c,_0x1443ed['V']['array'])};}[_0x3a1149(0x15b)](_0x357dc5,_0x2a909b,_0x20acda){const _0x3fefb5=_0x3a1149,_0x268ac1={'context':_0x357dc5,'width':_0x2a909b[_0x3fefb5(0x169)],'height':_0x2a909b['particlesTextureSize'],'pixelFormat':Cesium$3['PixelFormat']['RGBA'],'pixelDatatype':Cesium$3[_0x3fefb5(0x214)]['FLOAT'],'flipY':![],'sampler':new Cesium$3['Sampler']({'minificationFilter':Cesium$3['TextureMinificationFilter'][_0x3fefb5(0x212)],'magnificationFilter':Cesium$3['TextureMagnificationFilter']['NEAREST']})},_0x41a44a=this[_0x3fefb5(0x226)](_0x2a909b[_0x3fefb5(0x236)],_0x20acda),_0x40fa4b=new Float32Array(0x4*_0x2a909b['maxParticles'])['fill'](0x0);this['particlesTextures']={'particlesWind':Util[_0x3fefb5(0x1c5)](_0x268ac1),'currentParticlesPosition':Util[_0x3fefb5(0x1c5)](_0x268ac1,_0x41a44a),'nextParticlesPosition':Util['createTexture'](_0x268ac1,_0x41a44a),'currentParticlesSpeed':Util['createTexture'](_0x268ac1,_0x40fa4b),'nextParticlesSpeed':Util[_0x3fefb5(0x1c5)](_0x268ac1,_0x40fa4b),'postProcessingPosition':Util['createTexture'](_0x268ac1,_0x41a44a),'postProcessingSpeed':Util['createTexture'](_0x268ac1,_0x40fa4b)};}['randomizeParticles'](_0x4719a7,_0x23d702){const _0x1199a9=_0x3a1149,_0x1d73d0=new Float32Array(0x4*_0x4719a7);for(let _0x473122=0x0;_0x473122<_0x4719a7;_0x473122++){_0x1d73d0[0x4*_0x473122]=Cesium$3['Math']['randomBetween'](_0x23d702['lonRange']['x'],_0x23d702['lonRange']['y']),_0x1d73d0[0x4*_0x473122+0x1]=Cesium$3['Math']['randomBetween'](_0x23d702[_0x1199a9(0x1b9)]['x'],_0x23d702[_0x1199a9(0x1b9)]['y']),_0x1d73d0[0x4*_0x473122+0x2]=Cesium$3['Math']['randomBetween'](this[_0x1199a9(0x183)]['lev'][_0x1199a9(0x1d4)],this['data']['lev']['max']),_0x1d73d0[0x4*_0x473122+0x3]=0x0;}return _0x1d73d0;}['destroyParticlesTextures'](){Object['keys'](this['particlesTextures'])['forEach'](_0x45bbe6=>{const _0x217ff4=_0x2c91;this[_0x217ff4(0x213)][_0x45bbe6][_0x217ff4(0x1fc)]();});}[_0x3a1149(0x170)](_0x8394d1,_0x3ecc5f,_0x547564){const _0x5bc7df=_0x3a1149,_0x63c721=new Cesium$3[(_0x5bc7df(0x1bb))](_0x8394d1['dimensions'][_0x5bc7df(0x217)],_0x8394d1['dimensions']['lat'],_0x8394d1[_0x5bc7df(0x1e7)]['lev']),_0x102cd7=new Cesium$3['Cartesian3'](_0x8394d1['lon'][_0x5bc7df(0x1d4)],_0x8394d1[_0x5bc7df(0x1da)]['min'],_0x8394d1[_0x5bc7df(0x1e0)][_0x5bc7df(0x1d4)]),_0x4964f4=new Cesium$3[(_0x5bc7df(0x1bb))](_0x8394d1['lon']['max'],_0x8394d1[_0x5bc7df(0x1da)]['max'],_0x8394d1['lev'][_0x5bc7df(0x22c)]),_0x4b8312=new Cesium$3['Cartesian3']((_0x4964f4['x']-_0x102cd7['x'])/(_0x63c721['x']-0x1),(_0x4964f4['y']-_0x102cd7['y'])/(_0x63c721['y']-0x1),_0x63c721['z']>0x1?(_0x4964f4['z']-_0x102cd7['z'])/(_0x63c721['z']-0x1):0x1),_0x341eec=new Cesium$3['Cartesian2'](_0x8394d1['U'][_0x5bc7df(0x1d4)],_0x8394d1['U'][_0x5bc7df(0x22c)]),_0x207e8e=new Cesium$3[(_0x5bc7df(0x1f1))](_0x8394d1['V']['min'],_0x8394d1['V']['max']),_0x1e2580=this;this['primitives']={'getWind':new CustomPrimitive({'commandType':_0x5bc7df(0x222),'uniformMap':{'U':function(){return _0x1e2580['windTextures']['U'];},'V':function(){return _0x1e2580['windTextures']['V'];},'currentParticlesPosition':function(){const _0x43ed99=_0x5bc7df;return _0x1e2580[_0x43ed99(0x213)]['currentParticlesPosition'];},'dimension':function(){return _0x63c721;},'minimum':function(){return _0x102cd7;},'maximum':function(){return _0x4964f4;},'interval':function(){return _0x4b8312;}},'fragmentShaderSource':new Cesium$3['ShaderSource']({'sources':[getWind_frag]}),'outputTexture':this['particlesTextures']['particlesWind'],'preExecute':function(){const _0x51af6c=_0x5bc7df;_0x1e2580['primitives'][_0x51af6c(0x17b)]['commandToExecute']['outputTexture']=_0x1e2580[_0x51af6c(0x213)][_0x51af6c(0x1ba)];}}),'updateSpeed':new CustomPrimitive({'commandType':'Compute','uniformMap':{'currentParticlesSpeed':function(){const _0x526daa=_0x5bc7df;return _0x1e2580['particlesTextures'][_0x526daa(0x193)];},'particlesWind':function(){const _0xd4ee56=_0x5bc7df;return _0x1e2580[_0xd4ee56(0x213)]['particlesWind'];},'uSpeedRange':function(){return _0x341eec;},'vSpeedRange':function(){return _0x207e8e;},'pixelSize':function(){return _0x547564['pixelSize'];},'speedFactor':function(){return _0x3ecc5f['speedFactor'];}},'fragmentShaderSource':new Cesium$3['ShaderSource']({'sources':[updateSpeed_frag]}),'outputTexture':this['particlesTextures']['nextParticlesSpeed'],'preExecute':function(){const _0x25bbc5=_0x5bc7df,_0xdbdbfc=_0x1e2580['particlesTextures'][_0x25bbc5(0x193)];_0x1e2580['particlesTextures']['currentParticlesSpeed']=_0x1e2580[_0x25bbc5(0x213)][_0x25bbc5(0x20a)],_0x1e2580['particlesTextures']['postProcessingSpeed']=_0xdbdbfc,_0x1e2580['primitives'][_0x25bbc5(0x1c4)][_0x25bbc5(0x160)]['outputTexture']=_0x1e2580[_0x25bbc5(0x213)][_0x25bbc5(0x18c)];}}),'updatePosition':new CustomPrimitive({'commandType':'Compute','uniformMap':{'currentParticlesPosition':function(){return _0x1e2580['particlesTextures']['currentParticlesPosition'];},'currentParticlesSpeed':function(){return _0x1e2580['particlesTextures']['currentParticlesSpeed'];}},'fragmentShaderSource':new Cesium$3['ShaderSource']({'sources':[updatePosition_frag]}),'outputTexture':this[_0x5bc7df(0x213)][_0x5bc7df(0x1ad)],'preExecute':function(){const _0xbdd44e=_0x5bc7df,_0x2b132b=_0x1e2580['particlesTextures']['currentParticlesPosition'];_0x1e2580[_0xbdd44e(0x213)]['currentParticlesPosition']=_0x1e2580['particlesTextures']['postProcessingPosition'],_0x1e2580[_0xbdd44e(0x213)][_0xbdd44e(0x22f)]=_0x2b132b,_0x1e2580[_0xbdd44e(0x199)]['updatePosition'][_0xbdd44e(0x160)]['outputTexture']=_0x1e2580['particlesTextures']['nextParticlesPosition'];}}),'postProcessingPosition':new CustomPrimitive({'commandType':'Compute','uniformMap':{'nextParticlesPosition':function(){const _0xf09b6=_0x5bc7df;return _0x1e2580[_0xf09b6(0x213)]['nextParticlesPosition'];},'nextParticlesSpeed':function(){const _0x4a28a9=_0x5bc7df;return _0x1e2580[_0x4a28a9(0x213)]['nextParticlesSpeed'];},'lonRange':function(){const _0x5d4b38=_0x5bc7df;return _0x547564[_0x5d4b38(0x239)];},'latRange':function(){return _0x547564['latRange'];},'randomCoefficient':function(){const _0x1b1156=Math['random']();return _0x1b1156;},'dropRate':function(){return _0x3ecc5f['dropRate'];},'dropRateBump':function(){return _0x3ecc5f['dropRateBump'];}},'fragmentShaderSource':new Cesium$3['ShaderSource']({'sources':[postProcessingPosition_frag]}),'outputTexture':this['particlesTextures']['postProcessingPosition'],'preExecute':function(){const _0x4ae919=_0x5bc7df;_0x1e2580['primitives'][_0x4ae919(0x22f)][_0x4ae919(0x160)]['outputTexture']=_0x1e2580['particlesTextures'][_0x4ae919(0x22f)];}}),'postProcessingSpeed':new CustomPrimitive({'commandType':'Compute','uniformMap':{'postProcessingPosition':function(){return _0x1e2580['particlesTextures']['postProcessingPosition'];},'nextParticlesSpeed':function(){return _0x1e2580['particlesTextures']['nextParticlesSpeed'];}},'fragmentShaderSource':new Cesium$3[(_0x5bc7df(0x14f))]({'sources':[postProcessingSpeed_frag]}),'outputTexture':this['particlesTextures']['postProcessingSpeed'],'preExecute':function(){const _0x22c20b=_0x5bc7df;_0x1e2580['primitives']['postProcessingSpeed']['commandToExecute']['outputTexture']=_0x1e2580[_0x22c20b(0x213)]['postProcessingSpeed'];}})};}}const Cesium$2=mars3d__namespace[_0x3a1149(0x1af)];class ParticleSystem{constructor(_0x144d6c,_0x1a5a0f,_0x69db6f,_0x1b3730){const _0x3f6fcd=_0x3a1149;this['context']=_0x144d6c,_0x1a5a0f={..._0x1a5a0f},_0x1a5a0f[_0x3f6fcd(0x17a)]&&_0x1a5a0f['vdata']&&(_0x1a5a0f['dimensions']={},_0x1a5a0f[_0x3f6fcd(0x1e7)]['lon']=_0x1a5a0f['cols'],_0x1a5a0f[_0x3f6fcd(0x1e7)]['lat']=_0x1a5a0f[_0x3f6fcd(0x1d8)],_0x1a5a0f['dimensions']['lev']=_0x1a5a0f[_0x3f6fcd(0x1e0)]||0x1,_0x1a5a0f['lon']={},_0x1a5a0f[_0x3f6fcd(0x217)]['min']=_0x1a5a0f['xmin'],_0x1a5a0f['lon'][_0x3f6fcd(0x22c)]=_0x1a5a0f[_0x3f6fcd(0x1a9)],_0x1a5a0f['lat']={},_0x1a5a0f[_0x3f6fcd(0x1da)]['min']=_0x1a5a0f['ymin'],_0x1a5a0f['lat']['max']=_0x1a5a0f['ymax'],_0x1a5a0f['lev']={},_0x1a5a0f['lev']['min']=_0x1a5a0f[_0x3f6fcd(0x1ee)]??0x1,_0x1a5a0f['lev']['max']=_0x1a5a0f['levmax']??0x1,_0x1a5a0f['U']={},_0x1a5a0f['U']['array']=new Float32Array(_0x1a5a0f['udata']),_0x1a5a0f['U'][_0x3f6fcd(0x1d4)]=_0x1a5a0f['umin']??Math[_0x3f6fcd(0x1d4)](..._0x1a5a0f[_0x3f6fcd(0x17a)]),_0x1a5a0f['U']['max']=_0x1a5a0f['umax']??Math[_0x3f6fcd(0x22c)](..._0x1a5a0f['udata']),_0x1a5a0f['V']={},_0x1a5a0f['V']['array']=new Float32Array(_0x1a5a0f[_0x3f6fcd(0x159)]),_0x1a5a0f['V']['min']=_0x1a5a0f['vmin']??Math['min'](..._0x1a5a0f[_0x3f6fcd(0x159)]),_0x1a5a0f['V'][_0x3f6fcd(0x22c)]=_0x1a5a0f['vmax']??Math['max'](..._0x1a5a0f[_0x3f6fcd(0x159)])),this['data']=_0x1a5a0f,this['options']=_0x69db6f,this[_0x3f6fcd(0x205)]=_0x1b3730,this[_0x3f6fcd(0x200)]=new ParticlesComputing(this['context'],this[_0x3f6fcd(0x183)],this['options'],this['viewerParameters']),this['particlesRendering']=new ParticlesRendering(this['context'],this['data'],this['options'],this[_0x3f6fcd(0x205)],this['particlesComputing']);}['canvasResize'](_0xb5109c){const _0x4955ad=_0x3a1149;this[_0x4955ad(0x200)]['destroyParticlesTextures'](),Object['keys'](this['particlesComputing'][_0x4955ad(0x163)])['forEach'](_0x437d32=>{const _0x95fabe=_0x4955ad;this['particlesComputing'][_0x95fabe(0x163)][_0x437d32]['destroy']();}),this['particlesRendering']['textures'][_0x4955ad(0x240)]['destroy'](),Object['keys'](this['particlesRendering']['framebuffers'])[_0x4955ad(0x209)](_0x30b5e6=>{const _0x2a14d5=_0x4955ad;this['particlesRendering'][_0x2a14d5(0x1a4)][_0x30b5e6]['destroy']();}),this[_0x4955ad(0x177)]=_0xb5109c,this[_0x4955ad(0x200)]=new ParticlesComputing(this[_0x4955ad(0x177)],this[_0x4955ad(0x183)],this['options'],this['viewerParameters']),this[_0x4955ad(0x14b)]=new ParticlesRendering(this['context'],this[_0x4955ad(0x183)],this['options'],this['viewerParameters'],this['particlesComputing']);}['clearFramebuffers'](){const _0x30e455=_0x3a1149,_0x5e609d=new Cesium$2['ClearCommand']({'color':new Cesium$2['Color'](0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':undefined,'pass':Cesium$2['Pass']['OPAQUE']});Object[_0x30e455(0x210)](this['particlesRendering'][_0x30e455(0x1a4)])[_0x30e455(0x209)](_0x5dfb55=>{const _0x3d0c31=_0x30e455;_0x5e609d[_0x3d0c31(0x1a2)]=this['particlesRendering']['framebuffers'][_0x5dfb55],_0x5e609d[_0x3d0c31(0x15e)](this['context']);});}['refreshParticles'](_0x232a7a){const _0x438a26=_0x3a1149;this[_0x438a26(0x21d)](),this['particlesComputing']['destroyParticlesTextures'](),this['particlesComputing']['createParticlesTextures'](this['context'],this[_0x438a26(0x207)],this['viewerParameters']);if(_0x232a7a){var _0x386765;const _0x3fdd55=this['particlesRendering'][_0x438a26(0x1c8)](this['options']);this['particlesRendering']['primitives'][_0x438a26(0x22b)][_0x438a26(0x233)]=_0x3fdd55;const _0x4e940d=Cesium$2['VertexArray']['fromGeometry']({'context':this[_0x438a26(0x177)],'geometry':_0x3fdd55,'attributeLocations':this[_0x438a26(0x14b)]['primitives']['segments']['attributeLocations'],'bufferUsage':Cesium$2[_0x438a26(0x1f5)][_0x438a26(0x1ef)]});(_0x386765=this['particlesRendering'][_0x438a26(0x199)])!==null&&_0x386765!==void 0x0&&(_0x386765=_0x386765['segments'])!==null&&_0x386765!==void 0x0&&_0x386765[_0x438a26(0x160)]&&(this['particlesRendering']['primitives'][_0x438a26(0x22b)]['commandToExecute']['vertexArray']=_0x4e940d);}}[_0x3a1149(0x19a)](_0x2805b0){const _0x1d7abd=_0x3a1149;let _0x3745fc=![];this[_0x1d7abd(0x207)]['maxParticles']!==_0x2805b0[_0x1d7abd(0x236)]&&(_0x3745fc=!![]),Object['keys'](_0x2805b0)[_0x1d7abd(0x209)](_0x58c1b3=>{this['options'][_0x58c1b3]=_0x2805b0[_0x58c1b3];}),this['refreshParticles'](_0x3745fc);}['applyViewerParameters'](_0x332b97){Object['keys'](_0x332b97)['forEach'](_0x4e0db4=>{this['viewerParameters'][_0x4e0db4]=_0x332b97[_0x4e0db4];}),this['refreshParticles'](![]);}['destroy'](){const _0x425622=_0x3a1149;clearTimeout(this['canrefresh']),this['particlesComputing']['destroyParticlesTextures'](),Object[_0x425622(0x210)](this[_0x425622(0x200)]['windTextures'])[_0x425622(0x209)](_0x4d0134=>{const _0x368859=_0x425622;this['particlesComputing'][_0x368859(0x163)][_0x4d0134]['destroy']();}),this['particlesRendering'][_0x425622(0x1cc)]['colorTable']['destroy'](),Object['keys'](this['particlesRendering']['framebuffers'])['forEach'](_0x1178fa=>{const _0x4a74c3=_0x425622;this['particlesRendering']['framebuffers'][_0x1178fa][_0x4a74c3(0x1fc)]();});for(const _0x5adb65 in this){delete this[_0x5adb65];}}}const Cesium$1=mars3d__namespace['Cesium'],BaseLayer$1=mars3d__namespace[_0x3a1149(0x1f3)]['BaseLayer'],DEF_OPTIONS={'particlesNumber':0x1000,'fixedHeight':0x0,'fadeOpacity':0.996,'dropRate':0.003,'dropRateBump':0.01,'speedFactor':0.5,'lineWidth':0x2,'colors':['rgb(206,255,255)']};class WindLayer extends BaseLayer$1{constructor(_0x199db1={}){_0x199db1={...DEF_OPTIONS,..._0x199db1},super(_0x199db1),this['_setOptionsHook'](_0x199db1);}get['layer'](){return this['primitives'];}get[_0x3a1149(0x183)](){return this['_data'];}set['data'](_0xe8c048){const _0xe6512c=_0x3a1149;this[_0xe6512c(0x1d3)](_0xe8c048);}get['colors'](){const _0x447c38=_0x3a1149;return this[_0x447c38(0x207)]['colors'];}set['colors'](_0x2948eb){const _0x296e16=_0x3a1149;this[_0x296e16(0x207)]['colors']=_0x2948eb,this['particleSystem']&&this[_0x296e16(0x223)][_0x296e16(0x19a)]({'colors':_0x2948eb}),this[_0x296e16(0x1bc)]();}['_mountedHook'](){}['_addedHook'](){const _0x4def34=_0x3a1149;this[_0x4def34(0x191)]=this[_0x4def34(0x198)]['scene'],this['camera']=this['_map'][_0x4def34(0x1e9)],this['primitives']=new Cesium$1['PrimitiveCollection'](),this['_map']['scene']['primitives']['add'](this['primitives']),this['viewerParameters']={'lonRange':new Cesium$1['Cartesian2'](),'latRange':new Cesium$1['Cartesian2'](),'pixelSize':0x0},this[_0x4def34(0x1d1)]=new Cesium$1[(_0x4def34(0x1a1))](Cesium$1['Cartesian3'][_0x4def34(0x20b)],0.99*0x615299),this['updateViewerParameters'](),window[_0x4def34(0x1d5)]('resize',this[_0x4def34(0x1bc)]['bind'](this),![]),this['mouse_down']=![],this['mouse_move']=![],this['_map']['on'](mars3d__namespace[_0x4def34(0x1e4)][_0x4def34(0x1a0)],this['_onMapWhellEvent'],this),this['_map']['on'](mars3d__namespace['EventType']['mouseDown'],this['_onMouseDownEvent'],this),this[_0x4def34(0x198)]['on'](mars3d__namespace['EventType'][_0x4def34(0x1a7)],this['_onMouseUpEvent'],this),this['_map']['on'](mars3d__namespace[_0x4def34(0x1e4)]['mouseMove'],this['_onMouseMoveEvent'],this),this['_data']&&this[_0x4def34(0x1d3)](this['_data']);}[_0x3a1149(0x20c)](){const _0x3f81d1=_0x3a1149;window['removeEventListener']('resize',this['resize']),this['_map']['off'](mars3d__namespace['EventType'][_0x3f81d1(0x181)],this['_onMap_preRenderEvent'],this),this['_map']['off'](mars3d__namespace[_0x3f81d1(0x1e4)][_0x3f81d1(0x1a0)],this['_onMapWhellEvent'],this),this['_map'][_0x3f81d1(0x1c7)](mars3d__namespace['EventType']['mouseDown'],this['_onMouseDownEvent'],this),this[_0x3f81d1(0x198)][_0x3f81d1(0x1c7)](mars3d__namespace[_0x3f81d1(0x1e4)][_0x3f81d1(0x1a7)],this['_onMouseUpEvent'],this),this['_map'][_0x3f81d1(0x1c7)](mars3d__namespace[_0x3f81d1(0x1e4)]['mouseMove'],this['_onMouseMoveEvent'],this),this[_0x3f81d1(0x199)]['removeAll'](),this[_0x3f81d1(0x198)]['scene'][_0x3f81d1(0x199)]['remove'](this[_0x3f81d1(0x199)]);}[_0x3a1149(0x1bc)](){const _0x1c6500=_0x3a1149;if(!this[_0x1c6500(0x189)]||!this[_0x1c6500(0x223)])return;this[_0x1c6500(0x199)]['show']=![],this['primitives'][_0x1c6500(0x167)](),this['_map'][_0x1c6500(0x23a)](mars3d__namespace['EventType']['preRender'],this['_onMap_preRenderEvent'],this);}[_0x3a1149(0x204)](_0x50843a){const _0x6230ad=_0x3a1149;this['particleSystem']['canvasResize'](this['scene'][_0x6230ad(0x177)]),this['addPrimitives'](),this['primitives']['show']=!![];}['_onMapWhellEvent'](_0x3ccde4){const _0x10291b=_0x3a1149;clearTimeout(this['refreshTimer']);if(!this['show']||!this[_0x10291b(0x223)])return;this[_0x10291b(0x199)]['show']=![],this[_0x10291b(0x18b)]=setTimeout(()=>{if(!this['show'])return;this['redraw']();},0xc8);}['_onMouseDownEvent'](_0x1d2a5e){this['mouse_down']=!![];}[_0x3a1149(0x241)](_0x485ebc){const _0x4ac581=_0x3a1149;if(!this['show']||!this[_0x4ac581(0x223)])return;this['mouse_down']&&(this['primitives'][_0x4ac581(0x189)]=![],this['mouse_move']=!![]);}['_onMouseUpEvent'](_0x2411eb){const _0x3cda51=_0x3a1149;if(!this['show']||!this[_0x3cda51(0x223)])return;this['mouse_down']&&this['mouse_move']&&this['redraw'](),this[_0x3cda51(0x199)]['show']=!![],this['mouse_down']=![],this['mouse_move']=![];}[_0x3a1149(0x230)](){const _0x2a2830=_0x3a1149;if(!this['_map']||!this['show'])return;this[_0x2a2830(0x23c)](),this[_0x2a2830(0x223)]['applyViewerParameters'](this[_0x2a2830(0x205)]),this['primitives']['show']=!![];}['setData'](_0x13c785){const _0x25e108=_0x3a1149;this['_data']=_0x13c785,this['particleSystem']&&this[_0x25e108(0x223)]['destroy'](),this['particleSystem']=new ParticleSystem(this['scene'][_0x25e108(0x177)],_0x13c785,this[_0x25e108(0x1e2)](),this['viewerParameters']),this[_0x25e108(0x1ed)]();}['_setOptionsHook'](_0xd2fca9,_0x327e5c){const _0x11e236=_0x3a1149;if(_0xd2fca9)for(const _0x3718ed in _0xd2fca9){this[_0x3718ed]=_0xd2fca9[_0x3718ed];}this['particleSystem']&&this[_0x11e236(0x223)][_0x11e236(0x19a)](this['getOptions']());}['getOptions'](){const _0x1ecd69=_0x3a1149,_0x2f9d51=Math[_0x1ecd69(0x1c3)](Math[_0x1ecd69(0x19d)](this['particlesNumber']));return this['particlesNumber']=_0x2f9d51*_0x2f9d51,{'particlesTextureSize':_0x2f9d51,'maxParticles':this['particlesNumber'],'particleHeight':this[_0x1ecd69(0x1bf)],'fadeOpacity':this['fadeOpacity'],'dropRate':this['dropRate'],'dropRateBump':this['dropRateBump'],'speedFactor':this['speedFactor'],'lineWidth':this[_0x1ecd69(0x195)],'colors':this['colors']};}[_0x3a1149(0x1ed)](){const _0x504180=_0x3a1149;this['primitives'][_0x504180(0x221)](this['particleSystem']['particlesComputing']['primitives']['getWind']),this[_0x504180(0x199)][_0x504180(0x221)](this['particleSystem']['particlesComputing']['primitives']['updateSpeed']),this[_0x504180(0x199)][_0x504180(0x221)](this['particleSystem']['particlesComputing']['primitives'][_0x504180(0x1fd)]),this['primitives']['add'](this['particleSystem'][_0x504180(0x200)]['primitives']['postProcessingPosition']),this['primitives'][_0x504180(0x221)](this[_0x504180(0x223)][_0x504180(0x200)][_0x504180(0x199)][_0x504180(0x20a)]),this['primitives']['add'](this['particleSystem']['particlesRendering']['primitives']['segments']),this['primitives']['add'](this['particleSystem'][_0x504180(0x14b)][_0x504180(0x199)][_0x504180(0x22a)]),this[_0x504180(0x199)]['add'](this[_0x504180(0x223)]['particlesRendering']['primitives']['screen']);}['updateViewerParameters'](){const _0x3cedef=_0x3a1149;let _0x4917b0=this['camera'][_0x3cedef(0x218)](this['scene'][_0x3cedef(0x15d)]['ellipsoid']);if(!_0x4917b0){const _0x31a4a0=this['_map']['getExtent']();_0x4917b0=Cesium$1['Rectangle']['fromDegrees'](_0x31a4a0['xmin'],_0x31a4a0['ymin'],_0x31a4a0[_0x3cedef(0x1a9)],_0x31a4a0['ymax']);}const _0x301a55=Util['viewRectangleToLonLatRange'](_0x4917b0);this[_0x3cedef(0x205)]['lonRange']['x']=_0x301a55[_0x3cedef(0x217)]['min'],this['viewerParameters']['lonRange']['y']=_0x301a55[_0x3cedef(0x217)][_0x3cedef(0x22c)],this[_0x3cedef(0x205)]['latRange']['x']=_0x301a55['lat'][_0x3cedef(0x1d4)],this['viewerParameters']['latRange']['y']=_0x301a55['lat']['max'];const _0x56c15f=this['camera'][_0x3cedef(0x165)](this['globeBoundingSphere'],this[_0x3cedef(0x191)]['drawingBufferWidth'],this['scene']['drawingBufferHeight']);_0x56c15f>0x0&&(this['viewerParameters'][_0x3cedef(0x216)]=_0x56c15f);}}mars3d__namespace[_0x3a1149(0x211)]['register'](_0x3a1149(0x1ce),WindLayer),mars3d__namespace['layer'][_0x3a1149(0x1be)]=WindLayer;class CanvasParticle{constructor(){this['lng']=null,this['lat']=null,this['tlng']=null,this['tlat']=null,this['age']=null,this['speed']=null;}['destroy'](){for(const _0x491d7c in this){delete this[_0x491d7c];}}}class CanvasWindField{constructor(_0x105496){const _0x5e7b37=_0x3a1149;this[_0x5e7b37(0x19a)](_0x105496);}get['speedRate'](){return this['_speedRate'];}set['speedRate'](_0x56d194){const _0x183b9f=_0x3a1149;this['_speedRate']=(0x64-(_0x56d194>0x63?0x63:_0x56d194))*0x64,this[_0x183b9f(0x16e)]=[(this['xmax']-this[_0x183b9f(0x1ab)])/this['_speedRate'],(this['ymax']-this['ymin'])/this['_speedRate']];}get['maxAge'](){const _0x487d54=_0x3a1149;return this[_0x487d54(0x1fa)];}set['maxAge'](_0x2850fe){this['_maxAge']=_0x2850fe;}[_0x3a1149(0x19a)](_0x13158e){const _0x1993a5=_0x3a1149;this['options']=_0x13158e,this['maxAge']=_0x13158e[_0x1993a5(0x1ea)]||0x78,this['speedRate']=_0x13158e['speedRate']||0x32,this['particles']=[];const _0x52a01e=_0x13158e['particlesNumber']||0x1000;for(let _0x1cdcc6=0x0;_0x1cdcc6<_0x52a01e;_0x1cdcc6++){const _0x494358=this['_randomParticle'](new CanvasParticle());this['particles'][_0x1993a5(0x15c)](_0x494358);}}['setDate'](_0x202a24){const _0x4a7b89=_0x3a1149;this['rows']=_0x202a24['rows'],this[_0x4a7b89(0x19c)]=_0x202a24['cols'],this['xmin']=_0x202a24['xmin'],this['xmax']=_0x202a24['xmax'],this[_0x4a7b89(0x1e8)]=_0x202a24['ymin'],this['ymax']=_0x202a24[_0x4a7b89(0x188)],this['grid']=[];const _0x3706fa=_0x202a24['udata'],_0x131bd2=_0x202a24[_0x4a7b89(0x159)];let _0x2f6ef7=![];_0x3706fa['length']===this['rows']&&_0x3706fa[0x0][_0x4a7b89(0x1f2)]===this[_0x4a7b89(0x19c)]&&(_0x2f6ef7=!![]);let _0x1c26a6=0x0,_0x158919=null,_0x5abdc4=null;for(let _0x34fde0=0x0;_0x34fde0<this[_0x4a7b89(0x1d8)];_0x34fde0++){_0x158919=[];for(let _0xb0538e=0x0;_0xb0538e<this['cols'];_0xb0538e++,_0x1c26a6++){_0x2f6ef7?_0x5abdc4=this['_calcUV'](_0x3706fa[_0x34fde0][_0xb0538e],_0x131bd2[_0x34fde0][_0xb0538e]):_0x5abdc4=this['_calcUV'](_0x3706fa[_0x1c26a6],_0x131bd2[_0x1c26a6]),_0x158919[_0x4a7b89(0x15c)](_0x5abdc4);}this['grid']['push'](_0x158919);}this['options']['reverseY']&&this['grid']['reverse']();}['clear'](){const _0x1ef809=_0x3a1149;delete this[_0x1ef809(0x1d8)],delete this['cols'],delete this['xmin'],delete this[_0x1ef809(0x1a9)],delete this['ymin'],delete this['ymax'],delete this['grid'],delete this['particles'];}['toGridXY'](_0x432c84,_0x2343b8){const _0x22b40b=_0x3a1149,_0xaf96ff=(_0x432c84-this[_0x22b40b(0x1ab)])/(this['xmax']-this['xmin'])*(this['cols']-0x1),_0x5aa1a8=(this[_0x22b40b(0x188)]-_0x2343b8)/(this['ymax']-this[_0x22b40b(0x1e8)])*(this['rows']-0x1);return[_0xaf96ff,_0x5aa1a8];}[_0x3a1149(0x1ca)](_0x140281,_0x53c3e9){const _0xc72345=_0x3a1149;if(_0x140281<0x0||_0x140281>=this['cols']||_0x53c3e9>=this['rows'])return[0x0,0x0,0x0];const _0x3a8518=Math['floor'](_0x140281),_0x53f690=Math[_0xc72345(0x1c9)](_0x53c3e9);if(_0x3a8518===_0x140281&&_0x53f690===_0x53c3e9)return this['grid'][_0x53c3e9][_0x140281];const _0x5a1761=_0x3a8518+0x1,_0x23f7eb=_0x53f690+0x1,_0x3a1798=this['getUVByXY'](_0x3a8518,_0x53f690),_0x250d61=this['getUVByXY'](_0x5a1761,_0x53f690),_0x2d3578=this['getUVByXY'](_0x3a8518,_0x23f7eb),_0x2e2ea5=this[_0xc72345(0x1ca)](_0x5a1761,_0x23f7eb);let _0x1c6c23=null;try{_0x1c6c23=this['_bilinearInterpolation'](_0x140281-_0x3a8518,_0x53c3e9-_0x53f690,_0x3a1798,_0x250d61,_0x2d3578,_0x2e2ea5);}catch(_0x3333d5){console['log'](_0x140281,_0x53c3e9);}return _0x1c6c23;}['_bilinearInterpolation'](_0x3170ad,_0x2090f7,_0x25eab6,_0x550445,_0x3769b1,_0x1cc97d){const _0x376991=_0x3a1149,_0x3040de=0x1-_0x3170ad,_0x5ed901=0x1-_0x2090f7,_0x311102=_0x3040de*_0x5ed901,_0x1edd72=_0x3170ad*_0x5ed901,_0x1d0198=_0x3040de*_0x2090f7,_0x1d5eae=_0x3170ad*_0x2090f7,_0x2012a3=_0x25eab6[0x0]*_0x311102+_0x550445[0x0]*_0x1edd72+_0x3769b1[0x0]*_0x1d0198+_0x1cc97d[0x0]*_0x1d5eae,_0x31c995=_0x25eab6[0x1]*_0x311102+_0x550445[0x1]*_0x1edd72+_0x3769b1[0x1]*_0x1d0198+_0x1cc97d[0x1]*_0x1d5eae;return this[_0x376991(0x16c)](_0x2012a3,_0x31c995);}['_calcUV'](_0x5e1aaa,_0x4b2838){return[+_0x5e1aaa,+_0x4b2838,Math['sqrt'](_0x5e1aaa*_0x5e1aaa+_0x4b2838*_0x4b2838)];}['getUVByPoint'](_0x496e5f,_0x496170){if(!this['isInExtent'](_0x496e5f,_0x496170))return null;const _0x1a0ff5=this['toGridXY'](_0x496e5f,_0x496170),_0x2f2c40=this['getUVByXY'](_0x1a0ff5[0x0],_0x1a0ff5[0x1]);return _0x2f2c40;}['isInExtent'](_0x10b932,_0x5aafe9){return _0x10b932>=this['xmin']&&_0x10b932<=this['xmax']&&_0x5aafe9>=this['ymin']&&_0x5aafe9<=this['ymax']?!![]:![];}['getRandomLatLng'](){const _0x1c5204=_0x3a1149,_0x3f8b82=fRandomByfloat(this['xmin'],this['xmax']),_0x9d78a9=fRandomByfloat(this[_0x1c5204(0x1e8)],this['ymax']);return{'lat':_0x9d78a9,'lng':_0x3f8b82};}['getParticles'](){const _0x48917f=_0x3a1149;let _0x11643e,_0x109ad1,_0xa46d09;for(let _0x243ef9=0x0,_0x364ce3=this['particles']['length'];_0x243ef9<_0x364ce3;_0x243ef9++){let _0x35cd8d=this['particles'][_0x243ef9];_0x35cd8d['age']<=0x0&&(_0x35cd8d=this['_randomParticle'](_0x35cd8d));if(_0x35cd8d[_0x48917f(0x228)]>0x0){const _0x247edf=_0x35cd8d[_0x48917f(0x1b0)],_0x58cc89=_0x35cd8d['tlat'];_0xa46d09=this['getUVByPoint'](_0x247edf,_0x58cc89),_0xa46d09?(_0x11643e=_0x247edf+this[_0x48917f(0x16e)][0x0]*_0xa46d09[0x0],_0x109ad1=_0x58cc89+this[_0x48917f(0x16e)][0x1]*_0xa46d09[0x1],_0x35cd8d['lng']=_0x247edf,_0x35cd8d['lat']=_0x58cc89,_0x35cd8d['tlng']=_0x11643e,_0x35cd8d['tlat']=_0x109ad1,_0x35cd8d[_0x48917f(0x18e)]=_0xa46d09[0x2],_0x35cd8d['age']--):_0x35cd8d['age']=0x0;}}return this[_0x48917f(0x14d)];}['_randomParticle'](_0x257871){const _0x4daa75=_0x3a1149;let _0x297378,_0x1303db;for(let _0xb5635d=0x0;_0xb5635d<0x1e;_0xb5635d++){_0x297378=this['getRandomLatLng'](),_0x1303db=this['getUVByPoint'](_0x297378[_0x4daa75(0x23d)],_0x297378['lat']);if(_0x1303db&&_0x1303db[0x2]>0x0)break;}if(!_0x1303db)return _0x257871;const _0x3bfd84=_0x297378['lng']+this[_0x4daa75(0x16e)][0x0]*_0x1303db[0x0],_0x184c54=_0x297378[_0x4daa75(0x1da)]+this['_calc_speedRate'][0x1]*_0x1303db[0x1];return _0x257871['lng']=_0x297378['lng'],_0x257871['lat']=_0x297378['lat'],_0x257871['tlng']=_0x3bfd84,_0x257871['tlat']=_0x184c54,_0x257871['age']=Math['round'](Math[_0x4daa75(0x19b)]()*this['maxAge']),_0x257871['speed']=_0x1303db[0x2],_0x257871;}['destroy'](){for(const _0x591793 in this){delete this[_0x591793];}}}function fRandomByfloat(_0x2d656a,_0x10bd04){const _0x38bacb=_0x3a1149;return _0x2d656a+Math[_0x38bacb(0x19b)]()*(_0x10bd04-_0x2d656a);}const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer']['BaseLayer'];function _0x2c91(_0x3626c3,_0x125b68){const _0x3bd3da=_0x3bd3();return _0x2c91=function(_0x2c9110,_0x2eba0a){_0x2c9110=_0x2c9110-0x14a;let _0x2fe9cc=_0x3bd3da[_0x2c9110];return _0x2fe9cc;},_0x2c91(_0x3626c3,_0x125b68);}class CanvasWindLayer extends BaseLayer{constructor(_0x3a9403={}){const _0x12ba53=_0x3a1149;super(_0x3a9403),this['_setOptionsHook'](_0x3a9403),this[_0x12ba53(0x1b1)]=null,_0x3a9403['colors']&&_0x3a9403['steps']&&(this['_colorRamp']=new mars3d__namespace[(_0x12ba53(0x194))](_0x3a9403));}['_setOptionsHook'](_0x154c6f,_0xbfc915){const _0x450f5c=_0x3a1149;this['frameTime']=0x3e8/(_0x154c6f[_0x450f5c(0x151)]||0xa),this['_pointerEvents']=this['options']['pointerEvents']??![],this['color']=_0x154c6f['color']||_0x450f5c(0x17e),this['lineWidth']=_0x154c6f['lineWidth']||0x1,this['fixedHeight']=_0x154c6f['fixedHeight']??0x0,this['reverseY']=_0x154c6f[_0x450f5c(0x1cd)]??![],this[_0x450f5c(0x1d7)]&&this[_0x450f5c(0x1d7)]['setOptions'](_0x154c6f);}get['layer'](){return this['canvas'];}get['canvasWidth'](){const _0xcfbf8e=_0x3a1149;return this['_map']['scene'][_0xcfbf8e(0x1b1)][_0xcfbf8e(0x23f)];}get['canvasHeight'](){const _0x1d89db=_0x3a1149;return this['_map']['scene'][_0x1d89db(0x1b1)][_0x1d89db(0x173)];}get['pointerEvents'](){return this['_pointerEvents'];}set['pointerEvents'](_0x2564cc){const _0x13824d=_0x3a1149;this[_0x13824d(0x1ec)]=_0x2564cc;if(!this[_0x13824d(0x1b1)])return;_0x2564cc?this[_0x13824d(0x1b1)]['style'][_0x13824d(0x14e)]='all':this['canvas']['style']['pointer-events']='none';}get['particlesNumber'](){return this['options']['particlesNumber'];}set[_0x3a1149(0x224)](_0x2f593a){const _0x5088b3=_0x3a1149;this['options']['particlesNumber']=_0x2f593a,clearTimeout(this[_0x5088b3(0x1f8)]),this[_0x5088b3(0x1f8)]=setTimeout(()=>{const _0x3b08dd=_0x5088b3;this[_0x3b08dd(0x230)]();},0x1f4);}get[_0x3a1149(0x1d9)](){return this['options']['speedRate'];}set['speedRate'](_0x1f2408){const _0x52cb95=_0x3a1149;this[_0x52cb95(0x207)]['speedRate']=_0x1f2408,this['windField']&&(this['windField'][_0x52cb95(0x1d9)]=_0x1f2408);}get[_0x3a1149(0x1ea)](){const _0x5729b4=_0x3a1149;return this[_0x5729b4(0x207)]['maxAge'];}set['maxAge'](_0x577db2){const _0x4628d8=_0x3a1149;this['options'][_0x4628d8(0x1ea)]=_0x577db2,this['windField']&&(this['windField']['maxAge']=_0x577db2);}get['data'](){return this['windData'];}set['data'](_0x5385d7){this['setData'](_0x5385d7);}['_showHook'](_0x31693a){const _0x5c25fa=_0x3a1149;_0x31693a?this['_addedHook']():(this['windData']&&(this[_0x5c25fa(0x207)]['data']=this['windData']),this['_removedHook']());}['_mountedHook'](){const _0xafb3b4=_0x3a1149;this[_0xafb3b4(0x207)][_0xafb3b4(0x20d)]?this['initWorker']():this['windField']=new CanvasWindField(this['options']);}['_addedHook'](){const _0x7f89e7=_0x3a1149;this['canvas']=this['_createCanvas'](),this['canvasContext']=this[_0x7f89e7(0x1b1)][_0x7f89e7(0x18d)]('2d',{'willReadFrequently':!![]}),this[_0x7f89e7(0x175)](),this[_0x7f89e7(0x207)]['data']&&this[_0x7f89e7(0x1d3)](this[_0x7f89e7(0x207)]['data']);}[_0x3a1149(0x20c)](){const _0x1144c1=_0x3a1149;this['clear'](),this['unbindEvent'](),this['canvas']&&(this['_map']['container'][_0x1144c1(0x1a5)](this['canvas']),delete this['canvas']);}[_0x3a1149(0x20e)](){const _0x476666=_0x3a1149,_0xb3e295=mars3d__namespace['DomUtil'][_0x476666(0x15f)]('canvas',_0x476666(0x1e5),this['_map']['container']);return _0xb3e295['style']['position']='absolute',_0xb3e295['style']['top']='0px',_0xb3e295[_0x476666(0x225)][_0x476666(0x21e)]='0px',_0xb3e295['style']['width']=this['_map']['scene']['canvas']['clientWidth']+'px',_0xb3e295['style'][_0x476666(0x23e)]=this[_0x476666(0x198)]['scene']['canvas']['clientHeight']+'px',_0xb3e295[_0x476666(0x225)]['pointerEvents']=this['_pointerEvents']?_0x476666(0x166):'none',_0xb3e295['style'][_0x476666(0x235)]=this['options']['zIndex']??0x9,_0xb3e295['width']=this[_0x476666(0x198)]['scene'][_0x476666(0x1b1)][_0x476666(0x23f)],_0xb3e295[_0x476666(0x23e)]=this['_map']['scene'][_0x476666(0x1b1)][_0x476666(0x173)],_0xb3e295;}['resize'](){const _0x5e22cf=_0x3a1149;this['canvas']&&(this[_0x5e22cf(0x1b1)]['style']['width']=this[_0x5e22cf(0x198)]['scene']['canvas'][_0x5e22cf(0x23f)]+'px',this['canvas']['style']['height']=this[_0x5e22cf(0x198)]['scene']['canvas']['clientHeight']+'px',this['canvas']['width']=this['_map']['scene']['canvas'][_0x5e22cf(0x23f)],this['canvas'][_0x5e22cf(0x23e)]=this['_map']['scene']['canvas']['clientHeight']);}[_0x3a1149(0x175)](){const _0x47463e=_0x3a1149,_0x2d6ccb=this;let _0x2f6785=Date[_0x47463e(0x234)]();(function _0x4cc972(){const _0x56c8be=_0x47463e;_0x2d6ccb[_0x56c8be(0x192)]=window['requestAnimationFrame'](_0x4cc972);if(_0x2d6ccb[_0x56c8be(0x189)]&&_0x2d6ccb['windField']){const _0x38b5ef=Date['now'](),_0x13db4e=_0x38b5ef-_0x2f6785;_0x13db4e>_0x2d6ccb['frameTime']&&(_0x2f6785=_0x38b5ef-_0x13db4e%_0x2d6ccb[_0x56c8be(0x158)],_0x2d6ccb['update']());}}(),window['addEventListener']('resize',this['resize'][_0x47463e(0x21a)](this),![]),this['mouse_down']=![],this['mouse_move']=![],this['options']['mouseHidden']&&(this[_0x47463e(0x198)]['on'](mars3d__namespace['EventType']['wheel'],this[_0x47463e(0x220)],this),this['_map']['on'](mars3d__namespace[_0x47463e(0x1e4)][_0x47463e(0x1f6)],this[_0x47463e(0x196)],this),this[_0x47463e(0x198)]['on'](mars3d__namespace['EventType']['mouseUp'],this['_onMouseUpEvent'],this)));}['unbindEvent'](){const _0x54bde5=_0x3a1149;window['cancelAnimationFrame'](this['animateFrame']),delete this[_0x54bde5(0x192)],window['removeEventListener']('resize',this['resize']),this[_0x54bde5(0x207)]['mouseHidden']&&(this['_map']['off'](mars3d__namespace[_0x54bde5(0x1e4)][_0x54bde5(0x1a0)],this['_onMapWhellEvent'],this),this[_0x54bde5(0x198)][_0x54bde5(0x1c7)](mars3d__namespace['EventType'][_0x54bde5(0x1f6)],this['_onMouseDownEvent'],this),this['_map']['off'](mars3d__namespace['EventType'][_0x54bde5(0x1a7)],this[_0x54bde5(0x15a)],this),this[_0x54bde5(0x198)][_0x54bde5(0x1c7)](mars3d__namespace[_0x54bde5(0x1e4)]['mouseMove'],this[_0x54bde5(0x241)],this));}['_onMapWhellEvent'](_0x3e49d1){const _0x2dd83e=_0x3a1149;clearTimeout(this['refreshTimer']);if(!this[_0x2dd83e(0x189)]||!this['canvas'])return;this['canvas'][_0x2dd83e(0x225)]['visibility']='hidden',this['refreshTimer']=setTimeout(()=>{const _0x13f9f5=_0x2dd83e;if(!this['show'])return;this[_0x13f9f5(0x230)](),this['canvas']['style']['visibility']=_0x13f9f5(0x206);},0xc8);}['_onMouseDownEvent'](_0x3df313){const _0xf69825=_0x3a1149;this['mouse_down']=!![],this['_map']['off'](mars3d__namespace['EventType'][_0xf69825(0x1d6)],this['_onMouseMoveEvent'],this),this[_0xf69825(0x198)]['on'](mars3d__namespace[_0xf69825(0x1e4)]['mouseMove'],this['_onMouseMoveEvent'],this);}[_0x3a1149(0x241)](_0x2602a2){const _0x576248=_0x3a1149;if(!this['show']||!this['canvas'])return;this['mouse_down']&&(this['canvas']['style'][_0x576248(0x1cf)]='hidden',this['mouse_move']=!![]);}['_onMouseUpEvent'](_0x55f252){const _0x30a521=_0x3a1149;if(!this['show']||!this['canvas'])return;this['_map']['off'](mars3d__namespace['EventType']['mouseMove'],this['_onMouseMoveEvent'],this),this['mouse_down']&&this[_0x30a521(0x178)]&&this['redraw'](),this[_0x30a521(0x1b1)][_0x30a521(0x225)]['visibility']='visible',this[_0x30a521(0x155)]=![],this[_0x30a521(0x178)]=![];}['setData'](_0x447bbc){const _0x1bad2e=_0x3a1149;this[_0x1bad2e(0x186)](),this[_0x1bad2e(0x21c)]=_0x447bbc,this['windField']['setDate'](_0x447bbc),this['redraw']();}['redraw'](){if(!this['show'])return;this['windField']['setOptions'](this['options']),this['update']();}['update'](){const _0x3bab00=_0x3a1149;if(this[_0x3bab00(0x18f)])return;this['_updateIng']=!![];if(this['worker'])this['windField']['update']();else{const _0x6e605c=this[_0x3bab00(0x1d7)]['getParticles']();this['_drawLines'](_0x6e605c);}this['_updateIng']=![];}['_drawLines'](_0x28f0f6){const _0x4f445a=_0x3a1149;this['canvasContext']['globalCompositeOperation']='destination-in',this['canvasContext']['fillRect'](0x0,0x0,this[_0x4f445a(0x168)],this['canvasHeight']),this['canvasContext']['globalCompositeOperation']='lighter',this['canvasContext'][_0x4f445a(0x1e6)]=0.9;const _0x1b5113=this['_map']['scene']['mode']!==Cesium[_0x4f445a(0x1c1)][_0x4f445a(0x172)],_0xf5417b=this['canvasWidth']*0.25;if(this['_colorRamp'])for(let _0x3b6701=0x0,_0x4a64d3=_0x28f0f6['length'];_0x3b6701<_0x4a64d3;_0x3b6701++){const _0x2856f3=_0x28f0f6[_0x3b6701],_0x29efff=this['_tomap'](_0x2856f3['lng'],_0x2856f3[_0x4f445a(0x1da)],_0x2856f3),_0xbe398b=this[_0x4f445a(0x164)](_0x2856f3[_0x4f445a(0x1b0)],_0x2856f3[_0x4f445a(0x152)],_0x2856f3);if(!_0x29efff||!_0xbe398b)continue;if(_0x1b5113&&Math[_0x4f445a(0x1fe)](_0x29efff[0x0]-_0xbe398b[0x0])>=_0xf5417b)continue;this['canvasContext'][_0x4f445a(0x1a3)](),this[_0x4f445a(0x17c)]['lineWidth']=this['lineWidth'],this['canvasContext']['strokeStyle']=this['_colorRamp']['getColor'](_0x2856f3[_0x4f445a(0x18e)]),this['canvasContext']['moveTo'](_0x29efff[0x0],_0x29efff[0x1]),this['canvasContext']['lineTo'](_0xbe398b[0x0],_0xbe398b[0x1]),this['canvasContext'][_0x4f445a(0x1de)]();}else{this['canvasContext']['beginPath'](),this['canvasContext']['lineWidth']=this[_0x4f445a(0x195)],this[_0x4f445a(0x17c)][_0x4f445a(0x20f)]=this[_0x4f445a(0x1b5)];for(let _0x1642fc=0x0,_0xf3ad8b=_0x28f0f6['length'];_0x1642fc<_0xf3ad8b;_0x1642fc++){const _0x2c4db3=_0x28f0f6[_0x1642fc],_0x32496f=this[_0x4f445a(0x164)](_0x2c4db3['lng'],_0x2c4db3['lat'],_0x2c4db3),_0x6f4e62=this['_tomap'](_0x2c4db3[_0x4f445a(0x1b0)],_0x2c4db3[_0x4f445a(0x152)],_0x2c4db3);if(!_0x32496f||!_0x6f4e62)continue;if(_0x1b5113&&Math['abs'](_0x32496f[0x0]-_0x6f4e62[0x0])>=_0xf5417b)continue;this[_0x4f445a(0x17c)]['moveTo'](_0x32496f[0x0],_0x32496f[0x1]),this[_0x4f445a(0x17c)]['lineTo'](_0x6f4e62[0x0],_0x6f4e62[0x1]);}this[_0x4f445a(0x17c)]['stroke']();}}[_0x3a1149(0x164)](_0x13234c,_0x2c72d8,_0x40fd1b){const _0x10417b=_0x3a1149,_0xd7b311=Cesium[_0x10417b(0x1bb)]['fromDegrees'](_0x13234c,_0x2c72d8,this['fixedHeight']),_0x21dc03=this['_map'][_0x10417b(0x191)];if(_0x21dc03[_0x10417b(0x190)]===Cesium[_0x10417b(0x1c1)]['SCENE3D']){const _0x2cbeb2=new Cesium['EllipsoidalOccluder'](_0x21dc03['globe']['ellipsoid'],_0x21dc03['camera']['positionWC']),_0x9fff17=_0x2cbeb2['isPointVisible'](_0xd7b311);if(!_0x9fff17)return _0x40fd1b[_0x10417b(0x228)]=0x0,null;}const _0x6f1c5e=Cesium['SceneTransforms']['wgs84ToWindowCoordinates'](this['_map']['scene'],_0xd7b311);return _0x6f1c5e?[_0x6f1c5e['x'],_0x6f1c5e['y']]:null;}['clear'](){const _0xd64a19=_0x3a1149;this[_0xd64a19(0x1d7)]['clear'](),delete this['windData'];}['initWorker'](){const _0x2feb6c=_0x3a1149;this['worker']=new Worker(this['options'][_0x2feb6c(0x20d)]),this[_0x2feb6c(0x20d)]['onmessage']=_0x57e657=>{const _0x5c9aea=_0x2feb6c;this['_drawLines'](_0x57e657[_0x5c9aea(0x183)]['particles']),this[_0x5c9aea(0x227)]=![];},this[_0x2feb6c(0x1d7)]={'init':_0x54a182=>{this['worker']['postMessage']({'type':'init','options':_0x54a182});},'setOptions':_0x38d653=>{const _0x3ed13e=_0x2feb6c;this[_0x3ed13e(0x20d)]['postMessage']({'type':'setOptions','options':_0x38d653});},'setDate':_0xbb37fb=>{this['worker']['postMessage']({'type':'setDate','data':_0xbb37fb});},'update':()=>{if(this['_updateIng2'])return;this['_updateIng2']=!![],this['worker']['postMessage']({'type':'update'});},'clear':()=>{const _0x43f0ae=_0x2feb6c;this['worker'][_0x43f0ae(0x1d2)]({'type':_0x43f0ae(0x186)});}},this['windField'][_0x2feb6c(0x22e)](this[_0x2feb6c(0x207)]);}}mars3d__namespace['LayerUtil']['register']('canvasWind',CanvasWindLayer),mars3d__namespace['layer']['CanvasWindLayer']=CanvasWindLayer,mars3d__namespace[_0x3a1149(0x153)]=CanvasWindField,mars3d__namespace[_0x3a1149(0x1f4)]=WindUtil,exports['CanvasWindField']=CanvasWindField,exports['CanvasWindLayer']=CanvasWindLayer,exports[_0x3a1149(0x1be)]=WindLayer,exports['WindUtil']=WindUtil,Object['defineProperty'](exports,'__esModule',{'value':!![]});
}));
