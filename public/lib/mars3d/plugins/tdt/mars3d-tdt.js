!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("mars3d")):"function"==typeof define&&define.amd?define(["exports","mars3d"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self)["mars3d-tdt"]={},e.mars3d)}(this,(function(e,t){"use strict";function r(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,t}var i=r(t);const n=i.Cesium;n.when||(n.when=n.when||{},n.when.defer=n.defer),function(e){var t={};function r(i){if(t[i])return t[i].exports;var n=t[i]={i:i,l:!1,exports:{}};return e[i].call(n.exports,n,n.exports,r),n.l=!0,n.exports}r.m=e,r.c=t,r.d=function(e,t,i){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)r.d(i,n,function(t){return e[t]}.bind(null,n));return i},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=242)}([function(e,t,r){var i=r(175),n=r(6);function o(t){return e.exports=o="function"==typeof n&&"symbol"==typeof i?function(e){return typeof e}:function(e){return e&&"function"==typeof n&&e.constructor===n&&e!==n.prototype?"symbol":typeof e},o(t)}e.exports=o},function(e,t,r){e.exports=r(172)},function(e,t,r){e.exports=r(169)},function(e,t,r){e.exports=r(160)},function(e,t,r){var i=r(50)("wks"),n=r(28),o=r(10).Symbol,s="function"==typeof o;(e.exports=function(e){return i[e]||(i[e]=s&&o[e]||(s?o:n)("Symbol."+e))}).store=i},function(e,t){},function(e,t,r){},function(e,t){var r=e.exports="undefined"!=typeof window&&window.Math===Math?window:"undefined"!=typeof self&&self.Math===Math?self:Function("return this")();"number"==typeof __g&&(__g=r)},function(e,t,r){e.exports=r(167)},function(e,t,r){e.exports=r(154)},function(e,t){var r=e.exports="undefined"!=typeof window&&window.Math===Math?window:"undefined"!=typeof self&&self.Math===Math?self:Function("return this")();"number"==typeof __g&&(__g=r)},function(e,t,r){},function(e,t,r){var i=r(7),n=r(5),o=r(59),s=r(32),a=r(31),f=function(e,t,r){var u,h,l,c=e&f.F,d=e&f.G,p=e&f.S,x=e&f.P,g=e&f.B,v=e&f.W,y=d?n:n[t]||(n[t]={}),b=y.prototype,m=d?i:p?i[t]:(i[t]||{}).prototype;for(u in d&&(r=t),r)(h=!c&&m&&void 0!==m[u])&&a(y,u)||(l=h?m[u]:r[u],y[u]=d&&"function"!=typeof m[u]?r[u]:g&&h?o(l,i):v&&m[u]===l?function(e){var t=function(t,r,i){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,r)}return new e(t,r,i)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(l):x&&"function"==typeof l?o(Function.call,l):l,x&&((y.virtual||(y.virtual={}))[u]=l,e&f.R&&b&&!b[u]&&s(b,u,l)))};f.F=1,f.G=2,f.S=4,f.P=8,f.B=16,f.W=32,f.U=64,f.R=128,e.exports=f},function(e,t,r){var i=r(17),n=r(98),o=r(44),s=Object.defineProperty;t.f=r(11)?Object.defineProperty:function(e,t,r){if(i(e),t=o(t,!0),i(r),n)try{return s(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},function(e,t,r){var i=r(75)("wks"),n=r(58),o=r(7).Symbol,s="function"==typeof o;(e.exports=function(e){return i[e]||(i[e]=s&&o[e]||(s?o:n)("Symbol."+e))}).store=i},function(e,t,r){e.exports=r(147)},function(e,t,r){e.exports=r(164)},function(e,t,r){var i=r(21);e.exports=function(e){if(!i(e))throw TypeError(e+" is not an object!");return e}},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t,r){var i=r(29),n=Math.min;e.exports=function(e){return e>0?n(i(e),9007199254740991):0}},function(e,t,r){var i=r(13),n=r(45);e.exports=r(11)?function(e,t,r){return i.f(e,t,n(1,r))}:function(e,t,r){return e[t]=r,e}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,r){var i=r(10),n=r(20),o=r(24),s=r(28)("src"),a=r(150),f=(""+a).split("toString");r(43).inspectSource=function(e){return a.call(e)},(e.exports=function(e,t,r,a){var u="function"==typeof r;u&&(o(r,"name")||n(r,"name",t)),e[t]!==r&&(u&&(o(r,s)||n(r,s,e[t]?""+e[t]:f.join(String(t)))),e===i?e[t]=r:a?e[t]?e[t]=r:n(e,t,r):(delete e[t],n(e,t,r)))})(Function.prototype,"toString",(function(){return"function"==typeof this&&this[s]||a.call(this)}))},function(e,t,r){var i=r(10),n=r(43),o=r(20),s=r(22),a=r(51),f=function(e,t,r){var u,h,l,c,d=e&f.F,p=e&f.G,x=e&f.S,g=e&f.P,v=e&f.B,y=p?i:x?i[t]||(i[t]={}):(i[t]||{}).prototype,b=p?n:n[t]||(n[t]={}),m=b.prototype||(b.prototype={});for(u in p&&(r=t),r)l=((h=!d&&y&&void 0!==y[u])?y:r)[u],c=v&&h?a(l,i):g&&"function"==typeof l?a(Function.call,l):l,y&&s(y,u,l,e&f.U),b[u]!==l&&o(b,u,c),g&&m[u]!==l&&(m[u]=l)};i.core=n,f.F=1,f.G=2,f.S=4,f.P=8,f.B=16,f.W=32,f.U=64,f.R=128,e.exports=f},function(e,t){var r={}.hasOwnProperty;e.exports=function(e,t){return r.call(e,t)}},function(e,t,r){e.exports=!r(40)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},function(e,t,r){var i=r(27),n=r(107),o=r(78),s=Object.defineProperty;t.f=r(25)?Object.defineProperty:function(e,t,r){if(i(e),t=o(t,!0),i(r),n)try{return s(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},function(e,t,r){var i=r(33);e.exports=function(e){if(!i(e))throw TypeError(e+" is not an object!");return e}},function(e,t){var r=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++r+i).toString(36))}},function(e,t){var r=Math.ceil,i=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?i:r)(e)}},function(e,t,r){var i=r(38);e.exports=function(e){return Object(i(e))}},function(e,t){var r={}.hasOwnProperty;e.exports=function(e,t){return r.call(e,t)}},function(e,t,r){var i=r(26),n=r(61);e.exports=r(25)?function(e,t,r){return i.f(e,t,n(1,r))}:function(e,t,r){return e[t]=r,e}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,r){},function(e,t){e.exports=!1},function(e,t){e.exports={}},function(e,t,r){var i=r(100),n=r(38);e.exports=function(e){return i(n(e))}},function(e,t){e.exports=function(e){if(null===e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,r){var i=r(156),n=r(56);e.exports=function(e){return i(n(e))}},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t,r){var i=r(102),n=r(71).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return i(e,n)}},function(e,t,r){},function(e,t){},function(e,t,r){var i=r(21);e.exports=function(e,t){if(!i(e))return e;var r,n;if(t&&"function"==typeof(r=e.toString)&&!i(n=r.call(e)))return n;if("function"==typeof(r=e.valueOf)&&!i(n=r.call(e)))return n;if(!t&&"function"==typeof(r=e.toString)&&!i(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){var r={}.toString;e.exports=function(e){return r.call(e).slice(8,-1)}},function(e,t){var r={}.toString;e.exports=function(e){return r.call(e).slice(8,-1)}},function(e,t){e.exports=!0},function(e,t){e.exports={}},function(e,t,r){var i=r(43),n=r(10),o=n["__core-js_shared__"]||(n["__core-js_shared__"]={});(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:i.version,mode:r(35)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(e,t,r){var i=r(101);e.exports=function(e,t,r){if(i(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,i){return e.call(t,r,i)};case 3:return function(r,i,n){return e.call(t,r,i,n)}}return function(){return e.apply(t,arguments)}}},function(e,t,r){var i=r(17),n=r(152),o=r(71),s=r(70)("IE_PROTO"),a=function(){},f=function(){var e,t=r(99)("iframe"),i=o.length;for(t.style.display="none",r(153).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),f=e.F;i--;)delete f.prototype[o[i]];return f()};e.exports=Object.create||function(e,t){var r;return null!==e?(a.prototype=i(e),r=new a,a.prototype=null,r[s]=e):r=f(),void 0===t?r:n(r,t)}},function(e,t,r){var i=r(102),n=r(71);e.exports=Object.keys||function(e){return i(e,n)}},function(e,t,r){var i=r(29),n=Math.max,o=Math.min;e.exports=function(e,t){return(e=i(e))<0?n(e+t,0):o(e,t)}},function(e,t,r){var i=r(13).f,n=r(24),o=r(4)("toStringTag");e.exports=function(e,t,r){e&&!n(e=r?e:e.prototype,o)&&i(e,o,{configurable:!0,value:t})}},function(e,t){e.exports=function(e){if(null===e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,r){var i=r(105),n=r(76);e.exports=Object.keys||function(e){return i(e,n)}},function(e,t){var r=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++r+i).toString(36))}},function(e,t,r){var i=r(60);e.exports=function(e,t,r){if(i(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,i){return e.call(t,r,i)};case 3:return function(r,i,n){return e.call(t,r,i,n)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t,r){var i=r(63),n=RegExp.prototype.exec;e.exports=function(e,t){var r=e.exec;if("function"==typeof r){var o=r.call(e,t);if("object"!=typeof o)throw new TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==i(e))throw new TypeError("RegExp#exec called on incompatible receiver");return n.call(e,t)}},function(e,t,r){var i=r(46),n=r(4)("toStringTag"),o="Arguments"==i(function(){return arguments}());e.exports=function(e){var t,r,s;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),n))?r:o?i(t):"Object"===(s=i(t))&&"function"==typeof t.callee?"Arguments":s}},function(e,t,r){var i=r(17);e.exports=function(){var e=i(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},function(e,t,r){r(163);var i=r(22),n=r(20),o=r(18),s=r(38),a=r(4),f=r(82),u=a("species"),h=!o((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),l=function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var r="ab".split(e);return 2===r.length&&"a"===r[0]&&"b"===r[1]}();e.exports=function(e,t,r){var c=a(e),d=!o((function(){var t={};return t[c]=function(){return 7},7!==""[e](t)})),p=d?!o((function(){var t=!1,r=/a/;return r.exec=function(){return t=!0,null},"split"===e&&(r.constructor={},r.constructor[u]=function(){return r}),r[c](""),!t})):void 0;if(!d||!p||"replace"===e&&!h||"split"===e&&!l){var x=/./[c],g=r(s,c,""[e],(function(e,t,r,i,n){return t.exec===f?d&&!n?{done:!0,value:x.call(t,r,i)}:{done:!0,value:e.call(r,t,i)}:{done:!1}})),v=g[0],y=g[1];i(String.prototype,e,v),n(RegExp.prototype,c,2===t?function(e,t){return y.call(e,this,t)}:function(e){return y.call(e,this)})}}},function(e,t,r){var i=r(26).f,n=r(31),o=r(14)("toStringTag");e.exports=function(e,t,r){e&&!n(e=r?e:e.prototype,o)&&i(e,o,{configurable:!0,value:t})}},function(e,t,r){var i=r(93),n=r(45),o=r(37),s=r(44),a=r(24),f=r(98),u=Object.getOwnPropertyDescriptor;t.f=r(11)?u:function(e,t){if(e=o(e),t=s(t,!0),f)try{return u(e,t)}catch(e){}if(a(e,t))return n(!i.f.call(e,t),e[t])}},function(e,t,r){e.exports=r(214)},function(e,t,r){},function(e,t,r){var i=r(50)("keys"),n=r(28);e.exports=function(e){return i[e]||(i[e]=n(e))}},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,r){var i=r(56);e.exports=function(e){return Object(i(e))}},function(e,t){var r=Math.ceil,i=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?i:r)(e)}},function(e,t,r){var i=r(75)("keys"),n=r(58);e.exports=function(e){return i[e]||(i[e]=n(e))}},function(e,t,r){var i=r(5),n=r(7),o=n["__core-js_shared__"]||(n["__core-js_shared__"]={});(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:i.version,mode:r(48)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,r){var i=r(33),n=r(7).document,o=i(n)&&i(n.createElement);e.exports=function(e){return o?n.createElement(e):{}}},function(e,t,r){},function(e,t,r){},function(e,t,r){var i=r(21),n=r(46),o=r(4)("match");e.exports=function(e){var t;return i(e)&&(void 0!==(t=e[o])?!!t:"RegExp"===n(e))}},function(e,t,r){var i=r(162)(!0);e.exports=function(e,t,r){return t+(r?i(e,t).length:1)}},function(e,t,r){var i,n,o=r(64),s=RegExp.prototype.exec,a=String.prototype.replace,f=s,u=(i=/a/,n=/b*/g,s.call(i,"a"),s.call(n,"a"),0!==i.lastIndex||0!==n.lastIndex),h=void 0!==/()??/.exec("")[1];(u||h)&&(f=function(e){var t,r,i,n,f=this;return h&&(r=new RegExp("^"+f.source+"$(?!\\s)",o.call(f))),u&&(t=f.lastIndex),i=s.call(f,e),u&&i&&(f.lastIndex=f.global?i.index+i[0].length:t),h&&i&&i.length>1&&a.call(i[0],r,(function(){for(n=1;n<arguments.length-2;n++)void 0===arguments[n]&&(i[n]=void 0)})),i}),e.exports=f},function(e,t,r){var i=r(27),n=r(109),o=r(76),s=r(74)("IE_PROTO"),a=function(){},f=function(){var e,t=r(77)("iframe"),i=o.length;for(t.style.display="none",r(110).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),f=e.F;i--;)delete f.prototype[o[i]];return f()};e.exports=Object.create||function(e,t){var r;return null!==e?(a.prototype=i(e),r=new a,a.prototype=null,r[s]=e):r=f(),void 0===t?r:n(r,t)}},function(e,t,r){var i=r(13).f,n=Function.prototype,o=/^\s*function ([^ (]*)/;"name"in n||r(11)&&i(n,"name",{configurable:!0,get:function(){try{return(""+this).match(o)[1]}catch(e){return""}}})},function(e,t,r){r(166);var i=r(17),n=r(64),o=r(11),s=/./.toString,a=function(e){r(22)(RegExp.prototype,"toString",e,!0)};r(18)((function(){return"/a/b"!=s.call({source:"a",flags:"b"})}))?a((function(){var e=i(this);return"/".concat(e.source,"/","flags"in e?e.flags:!o&&e instanceof RegExp?n.call(e):void 0)})):"toString"!==s.name&&a((function(){return s.call(this)}))},function(e,t,r){var i=r(63),n={};n[r(4)("toStringTag")]="z",n+""!="[object z]"&&r(22)(Object.prototype,"toString",(function(){return"[object "+i(this)+"]"}),!0)},function(e,t){},function(e,t,r){},function(e,t,r){t.f=r(14)},function(e,t,r){var i=r(7),n=r(5),o=r(48),s=r(89),a=r(26).f;e.exports=function(e){var t=n.Symbol||(n.Symbol=o?{}:i.Symbol||{});"_"===e.charAt(0)||e in t||a(t,e,{value:s.f(e)})}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,r){var i=r(30),n=r(54),o=r(19);e.exports=function(e){for(var t=i(this),r=o(t.length),s=arguments.length,a=n(s>1?arguments[1]:void 0,r),f=s>2?arguments[2]:void 0,u=void 0===f?r:n(f,r);u>a;)t[a++]=e;return t}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,r){var i=r(60);function n(e){var t,r;this.promise=new e((function(e,i){if(void 0!==t||void 0!==r)throw TypeError("Bad Promise constructor");t=e,r=i})),this.resolve=i(t),this.reject=i(r)}e.exports.f=function(e){return new n(e)}},function(e,t){},function(e,t){e.exports=function(e){if(!e.webpackPolyfill){var t=Object.create(e);t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),Object.defineProperty(t,"exports",{enumerable:!0}),t.webpackPolyfill=1}return t}},function(e,t,r){var i=r(4)("unscopables"),n=Array.prototype;null===n[i]&&r(20)(n,i,{}),e.exports=function(e){n[i][e]=!0}},function(e,t,r){e.exports=!r(11)&&!r(18)((function(){return 7!=Object.defineProperty(r(99)("div"),"a",{get:function(){return 7}}).a}))},function(e,t,r){var i=r(21),n=r(10).document,o=i(n)&&i(n.createElement);e.exports=function(e){return o?n.createElement(e):{}}},function(e,t,r){var i=r(46);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"===i(e)?e.split(""):Object(e)}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t,r){var i=r(24),n=r(37),o=r(103)(!1),s=r(70)("IE_PROTO");e.exports=function(e,t){var r,a=n(e),f=0,u=[];for(r in a)r!==s&&i(a,r)&&u.push(r);for(;t.length>f;)i(a,r=t[f++])&&(~o(u,r)||u.push(r));return u}},function(e,t,r){var i=r(37),n=r(19),o=r(54);e.exports=function(e){return function(t,r,s){var a,f=i(t),u=n(f.length),h=o(s,u);if(e&&r!=r){for(;u>h;)if((a=f[h++])!==a)return!0}else for(;u>h;h++)if((e||h in f)&&f[h]===r)return e||h||0;return!e&&-1}}},function(e,t,r){var i=r(24),n=r(30),o=r(70)("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=n(e),i(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},function(e,t,r){var i=r(31),n=r(39),o=r(157)(!1),s=r(74)("IE_PROTO");e.exports=function(e,t){var r,a=n(e),f=0,u=[];for(r in a)r!==s&&i(a,r)&&u.push(r);for(;t.length>f;)i(a,r=t[f++])&&(~o(u,r)||u.push(r));return u}},function(e,t,r){var i=r(73),n=Math.min;e.exports=function(e){return e>0?n(i(e),9007199254740991):0}},function(e,t,r){e.exports=!r(25)&&!r(40)((function(){return 7!=Object.defineProperty(r(77)("div"),"a",{get:function(){return 7}}).a}))},function(e,t,r){var i=r(17),n=r(101),o=r(4)("species");e.exports=function(e,t){var r,s=i(e).constructor;return void 0===s||null===(r=i(s)[o])?t:n(r)}},function(e,t,r){var i=r(26),n=r(27),o=r(57);e.exports=r(25)?Object.defineProperties:function(e,t){n(e);for(var r,s=o(t),a=s.length,f=0;a>f;)i.f(e,r=s[f++],t[r]);return e}},function(e,t,r){var i=r(7).document;e.exports=i&&i.documentElement},function(e,t,r){var i=r(47);e.exports=Array.isArray||function(e){return"Array"===i(e)}},function(e,t,r){var i=r(12),n=r(56),o=r(40),s=r(87),a="["+s+"]",f=RegExp("^"+a+a+"*"),u=RegExp(a+a+"*$"),h=function(e,t,r){var n={},a=o((function(){return!!s[e]()||"​"!=="​"[e]()})),f=n[e]=a?t(l):s[e];r&&(n[r]=f),i(i.P+i.F*a,"String",n)},l=h.trim=function(e,t){return e=String(n(e)),1&t&&(e=e.replace(f,"")),2&t&&(e=e.replace(u,"")),e};e.exports=h},function(e,t,r){var i=r(177)(!0);r(114)(String,"String",(function(e){this._t=String(e),this._i=0}),(function(){var e,t=this._t,r=this._i;return r>=t.length?{value:void 0,done:!0}:(e=i(t,r),this._i+=e.length,{value:e,done:!1})}))},function(e,t,r){var i=r(48),n=r(12),o=r(115),s=r(32),a=r(49),f=r(178),u=r(66),h=r(179),l=r(14)("iterator"),c=!([].keys&&"next"in[].keys()),d=function(){return this};e.exports=function(e,t,r,p,x,g,v){f(r,t,p);var y,b,m,w=function(e){if(!c&&e in I)return I[e];switch(e){case"keys":case"values":return function(){return new r(this,e)}}return function(){return new r(this,e)}},E=t+" Iterator",T="values"===x,S=!1,I=e.prototype,A=I[l]||I["@@iterator"]||x&&I[x],_=A||w(x),P=x?T?w("entries"):_:void 0,O="Array"===t&&I.entries||A;if(O&&(m=h(O.call(new e)))!==Object.prototype&&m.next&&(u(m,E,!0),i||"function"==typeof m[l]||s(m,l,d)),T&&A&&"values"!==A.name&&(S=!0,_=function(){return A.call(this)}),i&&!v||!c&&!S&&I[l]||s(I,l,_),a[t]=_,a[E]=d,x)if(y={values:T?_:w("values"),keys:g?_:w("keys"),entries:P},v)for(b in y)b in I||o(I,b,y[b]);else n(n.P+n.F*(c||S),t,y);return y}},function(e,t,r){e.exports=r(32)},function(e,t,r){},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,r){var i=r(105),n=r(76).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return i(e,n)}},function(e,t){},function(e,t,r){var i,n,o
/**
       * @license bytebuffer.js (c) 2015 Daniel Wirtz <<EMAIL>>
       * Backing buffer: ArrayBuffer, Accessor: Uint8Array
       * Released under the Apache License, Version 2.0
       * see: https://github.com/dcodeIO/bytebuffer.js for details
       */;n=[r(191)],void 0===(o="function"==typeof(i=function(e){var t=function(e,r,n){if(void 0===e&&(e=t.DEFAULT_CAPACITY),void 0===r&&(r=t.DEFAULT_ENDIAN),void 0===n&&(n=t.DEFAULT_NOASSERT),!n){if((e|=0)<0)throw RangeError("Illegal capacity");r=!!r,n=!!n}this.buffer=0===e?i:new ArrayBuffer(e),this.view=0===e?null:new Uint8Array(this.buffer),this.offset=0,this.markedOffset=-1,this.limit=e,this.littleEndian=r,this.noAssert=n};t.VERSION="5.0.1",t.LITTLE_ENDIAN=!0,t.BIG_ENDIAN=!1,t.DEFAULT_CAPACITY=16,t.DEFAULT_ENDIAN=t.BIG_ENDIAN,t.DEFAULT_NOASSERT=!1,t.Long=e||null;var r=t.prototype;r.__isByteBuffer__,Object.defineProperty(r,"__isByteBuffer__",{value:!0,enumerable:!1,configurable:!1});var i=new ArrayBuffer(0),n=String.fromCharCode;function o(e){var t=0;return function(){return t<e.length?e.charCodeAt(t++):null}}function s(){var e=[],t=[];return function(){if(0===arguments.length)return t.join("")+n.apply(String,e);e.length+arguments.length>1024&&(t.push(n.apply(String,e)),e.length=0),Array.prototype.push.apply(e,arguments)}}function a(e,t,r,i,n){var o,s,a=8*n-i-1,f=(1<<a)-1,u=f>>1,h=-7,l=r?n-1:0,c=r?-1:1,d=e[t+l];for(l+=c,o=d&(1<<-h)-1,d>>=-h,h+=a;h>0;o=256*o+e[t+l],l+=c,h-=8);for(s=o&(1<<-h)-1,o>>=-h,h+=i;h>0;s=256*s+e[t+l],l+=c,h-=8);if(0===o)o=1-u;else{if(o===f)return s?NaN:1/0*(d?-1:1);s+=Math.pow(2,i),o-=u}return(d?-1:1)*s*Math.pow(2,o-i)}function f(e,t,r,i,n,o){var s,a,f,u=8*o-n-1,h=(1<<u)-1,l=h>>1,c=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,d=i?0:o-1,p=i?1:-1,x=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=h):(s=Math.floor(Math.log(t)/Math.LN2),t*(f=Math.pow(2,-s))<1&&(s--,f*=2),(t+=s+l>=1?c/f:c*Math.pow(2,1-l))*f>=2&&(s++,f/=2),s+l>=h?(a=0,s=h):s+l>=1?(a=(t*f-1)*Math.pow(2,n),s+=l):(a=t*Math.pow(2,l-1)*Math.pow(2,n),s=0));n>=8;e[r+d]=255&a,d+=p,a/=256,n-=8);for(s=s<<n|a,u+=n;u>0;e[r+d]=255&s,d+=p,s/=256,u-=8);e[r+d-p]|=128*x}t.accessor=function(){return Uint8Array},t.allocate=function(e,r,i){return new t(e,r,i)},t.concat=function(e,r,i,n){"boolean"!=typeof r&&"string"==typeof r||(n=i,i=r,r=void 0);for(var o,s=0,a=0,f=e.length;a<f;++a)t.isByteBuffer(e[a])||(e[a]=t.wrap(e[a],r)),(o=e[a].limit-e[a].offset)>0&&(s+=o);if(0===s)return new t(0,i,n);var u,h=new t(s,i,n);for(a=0;a<f;)(o=(u=e[a++]).limit-u.offset)<=0||(h.view.set(u.view.subarray(u.offset,u.limit),h.offset),h.offset+=o);return h.limit=h.offset,h.offset=0,h},t.isByteBuffer=function(e){return!0===(e&&e.__isByteBuffer__)},t.type=function(){return ArrayBuffer},t.wrap=function(e,i,n,o){if("string"!=typeof i&&(o=n,n=i,i=void 0),"string"==typeof e)switch(void 0===i&&(i="utf8"),i){case"base64":return t.fromBase64(e,n);case"hex":return t.fromHex(e,n);case"binary":return t.fromBinary(e,n);case"utf8":return t.fromUTF8(e,n);case"debug":return t.fromDebug(e,n);default:throw Error("Unsupported encoding: "+i)}if(null===e||"object"!=typeof e)throw TypeError("Illegal buffer");var s;if(t.isByteBuffer(e))return(s=r.clone.call(e)).markedOffset=-1,s;if(e instanceof Uint8Array)s=new t(0,n,o),e.length>0&&(s.buffer=e.buffer,s.offset=e.byteOffset,s.limit=e.byteOffset+e.byteLength,s.view=new Uint8Array(e.buffer));else if(e instanceof ArrayBuffer)s=new t(0,n,o),e.byteLength>0&&(s.buffer=e,s.offset=0,s.limit=e.byteLength,s.view=e.byteLength>0?new Uint8Array(e):null);else{if("[object Array]"!==Object.prototype.toString.call(e))throw TypeError("Illegal buffer");(s=new t(e.length,n,o)).limit=e.length;for(var a=0;a<e.length;++a)s.view[a]=e[a]}return s},r.writeBitSet=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if(!(e instanceof Array))throw TypeError("Illegal BitSet: Not an array");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var i,n=t,o=e.length,s=o>>3,a=0;for(t+=this.writeVarint32(o,t);s--;)i=1&!!e[a++]|(1&!!e[a++])<<1|(1&!!e[a++])<<2|(1&!!e[a++])<<3|(1&!!e[a++])<<4|(1&!!e[a++])<<5|(1&!!e[a++])<<6|(1&!!e[a++])<<7,this.writeByte(i,t++);if(a<o){var f=0;for(i=0;a<o;)i|=(1&!!e[a++])<<f++;this.writeByte(i,t++)}return r?(this.offset=t,this):t-n},r.readBitSet=function(e){var t=void 0===e;t&&(e=this.offset);var r,i=this.readVarint32(e),n=i.value,o=n>>3,s=0,a=[];for(e+=i.length;o--;)r=this.readByte(e++),a[s++]=!!(1&r),a[s++]=!!(2&r),a[s++]=!!(4&r),a[s++]=!!(8&r),a[s++]=!!(16&r),a[s++]=!!(32&r),a[s++]=!!(64&r),a[s++]=!!(128&r);if(s<n){var f=0;for(r=this.readByte(e++);s<n;)a[s++]=!!(r>>f++&1)}return t&&(this.offset=e),a},r.readBytes=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+e>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+"+e+") <= "+this.buffer.byteLength)}var i=this.slice(t,t+e);return r&&(this.offset+=e),i},r.writeBytes=r.append,r.writeInt8=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=1;var i=this.buffer.byteLength;return t>i&&this.resize((i*=2)>t?i:t),t-=1,this.view[t]=e,r&&(this.offset+=1),this},r.writeByte=r.writeInt8,r.readInt8=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r=this.view[e];return 128==(128&r)&&(r=-(255-r+1)),t&&(this.offset+=1),r},r.readByte=r.readInt8,r.writeUint8=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=1;var i=this.buffer.byteLength;return t>i&&this.resize((i*=2)>t?i:t),t-=1,this.view[t]=e,r&&(this.offset+=1),this},r.writeUInt8=r.writeUint8,r.readUint8=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r=this.view[e];return t&&(this.offset+=1),r},r.readUInt8=r.readUint8,r.writeInt16=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=2;var i=this.buffer.byteLength;return t>i&&this.resize((i*=2)>t?i:t),t-=2,this.littleEndian?(this.view[t+1]=(65280&e)>>>8,this.view[t]=255&e):(this.view[t]=(65280&e)>>>8,this.view[t+1]=255&e),r&&(this.offset+=2),this},r.writeShort=r.writeInt16,r.readInt16=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+2>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+2) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e],r|=this.view[e+1]<<8):(r=this.view[e]<<8,r|=this.view[e+1]),32768==(32768&r)&&(r=-(65535-r+1)),t&&(this.offset+=2),r},r.readShort=r.readInt16,r.writeUint16=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=2;var i=this.buffer.byteLength;return t>i&&this.resize((i*=2)>t?i:t),t-=2,this.littleEndian?(this.view[t+1]=(65280&e)>>>8,this.view[t]=255&e):(this.view[t]=(65280&e)>>>8,this.view[t+1]=255&e),r&&(this.offset+=2),this},r.writeUInt16=r.writeUint16,r.readUint16=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+2>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+2) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e],r|=this.view[e+1]<<8):(r=this.view[e]<<8,r|=this.view[e+1]),t&&(this.offset+=2),r},r.readUInt16=r.readUint16,r.writeInt32=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var i=this.buffer.byteLength;return t>i&&this.resize((i*=2)>t?i:t),t-=4,this.littleEndian?(this.view[t+3]=e>>>24&255,this.view[t+2]=e>>>16&255,this.view[t+1]=e>>>8&255,this.view[t]=255&e):(this.view[t]=e>>>24&255,this.view[t+1]=e>>>16&255,this.view[t+2]=e>>>8&255,this.view[t+3]=255&e),r&&(this.offset+=4),this},r.writeInt=r.writeInt32,r.readInt32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e+2]<<16,r|=this.view[e+1]<<8,r|=this.view[e],r+=this.view[e+3]<<24>>>0):(r=this.view[e+1]<<16,r|=this.view[e+2]<<8,r|=this.view[e+3],r+=this.view[e]<<24>>>0),r|=0,t&&(this.offset+=4),r},r.readInt=r.readInt32,r.writeUint32=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var i=this.buffer.byteLength;return t>i&&this.resize((i*=2)>t?i:t),t-=4,this.littleEndian?(this.view[t+3]=e>>>24&255,this.view[t+2]=e>>>16&255,this.view[t+1]=e>>>8&255,this.view[t]=255&e):(this.view[t]=e>>>24&255,this.view[t+1]=e>>>16&255,this.view[t+2]=e>>>8&255,this.view[t+3]=255&e),r&&(this.offset+=4),this},r.writeUInt32=r.writeUint32,r.readUint32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e+2]<<16,r|=this.view[e+1]<<8,r|=this.view[e],r+=this.view[e+3]<<24>>>0):(r=this.view[e+1]<<16,r|=this.view[e+2]<<8,r|=this.view[e+3],r+=this.view[e]<<24>>>0),t&&(this.offset+=4),r},r.readUInt32=r.readUint32,e&&(r.writeInt64=function(t,r){var i=void 0===r;if(i&&(r=this.offset),!this.noAssert){if("number"==typeof t)t=e.fromNumber(t);else if("string"==typeof t)t=e.fromString(t);else if(!(t&&t instanceof e))throw TypeError("Illegal value: "+t+" (not an integer or Long)");if("number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}"number"==typeof t?t=e.fromNumber(t):"string"==typeof t&&(t=e.fromString(t)),r+=8;var n=this.buffer.byteLength;r>n&&this.resize((n*=2)>r?n:r),r-=8;var o=t.low,s=t.high;return this.littleEndian?(this.view[r+3]=o>>>24&255,this.view[r+2]=o>>>16&255,this.view[r+1]=o>>>8&255,this.view[r]=255&o,r+=4,this.view[r+3]=s>>>24&255,this.view[r+2]=s>>>16&255,this.view[r+1]=s>>>8&255,this.view[r]=255&s):(this.view[r]=s>>>24&255,this.view[r+1]=s>>>16&255,this.view[r+2]=s>>>8&255,this.view[r+3]=255&s,r+=4,this.view[r]=o>>>24&255,this.view[r+1]=o>>>16&255,this.view[r+2]=o>>>8&255,this.view[r+3]=255&o),i&&(this.offset+=8),this},r.writeLong=r.writeInt64,r.readInt64=function(t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+8) <= "+this.buffer.byteLength)}var i=0,n=0;this.littleEndian?(i=this.view[t+2]<<16,i|=this.view[t+1]<<8,i|=this.view[t],i+=this.view[t+3]<<24>>>0,t+=4,n=this.view[t+2]<<16,n|=this.view[t+1]<<8,n|=this.view[t],n+=this.view[t+3]<<24>>>0):(n=this.view[t+1]<<16,n|=this.view[t+2]<<8,n|=this.view[t+3],n+=this.view[t]<<24>>>0,t+=4,i=this.view[t+1]<<16,i|=this.view[t+2]<<8,i|=this.view[t+3],i+=this.view[t]<<24>>>0);var o=new e(i,n,!1);return r&&(this.offset+=8),o},r.readLong=r.readInt64,r.writeUint64=function(t,r){var i=void 0===r;if(i&&(r=this.offset),!this.noAssert){if("number"==typeof t)t=e.fromNumber(t);else if("string"==typeof t)t=e.fromString(t);else if(!(t&&t instanceof e))throw TypeError("Illegal value: "+t+" (not an integer or Long)");if("number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}"number"==typeof t?t=e.fromNumber(t):"string"==typeof t&&(t=e.fromString(t)),r+=8;var n=this.buffer.byteLength;r>n&&this.resize((n*=2)>r?n:r),r-=8;var o=t.low,s=t.high;return this.littleEndian?(this.view[r+3]=o>>>24&255,this.view[r+2]=o>>>16&255,this.view[r+1]=o>>>8&255,this.view[r]=255&o,r+=4,this.view[r+3]=s>>>24&255,this.view[r+2]=s>>>16&255,this.view[r+1]=s>>>8&255,this.view[r]=255&s):(this.view[r]=s>>>24&255,this.view[r+1]=s>>>16&255,this.view[r+2]=s>>>8&255,this.view[r+3]=255&s,r+=4,this.view[r]=o>>>24&255,this.view[r+1]=o>>>16&255,this.view[r+2]=o>>>8&255,this.view[r+3]=255&o),i&&(this.offset+=8),this},r.writeUInt64=r.writeUint64,r.readUint64=function(t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+8) <= "+this.buffer.byteLength)}var i=0,n=0;this.littleEndian?(i=this.view[t+2]<<16,i|=this.view[t+1]<<8,i|=this.view[t],i+=this.view[t+3]<<24>>>0,t+=4,n=this.view[t+2]<<16,n|=this.view[t+1]<<8,n|=this.view[t],n+=this.view[t+3]<<24>>>0):(n=this.view[t+1]<<16,n|=this.view[t+2]<<8,n|=this.view[t+3],n+=this.view[t]<<24>>>0,t+=4,i=this.view[t+1]<<16,i|=this.view[t+2]<<8,i|=this.view[t+3],i+=this.view[t]<<24>>>0);var o=new e(i,n,!0);return r&&(this.offset+=8),o},r.readUInt64=r.readUint64),r.writeFloat32=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e)throw TypeError("Illegal value: "+e+" (not a number)");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var i=this.buffer.byteLength;return t>i&&this.resize((i*=2)>t?i:t),t-=4,f(this.view,e,t,this.littleEndian,23,4),r&&(this.offset+=4),this},r.writeFloat=r.writeFloat32,r.readFloat32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=a(this.view,e,this.littleEndian,23,4);return t&&(this.offset+=4),r},r.readFloat=r.readFloat32,r.writeFloat64=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e)throw TypeError("Illegal value: "+e+" (not a number)");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=8;var i=this.buffer.byteLength;return t>i&&this.resize((i*=2)>t?i:t),t-=8,f(this.view,e,t,this.littleEndian,52,8),r&&(this.offset+=8),this},r.writeDouble=r.writeFloat64,r.readFloat64=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+8) <= "+this.buffer.byteLength)}var r=a(this.view,e,this.littleEndian,52,8);return t&&(this.offset+=8),r},r.readDouble=r.readFloat64,t.MAX_VARINT32_BYTES=5,t.calculateVarint32=function(e){return(e>>>=0)<128?1:e<16384?2:e<1<<21?3:e<1<<28?4:5},t.zigZagEncode32=function(e){return((e|=0)<<1^e>>31)>>>0},t.zigZagDecode32=function(e){return e>>>1^-(1&e)|0},r.writeVarint32=function(e,r){var i=void 0===r;if(i&&(r=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}var n,o=t.calculateVarint32(e);r+=o;var s=this.buffer.byteLength;for(r>s&&this.resize((s*=2)>r?s:r),r-=o,e>>>=0;e>=128;)n=127&e|128,this.view[r++]=n,e>>>=7;return this.view[r++]=e,i?(this.offset=r,this):o},r.writeVarint32ZigZag=function(e,r){return this.writeVarint32(t.zigZagEncode32(e),r)},r.readVarint32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r,i=0,n=0;do{if(!this.noAssert&&e>this.limit){var o=Error("Truncated");throw o.truncated=!0,o}r=this.view[e++],i<5&&(n|=(127&r)<<7*i),++i}while(0!=(128&r));return n|=0,t?(this.offset=e,n):{value:n,length:i}},r.readVarint32ZigZag=function(e){var r=this.readVarint32(e);return"object"==typeof r?r.value=t.zigZagDecode32(r.value):r=t.zigZagDecode32(r),r},e&&(t.MAX_VARINT64_BYTES=10,t.calculateVarint64=function(t){"number"==typeof t?t=e.fromNumber(t):"string"==typeof t&&(t=e.fromString(t));var r=t.toInt()>>>0,i=t.shiftRightUnsigned(28).toInt()>>>0,n=t.shiftRightUnsigned(56).toInt()>>>0;return 0===n?0===i?r<16384?r<128?1:2:r<1<<21?3:4:i<16384?i<128?5:6:i<1<<21?7:8:n<128?9:10},t.zigZagEncode64=function(t){return"number"==typeof t?t=e.fromNumber(t,!1):"string"==typeof t?t=e.fromString(t,!1):!1!==t.unsigned&&(t=t.toSigned()),t.shiftLeft(1).xor(t.shiftRight(63)).toUnsigned()},t.zigZagDecode64=function(t){return"number"==typeof t?t=e.fromNumber(t,!1):"string"==typeof t?t=e.fromString(t,!1):!1!==t.unsigned&&(t=t.toSigned()),t.shiftRightUnsigned(1).xor(t.and(e.ONE).toSigned().negate()).toSigned()},r.writeVarint64=function(r,i){var n=void 0===i;if(n&&(i=this.offset),!this.noAssert){if("number"==typeof r)r=e.fromNumber(r);else if("string"==typeof r)r=e.fromString(r);else if(!(r&&r instanceof e))throw TypeError("Illegal value: "+r+" (not an integer or Long)");if("number"!=typeof i||i%1!=0)throw TypeError("Illegal offset: "+i+" (not an integer)");if((i>>>=0)<0||i+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+i+" (+0) <= "+this.buffer.byteLength)}"number"==typeof r?r=e.fromNumber(r,!1):"string"==typeof r?r=e.fromString(r,!1):!1!==r.unsigned&&(r=r.toSigned());var o=t.calculateVarint64(r),s=r.toInt()>>>0,a=r.shiftRightUnsigned(28).toInt()>>>0,f=r.shiftRightUnsigned(56).toInt()>>>0;i+=o;var u=this.buffer.byteLength;switch(i>u&&this.resize((u*=2)>i?u:i),i-=o,o){case 10:this.view[i+9]=f>>>7&1;case 9:this.view[i+8]=9!==o?128|f:127&f;case 8:this.view[i+7]=8!==o?a>>>21|128:a>>>21&127;case 7:this.view[i+6]=7!==o?a>>>14|128:a>>>14&127;case 6:this.view[i+5]=6!==o?a>>>7|128:a>>>7&127;case 5:this.view[i+4]=5!==o?128|a:127&a;case 4:this.view[i+3]=4!==o?s>>>21|128:s>>>21&127;case 3:this.view[i+2]=3!==o?s>>>14|128:s>>>14&127;case 2:this.view[i+1]=2!==o?s>>>7|128:s>>>7&127;case 1:this.view[i]=1!==o?128|s:127&s}return n?(this.offset+=o,this):o},r.writeVarint64ZigZag=function(e,r){return this.writeVarint64(t.zigZagEncode64(e),r)},r.readVarint64=function(t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+1) <= "+this.buffer.byteLength)}var i=t,n=0,o=0,s=0,a=0;if(n=127&(a=this.view[t++]),128&a&&(n|=(127&(a=this.view[t++]))<<7,(128&a||this.noAssert&&void 0===a)&&(n|=(127&(a=this.view[t++]))<<14,(128&a||this.noAssert&&void 0===a)&&(n|=(127&(a=this.view[t++]))<<21,(128&a||this.noAssert&&void 0===a)&&(o=127&(a=this.view[t++]),(128&a||this.noAssert&&void 0===a)&&(o|=(127&(a=this.view[t++]))<<7,(128&a||this.noAssert&&void 0===a)&&(o|=(127&(a=this.view[t++]))<<14,(128&a||this.noAssert&&void 0===a)&&(o|=(127&(a=this.view[t++]))<<21,(128&a||this.noAssert&&void 0===a)&&(s=127&(a=this.view[t++]),(128&a||this.noAssert&&void 0===a)&&(s|=(127&(a=this.view[t++]))<<7,128&a||this.noAssert&&void 0===a))))))))))throw Error("Buffer overrun");var f=e.fromBits(n|o<<28,o>>>4|s<<24,!1);return r?(this.offset=t,f):{value:f,length:t-i}},r.readVarint64ZigZag=function(r){var i=this.readVarint64(r);return i&&i.value instanceof e?i.value=t.zigZagDecode64(i.value):i=t.zigZagDecode64(i),i}),r.writeCString=function(e,t){var r=void 0===t;r&&(t=this.offset);var i,n=e.length;if(!this.noAssert){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");for(i=0;i<n;++i)if(0===e.charCodeAt(i))throw RangeError("Illegal str: Contains NULL-characters");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}n=h.calculateUTF16asUTF8(o(e))[1],t+=n+1;var s=this.buffer.byteLength;return t>s&&this.resize((s*=2)>t?s:t),t-=n+1,h.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),this.view[t++]=0,r?(this.offset=t,this):n},r.readCString=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r,i=e,n=-1;return h.decodeUTF8toUTF16(function(){if(0===n)return null;if(e>=this.limit)throw RangeError("Illegal range: Truncated data, "+e+" < "+this.limit);return 0===(n=this.view[e++])?null:n}.bind(this),r=s(),!0),t?(this.offset=e,r()):{string:r(),length:e-i}},r.writeIString=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var i,n=t;i=h.calculateUTF16asUTF8(o(e),this.noAssert)[1],t+=4+i;var s=this.buffer.byteLength;if(t>s&&this.resize((s*=2)>t?s:t),t-=4+i,this.littleEndian?(this.view[t+3]=i>>>24&255,this.view[t+2]=i>>>16&255,this.view[t+1]=i>>>8&255,this.view[t]=255&i):(this.view[t]=i>>>24&255,this.view[t+1]=i>>>16&255,this.view[t+2]=i>>>8&255,this.view[t+3]=255&i),t+=4,h.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),t!==n+4+i)throw RangeError("Illegal range: Truncated data, "+t+" === "+(t+4+i));return r?(this.offset=t,this):t-n},r.readIString=function(e){var r=void 0===e;if(r&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var i=e,n=this.readUint32(e),o=this.readUTF8String(n,t.METRICS_BYTES,e+=4);return e+=o.length,r?(this.offset=e,o.string):{string:o.string,length:e-i}},t.METRICS_CHARS="c",t.METRICS_BYTES="b",r.writeUTF8String=function(e,t){var r,i=void 0===t;if(i&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var n=t;r=h.calculateUTF16asUTF8(o(e))[1],t+=r;var s=this.buffer.byteLength;return t>s&&this.resize((s*=2)>t?s:t),t-=r,h.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),i?(this.offset=t,this):t-n},r.writeString=r.writeUTF8String,t.calculateUTF8Chars=function(e){return h.calculateUTF16asUTF8(o(e))[0]},t.calculateUTF8Bytes=function(e){return h.calculateUTF16asUTF8(o(e))[1]},t.calculateString=t.calculateUTF8Bytes,r.readUTF8String=function(e,r,i){"number"==typeof r&&(i=r,r=void 0);var n=void 0===i;if(n&&(i=this.offset),void 0===r&&(r=t.METRICS_CHARS),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal length: "+e+" (not an integer)");if(e|=0,"number"!=typeof i||i%1!=0)throw TypeError("Illegal offset: "+i+" (not an integer)");if((i>>>=0)<0||i+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+i+" (+0) <= "+this.buffer.byteLength)}var o,a=0,f=i;if(r===t.METRICS_CHARS){if(o=s(),h.decodeUTF8(function(){return a<e&&i<this.limit?this.view[i++]:null}.bind(this),(function(e){++a,h.UTF8toUTF16(e,o)})),a!==e)throw RangeError("Illegal range: Truncated data, "+a+" === "+e);return n?(this.offset=i,o()):{string:o(),length:i-f}}if(r===t.METRICS_BYTES){if(!this.noAssert){if("number"!=typeof i||i%1!=0)throw TypeError("Illegal offset: "+i+" (not an integer)");if((i>>>=0)<0||i+e>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+i+" (+"+e+") <= "+this.buffer.byteLength)}var u=i+e;if(h.decodeUTF8toUTF16(function(){return i<u?this.view[i++]:null}.bind(this),o=s(),this.noAssert),i!==u)throw RangeError("Illegal range: Truncated data, "+i+" === "+u);return n?(this.offset=i,o()):{string:o(),length:i-f}}throw TypeError("Unsupported metrics: "+r)},r.readString=r.readUTF8String,r.writeVString=function(e,r){var i=void 0===r;if(i&&(r=this.offset),!this.noAssert){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");if("number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}var n,s,a=r;n=h.calculateUTF16asUTF8(o(e),this.noAssert)[1],s=t.calculateVarint32(n),r+=s+n;var f=this.buffer.byteLength;if(r>f&&this.resize((f*=2)>r?f:r),r-=s+n,r+=this.writeVarint32(n,r),h.encodeUTF16toUTF8(o(e),function(e){this.view[r++]=e}.bind(this)),r!==a+n+s)throw RangeError("Illegal range: Truncated data, "+r+" === "+(r+n+s));return i?(this.offset=r,this):r-a},r.readVString=function(e){var r=void 0===e;if(r&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var i=e,n=this.readVarint32(e),o=this.readUTF8String(n.value,t.METRICS_BYTES,e+=n.length);return e+=o.length,r?(this.offset=e,o.string):{string:o.string,length:e-i}},r.append=function(e,r,i){"number"!=typeof r&&"string"==typeof r||(i=r,r=void 0);var n=void 0===i;if(n&&(i=this.offset),!this.noAssert){if("number"!=typeof i||i%1!=0)throw TypeError("Illegal offset: "+i+" (not an integer)");if((i>>>=0)<0||i+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+i+" (+0) <= "+this.buffer.byteLength)}e instanceof t||(e=t.wrap(e,r));var o=e.limit-e.offset;if(o<=0)return this;i+=o;var s=this.buffer.byteLength;return i>s&&this.resize((s*=2)>i?s:i),i-=o,this.view.set(e.view.subarray(e.offset,e.limit),i),e.offset+=o,n&&(this.offset+=o),this},r.appendTo=function(e,t){return e.append(this,t),this},r.assert=function(e){return this.noAssert=!e,this},r.capacity=function(){return this.buffer.byteLength},r.clear=function(){return this.offset=0,this.limit=this.buffer.byteLength,this.markedOffset=-1,this},r.clone=function(e){var r=new t(0,this.littleEndian,this.noAssert);return e?(r.buffer=new ArrayBuffer(this.buffer.byteLength),r.view=new Uint8Array(r.buffer)):(r.buffer=this.buffer,r.view=this.view),r.offset=this.offset,r.markedOffset=this.markedOffset,r.limit=this.limit,r},r.compact=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}if(0===e&&t===this.buffer.byteLength)return this;var r=t-e;if(0===r)return this.buffer=i,this.view=null,this.markedOffset>=0&&(this.markedOffset-=e),this.offset=0,this.limit=0,this;var n=new ArrayBuffer(r),o=new Uint8Array(n);return o.set(this.view.subarray(e,t)),this.buffer=n,this.view=o,this.markedOffset>=0&&(this.markedOffset-=e),this.offset=0,this.limit=r,this},r.copy=function(e,r){if(void 0===e&&(e=this.offset),void 0===r&&(r=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal end: Not an integer");if(r>>>=0,e<0||e>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+r+" <= "+this.buffer.byteLength)}if(e===r)return new t(0,this.littleEndian,this.noAssert);var i=r-e,n=new t(i,this.littleEndian,this.noAssert);return n.offset=0,n.limit=i,n.markedOffset>=0&&(n.markedOffset-=e),this.copyTo(n,0,e,r),n},r.copyTo=function(e,r,i,n){var o,s;if(!this.noAssert&&!t.isByteBuffer(e))throw TypeError("Illegal target: Not a ByteBuffer");if(r=(s=void 0===r)?e.offset:0|r,i=(o=void 0===i)?this.offset:0|i,n=void 0===n?this.limit:0|n,r<0||r>e.buffer.byteLength)throw RangeError("Illegal target range: 0 <= "+r+" <= "+e.buffer.byteLength);if(i<0||n>this.buffer.byteLength)throw RangeError("Illegal source range: 0 <= "+i+" <= "+this.buffer.byteLength);var a=n-i;return 0===a?e:(e.ensureCapacity(r+a),e.view.set(this.view.subarray(i,n),r),o&&(this.offset+=a),s&&(e.offset+=a),this)},r.ensureCapacity=function(e){var t=this.buffer.byteLength;return t<e?this.resize((t*=2)>e?t:e):this},r.fill=function(e,t,r){var i=void 0===t;if(i&&(t=this.offset),"string"==typeof e&&e.length>0&&(e=e.charCodeAt(0)),void 0===t&&(t=this.offset),void 0===r&&(r=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal begin: Not an integer");if(t>>>=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal end: Not an integer");if(r>>>=0,t<0||t>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+t+" <= "+r+" <= "+this.buffer.byteLength)}if(t>=r)return this;for(;t<r;)this.view[t++]=e;return i&&(this.offset=t),this},r.flip=function(){return this.limit=this.offset,this.offset=0,this},r.mark=function(e){if(e=void 0===e?this.offset:e,!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+0) <= "+this.buffer.byteLength)}return this.markedOffset=e,this},r.order=function(e){if(!this.noAssert&&"boolean"!=typeof e)throw TypeError("Illegal littleEndian: Not a boolean");return this.littleEndian=!!e,this},r.LE=function(e){return this.littleEndian=void 0===e||!!e,this},r.BE=function(e){return this.littleEndian=void 0!==e&&!e,this},r.prepend=function(e,r,i){"number"!=typeof r&&"string"==typeof r||(i=r,r=void 0);var n=void 0===i;if(n&&(i=this.offset),!this.noAssert){if("number"!=typeof i||i%1!=0)throw TypeError("Illegal offset: "+i+" (not an integer)");if((i>>>=0)<0||i+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+i+" (+0) <= "+this.buffer.byteLength)}e instanceof t||(e=t.wrap(e,r));var o=e.limit-e.offset;if(o<=0)return this;var s=o-i;if(s>0){var a=new ArrayBuffer(this.buffer.byteLength+s),f=new Uint8Array(a);f.set(this.view.subarray(i,this.buffer.byteLength),o),this.buffer=a,this.view=f,this.offset+=s,this.markedOffset>=0&&(this.markedOffset+=s),this.limit+=s,i+=s}else new Uint8Array(this.buffer);return this.view.set(e.view.subarray(e.offset,e.limit),i-o),e.offset=e.limit,n&&(this.offset-=o),this},r.prependTo=function(e,t){return e.prepend(this,t),this},r.printDebug=function(e){"function"!=typeof e&&(e=console.log.bind(console)),e(this.toString()+"\n-------------------------------------------------------------------\n"+this.toDebug(!0))},r.remaining=function(){return this.limit-this.offset},r.reset=function(){return this.markedOffset>=0?(this.offset=this.markedOffset,this.markedOffset=-1):this.offset=0,this},r.resize=function(e){if(!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal capacity: "+e+" (not an integer)");if((e|=0)<0)throw RangeError("Illegal capacity: 0 <= "+e)}if(this.buffer.byteLength<e){var t=new ArrayBuffer(e),r=new Uint8Array(t);r.set(this.view),this.buffer=t,this.view=r}return this},r.reverse=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}return e===t||Array.prototype.reverse.call(this.view.subarray(e,t)),this},r.skip=function(e){if(!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal length: "+e+" (not an integer)");e|=0}var t=this.offset+e;if(!this.noAssert&&(t<0||t>this.buffer.byteLength))throw RangeError("Illegal length: 0 <= "+this.offset+" + "+e+" <= "+this.buffer.byteLength);return this.offset=t,this},r.slice=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}var r=this.clone();return r.offset=e,r.limit=t,r},r.toBuffer=function(e){var t=this.offset,r=this.limit;if(!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: Not an integer");if(t>>>=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal limit: Not an integer");if(r>>>=0,t<0||t>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+t+" <= "+r+" <= "+this.buffer.byteLength)}if(!e&&0===t&&r===this.buffer.byteLength)return this.buffer;if(t===r)return i;var n=new ArrayBuffer(r-t);return new Uint8Array(n).set(new Uint8Array(this.buffer).subarray(t,r),0),n},r.toArrayBuffer=r.toBuffer,r.toString=function(e,t,r){if(void 0===e)return"ByteBufferAB(offset="+this.offset+",markedOffset="+this.markedOffset+",limit="+this.limit+",capacity="+this.capacity()+")";switch("number"==typeof e&&(r=t=e="utf8"),e){case"utf8":return this.toUTF8(t,r);case"base64":return this.toBase64(t,r);case"hex":return this.toHex(t,r);case"binary":return this.toBinary(t,r);case"debug":return this.toDebug();case"columns":return this.toColumns();default:throw Error("Unsupported encoding: "+e)}};var u=function(){for(var e={},t=[65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,48,49,50,51,52,53,54,55,56,57,43,47],r=[],i=0,n=t.length;i<n;++i)r[t[i]]=i;return e.encode=function(e,r){for(var i,n;null!==(i=e());)r(t[i>>2&63]),n=(3&i)<<4,null!==(i=e())?(r(t[63&((n|=i>>4&15)|i>>4&15)]),n=(15&i)<<2,null!==(i=e())?(r(t[63&(n|i>>6&3)]),r(t[63&i])):(r(t[63&n]),r(61))):(r(t[63&n]),r(61),r(61))},e.decode=function(e,t){var i,n,o;function s(e){throw Error("Illegal character code: "+e)}for(;null!==(i=e());)if(void 0===(n=r[i])&&s(i),null!==(i=e())&&(void 0===(o=r[i])&&s(i),t(n<<2>>>0|(48&o)>>4),null!==(i=e()))){if(void 0===(n=r[i])){if(61===i)break;s(i)}if(t((15&o)<<4>>>0|(60&n)>>2),null!==(i=e())){if(void 0===(o=r[i])){if(61===i)break;s(i)}t((3&n)<<6>>>0|o)}}},e.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)},e}();r.toBase64=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),t|=0,(e|=0)<0||t>this.capacity||e>t)throw RangeError("begin, end");var r;return u.encode(function(){return e<t?this.view[e++]:null}.bind(this),r=s()),r()},t.fromBase64=function(e,r){if("string"!=typeof e)throw TypeError("str");var i=new t(e.length/4*3,r),n=0;return u.decode(o(e),(function(e){i.view[n++]=e})),i.limit=n,i},t.btoa=function(e){return t.fromBinary(e).toBase64()},t.atob=function(e){return t.fromBase64(e).toBinary()},r.toBinary=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),t|=0,(e|=0)<0||t>this.capacity()||e>t)throw RangeError("begin, end");if(e===t)return"";for(var r=[],i=[];e<t;)r.push(this.view[e++]),r.length>=1024&&(i.push(String.fromCharCode.apply(String,r)),r=[]);return i.join("")+String.fromCharCode.apply(String,r)},t.fromBinary=function(e,r){if("string"!=typeof e)throw TypeError("str");for(var i,n=0,o=e.length,s=new t(o,r);n<o;){if((i=e.charCodeAt(n))>255)throw RangeError("illegal char code: "+i);s.view[n++]=i}return s.limit=o,s},r.toDebug=function(e){for(var t,r=-1,i=this.buffer.byteLength,n="",o="",s="";r<i;){if(-1!==r&&(n+=(t=this.view[r])<16?"0"+t.toString(16).toUpperCase():t.toString(16).toUpperCase(),e&&(o+=t>32&&t<127?String.fromCharCode(t):".")),++r,e&&r>0&&r%16==0&&r!==i){for(;n.length<51;)n+=" ";s+=n+o+"\n",n=o=""}r===this.offset&&r===this.limit?n+=r===this.markedOffset?"!":"|":r===this.offset?n+=r===this.markedOffset?"[":"<":r===this.limit?n+=r===this.markedOffset?"]":">":n+=r===this.markedOffset?"'":e||0!==r&&r!==i?" ":""}if(e&&" "!==n){for(;n.length<51;)n+=" ";s+=n+o+"\n"}return e?s:n},t.fromDebug=function(e,r,i){for(var n,o,s=e.length,a=new t((s+1)/3|0,r,i),f=0,u=0,h=!1,l=!1,c=!1,d=!1,p=!1;f<s;){switch(n=e.charAt(f++)){case"!":if(!i){if(l||c||d){p=!0;break}l=c=d=!0}a.offset=a.markedOffset=a.limit=u,h=!1;break;case"|":if(!i){if(l||d){p=!0;break}l=d=!0}a.offset=a.limit=u,h=!1;break;case"[":if(!i){if(l||c){p=!0;break}l=c=!0}a.offset=a.markedOffset=u,h=!1;break;case"<":if(!i){if(l){p=!0;break}l=!0}a.offset=u,h=!1;break;case"]":if(!i){if(d||c){p=!0;break}d=c=!0}a.limit=a.markedOffset=u,h=!1;break;case">":if(!i){if(d){p=!0;break}d=!0}a.limit=u,h=!1;break;case"'":if(!i){if(c){p=!0;break}c=!0}a.markedOffset=u,h=!1;break;case" ":h=!1;break;default:if(!i&&h){p=!0;break}if(o=parseInt(n+e.charAt(f++),16),!i&&(isNaN(o)||o<0||o>255))throw TypeError("Illegal str: Not a debug encoded string");a.view[u++]=o,h=!0}if(p)throw TypeError("Illegal str: Invalid symbol at "+f)}if(!i){if(!l||!d)throw TypeError("Illegal str: Missing offset or limit");if(u<a.buffer.byteLength)throw TypeError("Illegal str: Not a debug encoded string (is it hex?) "+u+" < "+s)}return a},r.toHex=function(e,t){if(e=void 0===e?this.offset:e,t=void 0===t?this.limit:t,!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}for(var r,i=new Array(t-e);e<t;)(r=this.view[e++])<16?i.push("0",r.toString(16)):i.push(r.toString(16));return i.join("")},t.fromHex=function(e,r,i){if(!i){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");if(e.length%2!=0)throw TypeError("Illegal str: Length not a multiple of 2")}for(var n,o=e.length,s=new t(o/2|0,r),a=0,f=0;a<o;a+=2){if(n=parseInt(e.substring(a,a+2),16),!i&&(!isFinite(n)||n<0||n>255))throw TypeError("Illegal str: Contains non-hex characters");s.view[f++]=n}return s.limit=f,s};var h=function(){var e={MAX_CODEPOINT:1114111,encodeUTF8:function(e,t){var r=null;for("number"==typeof e&&(r=e,e=function(){return null});null!==r||null!==(r=e());)r<128?t(127&r):r<2048?(t(r>>6&31|192),t(63&r|128)):r<65536?(t(r>>12&15|224),t(r>>6&63|128),t(63&r|128)):(t(r>>18&7|240),t(r>>12&63|128),t(r>>6&63|128),t(63&r|128)),r=null},decodeUTF8:function(e,t){for(var r,i,n,o,s=function(e){e=e.slice(0,e.indexOf(null));var t=Error(e.toString());throw t.name="TruncatedError",t.bytes=e,t};null!==(r=e());)if(0==(128&r))t(r);else if(192==(224&r))null===(i=e())&&s([r,i]),t((31&r)<<6|63&i);else if(224==(240&r))(null===(i=e())||null===(n=e()))&&s([r,i,n]),t((15&r)<<12|(63&i)<<6|63&n);else{if(240!=(248&r))throw RangeError("Illegal starting byte: "+r);(null===(i=e())||null===(n=e())||null===(o=e()))&&s([r,i,n,o]),t((7&r)<<18|(63&i)<<12|(63&n)<<6|63&o)}},UTF16toUTF8:function(e,t){for(var r,i=null;null!==(r=null!==i?i:e());)r>=55296&&r<=57343&&null!==(i=e())&&i>=56320&&i<=57343?(t(1024*(r-55296)+i-56320+65536),i=null):t(r);null!==i&&t(i)},UTF8toUTF16:function(e,t){var r=null;for("number"==typeof e&&(r=e,e=function(){return null});null!==r||null!==(r=e());)r<=65535?t(r):(t(55296+((r-=65536)>>10)),t(r%1024+56320)),r=null},encodeUTF16toUTF8:function(t,r){e.UTF16toUTF8(t,(function(t){e.encodeUTF8(t,r)}))},decodeUTF8toUTF16:function(t,r){e.decodeUTF8(t,(function(t){e.UTF8toUTF16(t,r)}))},calculateCodePoint:function(e){return e<128?1:e<2048?2:e<65536?3:4},calculateUTF8:function(e){for(var t,r=0;null!==(t=e());)r+=t<128?1:t<2048?2:t<65536?3:4;return r},calculateUTF16asUTF8:function(t){var r=0,i=0;return e.UTF16toUTF8(t,(function(e){++r,i+=e<128?1:e<2048?2:e<65536?3:4})),[r,i]}};return e}();return r.toUTF8=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}var r;try{h.decodeUTF8toUTF16(function(){return e<t?this.view[e++]:null}.bind(this),r=s())}catch(r){if(e!==t)throw RangeError("Illegal range: Truncated data, "+e+" !== "+t)}return r()},t.fromUTF8=function(e,r,i){if(!i&&"string"!=typeof e)throw TypeError("Illegal str: Not a string");var n=new t(h.calculateUTF16asUTF8(o(e),!0)[1],r,i),s=0;return h.encodeUTF16toUTF8(o(e),(function(e){n.view[s++]=e})),n.limit=s,n},t})?i.apply(t,n):i)||(e.exports=o)},function(e,t){(function(t){e.exports=t}).call(this,{})},function(e,t,r){(function(e){function r(e,t){for(var r=0,i=e.length-1;i>=0;i--){var n=e[i];"."===n?e.splice(i,1):".."===n?(e.splice(i,1),r++):r&&(e.splice(i,1),r--)}if(t)for(;r--;r)e.unshift("..");return e}function i(e,t){if(e.filter)return e.filter(t);for(var r=[],i=0;i<e.length;i++)t(e[i],i,e)&&r.push(e[i]);return r}t.resolve=function(){for(var t="",n=!1,o=arguments.length-1;o>=-1&&!n;o--){var s=o>=0?arguments[o]:e.cwd();if("string"!=typeof s)throw new TypeError("Arguments to path.resolve must be strings");s&&(t=s+"/"+t,n="/"===s.charAt(0))}return(n?"/":"")+(t=r(i(t.split("/"),(function(e){return!!e})),!n).join("/"))||"."},t.normalize=function(e){var o=t.isAbsolute(e),s="/"===n(e,-1);return(e=r(i(e.split("/"),(function(e){return!!e})),!o).join("/"))||o||(e="."),e&&s&&(e+="/"),(o?"/":"")+e},t.isAbsolute=function(e){return"/"===e.charAt(0)},t.join=function(){var e=Array.prototype.slice.call(arguments,0);return t.normalize(i(e,(function(e,t){if("string"!=typeof e)throw new TypeError("Arguments to path.join must be strings");return e})).join("/"))},t.relative=function(e,r){function i(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=t.resolve(e).substr(1),r=t.resolve(r).substr(1);for(var n=i(e.split("/")),o=i(r.split("/")),s=Math.min(n.length,o.length),a=s,f=0;f<s;f++)if(n[f]!==o[f]){a=f;break}var u=[];for(f=a;f<n.length;f++)u.push("..");return(u=u.concat(o.slice(a))).join("/")},t.sep="/",t.delimiter=":",t.dirname=function(e){if("string"!=typeof e&&(e+=""),0===e.length)return".";for(var t=e.charCodeAt(0),r=47===t,i=-1,n=!0,o=e.length-1;o>=1;--o)if(47===(t=e.charCodeAt(o))){if(!n){i=o;break}}else n=!1;return-1===i?r?"/":".":r&&1===i?"/":e.slice(0,i)},t.basename=function(e,t){var r=function(e){"string"!=typeof e&&(e+="");var t,r=0,i=-1,n=!0;for(t=e.length-1;t>=0;--t)if(47===e.charCodeAt(t)){if(!n){r=t+1;break}}else-1===i&&(n=!1,i=t+1);return-1===i?"":e.slice(r,i)}(e);return t&&r.substr(-1*t.length)===t&&(r=r.substr(0,r.length-t.length)),r},t.extname=function(e){"string"!=typeof e&&(e+="");for(var t=-1,r=0,i=-1,n=!0,o=0,s=e.length-1;s>=0;--s){var a=e.charCodeAt(s);if(47!==a)-1===i&&(n=!1,i=s+1),46===a?-1===t?t=s:1!==o&&(o=1):-1!==t&&(o=-1);else if(!n){r=s+1;break}}return-1===t||-1===i||0===o||1===o&&t===i-1&&t===r+1?"":e.slice(t,i)};var n="b"==="ab".substr(-1)?function(e,t,r){return e.substr(t,r)}:function(e,t,r){return t<0&&(t=e.length+t),e.substr(t,r)}}).call(this,r(95))},function(e,t,r){for(var i,n=r(10),o=r(20),s=r(28),a=s("typed_array"),f=s("view"),u=!(!n.ArrayBuffer||!n.DataView),h=u,l=0,c="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");l<9;)(i=n[c[l++]])?(o(i.prototype,a,!0),o(i.prototype,f,!0)):h=!1;e.exports={ABV:u,CONSTR:h,TYPED:a,VIEW:f}},function(e,t,r){var i=r(22);e.exports=function(e,t,r){for(var n in t)i(e,n,t[n],r);return e}},function(e,t){e.exports=function(e,t,r,i){if(!(e instanceof t)||void 0!==i&&i in e)throw TypeError(r+": incorrect invocation!");return e}},function(e,t,r){var i=r(29),n=r(19);e.exports=function(e){if(void 0===e)return 0;var t=i(e),r=n(t);if(t!==r)throw RangeError("Wrong length!");return r}},function(e,t,r){var i=r(46);e.exports=Array.isArray||function(e){return"Array"===i(e)}},function(e,t,r){var i=r(10),n=r(13),o=r(11),s=r(4)("species");e.exports=function(e){var t=i[e];o&&t&&!t[s]&&n.f(t,s,{configurable:!0,get:function(){return this}})}},function(e,t,r){var i=r(23),n=r(19),o=r(130),s="".endsWith;i(i.P+i.F*r(131)("endsWith"),"String",{endsWith:function(e){var t=o(this,e,"endsWith"),r=arguments.length>1?arguments[1]:void 0,i=n(t.length),a=void 0===r?i:Math.min(n(r),i),f=String(e);return s?s.call(t,f,a):t.slice(a-f.length,a)===f}})},function(e,t,r){var i=r(80),n=r(38);e.exports=function(e,t,r){if(i(t))throw TypeError("String#"+r+" doesn't accept regex!");return String(n(e))}},function(e,t,r){var i=r(4)("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[i]=!1,!"/./"[e](t)}catch(e){}}return!0}},function(e,t,r){var i=r(23),n=r(19),o=r(130),s="".startsWith;i(i.P+i.F*r(131)("startsWith"),"String",{startsWith:function(e){var t=o(this,e,"startsWith"),r=n(Math.min(arguments.length>1?arguments[1]:void 0,t.length)),i=String(e);return s?s.call(t,i,r):t.slice(r,r+i.length)===i}})},function(e,t,r){var i=r(47),n=r(14)("toStringTag"),o="Arguments"==i(function(){return arguments}());e.exports=function(e){var t,r,s;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),n))?r:o?i(t):"Object"===(s=i(t))&&"function"==typeof t.callee?"Arguments":s}},function(e,t,r){var i=r(27),n=r(60),o=r(14)("species");e.exports=function(e,t){var r,s=i(e).constructor;return void 0===s||null===(r=i(s)[o])?t:n(r)}},function(e,t,r){var i,n,o,s=r(59),a=r(221),f=r(110),u=r(77),h=r(7),l=h.process,c=h.setImmediate,d=h.clearImmediate,p=h.MessageChannel,x=h.Dispatch,g=0,v={},y=function(){var e=+this;if(v.hasOwnProperty(e)){var t=v[e];delete v[e],t()}},b=function(e){y.call(e.data)};c&&d||(c=function(e){for(var t=[],r=1;arguments.length>r;)t.push(arguments[r++]);return v[++g]=function(){a("function"==typeof e?e:Function(e),t)},i(g),g},d=function(e){delete v[e]},"process"===r(47)(l)?i=function(e){l.nextTick(s(y,e,1))}:x&&x.now?i=function(e){x.now(s(y,e,1))}:p?(o=(n=new p).port2,n.port1.onmessage=b,i=s(o.postMessage,o,1)):h.addEventListener&&"function"==typeof postMessage&&!h.importScripts?(i=function(e){h.postMessage(e+"","*")},h.addEventListener("message",b,!1)):i="onreadystatechange"in u("script")?function(e){f.appendChild(u("script")).onreadystatechange=function(){f.removeChild(this),y.call(e)}}:function(e){setTimeout(s(y,e,1),0)}),e.exports={set:c,clear:d}},function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},function(e,t,r){var i=r(27),n=r(33),o=r(94);e.exports=function(e,t){if(i(e),n(t)&&t.constructor===e)return t;var r=o.f(e);return(0,r.resolve)(t),r.promise}},function(e,t,r){var i=r(10),n=r(43),o=r(35),s=r(139),a=r(13).f;e.exports=function(e){var t=n.Symbol||(n.Symbol=o?{}:i.Symbol||{});"_"===e.charAt(0)||e in t||a(t,e,{value:s.f(e)})}},function(e,t,r){t.f=r(4)},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,r){var i=r(10),n=r(24),o=r(46),s=r(142),a=r(44),f=r(18),u=r(41).f,h=r(67).f,l=r(13).f,c=r(235).trim,d=i.Number,p=d,x=d.prototype,g="Number"===o(r(52)(x)),v="trim"in String.prototype,y=function(e){var t=a(e,!1);if("string"==typeof t&&t.length>2){var r,i,n,o=(t=v?t.trim():c(t,3)).charCodeAt(0);if(43===o||45===o){if(88===(r=t.charCodeAt(2))||120===r)return NaN}else if(48===o){switch(t.charCodeAt(1)){case 66:case 98:i=2,n=49;break;case 79:case 111:i=8,n=55;break;default:return+t}for(var s,f=t.slice(2),u=0,h=f.length;u<h;u++)if((s=f.charCodeAt(u))<48||s>n)return NaN;return parseInt(f,i)}}return+t};if(!d(" 0o1")||!d("0b1")||d("+0x1")){d=function(e){var t=arguments.length<1?0:e,r=this;return r instanceof d&&(g?f((function(){x.valueOf.call(r)})):"Number"!==o(r))?s(new p(y(t)),r,d):y(t)};for(var b,m=r(11)?u(p):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),w=0;m.length>w;w++)n(p,b=m[w])&&!n(d,b)&&l(d,b,h(p,b));d.prototype=x,x.constructor=d,r(22)(i,"Number",d)}},function(e,t,r){var i=r(21),n=r(234).set;e.exports=function(e,t,r){var o,s=t.constructor;return s!==r&&"function"==typeof s&&(o=s.prototype)!==r.prototype&&i(o)&&n&&n(e,o),e}},function(e,t,r){var i=r(10),n=r(142),o=r(13).f,s=r(41).f,a=r(80),f=r(64),u=i.RegExp,h=u,l=u.prototype,c=/a/g,d=/a/g,p=new u(c)!==c;if(r(11)&&(!p||r(18)((function(){return d[r(4)("match")]=!1,u(c)!==c||u(d)===d||"/a/i"!==u(c,"i")})))){u=function(e,t){var r=this instanceof u,i=a(e),o=void 0===t;return!r&&i&&e.constructor===u&&o?e:n(p?new h(i&&!o?e.source:e,t):h((i=e instanceof u)?e.source:e,i&&o?f.call(e):t),r?this:l,u)};for(var x=function(e){e in u||o(u,e,{configurable:!0,get:function(){return h[e]},set:function(t){h[e]=t}})},g=s(h),v=0;g.length>v;)x(g[v++]);l.constructor=u,u.prototype=l,r(22)(i,"RegExp",u)}r(128)("RegExp")},function(e,t,r){e.exports=r(193)},function(e,t,r){e.exports=r(209)},function(e,t,r){(function(e,t){var i=r(15),n=r.n(i),o=(r(69),r(42),r(9)),s=r.n(o),a=r(3),f=r.n(a),u=(r(79),r(16)),h=r.n(u),l=(r(84),r(85),r(86),r(8)),c=r.n(l),d=r(2),p=r.n(d),x=r(1),g=r.n(x),v=(r(88),r(0)),y=r.n(v);!function(e,i){var n=r(120);window.dcodeIO=window.dcodeIO||{},window.dcodeIO.ByteBuffer=n,"function"==typeof define&&r(121)?define(["bytebuffer"],i):"object"===y()(t)&&t&&t.exports?t.exports=i(r(120),!0):(e.dcodeIO=e.dcodeIO||{}).ProtoBuf=i(e.dcodeIO.ByteBuffer)}(window,(function(t,i){var o,a={};return a.ByteBuffer=t,a.Long=t.Long||null,a.VERSION="5.0.1",a.WIRE_TYPES={},a.WIRE_TYPES.VARINT=0,a.WIRE_TYPES.BITS64=1,a.WIRE_TYPES.LDELIM=2,a.WIRE_TYPES.STARTGROUP=3,a.WIRE_TYPES.ENDGROUP=4,a.WIRE_TYPES.BITS32=5,a.PACKABLE_WIRE_TYPES=[a.WIRE_TYPES.VARINT,a.WIRE_TYPES.BITS64,a.WIRE_TYPES.BITS32],a.TYPES={int32:{name:"int32",wireType:a.WIRE_TYPES.VARINT,defaultValue:0},uint32:{name:"uint32",wireType:a.WIRE_TYPES.VARINT,defaultValue:0},sint32:{name:"sint32",wireType:a.WIRE_TYPES.VARINT,defaultValue:0},int64:{name:"int64",wireType:a.WIRE_TYPES.VARINT,defaultValue:a.Long?a.Long.ZERO:void 0},uint64:{name:"uint64",wireType:a.WIRE_TYPES.VARINT,defaultValue:a.Long?a.Long.UZERO:void 0},sint64:{name:"sint64",wireType:a.WIRE_TYPES.VARINT,defaultValue:a.Long?a.Long.ZERO:void 0},bool:{name:"bool",wireType:a.WIRE_TYPES.VARINT,defaultValue:!1},double:{name:"double",wireType:a.WIRE_TYPES.BITS64,defaultValue:0},string:{name:"string",wireType:a.WIRE_TYPES.LDELIM,defaultValue:""},bytes:{name:"bytes",wireType:a.WIRE_TYPES.LDELIM,defaultValue:null},fixed32:{name:"fixed32",wireType:a.WIRE_TYPES.BITS32,defaultValue:0},sfixed32:{name:"sfixed32",wireType:a.WIRE_TYPES.BITS32,defaultValue:0},fixed64:{name:"fixed64",wireType:a.WIRE_TYPES.BITS64,defaultValue:a.Long?a.Long.UZERO:void 0},sfixed64:{name:"sfixed64",wireType:a.WIRE_TYPES.BITS64,defaultValue:a.Long?a.Long.ZERO:void 0},float:{name:"float",wireType:a.WIRE_TYPES.BITS32,defaultValue:0},enum:{name:"enum",wireType:a.WIRE_TYPES.VARINT,defaultValue:0},message:{name:"message",wireType:a.WIRE_TYPES.LDELIM,defaultValue:null},group:{name:"group",wireType:a.WIRE_TYPES.STARTGROUP,defaultValue:null}},a.MAP_KEY_TYPES=[a.TYPES.int32,a.TYPES.sint32,a.TYPES.sfixed32,a.TYPES.uint32,a.TYPES.fixed32,a.TYPES.int64,a.TYPES.sint64,a.TYPES.sfixed64,a.TYPES.uint64,a.TYPES.fixed64,a.TYPES.bool,a.TYPES.string,a.TYPES.bytes],a.ID_MIN=1,a.ID_MAX=536870911,a.convertFieldsToCamelCase=!1,a.populateAccessors=!0,a.populateDefaults=!0,a.Util=((o={}).IS_NODE=!("object"!==(void 0===e?"undefined":y()(e))||"[object process]"!=e+""||e.browser),o.XHR=function(){for(var e=[function(){return new XMLHttpRequest},function(){return new ActiveXObject("Msxml2.XMLHTTP")},function(){return new ActiveXObject("Msxml3.XMLHTTP")},function(){return new ActiveXObject("Microsoft.XMLHTTP")}],t=null,r=0;r<e.length;r++){try{t=e[r]()}catch(e){continue}break}if(!t)throw Error("XMLHttpRequest is not supported");return t},o.fetch=function(e,t){if(t&&"function"!=typeof t&&(t=null),o.IS_NODE){var i=r(192);if(t)i.readFile(e,(function(e,r){t(e?null:""+r)}));else try{return i.readFileSync(e)}catch(e){return null}}else{var n=o.XHR();if(n.open("GET",e,!!t),n.setRequestHeader("Accept","text/plain"),"function"==typeof n.overrideMimeType&&n.overrideMimeType("text/plain"),!t)return n.send(null),200===n.status||0===n.status&&"string"==typeof n.responseText?n.responseText:null;n.onreadystatechange=function(){4===n.readyState&&(200===n.status||0===n.status&&"string"==typeof n.responseText?t(n.responseText):t(null))},4!==n.readyState&&n.send(null)}},o.toCamelCase=function(e){return e.replace(/_([a-zA-Z])/g,(function(e,t){return t.toUpperCase()}))},o),a.Lang={DELIM:/[\s\{\}=;:\[\],'"\(\)<>]/g,RULE:/^(?:required|optional|repeated|map)$/,TYPE:/^(?:double|float|int32|uint32|sint32|int64|uint64|sint64|fixed32|sfixed32|fixed64|sfixed64|bool|string|bytes)$/,NAME:/^[a-zA-Z_][a-zA-Z_0-9]*$/,TYPEDEF:/^[a-zA-Z][a-zA-Z_0-9]*$/,TYPEREF:/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)+$/,FQTYPEREF:/^(?:\.[a-zA-Z][a-zA-Z_0-9]*)+$/,NUMBER:/^-?(?:[1-9][0-9]*|0|0[xX][0-9a-fA-F]+|0[0-7]+|([0-9]*(\.[0-9]*)?([Ee][+-]?[0-9]+)?)|inf|nan)$/,NUMBER_DEC:/^(?:[1-9][0-9]*|0)$/,NUMBER_HEX:/^0[xX][0-9a-fA-F]+$/,NUMBER_OCT:/^0[0-7]+$/,NUMBER_FLT:/^([0-9]*(\.[0-9]*)?([Ee][+-]?[0-9]+)?|inf|nan)$/,BOOL:/^(?:true|false)$/i,ID:/^(?:[1-9][0-9]*|0|0[xX][0-9a-fA-F]+|0[0-7]+)$/,NEGID:/^\-?(?:[1-9][0-9]*|0|0[xX][0-9a-fA-F]+|0[0-7]+)$/,WHITESPACE:/\s/,STRING:/(?:"([^"\\]*(?:\\.[^"\\]*)*)")|(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,STRING_DQ:/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,STRING_SQ:/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g},a.DotProto=function(e,t){function r(e,r){var i=-1,n=1;if("-"===e.charAt(0)&&(n=-1,e=e.substring(1)),t.NUMBER_DEC.test(e))i=g()(e);else if(t.NUMBER_HEX.test(e))i=g()(e.substring(2),16);else{if(!t.NUMBER_OCT.test(e))throw Error("illegal id value: "+(0>n?"-":"")+e);i=g()(e.substring(1),8)}if(i=n*i|0,!r&&0>i)throw Error("illegal id value: "+(0>n?"-":"")+e);return i}function i(e){var r=1;if("-"===e.charAt(0)&&(r=-1,e=e.substring(1)),t.NUMBER_DEC.test(e))return r*g()(e,10);if(t.NUMBER_HEX.test(e))return r*g()(e.substring(2),16);if(t.NUMBER_OCT.test(e))return r*g()(e.substring(1),8);if("inf"===e)return 1/0*r;if("nan"===e)return NaN;if(t.NUMBER_FLT.test(e))return r*p()(e);throw Error("illegal number value: "+(0>r?"-":"")+e)}function n(e,t,r){void 0===e[t]?e[t]=r:(c()(e[t])||(e[t]=[e[t]]),e[t].push(r))}var o={},s=function(e){this.source=e+"",this.index=0,this.line=1,this.stack=[],this._stringOpen=null};(a=s.prototype)._readString=function(){var e='"'===this._stringOpen?t.STRING_DQ:t.STRING_SQ;e.lastIndex=this.index-1;var r=e.exec(this.source);if(!r)throw Error("unterminated string");return this.index=e.lastIndex,this.stack.push(this._stringOpen),this._stringOpen=null,r[1]},a.next=function(){if(0<this.stack.length)return this.stack.shift();if(this.index>=this.source.length)return null;if(null!==this._stringOpen)return this._readString();var e,r;do{for(e=!1;t.WHITESPACE.test(r=this.source.charAt(this.index));)if("\n"===r&&++this.line,++this.index===this.source.length)return null;if("/"===this.source.charAt(this.index))if(++this.index,"/"===this.source.charAt(this.index)){for(;"\n"!==this.source.charAt(++this.index);)if(this.index===this.source.length)return null;++this.index,++this.line,e=!0}else{if("*"!==(r=this.source.charAt(this.index)))return"/";do{if("\n"===r&&++this.line,++this.index===this.source.length)return null;e=r,r=this.source.charAt(this.index)}while("*"!==e||"/"!==r);++this.index,e=!0}}while(e);if(this.index===this.source.length)return null;if(r=this.index,t.DELIM.lastIndex=0,!t.DELIM.test(this.source.charAt(r++)))for(;r<this.source.length&&!t.DELIM.test(this.source.charAt(r));)++r;return'"'!==(r=this.source.substring(this.index,this.index=r))&&"'"!==r||(this._stringOpen=r),r},a.peek=function(){if(0===this.stack.length){var e=this.next();if(null===e)return null;this.stack.push(e)}return this.stack[0]},a.skip=function(e){var t=this.next();if(t!==e)throw Error("illegal '"+t+"', '"+e+"' expected")},a.omit=function(e){return this.peek()===e&&(this.next(),!0)},a.toString=function(){return"Tokenizer ("+this.index+"/"+this.source.length+" at line "+this.line+")"},o.Tokenizer=s;var a,f=function(e){this.tn=new s(e),this.proto3=!1};return(a=f.prototype).parse=function(){var e,r,i={name:"[ROOT]",package:null,messages:[],enums:[],imports:[],options:{},services:[]},n=!0;try{for(;e=this.tn.next();)switch(e){case"package":if(!n||null!==i.package)throw Error("unexpected 'package'");if(e=this.tn.next(),!t.TYPEREF.test(e))throw Error("illegal package name: "+e);this.tn.skip(";"),i.package=e;break;case"import":if(!n)throw Error("unexpected 'import'");("public"===(e=this.tn.peek())||(r="weak"===e))&&this.tn.next(),e=this._readString(),this.tn.skip(";"),r||i.imports.push(e);break;case"syntax":if(!n)throw Error("unexpected 'syntax'");this.tn.skip("="),"proto3"===(i.syntax=this._readString())&&(this.proto3=!0),this.tn.skip(";");break;case"message":this._parseMessage(i,null),n=!1;break;case"enum":this._parseEnum(i),n=!1;break;case"option":this._parseOption(i);break;case"service":this._parseService(i);break;case"extend":this._parseExtend(i);break;default:throw Error("unexpected '"+e+"'")}}catch(e){throw e.message="Parse error at line "+this.tn.line+": "+e.message,e}return delete i.name,i},f.parse=function(e){return new f(e).parse()},a._readString=function(){var e,t="";do{if("'"!==(e=this.tn.next())&&'"'!==e)throw Error("illegal string delimiter: "+e);t+=this.tn.next(),this.tn.skip(e),e=this.tn.peek()}while('"'===e||'"'===e);return t},a._readValue=function(e){var r=this.tn.peek();if('"'===r||"'"===r)return this._readString();if(this.tn.next(),t.NUMBER.test(r))return i(r);if(t.BOOL.test(r))return"true"===r.toLowerCase();if(e&&t.TYPEREF.test(r))return r;throw Error("illegal value: "+r)},a._parseOption=function(e,r){var i=this.tn.next(),n=!1;if("("===i&&(n=!0,i=this.tn.next()),!t.TYPEREF.test(i))throw Error("illegal option name: "+i);var o=i;n&&(this.tn.skip(")"),o="("+o+")",i=this.tn.peek(),t.FQTYPEREF.test(i)&&(o+=i,this.tn.next())),this.tn.skip("="),this._parseOptionValue(e,o),r||this.tn.skip(";")},a._parseOptionValue=function(e,r){var i=this.tn.peek();if("{"!==i)n(e.options,r,this._readValue(!0));else for(this.tn.skip("{");"}"!==(i=this.tn.next());){if(!t.NAME.test(i))throw Error("illegal option name: "+r+"."+i);this.tn.omit(":")?n(e.options,r+"."+i,this._readValue(!0)):this._parseOptionValue(e,r+"."+i)}},a._parseService=function(e){var r=this.tn.next();if(!t.NAME.test(r))throw Error("illegal service name at line "+this.tn.line+": "+r);var i={name:r,rpc:{},options:{}};for(this.tn.skip("{");"}"!==(r=this.tn.next());)if("option"===r)this._parseOption(i);else{if("rpc"!==r)throw Error("illegal service token: "+r);this._parseServiceRPC(i)}this.tn.omit(";"),e.services.push(i)},a._parseServiceRPC=function(e){var r=this.tn.next();if(!t.NAME.test(r))throw Error("illegal rpc service method name: "+r);var i=r,n={request:null,response:null,request_stream:!1,response_stream:!1,options:{}};if(this.tn.skip("("),"stream"===(r=this.tn.next()).toLowerCase()&&(n.request_stream=!0,r=this.tn.next()),!t.TYPEREF.test(r))throw Error("illegal rpc service request type: "+r);if(n.request=r,this.tn.skip(")"),"returns"!==(r=this.tn.next()).toLowerCase())throw Error("illegal rpc service request type delimiter: "+r);if(this.tn.skip("("),"stream"===(r=this.tn.next()).toLowerCase()&&(n.response_stream=!0,r=this.tn.next()),n.response=r,this.tn.skip(")"),"{"===(r=this.tn.peek())){for(this.tn.next();"}"!==(r=this.tn.next());){if("option"!==r)throw Error("illegal rpc service token: "+r);this._parseOption(n)}this.tn.omit(";")}else this.tn.skip(";");void 0===e.rpc&&(e.rpc={}),e.rpc[i]=n},a._parseMessage=function(e,i){var n=!!i,o=this.tn.next(),s={name:"",fields:[],enums:[],messages:[],options:{},services:[],oneofs:{}};if(!t.NAME.test(o))throw Error("illegal "+(n?"group":"message")+" name: "+o);for(s.name=o,n&&(this.tn.skip("="),i.id=r(this.tn.next()),s.isGroup=!0),"["===(o=this.tn.peek())&&i&&this._parseFieldOptions(i),this.tn.skip("{");"}"!==(o=this.tn.next());)if(t.RULE.test(o))this._parseMessageField(s,o);else if("oneof"===o)this._parseMessageOneOf(s);else if("enum"===o)this._parseEnum(s);else if("message"===o)this._parseMessage(s);else if("option"===o)this._parseOption(s);else if("service"===o)this._parseService(s);else if("extensions"===o)s.hasOwnProperty("extensions")?s.extensions=s.extensions.concat(this._parseExtensionRanges()):s.extensions=this._parseExtensionRanges();else if("reserved"===o)this._parseIgnored();else if("extend"===o)this._parseExtend(s);else{if(!t.TYPEREF.test(o))throw Error("illegal message token: "+o);if(!this.proto3)throw Error("illegal field rule: "+o);this._parseMessageField(s,"optional",o)}return this.tn.omit(";"),e.messages.push(s),s},a._parseIgnored=function(){for(;";"!==this.tn.peek();)this.tn.next();this.tn.skip(";")},a._parseMessageField=function(e,i,n){if(!t.RULE.test(i))throw Error("illegal message field rule: "+i);var o={rule:i,type:"",name:"",options:{},id:0};if("map"===i){if(n)throw Error("illegal type: "+n);if(this.tn.skip("<"),i=this.tn.next(),!t.TYPE.test(i)&&!t.TYPEREF.test(i))throw Error("illegal message field type: "+i);if(o.keytype=i,this.tn.skip(","),i=this.tn.next(),!t.TYPE.test(i)&&!t.TYPEREF.test(i))throw Error("illegal message field: "+i);if(o.type=i,this.tn.skip(">"),i=this.tn.next(),!t.NAME.test(i))throw Error("illegal message field name: "+i);o.name=i,this.tn.skip("="),o.id=r(this.tn.next()),"["===(i=this.tn.peek())&&this._parseFieldOptions(o),this.tn.skip(";")}else if("group"===(n=void 0!==n?n:this.tn.next())){if(i=this._parseMessage(e,o),!/^[A-Z]/.test(i.name))throw Error("illegal group name: "+i.name);o.type=i.name,o.name=i.name.toLowerCase(),this.tn.omit(";")}else{if(!t.TYPE.test(n)&&!t.TYPEREF.test(n))throw Error("illegal message field type: "+n);if(o.type=n,i=this.tn.next(),!t.NAME.test(i))throw Error("illegal message field name: "+i);o.name=i,this.tn.skip("="),o.id=r(this.tn.next()),"["===(i=this.tn.peek())&&this._parseFieldOptions(o),this.tn.skip(";")}return e.fields.push(o),o},a._parseMessageOneOf=function(e){var r=this.tn.next();if(!t.NAME.test(r))throw Error("illegal oneof name: "+r);var i=r,n=[];for(this.tn.skip("{");"}"!==(r=this.tn.next());)(r=this._parseMessageField(e,"optional",r)).oneof=i,n.push(r.id);this.tn.omit(";"),e.oneofs[i]=n},a._parseFieldOptions=function(e){this.tn.skip("[");for(var t=!0;"]"!==this.tn.peek();)t||this.tn.skip(","),this._parseOption(e,!0),t=!1;this.tn.next()},a._parseEnum=function(e){var i={name:"",values:[],options:{}},n=this.tn.next();if(!t.NAME.test(n))throw Error("illegal name: "+n);for(i.name=n,this.tn.skip("{");"}"!==(n=this.tn.next());)if("option"===n)this._parseOption(i);else{if(!t.NAME.test(n))throw Error("illegal name: "+n);this.tn.skip("=");var o={name:n,id:r(this.tn.next(),!0)};"["===(n=this.tn.peek())&&this._parseFieldOptions({options:{}}),this.tn.skip(";"),i.values.push(o)}this.tn.omit(";"),e.enums.push(i)},a._parseExtensionRanges=function(){var t,r,n=[];do{for(r=[];;){switch(t=this.tn.next()){case"min":t=e.ID_MIN;break;case"max":t=e.ID_MAX;break;default:t=i(t)}if(r.push(t),2===r.length)break;if("to"!==this.tn.peek()){r.push(t);break}this.tn.next()}n.push(r)}while(this.tn.omit(","));return this.tn.skip(";"),n},a._parseExtend=function(e){var r=this.tn.next();if(!t.TYPEREF.test(r))throw Error("illegal extend reference: "+r);var i={ref:r,fields:[]};for(this.tn.skip("{");"}"!==(r=this.tn.next());)if(t.RULE.test(r))this._parseMessageField(i,r);else{if(!t.TYPEREF.test(r))throw Error("illegal extend token: "+r);if(!this.proto3)throw Error("illegal field rule: "+r);this._parseMessageField(i,"optional",r)}return this.tn.omit(";"),e.messages.push(i),i},a.toString=function(){return"Parser at line "+this.tn.line},o.Parser=f,o}(a,a.Lang),a.Reflect=function(e){function r(t,r){if(t&&"number"==typeof t.low&&"number"==typeof t.high&&"boolean"==typeof t.unsigned&&t.low==t.low&&t.high==t.high)return new e.Long(t.low,t.high,void 0===r?t.unsigned:r);if("string"==typeof t)return e.Long.fromString(t,r||!1,10);if("number"==typeof t)return e.Long.fromNumber(t,r||!1);throw Error("not convertible to Long")}function i(t,r){var n=7&(o=r.readVarint32()),o=o>>>3;switch(n){case e.WIRE_TYPES.VARINT:do{o=r.readUint8()}while(128==(128&o));break;case e.WIRE_TYPES.BITS64:r.offset+=8;break;case e.WIRE_TYPES.LDELIM:o=r.readVarint32(),r.offset+=o;break;case e.WIRE_TYPES.STARTGROUP:i(o,r);break;case e.WIRE_TYPES.ENDGROUP:if(o===t)return!1;throw Error("Illegal GROUPEND after unknown group: "+o+" ("+t+" expected)");case e.WIRE_TYPES.BITS32:r.offset+=4;break;default:throw Error("Illegal wire type in unknown group "+t+": "+n)}return!0}var o={},a=function(e,t,r){this.builder=e,this.parent=t,this.name=r};(u=a.prototype).fqn=function(){for(var e=this.name,t=this;null!==(t=t.parent);)e=t.name+"."+e;return e},u.toString=function(e){return(e?this.className+" ":"")+this.fqn()},u.build=function(){throw Error(this.toString(!0)+" cannot be built directly")},o.T=a;var u,l=function(e,t,r,i,n){a.call(this,e,t,r),this.className="Namespace",this.children=[],this.options=i||{},this.syntax=n||"proto2"};(u=l.prototype=h()(a.prototype)).getChildren=function(e){if(null===(e=e||null))return this.children.slice();for(var t=[],r=0,i=this.children.length;r<i;++r)this.children[r]instanceof e&&t.push(this.children[r]);return t},u.addChild=function(e){var t;if(t=this.getChild(e.name))if(t instanceof x.Field&&t.name!==t.originalName&&null===this.getChild(t.originalName))t.name=t.originalName;else{if(!(e instanceof x.Field&&e.name!==e.originalName&&null===this.getChild(e.originalName)))throw Error("Duplicate name in namespace "+this.toString(!0)+": "+e.name);e.name=e.originalName}this.children.push(e)},u.getChild=function(e){for(var t="number"==typeof e?"id":"name",r=0,i=this.children.length;r<i;++r)if(this.children[r][t]===e)return this.children[r];return null},u.resolve=function(e,t){var r="string"==typeof e?e.split("."):e,i=this,n=0;if(""===r[n]){for(;null!==i.parent;)i=i.parent;n++}do{do{if(!(i instanceof o.Namespace)){i=null;break}if(!((i=i.getChild(r[n]))&&i instanceof o.T)||t&&!(i instanceof o.Namespace)){i=null;break}n++}while(n<r.length);if(null!==i)break;if(null!==this.parent)return this.parent.resolve(e,t)}while(null!==i);return i},u.qn=function(e){var t=[],r=e;do{t.unshift(r.name),r=r.parent}while(null!==r);for(r=1;r<=t.length;r++){var i=t.slice(t.length-r);if(e===this.resolve(i,e instanceof o.Namespace))return i.join(".")}return e.fqn()},u.build=function(){for(var e,t={},r=this.children,i=0,n=r.length;i<n;++i)(e=r[i])instanceof l&&(t[e.name]=e.build());return f.a&&f()(t,"$options",{value:this.buildOpt()}),t},u.buildOpt=function(){for(var e={},t=s()(this.options),r=0,i=t.length;r<i;++r)e[t[r]]=this.options[t[r]];return e},u.getOption=function(e){return void 0===e?this.options:void 0!==this.options[e]?this.options[e]:null},o.Namespace=l;var d=function(t,r,i,n,o){if(this.type=t,this.resolvedType=r,this.isMapKey=i,this.syntax=n,this.name=o,i&&0>e.MAP_KEY_TYPES.indexOf(t))throw Error("Invalid map key type: "+t.name)},p=d.prototype;d.defaultFieldValue=function(r){if("string"==typeof r&&(r=e.TYPES[r]),void 0===r.defaultValue)throw Error("default value for type "+r.name+" is not supported");return r===e.TYPES.bytes?new t(0):r.defaultValue},p.toString=function(){return(this.name||"")+(this.isMapKey?"map":"value")+" element"},p.verifyValue=function(i){function n(e,t){throw Error("Illegal value for "+o.toString(!0)+" of type "+o.type.name+": "+e+" ("+t+")")}var o=this;switch(this.type){case e.TYPES.int32:case e.TYPES.sint32:case e.TYPES.sfixed32:return("number"!=typeof i||i==i&&0!=i%1)&&n(y()(i),"not an integer"),4294967295<i?0|i:i;case e.TYPES.uint32:case e.TYPES.fixed32:return("number"!=typeof i||i==i&&0!=i%1)&&n(y()(i),"not an integer"),0>i?i>>>0:i;case e.TYPES.int64:case e.TYPES.sint64:case e.TYPES.sfixed64:if(e.Long)try{return r(i,!1)}catch(e){n(y()(i),e.message)}else n(y()(i),"requires Long.js");case e.TYPES.uint64:case e.TYPES.fixed64:if(e.Long)try{return r(i,!0)}catch(e){n(y()(i),e.message)}else n(y()(i),"requires Long.js");case e.TYPES.bool:return"boolean"!=typeof i&&n(y()(i),"not a boolean"),i;case e.TYPES.float:case e.TYPES.double:return"number"!=typeof i&&n(y()(i),"not a number"),i;case e.TYPES.string:return"string"==typeof i||i&&i instanceof String||n(y()(i),"not a string"),""+i;case e.TYPES.bytes:return t.isByteBuffer(i)?i:t.wrap(i,"base64");case e.TYPES.enum:for(var s=this.resolvedType.getChildren(e.Reflect.Enum.Value),a=0;a<s.length;a++)if(s[a].name===i||s[a].id===i)return s[a].id;if("proto3"===this.syntax)return("number"!=typeof i||i==i&&0!=i%1)&&n(y()(i),"not an integer"),(4294967295<i||0>i)&&n(y()(i),"not in range for uint32"),i;n(i,"not a valid enum value");case e.TYPES.group:case e.TYPES.message:if(i&&"object"===y()(i)||n(y()(i),"object expected"),i instanceof this.resolvedType.clazz)return i;if(i instanceof e.Builder.Message){for(a in s={},i)i.hasOwnProperty(a)&&(s[a]=i[a]);i=s}return new this.resolvedType.clazz(i)}throw Error("[INTERNAL] Illegal value for "+this.toString(!0)+": "+i+" (undefined type "+this.type+")")},p.calculateLength=function(r,i){if(null===i)return 0;var n;switch(this.type){case e.TYPES.int32:return 0>i?t.calculateVarint64(i):t.calculateVarint32(i);case e.TYPES.uint32:return t.calculateVarint32(i);case e.TYPES.sint32:return t.calculateVarint32(t.zigZagEncode32(i));case e.TYPES.fixed32:case e.TYPES.sfixed32:case e.TYPES.float:return 4;case e.TYPES.int64:case e.TYPES.uint64:return t.calculateVarint64(i);case e.TYPES.sint64:return t.calculateVarint64(t.zigZagEncode64(i));case e.TYPES.fixed64:case e.TYPES.sfixed64:return 8;case e.TYPES.bool:return 1;case e.TYPES.enum:return t.calculateVarint32(i);case e.TYPES.double:return 8;case e.TYPES.string:return n=t.calculateUTF8Bytes(i),t.calculateVarint32(n)+n;case e.TYPES.bytes:if(0>i.remaining())throw Error("Illegal value for "+this.toString(!0)+": "+i.remaining()+" bytes remaining");return t.calculateVarint32(i.remaining())+i.remaining();case e.TYPES.message:return n=this.resolvedType.calculate(i),t.calculateVarint32(n)+n;case e.TYPES.group:return(n=this.resolvedType.calculate(i))+t.calculateVarint32(r<<3|e.WIRE_TYPES.ENDGROUP)}throw Error("[INTERNAL] Illegal value to encode in "+this.toString(!0)+": "+i+" (unknown type)")},p.encodeValue=function(r,i,n){if(null===i)return n;switch(this.type){case e.TYPES.int32:0>i?n.writeVarint64(i):n.writeVarint32(i);break;case e.TYPES.uint32:n.writeVarint32(i);break;case e.TYPES.sint32:n.writeVarint32ZigZag(i);break;case e.TYPES.fixed32:n.writeUint32(i);break;case e.TYPES.sfixed32:n.writeInt32(i);break;case e.TYPES.int64:case e.TYPES.uint64:n.writeVarint64(i);break;case e.TYPES.sint64:n.writeVarint64ZigZag(i);break;case e.TYPES.fixed64:n.writeUint64(i);break;case e.TYPES.sfixed64:n.writeInt64(i);break;case e.TYPES.bool:"string"==typeof i?n.writeVarint32("false"===i.toLowerCase()?0:!!i):n.writeVarint32(i?1:0);break;case e.TYPES.enum:n.writeVarint32(i);break;case e.TYPES.float:n.writeFloat32(i);break;case e.TYPES.double:n.writeFloat64(i);break;case e.TYPES.string:n.writeVString(i);break;case e.TYPES.bytes:if(0>i.remaining())throw Error("Illegal value for "+this.toString(!0)+": "+i.remaining()+" bytes remaining");r=i.offset,n.writeVarint32(i.remaining()),n.append(i),i.offset=r;break;case e.TYPES.message:r=(new t).LE(),this.resolvedType.encode(i,r),n.writeVarint32(r.offset),n.append(r.flip());break;case e.TYPES.group:this.resolvedType.encode(i,n),n.writeVarint32(r<<3|e.WIRE_TYPES.ENDGROUP);break;default:throw Error("[INTERNAL] Illegal value to encode in "+this.toString(!0)+": "+i+" (unknown type)")}return n},p.decode=function(t,r,i){if(r!==this.type.wireType)throw Error("Unexpected wire type for element");switch(this.type){case e.TYPES.int32:return 0|t.readVarint32();case e.TYPES.uint32:return t.readVarint32()>>>0;case e.TYPES.sint32:return 0|t.readVarint32ZigZag();case e.TYPES.fixed32:return t.readUint32()>>>0;case e.TYPES.sfixed32:return 0|t.readInt32();case e.TYPES.int64:return t.readVarint64();case e.TYPES.uint64:return t.readVarint64().toUnsigned();case e.TYPES.sint64:return t.readVarint64ZigZag();case e.TYPES.fixed64:return t.readUint64();case e.TYPES.sfixed64:return t.readInt64();case e.TYPES.bool:return!!t.readVarint32();case e.TYPES.enum:return t.readVarint32();case e.TYPES.float:return t.readFloat();case e.TYPES.double:return t.readDouble();case e.TYPES.string:return t.readVString();case e.TYPES.bytes:if(i=t.readVarint32(),t.remaining()<i)throw Error("Illegal number of bytes for "+this.toString(!0)+": "+i+" required but got only "+t.remaining());return(r=t.clone()).limit=r.offset+i,t.offset+=i,r;case e.TYPES.message:return i=t.readVarint32(),this.resolvedType.decode(t,i);case e.TYPES.group:return this.resolvedType.decode(t,-1,i)}throw Error("[INTERNAL] Illegal decode type")},p.valueFromString=function(r){if(!this.isMapKey)throw Error("valueFromString() called on non-map-key element");switch(this.type){case e.TYPES.int32:case e.TYPES.sint32:case e.TYPES.sfixed32:case e.TYPES.uint32:case e.TYPES.fixed32:return this.verifyValue(g()(r));case e.TYPES.int64:case e.TYPES.sint64:case e.TYPES.sfixed64:case e.TYPES.uint64:case e.TYPES.fixed64:return this.verifyValue(r);case e.TYPES.bool:return"true"===r;case e.TYPES.string:return this.verifyValue(r);case e.TYPES.bytes:return t.fromBinary(r)}},p.valueToString=function(t){if(!this.isMapKey)throw Error("valueToString() called on non-map-key element");return this.type===e.TYPES.bytes?t.toString("binary"):t.toString()},o.Element=d;var x=function(e,t,r,i,n,o){l.call(this,e,t,r,i,o),this.className="Message",this.extensions=void 0,this.clazz=null,this.isGroup=!!n,this._fieldsByName=this._fieldsById=this._fields=null};(p=x.prototype=h()(l.prototype)).build=function(r){if(this.clazz&&!r)return this.clazz;r=function(e,r){function i(r,n,o,s){if(null===r||"object"!==y()(r))return s&&s instanceof e.Reflect.Enum&&null!==(h=e.Reflect.Enum.getName(s.object,r))?h:r;if(t.isByteBuffer(r))return n?r.toBase64():r.toBuffer();if(e.Long.isLong(r))return o?r.toString():e.Long.fromValue(r);var a;if(c()(r))return a=[],r.forEach((function(e,t){a[t]=i(e,n,o,s)})),a;if(a={},r instanceof e.Map){for(var f=(h=r.entries()).next();!f.done;f=h.next())a[r.keyElem.valueToString(f.value[0])]=i(f.value[1],n,o,r.valueElem.resolvedType);return a}var u,h=r.$type;for(u in f=void 0,r)r.hasOwnProperty(u)&&(h&&(f=h.getChild(u))?a[u]=i(r[u],n,o,f.resolvedType):a[u]=i(r[u],n,o));return a}var o=r.getChildren(e.Reflect.Message.Field),s=r.getChildren(e.Reflect.Message.OneOf),a=function i(n,a){e.Builder.Message.call(this);for(var f=0,u=s.length;f<u;++f)this[s[f].name]=null;for(f=0,u=o.length;f<u;++f){var h=o[f];this[h.name]=h.repeated?[]:h.map?new e.Map(h):null,!h.required&&"proto3"!==r.syntax||null===h.defaultValue||(this[h.name]=h.defaultValue)}if(0<arguments.length)if(1!==arguments.length||null===n||"object"!==y()(n)||!("function"!=typeof n.encode||n instanceof i)||c()(n)||n instanceof e.Map||t.isByteBuffer(n)||n instanceof ArrayBuffer||e.Long&&n instanceof e.Long)for(f=0,u=arguments.length;f<u;++f)void 0!==(h=arguments[f])&&this.$set(o[f].name,h);else this.$set(n)},u=a.prototype=h()(e.Builder.Message.prototype);u.add=function(t,i,n){var o=r._fieldsByName[t];if(!n){if(!o)throw Error(this+"#"+t+" is undefined");if(!(o instanceof e.Reflect.Message.Field))throw Error(this+"#"+t+" is not a field: "+o.toString(!0));if(!o.repeated)throw Error(this+"#"+t+" is not a repeated field");i=o.verifyValue(i,!0)}return null===this[t]&&(this[t]=[]),this[t].push(i),this},u.$add=u.add,u.set=function(t,i,n){if(t&&"object"===y()(t)){for(var o in n=i,t)t.hasOwnProperty(o)&&void 0!==(i=t[o])&&this.$set(o,i,n);return this}if(o=r._fieldsByName[t],n)this[t]=i;else{if(!o)throw Error(this+"#"+t+" is not a field: undefined");if(!(o instanceof e.Reflect.Message.Field))throw Error(this+"#"+t+" is not a field: "+o.toString(!0));this[o.name]=i=o.verifyValue(i)}return o&&o.oneof&&(n=this[o.oneof.name],null!==i?(null!==n&&n!==o.name&&(this[n]=null),this[o.oneof.name]=o.name):n===t&&(this[o.oneof.name]=null)),this},u.$set=u.set,u.get=function(t,i){if(i)return this[t];var n=r._fieldsByName[t];if(!(n&&n instanceof e.Reflect.Message.Field))throw Error(this+"#"+t+" is not a field: undefined");if(!(n instanceof e.Reflect.Message.Field))throw Error(this+"#"+t+" is not a field: "+n.toString(!0));return this[n.name]},u.$get=u.get;for(var l=0;l<o.length;l++){var d=o[l];d instanceof e.Reflect.Message.ExtensionField||r.builder.options.populateAccessors&&function(e){var t=(t=e.originalName.replace(/(_[a-zA-Z])/g,(function(e){return e.toUpperCase().replace("_","")}))).substring(0,1).toUpperCase()+t.substring(1),i=e.originalName.replace(/([A-Z])/g,(function(e){return"_"+e})),n=function(t,r){return this[e.name]=r?t:e.verifyValue(t),this},o=function(){return this[e.name]};null===r.getChild("set"+t)&&(u["set"+t]=n),null===r.getChild("set_"+i)&&(u["set_"+i]=n),null===r.getChild("get"+t)&&(u["get"+t]=o),null===r.getChild("get_"+i)&&(u["get_"+i]=o)}(d)}return u.encode=function(e,i){"boolean"==typeof e&&(i=e,e=void 0);var n=!1;e||(e=new t,n=!0);var o=e.littleEndian;try{return r.encode(this,e.LE(),i),(n?e.flip():e).LE(o)}catch(t){throw e.LE(o),t}},a.encode=function(e,t,r){return new a(e).encode(t,r)},u.calculate=function(){return r.calculate(this)},u.encodeDelimited=function(e,i){var n=!1;e||(e=new t,n=!0);var o=(new t).LE();return r.encode(this,o,i).flip(),e.writeVarint32(o.remaining()),e.append(o),n?e.flip():e},u.encodeAB=function(){try{return this.encode().toArrayBuffer()}catch(e){throw e.encoded&&(e.encoded=e.encoded.toArrayBuffer()),e}},u.toArrayBuffer=u.encodeAB,u.encodeNB=function(){try{return this.encode().toBuffer()}catch(e){throw e.encoded&&(e.encoded=e.encoded.toBuffer()),e}},u.toBuffer=u.encodeNB,u.encode64=function(){try{return this.encode().toBase64()}catch(e){throw e.encoded&&(e.encoded=e.encoded.toBase64()),e}},u.toBase64=u.encode64,u.encodeHex=function(){try{return this.encode().toHex()}catch(e){throw e.encoded&&(e.encoded=e.encoded.toHex()),e}},u.toHex=u.encodeHex,u.toRaw=function(e,t){return i(this,!!e,!!t,this.$type)},u.encodeJSON=function(){return n()(i(this,!0,!0,this.$type))},a.decode=function(e,i,n){"string"==typeof i&&(n=i,i=-1),"string"==typeof e?e=t.wrap(e,n||"base64"):t.isByteBuffer(e)||(e=t.wrap(e)),n=e.littleEndian;try{var o=r.decode(e.LE(),i);return e.LE(n),o}catch(t){throw e.LE(n),t}},a.decodeDelimited=function(e,i){if("string"==typeof e?e=t.wrap(e,i||"base64"):t.isByteBuffer(e)||(e=t.wrap(e)),1>e.remaining())return null;var n=e.offset,o=e.readVarint32();if(e.remaining()<o)return e.offset=n,null;try{var s=r.decode(e.slice(e.offset,e.offset+o).LE());return e.offset+=o,s}catch(t){throw e.offset+=o,t}},a.decode64=function(e){return a.decode(e,"base64")},a.decodeHex=function(e){return a.decode(e,"hex")},a.decodeJSON=function(e){return new a(JSON.parse(e))},u.toString=function(){return r.toString()},f.a&&(f()(a,"$options",{value:r.buildOpt()}),f()(u,"$options",{value:a.$options}),f()(a,"$type",{value:r}),f()(u,"$type",{value:r})),a}(e,this),this._fields=[],this._fieldsById={},this._fieldsByName={};for(var i,o=0,s=this.children.length;o<s;o++)if((i=this.children[o])instanceof b||i instanceof x||i instanceof w){if(r.hasOwnProperty(i.name))throw Error("Illegal reflect child of "+this.toString(!0)+": "+i.toString(!0)+" cannot override static property '"+i.name+"'");r[i.name]=i.build()}else if(i instanceof x.Field)i.build(),this._fields.push(i),this._fieldsById[i.id]=i,this._fieldsByName[i.name]=i;else if(!(i instanceof x.OneOf||i instanceof m))throw Error("Illegal reflect child of "+this.toString(!0)+": "+this.children[o].toString(!0));return this.clazz=r},p.encode=function(e,t,r){for(var i,n,o=null,s=0,a=this._fields.length;s<a;++s)n=e[(i=this._fields[s]).name],i.required&&null===n?null===o&&(o=i):i.encode(r?n:i.verifyValue(n),t,e);if(null!==o)throw(e=Error("Missing at least one required field for "+this.toString(!0)+": "+o)).encoded=t,e;return t},p.calculate=function(e){for(var t,r,i=0,n=0,o=this._fields.length;n<o;++n){if(r=e[(t=this._fields[n]).name],t.required&&null===r)throw Error("Missing at least one required field for "+this.toString(!0)+": "+t);i+=t.calculate(r,e)}return i},p.decode=function(t,r,n){"number"!=typeof r&&(r=-1);for(var o,s,a,f=t.offset,u=new this.clazz;t.offset<f+r||-1===r&&0<t.remaining();){if(a=(o=t.readVarint32())>>>3,(s=7&o)===e.WIRE_TYPES.ENDGROUP){if(a!==n)throw Error("Illegal group end indicator for "+this.toString(!0)+": "+a+" ("+(n?n+" expected":"not a group")+")");break}if(o=this._fieldsById[a])o.repeated&&!o.options.packed?u[o.name].push(o.decode(s,t)):o.map?(s=o.decode(s,t),u[o.name].set(s[0],s[1])):(u[o.name]=o.decode(s,t),o.oneof&&(null!==(s=u[o.oneof.name])&&s!==o.name&&(u[s]=null),u[o.oneof.name]=o.name));else switch(s){case e.WIRE_TYPES.VARINT:t.readVarint32();break;case e.WIRE_TYPES.BITS32:t.offset+=4;break;case e.WIRE_TYPES.BITS64:t.offset+=8;break;case e.WIRE_TYPES.LDELIM:o=t.readVarint32(),t.offset+=o;break;case e.WIRE_TYPES.STARTGROUP:for(;i(a,t););break;default:throw Error("Illegal wire type for unknown field "+a+" in "+this.toString(!0)+"#decode: "+s)}}for(t=0,r=this._fields.length;t<r;++t)if(null===u[(o=this._fields[t]).name])if("proto3"===this.syntax)u[o.name]=o.defaultValue;else{if(o.required)throw(t=Error("Missing at least one required field for "+this.toString(!0)+": "+o.name)).decoded=u,t;e.populateDefaults&&null!==o.defaultValue&&(u[o.name]=o.defaultValue)}return u},o.Message=x;var v=function(t,r,i,n,o,s,f,u,h,l){a.call(this,t,r,s),this.className="Message.Field",this.required="required"===i,this.repeated="repeated"===i,this.map="map"===i,this.keyType=n||null,this.type=o,this.resolvedType=null,this.id=f,this.options=u||{},this.defaultValue=null,this.oneof=h||null,this.syntax=l||"proto2",this.originalName=this.name,this.keyElement=this.element=null,!this.builder.options.convertFieldsToCamelCase||this instanceof x.ExtensionField||(this.name=e.Util.toCamelCase(this.name))};(p=v.prototype=h()(a.prototype)).build=function(){this.element=new d(this.type,this.resolvedType,!1,this.syntax,this.name),this.map&&(this.keyElement=new d(this.keyType,void 0,!0,this.syntax,this.name)),"proto3"!==this.syntax||this.repeated||this.map?void 0!==this.options.default&&(this.defaultValue=this.verifyValue(this.options.default)):this.defaultValue=d.defaultFieldValue(this.type)},p.verifyValue=function(t,r){function i(e,t){throw Error("Illegal value for "+o.toString(!0)+" of type "+o.type.name+": "+e+" ("+t+")")}r=r||!1;var n,o=this;if(null===t)return this.required&&i(y()(t),"required"),"proto3"===this.syntax&&this.type!==e.TYPES.message&&i(y()(t),"proto3 field without field presence cannot be null"),null;if(this.repeated&&!r){c()(t)||(t=[t]);var s=[];for(n=0;n<t.length;n++)s.push(this.element.verifyValue(t[n]));return s}return this.map&&!r?t instanceof e.Map?t:(t instanceof Object||i(y()(t),"expected ProtoBuf.Map or raw object for map field"),new e.Map(this,t)):(!this.repeated&&c()(t)&&i(y()(t),"no array expected"),this.element.verifyValue(t))},p.hasWirePresence=function(t,r){if("proto3"!==this.syntax)return null!==t;if(this.oneof&&r[this.oneof.name]===this.name)return!0;switch(this.type){case e.TYPES.int32:case e.TYPES.sint32:case e.TYPES.sfixed32:case e.TYPES.uint32:case e.TYPES.fixed32:return 0!==t;case e.TYPES.int64:case e.TYPES.sint64:case e.TYPES.sfixed64:case e.TYPES.uint64:case e.TYPES.fixed64:return 0!==t.low||0!==t.high;case e.TYPES.bool:return t;case e.TYPES.float:case e.TYPES.double:return 0!==t;case e.TYPES.string:return 0<t.length;case e.TYPES.bytes:return 0<t.remaining();case e.TYPES.enum:return 0!==t;case e.TYPES.message:return null!==t;default:return!0}},p.encode=function(r,i,n){if(null===this.type||"object"!==y()(this.type))throw Error("[INTERNAL] Unresolved type in "+this.toString(!0)+": "+this.type);if(null===r||this.repeated&&0===r.length)return i;try{var o;if(this.repeated)if(this.options.packed&&0<=e.PACKABLE_WIRE_TYPES.indexOf(this.type.wireType)){i.writeVarint32(this.id<<3|e.WIRE_TYPES.LDELIM),i.ensureCapacity(i.offset+=1);var s=i.offset;for(o=0;o<r.length;o++)this.element.encodeValue(this.id,r[o],i);var a=i.offset-s,f=t.calculateVarint32(a);if(1<f){var u=i.slice(s,i.offset);s+=f-1,i.offset=s,i.append(u)}i.writeVarint32(a,s-f)}else for(o=0;o<r.length;o++)i.writeVarint32(this.id<<3|this.type.wireType),this.element.encodeValue(this.id,r[o],i);else this.map?r.forEach((function(r,n,o){o=t.calculateVarint32(8|this.keyType.wireType)+this.keyElement.calculateLength(1,n)+t.calculateVarint32(16|this.type.wireType)+this.element.calculateLength(2,r),i.writeVarint32(this.id<<3|e.WIRE_TYPES.LDELIM),i.writeVarint32(o),i.writeVarint32(8|this.keyType.wireType),this.keyElement.encodeValue(1,n,i),i.writeVarint32(16|this.type.wireType),this.element.encodeValue(2,r,i)}),this):this.hasWirePresence(r,n)&&(i.writeVarint32(this.id<<3|this.type.wireType),this.element.encodeValue(this.id,r,i))}catch(e){throw Error("Illegal value for "+this.toString(!0)+": "+r+" ("+e+")")}return i},p.calculate=function(r,i){if(r=this.verifyValue(r),null===this.type||"object"!==y()(this.type))throw Error("[INTERNAL] Unresolved type in "+this.toString(!0)+": "+this.type);if(null===r||this.repeated&&0===r.length)return 0;var n=0;try{var o,s;if(this.repeated)if(this.options.packed&&0<=e.PACKABLE_WIRE_TYPES.indexOf(this.type.wireType)){for(n+=t.calculateVarint32(this.id<<3|e.WIRE_TYPES.LDELIM),o=s=0;o<r.length;o++)s+=this.element.calculateLength(this.id,r[o]);n+=t.calculateVarint32(s),n+=s}else for(o=0;o<r.length;o++)n+=t.calculateVarint32(this.id<<3|this.type.wireType),n+=this.element.calculateLength(this.id,r[o]);else this.map?r.forEach((function(r,i,o){r=t.calculateVarint32(8|this.keyType.wireType)+this.keyElement.calculateLength(1,i)+t.calculateVarint32(16|this.type.wireType)+this.element.calculateLength(2,r),n+=t.calculateVarint32(this.id<<3|e.WIRE_TYPES.LDELIM),n+=t.calculateVarint32(r),n+=r}),this):this.hasWirePresence(r,i)&&(n+=t.calculateVarint32(this.id<<3|this.type.wireType),n+=this.element.calculateLength(this.id,r))}catch(e){throw Error("Illegal value for "+this.toString(!0)+": "+r+" ("+e+")")}return n},p.decode=function(t,r,i){if(!(!this.map&&t===this.type.wireType||!i&&this.repeated&&this.options.packed&&t===e.WIRE_TYPES.LDELIM||this.map&&t===e.WIRE_TYPES.LDELIM))throw Error("Illegal wire type for field "+this.toString(!0)+": "+t+" ("+this.type.wireType+" expected)");if(t===e.WIRE_TYPES.LDELIM&&this.repeated&&this.options.packed&&0<=e.PACKABLE_WIRE_TYPES.indexOf(this.type.wireType)&&!i){for(t=r.readVarint32(),t=r.offset+t,i=[];r.offset<t;)i.push(this.decode(this.type.wireType,r,!0));return i}if(this.map){var n=d.defaultFieldValue(this.keyType);if(i=d.defaultFieldValue(this.type),t=r.readVarint32(),r.remaining()<t)throw Error("Illegal number of bytes for "+this.toString(!0)+": "+t+" required but got only "+r.remaining());var o=r.clone();for(o.limit=o.offset+t,r.offset+=t;0<o.remaining();)if(t=7&(r=o.readVarint32()),1==(r>>>=3))n=this.keyElement.decode(o,t,r);else{if(2!==r)throw Error("Unexpected tag in map field key/value submessage");i=this.element.decode(o,t,r)}return[n,i]}return this.element.decode(r,t,this.id)},o.Message.Field=v,(p=function(e,t,r,i,n,o,s){v.call(this,e,t,r,null,i,n,o,s)}).prototype=h()(v.prototype),o.Message.ExtensionField=p,o.Message.OneOf=function(e,t,r){a.call(this,e,t,r),this.fields=[]};var b=function(e,t,r,i,n){l.call(this,e,t,r,i,n),this.className="Enum",this.object=null};b.getName=function(e,t){for(var r,i=s()(e),n=0;n<i.length;++n)if(e[r=i[n]]===t)return r;return null},(b.prototype=h()(l.prototype)).build=function(t){if(this.object&&!t)return this.object;t=new e.Builder.Enum;for(var r=this.getChildren(b.Value),i=0,n=r.length;i<n;++i)t[r[i].name]=r[i].id;return f.a&&f()(t,"$options",{value:this.buildOpt(),enumerable:!1}),this.object=t},o.Enum=b,(p=function(e,t,r,i){a.call(this,e,t,r),this.className="Enum.Value",this.id=i}).prototype=h()(a.prototype),o.Enum.Value=p;var m=function(e,t,r,i){a.call(this,e,t,r),this.field=i};m.prototype=h()(a.prototype),o.Extension=m;var w=function(e,t,r,i){l.call(this,e,t,r,i),this.className="Service",this.clazz=null};(w.prototype=h()(l.prototype)).build=function(r){return this.clazz&&!r?this.clazz:this.clazz=function(e,r){for(var i=function(t){e.Builder.Service.call(this),this.rpcImpl=t||function(e,t,r){setTimeout(r.bind(this,Error("Not implemented, see: https://github.com/dcodeIO/ProtoBuf.js/wiki/Services")),0)}},n=i.prototype=h()(e.Builder.Service.prototype),o=r.getChildren(e.Reflect.Service.RPCMethod),s=0;s<o.length;s++)!function(e){n[e.name]=function(i,n){try{try{i=e.resolvedRequestType.clazz.decode(t.wrap(i))}catch(e){if(!(e instanceof TypeError))throw e}if(null===i||"object"!==y()(i))throw Error("Illegal arguments");i instanceof e.resolvedRequestType.clazz||(i=new e.resolvedRequestType.clazz(i)),this.rpcImpl(e.fqn(),i,(function(t,i){if(t)n(t);else{null===i&&(i="");try{i=e.resolvedResponseType.clazz.decode(i)}catch(e){}i&&i instanceof e.resolvedResponseType.clazz?n(null,i):n(Error("Illegal response type received in service method "+r.name+"#"+e.name))}}))}catch(e){setTimeout(n.bind(this,e),0)}},i[e.name]=function(t,r,n){new i(t)[e.name](r,n)},f.a&&(f()(i[e.name],"$options",{value:e.buildOpt()}),f()(n[e.name],"$options",{value:i[e.name].$options}))}(o[s]);return f.a&&(f()(i,"$options",{value:r.buildOpt()}),f()(n,"$options",{value:i.$options}),f()(i,"$type",{value:r}),f()(n,"$type",{value:r})),i}(e,this)},o.Service=w;var E=function(e,t,r,i){a.call(this,e,t,r),this.className="Service.Method",this.options=i||{}};return(E.prototype=h()(a.prototype)).buildOpt=u.buildOpt,o.Service.Method=E,(u=function(e,t,r,i,n,o,s,a){E.call(this,e,t,r,a),this.className="Service.RPCMethod",this.requestName=i,this.responseName=n,this.requestStream=o,this.responseStream=s,this.resolvedResponseType=this.resolvedRequestType=null}).prototype=h()(E.prototype),o.Service.RPCMethod=u,o}(a),a.Builder=function(e,t,i){var o=function(e){this.ptr=this.ns=new i.Namespace(this,null,""),this.resolved=!1,this.result=null,this.files={},this.importRoot=null,this.options=e||{}},a=o.prototype;return o.isMessage=function(e){return"string"==typeof e.name&&void 0===e.values&&void 0===e.rpc},o.isMessageField=function(e){return"string"==typeof e.rule&&"string"==typeof e.name&&"string"==typeof e.type&&void 0!==e.id},o.isEnum=function(e){return!("string"!=typeof e.name||void 0===e.values||!c()(e.values)||0===e.values.length)},o.isService=function(e){return!("string"!=typeof e.name||"object"!==y()(e.rpc)||!e.rpc)},o.isExtend=function(e){return"string"==typeof e.ref},a.reset=function(){return this.ptr=this.ns,this},a.define=function(e){if("string"!=typeof e||!t.TYPEREF.test(e))throw Error("illegal namespace: "+e);return e.split(".").forEach((function(e){var t=this.ptr.getChild(e);null===t&&this.ptr.addChild(t=new i.Namespace(this,this.ptr,e)),this.ptr=t}),this),this},a.create=function(t){if(!t)return this;if(c()(t)){if(0===t.length)return this;t=t.slice()}else t=[t];for(var r=[t];0<r.length;){if(t=r.pop(),!c()(t))throw Error("not a valid namespace: "+n()(t));for(;0<t.length;){var a=t.shift();if(o.isMessage(a)){var f=new i.Message(this,this.ptr,a.name,a.options,a.isGroup,a.syntax),u={};a.oneofs&&s()(a.oneofs).forEach((function(e){f.addChild(u[e]=new i.Message.OneOf(this,f,e))}),this),a.fields&&a.fields.forEach((function(e){if(null!==f.getChild(0|e.id))throw Error("duplicate or invalid field id in "+f.name+": "+e.id);if(e.options&&"object"!==y()(e.options))throw Error("illegal field options in "+f.name+"#"+e.name);var t=null;if("string"==typeof e.oneof&&!(t=u[e.oneof]))throw Error("illegal oneof in "+f.name+"#"+e.name+": "+e.oneof);e=new i.Message.Field(this,f,e.rule,e.keytype,e.type,e.name,e.id,e.options,t,a.syntax),t&&t.fields.push(e),f.addChild(e)}),this);var h=[];if(a.enums&&a.enums.forEach((function(e){h.push(e)})),a.messages&&a.messages.forEach((function(e){h.push(e)})),a.services&&a.services.forEach((function(e){h.push(e)})),a.extensions&&(f.extensions="number"==typeof a.extensions[0]?[a.extensions]:a.extensions),this.ptr.addChild(f),0<h.length){r.push(t),t=h,h=null,this.ptr=f,f=null;continue}h=null}else if(o.isEnum(a))f=new i.Enum(this,this.ptr,a.name,a.options,a.syntax),a.values.forEach((function(e){f.addChild(new i.Enum.Value(this,f,e.name,e.id))}),this),this.ptr.addChild(f);else if(o.isService(a))f=new i.Service(this,this.ptr,a.name,a.options),s()(a.rpc).forEach((function(e){var t=a.rpc[e];f.addChild(new i.Service.RPCMethod(this,f,e,t.request,t.response,!!t.request_stream,!!t.response_stream,t.options))}),this),this.ptr.addChild(f);else{if(!o.isExtend(a))throw Error("not a valid definition: "+n()(a));if(f=this.ptr.resolve(a.ref,!0))a.fields.forEach((function(t){if(null!==f.getChild(0|t.id))throw Error("duplicate extended field id in "+f.name+": "+t.id);if(f.extensions){var r=!1;if(f.extensions.forEach((function(e){t.id>=e[0]&&t.id<=e[1]&&(r=!0)})),!r)throw Error("illegal extended field id in "+f.name+": "+t.id+" (not within valid ranges)")}var n=t.name;this.options.convertFieldsToCamelCase&&(n=e.Util.toCamelCase(n)),n=new i.Message.ExtensionField(this,f,t.rule,t.type,this.ptr.fqn()+"."+n,t.id,t.options);var o=new i.Extension(this,this.ptr,t.name,n);n.extension=o,this.ptr.addChild(o),f.addChild(n)}),this);else if(!/\.?google\.protobuf\./.test(a.ref))throw Error("extended message "+a.ref+" is not defined")}f=a=null}t=null,this.ptr=this.ptr.parent}return this.resolved=!1,this.result=null,this},a.import=function(t,i){var n="/";if("string"==typeof i){if(e.Util.IS_NODE&&(i=r(122).resolve(i)),!0===this.files[i])return this.reset();this.files[i]=!0}else if("object"===y()(i)){var o=i.root;if(e.Util.IS_NODE&&(o=r(122).resolve(o)),(0<=o.indexOf("\\")||0<=i.file.indexOf("\\"))&&(n="\\"),o=o+n+i.file,!0===this.files[o])return this.reset();this.files[o]=!0}if(t.imports&&0<t.imports.length){var a=!1;"object"===y()(i)?(this.importRoot=i.root,a=!0,o=this.importRoot,i=i.file,(0<=o.indexOf("\\")||0<=i.indexOf("\\"))&&(n="\\")):"string"==typeof i?this.importRoot?o=this.importRoot:0<=i.indexOf("/")?""===(o=i.replace(/\/[^\/]*$/,""))&&(o="/"):0<=i.indexOf("\\")?(o=i.replace(/\\[^\\]*$/,""),n="\\"):o=".":o=null;for(var f=0;f<t.imports.length;f++)if("string"==typeof t.imports[f]){if(!o)throw Error("cannot determine import root");var u=t.imports[f];if("google/protobuf/descriptor.proto"!==u&&(u=o+n+u,!0!==this.files[u])){/\.proto$/i.test(u)&&!e.DotProto&&(u=u.replace(/\.proto$/,".json"));var h=e.Util.fetch(u);if(null===h)throw Error("failed to import '"+u+"' in '"+i+"': file not found");/\.json$/i.test(u)?this.import(JSON.parse(h+""),u):this.import(e.DotProto.Parser.parse(h),u)}}else i?/\.(\w+)$/.test(i)?this.import(t.imports[f],i.replace(/^(.+)\.(\w+)$/,(function(e,t,r){return t+"_import"+f+"."+r}))):this.import(t.imports[f],i+"_import"+f):this.import(t.imports[f]);a&&(this.importRoot=null)}t.package&&this.define(t.package),t.syntax&&function e(t){t.messages&&t.messages.forEach((function(r){r.syntax=t.syntax,e(r)})),t.enums&&t.enums.forEach((function(e){e.syntax=t.syntax}))}(t);var l=this.ptr;return t.options&&s()(t.options).forEach((function(e){l.options[e]=t.options[e]})),t.messages&&(this.create(t.messages),this.ptr=l),t.enums&&(this.create(t.enums),this.ptr=l),t.services&&(this.create(t.services),this.ptr=l),t.extends&&this.create(t.extends),this.reset()},a.resolveAll=function(){var r;if(null===this.ptr||"object"===y()(this.ptr.type))return this;if(this.ptr instanceof i.Namespace)this.ptr.children.forEach((function(e){this.ptr=e,this.resolveAll()}),this);else if(this.ptr instanceof i.Message.Field){if(t.TYPE.test(this.ptr.type))this.ptr.type=e.TYPES[this.ptr.type];else{if(!t.TYPEREF.test(this.ptr.type))throw Error("illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.type);if(!(r=(this.ptr instanceof i.Message.ExtensionField?this.ptr.extension.parent:this.ptr.parent).resolve(this.ptr.type,!0)))throw Error("unresolvable type reference in "+this.ptr.toString(!0)+": "+this.ptr.type);if(this.ptr.resolvedType=r,r instanceof i.Enum){if(this.ptr.type=e.TYPES.enum,"proto3"===this.ptr.syntax&&"proto3"!==r.syntax)throw Error("proto3 message cannot reference proto2 enum")}else{if(!(r instanceof i.Message))throw Error("illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.type);this.ptr.type=r.isGroup?e.TYPES.group:e.TYPES.message}}if(this.ptr.map){if(!t.TYPE.test(this.ptr.keyType))throw Error("illegal key type for map field in "+this.ptr.toString(!0)+": "+this.ptr.keyType);this.ptr.keyType=e.TYPES[this.ptr.keyType]}}else if(this.ptr instanceof e.Reflect.Service.Method){if(!(this.ptr instanceof e.Reflect.Service.RPCMethod))throw Error("illegal service type in "+this.ptr.toString(!0));if(!((r=this.ptr.parent.resolve(this.ptr.requestName,!0))&&r instanceof e.Reflect.Message))throw Error("Illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.requestName);if(this.ptr.resolvedRequestType=r,!((r=this.ptr.parent.resolve(this.ptr.responseName,!0))&&r instanceof e.Reflect.Message))throw Error("Illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.responseName);this.ptr.resolvedResponseType=r}else if(!(this.ptr instanceof e.Reflect.Message.OneOf||this.ptr instanceof e.Reflect.Extension||this.ptr instanceof e.Reflect.Enum.Value))throw Error("illegal object in namespace: "+y()(this.ptr)+": "+this.ptr);return this.reset()},a.build=function(e){if(this.reset(),this.resolved||(this.resolveAll(),this.resolved=!0,this.result=null),null===this.result&&(this.result=this.ns.build()),!e)return this.result;e="string"==typeof e?e.split("."):e;for(var t=this.result,r=0;r<e.length;r++){if(!t[e[r]]){t=null;break}t=t[e[r]]}return t},a.lookup=function(e,t){return e?this.ns.resolve(e,t):this.ns},a.toString=function(){return"Builder"},o.Message=function(){},o.Enum=function(){},o.Service=function(){},o}(a,a.Lang,a.Reflect),a.Map=function(e,t){function r(e){var t=0;return{next:function(){return t<e.length?{done:!1,value:e[t++]}:{done:!0}}}}var i=function(e,r){if(!e.map)throw Error("field is not a map");if(this.field=e,this.keyElem=new t.Element(e.keyType,null,!0,e.syntax),this.valueElem=new t.Element(e.type,e.resolvedType,!1,e.syntax),this.map={},f()(this,"size",{get:function(){return s()(this.map).length}}),r)for(var i=s()(r),n=0;n<i.length;n++){var o=this.keyElem.valueFromString(i[n]),a=this.valueElem.verifyValue(r[i[n]]);this.map[this.keyElem.valueToString(o)]={key:o,value:a}}},n=i.prototype;return n.clear=function(){this.map={}},n.delete=function(e){var t=(e=this.keyElem.valueToString(this.keyElem.verifyValue(e)))in this.map;return delete this.map[e],t},n.entries=function(){for(var e,t=[],i=s()(this.map),n=0;n<i.length;n++)t.push([(e=this.map[i[n]]).key,e.value]);return r(t)},n.keys=function(){for(var e=[],t=s()(this.map),i=0;i<t.length;i++)e.push(this.map[t[i]].key);return r(e)},n.values=function(){for(var e=[],t=s()(this.map),i=0;i<t.length;i++)e.push(this.map[t[i]].value);return r(e)},n.forEach=function(e,t){for(var r,i=s()(this.map),n=0;n<i.length;n++)e.call(t,(r=this.map[i[n]]).value,r.key,this)},n.set=function(e,t){var r=this.keyElem.verifyValue(e),i=this.valueElem.verifyValue(t);return this.map[this.keyElem.valueToString(r)]={key:r,value:i},this},n.get=function(e){return(e=this.keyElem.valueToString(this.keyElem.verifyValue(e)))in this.map?this.map[e].value:void 0},n.has=function(e){return this.keyElem.valueToString(this.keyElem.verifyValue(e))in this.map},i}(0,a.Reflect),a.loadProto=function(e,t,r){return("string"==typeof t||t&&"string"==typeof t.file&&"string"==typeof t.root)&&(r=t,t=void 0),a.loadJson(a.DotProto.Parser.parse(e),t,r)},a.protoFromString=a.loadProto,a.loadProtoFile=function(e,t,r){if(t&&"object"===y()(t)?(r=t,t=null):t&&"function"==typeof t||(t=null),t)return a.Util.fetch("string"==typeof e?e:e.root+"/"+e.file,(function(i){if(null===i)t(Error("Failed to fetch file"));else try{t(null,a.loadProto(i,r,e))}catch(e){t(e)}}));var i=a.Util.fetch("object"===y()(e)?e.root+"/"+e.file:e);return null===i?null:a.loadProto(i,r,e)},a.protoFromFile=a.loadProtoFile,a.newBuilder=function(e){return void 0===(e=e||{}).convertFieldsToCamelCase&&(e.convertFieldsToCamelCase=a.convertFieldsToCamelCase),void 0===e.populateAccessors&&(e.populateAccessors=a.populateAccessors),new a.Builder(e)},a.loadJson=function(e,t,r){return("string"==typeof t||t&&"string"==typeof t.file&&"string"==typeof t.root)&&(r=t,t=null),t&&"object"===y()(t)||(t=a.newBuilder()),"string"==typeof e&&(e=JSON.parse(e)),t.import(e,r),t.resolveAll(),t},a.loadJsonFile=function(e,t,r){if(t&&"object"===y()(t)?(r=t,t=null):t&&"function"==typeof t||(t=null),t)return a.Util.fetch("string"==typeof e?e:e.root+"/"+e.file,(function(i){if(null===i)t(Error("Failed to fetch file"));else try{t(null,a.loadJson(JSON.parse(i),r,e))}catch(e){t(e)}}));var i=a.Util.fetch("object"===y()(e)?e.root+"/"+e.file:e);return null===i?null:a.loadJson(JSON.parse(i),r,e)},a}))}).call(this,r(95),r(96)(e))},function(e,t,r){var i=r(5),n=i.JSON||(i.JSON={stringify:JSON.stringify});e.exports=function(e){return n.stringify.apply(n,arguments)}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,r){},function(e,t,r){e.exports=r(50)("native-function-to-string",Function.toString)},function(e,t,r){var i=r(52),n=r(45),o=r(55),s={};r(20)(s,r(4)("iterator"),(function(){return this})),e.exports=function(e,t,r){e.prototype=i(s,{next:n(1,r)}),o(e,t+" Iterator")}},function(e,t,r){var i=r(13),n=r(17),o=r(53);e.exports=r(11)?Object.defineProperties:function(e,t){n(e);for(var r,s=o(t),a=s.length,f=0;a>f;)i.f(e,r=s[f++],t[r]);return e}},function(e,t,r){var i=r(10).document;e.exports=i&&i.documentElement},function(e,t,r){r(155),e.exports=r(5).Object.keys},function(e,t,r){var i=r(72),n=r(57);r(159)("keys",(function(){return function(e){return n(i(e))}}))},function(e,t,r){var i=r(47);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"===i(e)?e.split(""):Object(e)}},function(e,t,r){var i=r(39),n=r(106),o=r(158);e.exports=function(e){return function(t,r,s){var a,f=i(t),u=n(f.length),h=o(s,u);if(e&&r!=r){for(;u>h;)if((a=f[h++])!==a)return!0}else for(;u>h;h++)if((e||h in f)&&f[h]===r)return e||h||0;return!e&&-1}}},function(e,t,r){var i=r(73),n=Math.max,o=Math.min;e.exports=function(e,t){return(e=i(e))<0?n(e+t,0):o(e,t)}},function(e,t,r){var i=r(12),n=r(5),o=r(40);e.exports=function(e,t){var r=(n.Object||{})[e]||Object[e],s={};s[e]=t(r),i(i.S+i.F*o((function(){r(1)})),"Object",s)}},function(e,t,r){r(161);var i=r(5).Object;e.exports=function(e,t,r){return i.defineProperty(e,t,r)}},function(e,t,r){var i=r(12);i(i.S+i.F*!r(25),"Object",{defineProperty:r(26).f})},function(e,t,r){var i=r(29),n=r(38);e.exports=function(e){return function(t,r){var o,s,a=String(n(t)),f=i(r),u=a.length;return f<0||f>=u?e?"":void 0:(o=a.charCodeAt(f))<55296||o>56319||f+1===u||(s=a.charCodeAt(f+1))<56320||s>57343?e?a.charAt(f):o:e?a.slice(f,f+2):s-56320+(o-55296<<10)+65536}}},function(e,t,r){var i=r(82);r(23)({target:"RegExp",proto:!0,forced:i!==/./.exec},{exec:i})},function(e,t,r){r(165);var i=r(5).Object;e.exports=function(e,t){return i.create(e,t)}},function(e,t,r){var i=r(12);i(i.S,"Object",{create:r(83)})},function(e,t,r){r(11)&&"g"!==/./g.flags&&r(13).f(RegExp.prototype,"flags",{configurable:!0,get:r(64)})},function(e,t,r){r(168),e.exports=r(5).Array.isArray},function(e,t,r){var i=r(12);i(i.S,"Array",{isArray:r(111)})},function(e,t,r){r(170),e.exports=r(5).parseFloat},function(e,t,r){var i=r(12),n=r(171);i(i.G+i.F*(parseFloat!==n),{parseFloat:n})},function(e,t,r){var i=r(7).parseFloat,n=r(112).trim;e.exports=1/i(r(87)+"-0")!=-1/0?function(e){var t=n(String(e),3),r=i(t);return 0===r&&"-"===t.charAt(0)?-0:r}:i},function(e,t,r){r(173),e.exports=r(5).parseInt},function(e,t,r){var i=r(12),n=r(174);i(i.G+i.F*(parseInt!==n),{parseInt:n})},function(e,t,r){var i=r(7).parseInt,n=r(112).trim,o=r(87),s=/^[-+]?0[xX]/;e.exports=8!==i(o+"08")||22!==i(o+"0x16")?function(e,t){var r=n(String(e),3);return i(r,t>>>0||(s.test(r)?16:10))}:i},function(e,t,r){e.exports=r(176)},function(e,t,r){r(113),r(116),e.exports=r(89).f("iterator")},function(e,t,r){var i=r(73),n=r(56);e.exports=function(e){return function(t,r){var o,s,a=String(n(t)),f=i(r),u=a.length;return f<0||f>=u?e?"":void 0:(o=a.charCodeAt(f))<55296||o>56319||f+1===u||(s=a.charCodeAt(f+1))<56320||s>57343?e?a.charAt(f):o:e?a.slice(f,f+2):s-56320+(o-55296<<10)+65536}}},function(e,t,r){var i=r(83),n=r(61),o=r(66),s={};r(32)(s,r(14)("iterator"),(function(){return this})),e.exports=function(e,t,r){e.prototype=i(s,{next:n(1,r)}),o(e,t+" Iterator")}},function(e,t,r){var i=r(31),n=r(72),o=r(74)("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=n(e),i(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},function(e,t,r){var i=r(181),n=r(182),o=r(49),s=r(39);e.exports=r(114)(Array,"Array",(function(e,t){this._t=s(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,r=this._i++;return!e||r>=e.length?(this._t=void 0,n(1)):n(0,"keys"===t?r:"values"===t?e[r]:[r,e[r]])}),"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,r){r(184),r(119),r(189),r(190),e.exports=r(5).Symbol},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){var i,n
/**
       * @license long.js (c) 2013 Daniel Wirtz <<EMAIL>>
       * Released under the Apache License, Version 2.0
       * see: https://github.com/dcodeIO/long.js for details
       */;void 0===(n="function"==typeof(i=function(){function e(e,t,r){this.low=0|e,this.high=0|t,this.unsigned=!!r}function t(e){return!0===(e&&e.__isLong__)}e.prototype.__isLong__,Object.defineProperty(e.prototype,"__isLong__",{value:!0,enumerable:!1,configurable:!1}),e.isLong=t;var r={},i={};function n(e,t){var n,o,a;return t?(a=0<=(e>>>=0)&&e<256)&&(o=i[e])?o:(n=s(e,(0|e)<0?-1:0,!0),a&&(i[e]=n),n):(a=-128<=(e|=0)&&e<128)&&(o=r[e])?o:(n=s(e,e<0?-1:0,!1),a&&(r[e]=n),n)}function o(e,t){if(isNaN(e)||!isFinite(e))return t?x:p;if(t){if(e<0)return x;if(e>=l)return m}else{if(e<=-c)return w;if(e+1>=c)return b}return e<0?o(-e,t).neg():s(e%h|0,e/h|0,t)}function s(t,r,i){return new e(t,r,i)}e.fromInt=n,e.fromNumber=o,e.fromBits=s;var a=Math.pow;function f(e,t,r){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return p;if("number"==typeof t?(r=t,t=!1):t=!!t,(r=r||10)<2||36<r)throw RangeError("radix");var i;if((i=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===i)return f(e.substring(1),t,r).neg();for(var n=o(a(r,8)),s=p,u=0;u<e.length;u+=8){var h=Math.min(8,e.length-u),l=parseInt(e.substring(u,u+h),r);if(h<8){var c=o(a(r,h));s=s.mul(c).add(o(l))}else s=(s=s.mul(n)).add(o(l))}return s.unsigned=t,s}function u(t){return t instanceof e?t:"number"==typeof t?o(t):"string"==typeof t?f(t):s(t.low,t.high,t.unsigned)}e.fromString=f,e.fromValue=u;var h=4294967296,l=h*h,c=l/2,d=n(1<<24),p=n(0);e.ZERO=p;var x=n(0,!0);e.UZERO=x;var g=n(1);e.ONE=g;var v=n(1,!0);e.UONE=v;var y=n(-1);e.NEG_ONE=y;var b=s(-1,2147483647,!1);e.MAX_VALUE=b;var m=s(-1,-1,!0);e.MAX_UNSIGNED_VALUE=m;var w=s(0,-2147483648,!1);e.MIN_VALUE=w;var E=e.prototype;return E.toInt=function(){return this.unsigned?this.low>>>0:this.low},E.toNumber=function(){return this.unsigned?(this.high>>>0)*h+(this.low>>>0):this.high*h+(this.low>>>0)},E.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(w)){var t=o(e),r=this.div(t),i=r.mul(t).sub(this);return r.toString(e)+i.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var n=o(a(e,6),this.unsigned),s=this,f="";;){var u=s.div(n),h=(s.sub(u.mul(n)).toInt()>>>0).toString(e);if((s=u).isZero())return h+f;for(;h.length<6;)h="0"+h;f=""+h+f}},E.getHighBits=function(){return this.high},E.getHighBitsUnsigned=function(){return this.high>>>0},E.getLowBits=function(){return this.low},E.getLowBitsUnsigned=function(){return this.low>>>0},E.getNumBitsAbs=function(){if(this.isNegative())return this.eq(w)?64:this.neg().getNumBitsAbs();for(var e=0!==this.high?this.high:this.low,t=31;t>0&&0==(e&1<<t);t--);return 0!==this.high?t+33:t+1},E.isZero=function(){return 0===this.high&&0===this.low},E.isNegative=function(){return!this.unsigned&&this.high<0},E.isPositive=function(){return this.unsigned||this.high>=0},E.isOdd=function(){return 1==(1&this.low)},E.isEven=function(){return 0==(1&this.low)},E.equals=function(e){return t(e)||(e=u(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&this.high===e.high&&this.low===e.low},E.eq=E.equals,E.notEquals=function(e){return!this.eq(e)},E.neq=E.notEquals,E.lessThan=function(e){return this.comp(e)<0},E.lt=E.lessThan,E.lessThanOrEqual=function(e){return this.comp(e)<=0},E.lte=E.lessThanOrEqual,E.greaterThan=function(e){return this.comp(e)>0},E.gt=E.greaterThan,E.greaterThanOrEqual=function(e){return this.comp(e)>=0},E.gte=E.greaterThanOrEqual,E.compare=function(e){if(t(e)||(e=u(e)),this.eq(e))return 0;var r=this.isNegative(),i=e.isNegative();return r&&!i?-1:!r&&i?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},E.comp=E.compare,E.negate=function(){return!this.unsigned&&this.eq(w)?w:this.not().add(g)},E.neg=E.negate,E.add=function(e){t(e)||(e=u(e));var r=this.high>>>16,i=65535&this.high,n=this.low>>>16,o=65535&this.low,a=e.high>>>16,f=65535&e.high,h=e.low>>>16,l=0,c=0,d=0,p=0;return d+=(p+=o+(65535&e.low))>>>16,c+=(d+=n+h)>>>16,l+=(c+=i+f)>>>16,l+=r+a,s((d&=65535)<<16|(p&=65535),(l&=65535)<<16|(c&=65535),this.unsigned)},E.subtract=function(e){return t(e)||(e=u(e)),this.add(e.neg())},E.sub=E.subtract,E.multiply=function(e){if(this.isZero())return p;if(t(e)||(e=u(e)),e.isZero())return p;if(this.eq(w))return e.isOdd()?w:p;if(e.eq(w))return this.isOdd()?w:p;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(d)&&e.lt(d))return o(this.toNumber()*e.toNumber(),this.unsigned);var r=this.high>>>16,i=65535&this.high,n=this.low>>>16,a=65535&this.low,f=e.high>>>16,h=65535&e.high,l=e.low>>>16,c=65535&e.low,x=0,g=0,v=0,y=0;return v+=(y+=a*c)>>>16,g+=(v+=n*c)>>>16,v&=65535,g+=(v+=a*l)>>>16,x+=(g+=i*c)>>>16,g&=65535,x+=(g+=n*l)>>>16,g&=65535,x+=(g+=a*h)>>>16,x+=r*c+i*l+n*h+a*f,s((v&=65535)<<16|(y&=65535),(x&=65535)<<16|(g&=65535),this.unsigned)},E.mul=E.multiply,E.divide=function(e){if(t(e)||(e=u(e)),e.isZero())throw Error("division by zero");if(this.isZero())return this.unsigned?x:p;var r,i,n;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return x;if(e.gt(this.shru(1)))return v;n=x}else{if(this.eq(w))return e.eq(g)||e.eq(y)?w:e.eq(w)?g:(r=this.shr(1).div(e).shl(1)).eq(p)?e.isNegative()?g:y:(i=this.sub(e.mul(r)),n=r.add(i.div(e)));if(e.eq(w))return this.unsigned?x:p;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();n=p}for(i=this;i.gte(e);){r=Math.max(1,Math.floor(i.toNumber()/e.toNumber()));for(var s=Math.ceil(Math.log(r)/Math.LN2),f=s<=48?1:a(2,s-48),h=o(r),l=h.mul(e);l.isNegative()||l.gt(i);)l=(h=o(r-=f,this.unsigned)).mul(e);h.isZero()&&(h=g),n=n.add(h),i=i.sub(l)}return n},E.div=E.divide,E.modulo=function(e){return t(e)||(e=u(e)),this.sub(this.div(e).mul(e))},E.mod=E.modulo,E.not=function(){return s(~this.low,~this.high,this.unsigned)},E.and=function(e){return t(e)||(e=u(e)),s(this.low&e.low,this.high&e.high,this.unsigned)},E.or=function(e){return t(e)||(e=u(e)),s(this.low|e.low,this.high|e.high,this.unsigned)},E.xor=function(e){return t(e)||(e=u(e)),s(this.low^e.low,this.high^e.high,this.unsigned)},E.shiftLeft=function(e){return t(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?s(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):s(0,this.low<<e-32,this.unsigned)},E.shl=E.shiftLeft,E.shiftRight=function(e){return t(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?s(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):s(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},E.shr=E.shiftRight,E.shiftRightUnsigned=function(e){if(t(e)&&(e=e.toInt()),0==(e&=63))return this;var r=this.high;return e<32?s(this.low>>>e|r<<32-e,r>>>e,this.unsigned):s(32===e?r:r>>>e-32,0,this.unsigned)},E.shru=E.shiftRightUnsigned,E.toSigned=function(){return this.unsigned?s(this.low,this.high,!1):this},E.toUnsigned=function(){return this.unsigned?this:s(this.low,this.high,!0)},E.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},E.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24&255,255&e,e>>>8&255,e>>>16&255,e>>>24&255]},E.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t]},e})?i.apply(t,[]):i)||(e.exports=n)},function(e,t){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t){},function(e,t,r){var i=r(7),n=r(135).set,o=i.MutationObserver||i.WebKitMutationObserver,s=i.process,a=i.Promise,f="process"===r(47)(s);e.exports=function(){var e,t,r,u=function(){var i,n;for(f&&(i=s.domain)&&i.exit();e;){n=e.fn,e=e.next;try{n()}catch(i){throw e?r():t=void 0,i}}t=void 0,i&&i.enter()};if(f)r=function(){s.nextTick(u)};else if(!o||i.navigator&&i.navigator.standalone)if(a&&a.resolve){var h=a.resolve(void 0);r=function(){h.then(u)}}else r=function(){n.call(i,u)};else{var l=!0,c=document.createTextNode("");new o(u).observe(c,{characterData:!0}),r=function(){c.data=l=!l}}return function(i){var n={fn:i,next:void 0};t&&(t.next=n),e||(e=n,r()),t=n}}},function(e,t,r){var i=r(7).navigator;e.exports=i&&i.userAgent||""},function(e,t,r){var i=r(32);e.exports=function(e,t,r){for(var n in t)r&&e[n]?e[n]=t[n]:i(e,n,t[n]);return e}},function(e,t,r){var i=r(7),n=r(5),o=r(26),s=r(25),a=r(14)("species");e.exports=function(e){var t="function"==typeof n[e]?n[e]:i[e];s&&t&&!t[a]&&o.f(t,a,{configurable:!0,get:function(){return this}})}},function(e,t,r){var i=r(14)("iterator"),n=!1;try{var o=[7][i]();o.return=function(){n=!0},Array.from(o,(function(){throw 2}))}catch(e){}e.exports=function(e,t){if(!t&&!n)return!1;var r=!1;try{var o=[7],s=o[i]();s.next=function(){return{done:r=!0}},o[i]=function(){return s},e(o)}catch(e){}return r}},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t,r){},function(e,t){},function(e){},function(e,t){},function(e,t,r){},function(e,t){},function(e,t,r){},function(e,t,r){r.r(t),r(146);var i=(r(211),r(212),r(85),r(213),r(1)),o=r.n(i),s=r(68),a=r.n(s),f=(r(84),r(141),r(143),r(0)),u=r.n(f);let h={0:"length",8:"function",22:"Ellipsoid",49:"POSITIVE_INFINITY",69:"GeoOption",104:"getTime",109:"add",125:"min",139:"height",163:"globe",239:"ellipsoid",252:"scale",255:"GEO_VERSION_NUMBER",256:"Cesium Version: 1.63",265:"maxLevel",271:"scene",272:"WGS84",275:"toDegrees",294:"match",304:"addEventListener",315:"viewer",336:"style",362:"Color",374:"name",380:"push",398:"Entity",400:"values",404:"entities",408:"activate",410:"width",469:"slice",484:"forEach",545:"show",601:"splice",604:"pop",619:"Rectangle",638:"fromCssColorString",646:"index",682:"east",684:"north",694:"undefined",723:"indexOf",748:"point",795:"text",978:"sort",984:"showBackground",985:"backgroundColor",986:"horizontalOrigin",987:"verticalOrigin",988:"pixelOffset",990:"outlineWidth",1018:"bold",1163:"_ready",1183:"number",1203:"buffer",1234:"minY",1298:"getValue",1329:"toString",1513:"FLOAT",1674:"_tilingScheme",1711:"url",1883:"_name",1884:"createGuid",1894:"Event",2034:"split",2408:"level",2409:"rectangle",2439:"substring",2519:"GeographicTilingScheme",2530:"getNumberOfYTilesAtLevel",2542:"when",2543:"defer",2552:"resolve",2555:"getTileDataAvailable",2556:"_errorEvent",2557:"_credit",2558:"string",2564:"{s}",2571:"_maxTerrainLevel",2577:"TerrainProvider",2588:"replace",2606:"_surface",2611:"tileXYToRectangle",2621:"reject",2638:"subdomains",2660:"HeightmapTerrainData",2678:"_dataType",2679:"dataType",2680:"GeoTerrainProvider",2681:"INT",2682:"_tileType",2683:"tileType",2684:"HEIGHTMAP",2685:"_urls",2686:"urls",2687:"_urls_length",2688:"_url_i",2689:"_url_step",2690:"_readyPromise",2691:"credit",2692:"_heightmapWidth",2693:"_heightmapHeight",2694:"getEstimatedLevelZeroGeometricErrorForAHeightmap",2695:"_opacity",2696:"opacity",2697:"_maxExtent",2698:"maxExtent",2699:"_topLevel",2700:"_bottomLevel",2701:"bottomLevel",2702:"ready",2705:"requestTileGeometry",2706:"&y=",2707:"&l=",2708:"_proxy",2710:"QUANTIZED_MESH",2711:"_terrainDataStructure",2712:"_skirtHeight",2713:"getLevelMaximumGeometricError",2714:"getvHeightBuffer",2715:"_vHeightBuffer",2716:"transformBuffer",2718:"requestFunction",2719:"GET",2720:"open",2721:"responseType",2722:"arraybuffer",2723:"async",2724:"RequestScheduler",2725:"request",2726:"readyState",2727:"status",2728:"response",2729:"byteLength",2730:"Inflate",2731:"decompress",2732:"_rectangles",2735:"subarray",2749:"int",2750:"float",2751:"quantized-mesh",2752:"heightmap",2768:"onerror",2770:"onload",2806:"_moveEnd",2807:"_changed",3362:"build",3531:"keys",3608:"proxy",3609:"metadata",3610:"roadMetadata",3611:"roadUrl",3612:"labelGraphics",3613:"billboardGraphics",3614:"aotuCollide",3615:"collisionPadding",3616:"serverFirstStyle",3617:"tileCache",3618:"labelCache",3619:"_isInitial",3620:"_latelyGrid",3621:"_UUID",3622:"GEO_WTFS_LABEL_",3623:"_UUIDRoad",3624:"getCacheTile",3625:"removeCacheTile",3626:"getCacheLabel",3627:"oid",3628:"addCacheLabel",3629:"removeCacheLabel",3630:"timestamp",3631:"getTileUrl",3633:"getIcoUrl",3635:"getRoadTileUrl",3636:"_timer",3637:"_tilesToRender",3638:"getTilesToRender",3639:"compareArray",3640:"_queueCall",3641:"_latelyRefreshStamp",3642:"_latelyCollisionStamp",3643:"collisionDetection",3644:"boundBox",3645:"minLevel",3646:"{z}",3647:"{x}",3648:"CutString",3649:"tile",3650:"addCacheTile",3651:"addLabelAndIco",3652:"delaySynchronous",3653:"send",3656:"Name",3657:"initTDT",3658:"pois",3659:"createLabel",3660:"_timer2",3661:"coordinate",3662:"font",3663:"px ",3664:"stringTable",3665:"label",3667:"fontStyle",3670:"fontColor",3671:"shiningColor",3672:"shiningSize",3673:"backgroundPadding",3674:"eyeOffset",3675:"disableDepthTestDistance",3676:"heightReference",3677:"NONE",3678:"symbolID",3679:"billboard",3681:"displayHeight",3684:"priority",3685:"xyz",3686:"debug",3688:"SceneTransforms",3689:"getLabelReact",3696:"collisionBox",3697:"bindEvent",3698:"moveEnd",3699:"changed",3700:"unbindEvent",3702:"entity",3703:"minX",3706:"option optimize_for = LITE_RUNTIME;",3707:"package GEOPOI;",3708:"enum enumGeometryType {",3709:"ePoint = 0;",3710:"eMultiLineString = 1;",3711:"ePolygon = 2;",3712:"} ;",3713:"message PBPOI{",3714:"required uint64 OID = 1;",3715:"required string Name =2;",3716:"repeated double Coordinates =3 [packed=true];",3717:"required enumGeometryType GeometryType = 4;",3718:"optional int32 Interate = 5;",3719:"optional int32 SymbolID = 10  [default = 0];",3720:"optional double DisplayHeight = 11 [default = 32];",3721:"optional uint32 ShiningColor=12 [default =0];",3722:"optional uint32\tFontNameIndex=13 [default =0];",3723:"optional int32\tFontSize=14 [default =18];",3724:"optional uint32\tFontColor=15 [default =0];",3725:"repeated string s = 1;",3726:"message PBPOITile{",3727:"required int64 Version = 1;",3728:"required StringTable StringTable = 3;",3729:"repeated PBPOI POIS = 4;",3730:"ProtoBuf",3732:"loadProto",3733:"GeoPOI.proto",3734:"GEOPOI",3735:"PBPOITile",3736:"enum enumZCoordType {eCloseGround = 0;eCloseSeaSurface = 1;eRelativelyGround = 2;eAbsolute = 3;};",3737:"optional enumZCoordType ZCoordType = 16 [default = eAbsolute];",3738:"message StringTable {",3739:"required int64 TileKey = 2;",3740:"GeoPOI2.proto",3741:"optional int32 Priority = 5;",3742:"repeated int32 Interates =6 [packed=true];",3743:"optional int32 FontStyle=17;",3744:"optional int32 ShiningSize=18;",3745:"GeoPOI3.proto",3747:"version",3748:"Version",3749:"titleKey",3750:"TileKey",3751:"StringTable",3752:"POIS",3753:"OID",3754:"SymbolID",3755:"DisplayHeight",3756:"ShiningColor",3757:"fontNameIndex",3758:"FontNameIndex",3759:"fontSize",3760:"FontSize",3761:"ZCoordType",3763:"geometryType",3764:"Coordinates",3765:"interates",3766:"Interates",3767:"FontStyle",3768:"ShiningSize",3769:"GeoWTFS",3919:"execScript",3922:"getParent",3930:"bufferSize",3931:"bufferType",3932:"resize",3945:"Zlib.Inflate",3946:"Zlib.Inflate.prototype.decompress",3947:"Zlib.Inflate.BufferType",3948:"Zlib.Deflate",3949:"Zlib.Deflate.compress",3950:"Zlib.Deflate.prototype.compress"};function l(e){return e=Number(e),h[e]}var c;!function(e){("undefined"==typeof define?"undefined":u()(define))===l("0x8")&&define[l("0xd")]&&define(e)}((function(){})),(c=n)&&(c[l("0x45")]={GeoCamera:!0,GeoCameraEventAggregator:!0,GeoScreenSpaceCameraController:!0},c[l("0xff")]=l("0x100")),function(e){var t=e.GeoTerrainProvider=function(r){if(r=e.defaultValue(r,e.defaultValue.EMPTY_OBJECT),!e.defined(r.urls))throw new(e[l("0x15")])(l("0xa75"));this[l("0xa76")]=e.defaultValue(r[l("0xa77")],e[l("0xa78")][l("0xa79")]),this[l("0xa7a")]=e.defaultValue(r[l("0xa7b")],t[l("0xa7c")]),this[l("0xa7d")]=r[l("0xa7e")],this[l("0xa7f")]=this[l("0xa7d")].length,this[l("0xa80")]=0,this[l("0xa81")]=0,this[l("0xa0b")]=e.defaultValue(r[l("0xa77")],e[l("0xa78")][l("0xa79")]),this[l("0x9fc")]=new(e[l("0x766")]),this[l("0x48b")]=!1,this[l("0xa82")]=e.when.defer(),this._proxy=r.proxy,this._terrainDataStructure={heightScale:.001,heightOffset:-1e3,elementsPerHeight:3,stride:4,elementMultiplier:256,isBigEndian:!0};var i=r[l("0xa83")];u()(i)===l("0x9fe")&&(i=new(e[l("0x9ff")])(i)),this[l("0x9fd")]=i,this._tilingScheme=void 0,this._rectangles=[];var n=e.defaultValue(r[l("0xef")],e[l("0x16")][l("0x110")]);this[l("0x68a")]=new(e[l("0x9d7")])({ellipsoid:n}),this[l("0xa84")]=64,this[l("0xa85")]=64,this._levelZeroMaximumGeometricError=e[l("0xa11")][l("0xa86")](n,Math[l("0x7d")](this[l("0xa84")],this[l("0xa85")]),this[l("0x68a")].getNumberOfXTilesAtLevel(0)),this[l("0x48b")]=!0,this[l("0xa82")][l("0x9f8")](!0),this[l("0x75b")]=r[l("0x176")],this[l("0xa87")]=r[l("0xa88")],this[l("0xa89")]=r[l("0xa8a")],this[l("0xa8b")]=e.defaultValue(r.topLevel,5),this[l("0xa8c")]=e.defaultValue(r[l("0xa8d")],25)};function r(t,r){return(r=e.defined(r)?r:new e.Request)[l("0x6af")]=t,r[l("0xa9e")]=function(){var r,i,n=l("0xa9f"),o=new XMLHttpRequest;return o[l("0xaa0")](n,t,!0),o[l("0xaa1")]=l("0xaa2"),o[l("0xaa3")]=!1,o.send(null),r=o,i=e[l("0x9ee")].defer(),r.onreadystatechange=function(){if(4===r[l("0xaa6")])if(200===r[l("0xaa7")]){if(e.defined(r[l("0xaa8")])){for(var t=new DataView(r[l("0xaa8")]),n=new Uint8Array(t[l("0xaa9")]),o=0;o<t.byteLength;)n[o]=t.getUint8(o,!0),o++;var s=function(t){if(!(t[l("0x0")]<1e3)){var r=new(Zlib[l("0xaaa")])(t);return e.defined(r)?r[l("0xaab")]():void 0}}(n);e.defined(s)?i[l("0x9f8")](s):i[l("0xa3d")](void 0)}}else(400<=r[l("0xaa7")]||0===r[l("0xaa7")])&&i.reject(void 0)},i.promise},e[l("0xaa4")][l("0xaa5")](r)}Object.defineProperties(t.prototype,{errorEvent:{get:function(){return this[l("0x9fc")]}},credit:{get:function(){return this[l("0x9fd")]}},tilingScheme:{get:function(){if(!this[l("0xa8e")])throw new(e[l("0x15")])(l("0xa8f"));return this._tilingScheme}},ready:{get:function(){return this._ready}},readyPromise:{get:function(){return this[l("0xa82")][l("0xa90")]}},hasWaterMask:{get:function(){return!1}},hasVertexNormals:{get:function(){return!1}}}),t.prototype[l("0xa91")]=function(i,o,s,f){if(!this.ready)throw new e.DeveloperError(l("0xa8f"));1<this[l("0xa7f")]&&(this[l("0xa81")]<8?this[l("0xa81")]++:(this[l("0xa81")]=0,this[l("0xa80")]++,this[l("0xa80")]>=this._urls_length&&(this[l("0xa80")]=0)));var u=this[l("0xa7d")][this[l("0xa80")]];if(this[l("0x68a")][l("0x9e2")](s),s<this[l("0xa8c")]&&s>=this[l("0xa8b")]){var h,c=-1===u[l("0x2d3")]("?")?"?":"&",d=u+c+"x="+i+l("0xa92")+o+l("0xa93")+(s+1),p=this[l("0xa94")];if(e.defined(p)&&(d=p.getURL(d)),f=e.defaultValue(f,!0)){if(h=r(d,f),!e.defined(h))return}else h=r(d);if(this._tileType===t[l("0xa96")])return h.then((function(e){return function(e,t,r,i,n){var o=0,s=3*Float64Array[l("0x4b5")],a=4*Float64Array[l("0x4b5")],f=3*Uint16Array.BYTES_PER_ELEMENT,u=Uint16Array[l("0x4b5")],h=3*u,c=new DataView(t),d=new Cartesian3(c[l("0xaad")](o,!0),c[l("0xaad")](o+8,!0),c[l("0xaad")](o+16,!0));o+=s;var p=c[l("0xaae")](o,!0);o+=Float32Array.BYTES_PER_ELEMENT;var x=c[l("0xaae")](o,!0);o+=Float32Array.BYTES_PER_ELEMENT;var g=new BoundingSphere(new Cartesian3(c.getFloat64(o,!0),c[l("0xaad")](o+8,!0),c[l("0xaad")](o+16,!0)),c[l("0xaad")](o+s,!0));o+=a;var v=new Cartesian3(c[l("0xaad")](o,!0),c[l("0xaad")](o+8,!0),c[l("0xaad")](o+16,!0));o+=s;var y=c.getUint32(o,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var b=new Uint16Array(t,o,3*y);o+=y*f,65536<y&&(h=3*(u=Uint32Array[l("0x4b5")]));var m=b[l("0xaaf")](0,y),w=b[l("0xaaf")](y,2*y),E=b[l("0xaaf")](2*y,3*y);AttributeCompression.zigZagDeltaDecode(m,w,E),o%u!=0&&(o+=u-o%u);var T=c[l("0xab0")](o,!0);o+=Uint32Array[l("0x4b5")];var S=IndexDatatype[l("0xab1")](y,t,o,3*T);o+=T*h;for(var I=0,A=S[l("0x0")],_=0;_<A;++_){var P=S[_];S[_]=I-P,0===P&&++I}var O=c[l("0xab0")](o,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var k=IndexDatatype[l("0xab1")](y,t,o,O);o+=O*u;var R=c[l("0xab0")](o,!0);o+=Uint32Array[l("0x4b5")];var L=IndexDatatype[l("0xab1")](y,t,o,R);o+=R*u;var N=c[l("0xab0")](o,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var U=IndexDatatype[l("0xab1")](y,t,o,N);o+=N*u;var Y=c[l("0xab0")](o,!0);o+=Uint32Array[l("0x4b5")];var B,C,M=IndexDatatype.createTypedArrayFromArrayBuffer(y,t,o,Y);for(o+=Y*u;o<c[l("0xaa9")];){var F=c[l("0xab2")](o,!0);o+=Uint8Array[l("0x4b5")];var V=c.getUint32(o,!0);if(o+=Uint32Array[l("0x4b5")],F===QuantizedMeshExtensionIds[l("0xab3")]&&e[l("0xab4")])B=new Uint8Array(t,o,2*y);else if(F===QuantizedMeshExtensionIds[l("0xab5")]&&e._requestWaterMask)C=new Uint8Array(t,o,V);else if(F===QuantizedMeshExtensionIds[l("0xab6")]&&e[l("0xab7")]){var j=c[l("0xab0")](o,!0);if(0<j){var z=getStringFromTypedArray(new Uint8Array(t),o+Uint32Array[l("0x4b5")],j),D=JSON[l("0x946")](z)[l("0xab8")];if(defined(D))for(var G=0;G<D[l("0x0")];++G)for(var q=r+G+1,W=D[G],Z=(e._tilingScheme[l("0x9e2")](q),0);Z<W[l("0x0")];++Z){var X=W[Z];X[l("0xab9")],X.startY}}}o+=V}var H,$=5*e[l("0xa99")](r),J=e[l("0x68a")][l("0xa33")](i,n,r);return J.width<CesiumMath.PI_OVER_TWO+CesiumMath[l("0xaba")]&&(H=OrientedBoundingBox[l("0xabb")](J,p,x,e[l("0x68a")][l("0xef")])),new QuantizedMeshTerrainData({center:d,minimumHeight:p,maximumHeight:x,boundingSphere:g,orientedBoundingBox:H,horizonOcclusionPoint:v,quantizedVertices:b,encodedNormals:B,indices:S,westIndices:k,southIndices:L,eastIndices:U,northIndices:M,westSkirtHeight:$,southSkirtHeight:$,eastSkirtHeight:$,northSkirtHeight:$,waterMask:C,credits:e[l("0xabc")]})}(provider,e,s,i,o)}));var x=this;return h.then((function(t){var r=x.transformBuffer(t);if(e.defined(r)){var a=new(e[l("0xa64")])({buffer:r,width:x[l("0xa84")],height:x[l("0xa85")],childTileMask:n(x,i,o,s),structure:x[l("0xa97")]});return a[l("0xa98")]=6e3,a}}))}if(s<this[l("0xa8b")]){var g=this[l("0xa84")],v=this[l("0xa85")],y=n(this,i,o,s),b=this._terrainDataStructure;return new(e[l("0xa64")])({buffer:this.getvHeightBuffer(),width:g,height:v,childTileMask:y,structure:b})}return s>=this[l("0xa8c")]?new a.a((function(e,t){t("该级别不发送请求！")})):void 0},t.prototype[l("0xa99")]=function(t){if(!this[l("0xa8e")])throw new(e[l("0x15")])("requestTileGeometry must not be called before ready returns true.");return this._levelZeroMaximumGeometricError/(1<<t)},t.prototype[l("0x9fb")]=function(e,t,r){if(r<25)return!0},t.prototype[l("0xa9a")]=function(){var t=this[l("0xa9b")];if(!e.defined(t)){t=new Uint8ClampedArray(this._heightmapWidth*this._heightmapHeight*4);for(var r=0;r<this[l("0xa84")]*this[l("0xa85")]*4;)t[r++]=15,t[r++]=66,t[r++]=64,t[r++]=255;this[l("0xa9b")]=t}return t},t.prototype[l("0xa9c")]=function(t){var r=2;this._dataType===e.GeoTerrainProvider[l("0xa79")]?r=2:this._dataType===e[l("0xa78")].FLOAT&&(r=4);var i=t;if(i[l("0x0")]!==22500*r)return null;for(var n,s,a,f,u,h=new ArrayBuffer(r),c=new DataView(h),d=this[l("0xa84")],p=this[l("0xa85")],x=new Uint8Array(d*p*4),g=0;g<p;g++)for(var v=0;v<d;v++){f=o()(149*g/(p-1)),u=o()(149*v/(d-1)),(1e4<(n=4===r?(s=r*(150*f+u),c.setInt8(0,i[s]),c[l("0xa9d")](1,i[s+1]),c[l("0xa9d")](2,i[s+2]),c.setInt8(3,i[s+3]),c.getFloat32(0,!0)):i[s=r*(150*f+u)]+256*i[s+1])||n<-2e3)&&(n=0);var y=(n+1e3)/.001;x[a=4*(g*d+v)]=y/65536,x[1+a]=(y-256*x[a]*256)/256,x[2+a]=y-256*x[a]*256-256*x[1+a],x[3+a]=255}return x};var i=new(e[l("0x26b")]);function n(t,r,n,o){for(var a=t._tilingScheme,f=t[l("0xaac")],u=a[l("0xa33")](r,n,o),h=0,c=0;c<f[l("0x0")]&&15!==h;++c){var d=f[c];if(!(d[l("0x109")]<=o)){var p=d[l("0x969")],x=e[l("0x26b")][l("0x348")](p,u,i);e.defined(x)&&(s(a,p,2*r,2*n,o+1)&&(h|=4),s(a,p,2*r+1,2*n,o+1)&&(h|=8),s(a,p,2*r,2*n+1,o+1)&&(h|=1),s(a,p,2*r+1,2*n+1,o+1)&&(h|=2))}}return h}function s(t,r,n,o,s){var a=t[l("0xa33")](n,o,s);return e.defined(e.Rectangle.intersection(a,r,i))}t[l("0xa79")]=l("0xabd"),t[l("0x5e9")]=l("0xabe"),t.QUANTIZED_MESH=l("0xabf"),t[l("0xa7c")]=l("0xac0")}(n),function(e){var t=e.defaultValue;function r(r){r=t(r,t.EMPTY_OBJECT),this[l("0xe18")]=r.proxy,this[l("0x13b")]=r[l("0x13b")],this.url=r[l("0x6af")],this[l("0xe19")]=r.metadata,this[l("0xe1a")]=r.roadMetadata,this[l("0xe1b")]=r[l("0xe1b")],this[l("0xe1c")]=r.labelGraphics?r.labelGraphics:{},this[l("0xe1d")]=r[l("0xe1d")]?r.billboardGraphics:{},this[l("0xe1e")]=!!r.aotuCollide,this.collisionPadding=r[l("0xe1f")]?r[l("0xe1f")]:[3,5,3,5],this[l("0xe20")]=!!r[l("0xe20")],this[l("0xa4e")]=r.subdomains,Array.isArray(this[l("0xa4e")])?this[l("0xa4e")]=this[l("0xa4e")].slice():e.defined(this[l("0xa4e")])&&0<this[l("0xa4e")][l("0x0")]?this[l("0xa4e")]=this[l("0xa4e")][l("0x7f2")](""):this[l("0xa4e")]=["a","b","c"],this[l("0xe21")]=[],this[l("0xe22")]=[],this[l("0xe23")]=!1,this[l("0xe24")]=[],this._latelyRefreshStamp=0,this._latelyCollisionStamp=0;var i=e[l("0x75c")]();this[l("0xe25")]=l("0xe26")+i,this[l("0xe27")]="GEO_WTFS_LABEL_ROAD_"+i,this[l("0x13b")].camera.percentageChanged=.18,this.bindEvent()}function i(e,t){return t[l("0xe77")]>=e[l("0xe77")]&&t.minX<=e.maxX&&t[l("0x4d2")]>=e[l("0x4d2")]&&t[l("0x4d2")]<=e.maxY||t[l("0xe78")]>=e[l("0xe77")]&&t.maxX<=e[l("0xe78")]&&t[l("0xe79")]>=e[l("0x4d2")]&&t[l("0xe79")]<=e.maxY||t[l("0xe77")]>=e[l("0xe77")]&&t[l("0xe77")]<=e[l("0xe78")]&&t[l("0xe79")]>=e[l("0x4d2")]&&t.maxY<=e[l("0xe79")]||t[l("0xe78")]>=e[l("0xe77")]&&t.maxX<=e.maxX&&t[l("0x4d2")]>=e[l("0x4d2")]&&t[l("0x4d2")]<=e.maxY}function n(e,t){var r=e.x,i=e.y,n=e.width,o=e.height,s=t.x,a=t.y,f=t[l("0x19a")],u=t[l("0x8b")];return!(s<=r&&s+f<=r||r<=s&&r+n<=s||a<=i&&a+u<=i||i<=a&&i+o<=a)}e.defined,Object.defineProperties(r.prototype,{}),r.prototype[l("0xe28")]=function(e,t,r,i){for(var n=0;n<this[l("0xe21")].length;n++)if(this[l("0xe21")][n].x===e&&this[l("0xe21")][n].y===t&&this[l("0xe21")][n].z===r&&this[l("0xe21")][n].t===i)return this[l("0xe21")][n];return null},r.prototype.addCacheTile=function(e){999<this[l("0xe21")][l("0x0")]&&this[l("0xe21")][l("0x259")](0,500),this[l("0xe29")](e.x,e.y,e.z,e.t),this[l("0xe21")][l("0x17c")](e)},r.prototype[l("0xe29")]=function(e,t,r,i){for(var n=0;n<this.tileCache[l("0x0")];n++)if(this[l("0xe21")][n].x===e&&this[l("0xe21")][n].y===t&&this[l("0xe21")][n].z===r&&this[l("0xe21")][n].t===i){this[l("0xe21")][l("0x259")](n,1);break}},r.prototype[l("0xe2a")]=function(e){for(var t=0;t<this.labelCache[l("0x0")];t++)if(this.labelCache[t][l("0x176")]===this[l("0xe25")]&&this[l("0xe22")][t][l("0xe2b")]===e)return this.labelCache[t];return null},r.prototype[l("0xe2c")]=function(e){999<this[l("0xe22")].length&&this[l("0xe22")].splice(0,250),this[l("0xe2d")](e[l("0xe2b")]),e[l("0xe2e")]=(new Date).getTime(),this[l("0xe22")][l("0x17c")](e)},r.prototype[l("0xe2d")]=function(e){for(var t=0;t<this[l("0xe22")].length;t++)if(this[l("0xe22")][t][l("0x176")]===this._UUID&&this.labelCache[t][l("0xe2b")]===e){this[l("0xe22")][l("0x259")](t,1);break}},r.prototype[l("0xe2f")]=function(){return(this[l("0xe18")]?this.proxy.proxy:"")+this.url+l("0xe30")},r.prototype[l("0xe31")]=function(){return(this[l("0xe18")]?this.proxy[l("0xe18")]:"")+this.url+l("0xe32")},r.prototype[l("0xe33")]=function(){return(this[l("0xe18")]?this[l("0xe18")][l("0xe18")]:"")+this[l("0xe1b")]},r.prototype[l("0xaf6")]=function(){var e=this;clearTimeout(this[l("0xe34")]);var t=this[l("0x13b")].scene[l("0xa3")][l("0xa2e")];if(t[l("0xe35")][l("0x0")]<2||0<t._tileLoadQueueHigh.length)this[l("0xe34")]=setTimeout((function(){e[l("0xaf6")]()}),100);else{var r=this[l("0xe36")]();if(this[l("0xe37")](r,this._latelyGrid))return;this[l("0xe38")](r),e.delaySynchronous()}},r.prototype[l("0xaf7")]=function(){var e=(new Date).getTime(),t=e-this[l("0xe39")],r=e-this[l("0xe3a")];300<t&&this[l("0xaf6")](),150<r&&this[l("0xe3b")]()},r.prototype._queueCall=function(e){var t=this;t[l("0xe24")]=e,t[l("0xe39")]=(new Date)[l("0x68")](),e[l("0x1e4")]((function(e,r,n){if(t[l("0xe19")]&&i(t.metadata[l("0xe3c")],e.boundBox)){if(t.metadata[l("0xe3d")]>e[l("0x968")]+1||t[l("0xe19")][l("0x109")]<e[l("0x968")]+1)return;var o=t[l("0xe28")](e.x,e.y,e.level+1,0);if(o)t.addLabelAndIco(o);else{var s=t[l("0xe2f")]().replace(l("0xe3e"),e.level+1)[l("0xa1c")]("{y}",e.y).replace(l("0xe3f"),e.x).replace(l("0xa04"),t[l("0xa4e")][(e.x+e.y+e[l("0x968")])%t[l("0xa4e")][l("0x0")]]),f=new XMLHttpRequest;f[l("0xaa0")](l("0xa9f"),s,!0),f[l("0xaa1")]=l("0xaa2"),f[l("0xad2")]=function(){if(!(f.status<200||300<=f[l("0xaa7")])){var e=t[l("0xe40")](f[l("0xaa8")]);if(e)(r=a(e)).x=this.tile.x,r.y=this[l("0xe41")].y,r.z=this.tile.z,r.t=0,t[l("0xe42")](r),t[l("0xe43")](r);else{var r={x:this[l("0xe41")].x,y:this[l("0xe41")].y,z:this[l("0xe41")].z,t:0};t[l("0xe42")](r),t[l("0xe44")]()}}},f[l("0xad0")]=function(e){console.log(e)},f[l("0xe45")](),f.tile={x:e.x,y:e.y,z:e[l("0x968")]+1}}}if(t[l("0xe1a")]&&i(t[l("0xe1a")][l("0xe3c")],e.boundBox)){if(t[l("0xe1a")][l("0xe3d")]>e[l("0x968")]+1||t[l("0xe1a")][l("0x109")]<e[l("0x968")]+1)return;if(o=t[l("0xe28")](e.x,e.y,e[l("0x968")]+1,1))t[l("0xe43")](o);else{var u=t[l("0xe33")]()[l("0xa1c")](l("0xe3e"),e[l("0x968")]+1).replace("{y}",e.y)[l("0xa1c")]("{x}",e.x),h=new XMLHttpRequest;h[l("0xaa0")](l("0xa9f"),u,!0),h[l("0xaa1")]=l("0xe46"),h[l("0xad2")]=function(){if(!(h[l("0xaa7")]<200||300<=h[l("0xaa7")])){var e=h[l("0xaa8")];if(e){var r={pois:e[l("0x815")]((function(e,t,r){return{oid:e.LabelPoint.X+"_"+e[l("0xe47")].Y,name:e[l("0x344")][l("0x33d")][l("0xe48")],coordinate:[e.LabelPoint.X,e.LabelPoint.Y,e.LabelPoint.Z?e[l("0xe47")].Z:0]}})),x:this[l("0xe41")].x,y:this[l("0xe41")].y,z:this.tile.z,t:1};t[l("0xe42")](r),t[l("0xe43")](r)}else r={x:this.tile.x,y:this[l("0xe41")].y,z:this.tile.z,t:1},t[l("0xe42")](r),t[l("0xe44")]()}},h.onerror=function(e){console.error(e)},h[l("0xe45")](),h[l("0xe41")]={x:e.x,y:e.y,z:e[l("0x968")]+1}}}}))},r.prototype[l("0xe49")]=function(e){var t=0,r=this;r[l("0xe23")]=!0,r[l("0xe38")](e);var i=setInterval((function(){3<t&&(r[l("0xe23")]=!1,clearInterval(i)),t%2==0&&r[l("0xe1e")]&&r[l("0xe3b")](),t++}),600);return r},r.prototype[l("0xe36")]=function(){for(var t=this[l("0x13b")][l("0x10f")][l("0xa3")][l("0xa2e")]._tilesToRender.map((function(t,r,i){return{x:t.x,y:t.y,level:t.level,boundBox:{minX:e.Math.toDegrees(t[l("0x969")].west),minY:e.Math.toDegrees(t[l("0x969")].south),maxX:e.Math.toDegrees(t[l("0x969")][l("0x2aa")]),maxY:e.Math[l("0x113")](t[l("0x969")][l("0x2ac")])}}})).sort((function(e,t){return t[l("0x968")]-e.level})),r=[t[0][l("0x968")]],i=0;i<t[l("0x0")];i++)t[i][l("0x968")]!==r[r.length-1]&&r[l("0x17c")](t[i][l("0x968")]),4<r[l("0x0")]&&(t[l("0x259")](i),i--);return t},r.prototype.addLabelAndIco=function(e){if(e.pois)for(var t=0;t<e[l("0xe4a")][l("0x0")];t++){var r=this[l("0xe2a")](e[l("0xe4a")][t][l("0xe2b")]);r=r||this[l("0xe4b")](e[l("0xe4a")][t],e),this[l("0xe2c")](r)}this[l("0xe44")]()},r.prototype.delaySynchronous=function(){var e=this;clearTimeout(e._timer2),e[l("0xe4c")]=setTimeout((function(){e.synchronousLabel()}),100)},r.prototype.synchronousLabel=function(){for(var e=0;e<this.labelCache[l("0x0")];e++)this[l("0xe22")][e].timestamp>=this[l("0xe39")]&&!this.viewer[l("0x194")].contains(this.labelCache[e])&&(this._isInitial&&this[l("0xe1e")]&&(this[l("0xe22")][e][l("0x221")]=!1),this[l("0x13b")][l("0x194")][l("0x6d")](this.labelCache[e]));if(!this[l("0xe23")]){for(e=0;e<this.viewer.entities[l("0x190")][l("0x0")];e++)!this[l("0x13b")][l("0x194")][l("0x190")][e][l("0x176")]||this[l("0x13b")][l("0x194")][l("0x190")][e][l("0x176")]!==this[l("0xe25")]&&this.viewer[l("0x194")][l("0x190")][e].name!==this[l("0xe27")]||this[l("0x13b")][l("0x194")][l("0x190")][e].timestamp<this._latelyRefreshStamp&&(this[l("0x13b")][l("0x194")].remove(this[l("0x13b")][l("0x194")][l("0x190")][e]),e--);this[l("0xe1e")]&&this[l("0xe3b")]()}},r.prototype[l("0xe4b")]=function(t,r){if(t){var i,n,o={show:!0,position:e.Cartesian3.fromDegrees(t[l("0xe4d")][0],t[l("0xe4d")][1],t[l("0xe4d")][2]),label:{text:t.name}};this.serverFirstStyle&&void 0!==t.fontSize?(o.label[l("0xe4e")]=t.fontSize+l("0xe4f"),void 0!==t.fontNameIndex&&r[l("0xe50")]&&r[l("0xe50")][t.fontNameIndex]?o[l("0xe51")][l("0xe4e")]+=r[l("0xe50")][t.fontNameIndex]:o[l("0xe51")].font+=l("0xe52"),!this[l("0xe1c")][l("0x3fa")]&&1!==t[l("0xe53")]&&3!==t[l("0xe53")]||(i=!0),2!==t[l("0xe53")]&&3!==t.fontStyle||(n=!0),i&&(o[l("0xe51")][l("0xe4e")]=l("0xe54")+o[l("0xe51")].font),n&&(o[l("0xe51")][l("0xe4e")]=l("0xe55")+o[l("0xe51")][l("0xe4e")])):this[l("0xe1c")][l("0xe4e")]&&(o[l("0xe51")].font=this[l("0xe1c")][l("0xe4e")]),this[l("0xe20")]&&void 0!==t[l("0xe56")]?o[l("0xe51")].fillColor=e[l("0x16a")].fromCssColorString(this.HexadecimalConversion(t[l("0xe56")])):this.labelGraphics.fillColor&&(o.label[l("0x3dd")]=this[l("0xe1c")][l("0x3dd")]),this[l("0xe20")]&&void 0!==t[l("0xe57")]?o[l("0xe51")].outlineColor=e.Color[l("0x27e")](this.HexadecimalConversion(t[l("0xe57")])):this[l("0xe1c")][l("0x3df")]&&(o[l("0xe51")][l("0x3df")]=this[l("0xe1c")][l("0x3df")]),this[l("0xe20")]&&u()(t.shiningSize)===l("0x49f")?o.label[l("0x3de")]=t[l("0xe58")]:void 0!==this[l("0xe1c")].outlineWidth&&(o[l("0xe51")][l("0x3de")]=this[l("0xe1c")][l("0x3de")]),this.serverFirstStyle&&void 0!==t[l("0x3d8")]?o[l("0xe51")][l("0x3d8")]=t[l("0x3d8")]:void 0!==this[l("0xe1c")].showBackground&&(o[l("0xe51")][l("0x3d8")]=this[l("0xe1c")].showBackground),this.serverFirstStyle&&void 0!==t.backgroundColor?o[l("0xe51")][l("0x3d9")]=t[l("0x3d9")]:void 0!==this[l("0xe1c")][l("0x3d9")]&&(o[l("0xe51")][l("0x3d9")]=this[l("0xe1c")][l("0x3d9")]),this.serverFirstStyle&&void 0!==t[l("0xe59")]?o[l("0xe51")].backgroundPadding=t.backgroundPadding:void 0!==this[l("0xe1c")][l("0xe59")]&&(o[l("0xe51")][l("0xe59")]=this[l("0xe1c")][l("0xe59")]),r.t?(o[l("0xe51")].verticalOrigin=e[l("0x24d")][l("0x24c")],o[l("0xe51")][l("0x3da")]=e[l("0x24b")][l("0x24c")]):(this[l("0xe20")]&&void 0!==t[l("0x3db")]?o[l("0xe51")][l("0x3db")]=t[l("0x3db")]:void 0!==this[l("0xe1c")].verticalOrigin&&(o[l("0xe51")][l("0x3db")]=this[l("0xe1c")][l("0x3db")]),this[l("0xe20")]&&void 0!==t[l("0x3da")]?o.label[l("0x3da")]=t[l("0x3da")]:void 0!==this[l("0xe1c")].horizontalOrigin&&(o[l("0xe51")].horizontalOrigin=this[l("0xe1c")].horizontalOrigin)),this[l("0xe20")]&&void 0!==t.eyeOffset?o[l("0xe51")][l("0xe5a")]=t[l("0xe5a")]:void 0!==this[l("0xe1c")][l("0xe5a")]&&(o.label[l("0xe5a")]=this[l("0xe1c")][l("0xe5a")]),this[l("0xe20")]&&void 0!==t[l("0x3dc")]?o[l("0xe51")][l("0x3dc")]=t.pixelOffset:void 0!==this[l("0xe1c")].pixelOffset&&(o[l("0xe51")].pixelOffset=this.labelGraphics[l("0x3dc")]),this[l("0xe20")]&&void 0!==t[l("0x150")]?o.label[l("0x150")]=t[l("0x150")]:void 0!==this[l("0xe1c")][l("0x150")]&&(o[l("0xe51")].style=this.labelGraphics[l("0x150")]),this[l("0xe20")]&&void 0!==t[l("0xfc")]?o.label.scale=t.scale:void 0!==this[l("0xe1c")].scale&&(o.label[l("0xfc")]=this[l("0xe1c")][l("0xfc")]),o[l("0xe51")][l("0xe5b")]=this.labelGraphics[l("0xe5b")],o.label[l("0xe5c")]=this[l("0xe1c")][l("0xe5c")]?this.labelGraphics.heightReference:e.HeightReference[l("0xe5d")],void 0!==t.symbolID&&-1<t[l("0xe5e")]&&(o[l("0xe5f")]={image:this[l("0xe31")]()[l("0xa1c")](l("0xe60"),t[l("0xe5e")])[l("0xa1c")](l("0xa04"),this.subdomains[(r.x+r.y+r.z)%this[l("0xa4e")][l("0x0")]])},this[l("0xe20")]&&void 0!==t[l("0xe61")]?(o.billboard.width=t.displayHeight,o[l("0xe5f")].height=t[l("0xe61")]):void 0===this.billboardGraphics[l("0x19a")]&&void 0===this[l("0xe1d")][l("0x8b")]||(o[l("0xe5f")].width=this[l("0xe1d")].width,o.billboard[l("0x8b")]=this.billboardGraphics[l("0x8b")]),r.t?(o.label[l("0x3db")]=e[l("0x24d")][l("0x3c7")],o[l("0xe51")][l("0x3da")]=e[l("0x24b")][l("0x24c")]):(this[l("0xe20")]&&void 0!==t[l("0x3db")]?o[l("0xe5f")].verticalOrigin=t.verticalOrigin:void 0!==this.billboardGraphics.verticalOrigin&&(o[l("0xe5f")][l("0x3db")]=this[l("0xe1d")][l("0x3db")]),this[l("0xe20")]&&void 0!==t[l("0x3da")]?o[l("0xe5f")].horizontalOrigin=t.horizontalOrigin:void 0!==this.billboardGraphics[l("0x3da")]&&(o[l("0xe5f")][l("0x3da")]=this[l("0xe1d")][l("0x3da")])),this[l("0xe20")]&&void 0!==t[l("0xe5a")]?o[l("0xe5f")][l("0xe5a")]=t[l("0xe5a")]:void 0!==this[l("0xe1d")].eyeOffset&&(o[l("0xe5f")][l("0xe5a")]=this[l("0xe1d")][l("0xe5a")]),this[l("0xe20")]&&void 0!==t.pixelOffset?o.billboard[l("0x3dc")]=t[l("0x3dc")]:void 0!==this.billboardGraphics[l("0x3dc")]&&(o[l("0xe5f")].pixelOffset=this[l("0xe1d")][l("0x3dc")]),this[l("0xe20")]&&void 0!==t[l("0xfb")]?o[l("0xe5f")][l("0xfb")]=t[l("0xfb")]:void 0!==this.billboardGraphics.rotation&&(o.billboard[l("0xfb")]=this.billboardGraphics[l("0xfb")]),this[l("0xe20")]&&void 0!==t.alignedAxis?o[l("0xe5f")][l("0xe62")]=t[l("0xe62")]:void 0!==this.billboardGraphics[l("0xe62")]&&(o[l("0xe5f")].alignedAxis=this.billboardGraphics[l("0xe62")]),this[l("0xe20")]&&void 0!==t[l("0x169")]?o[l("0xe5f")].color=t[l("0x169")]:void 0!==this[l("0xe1d")].color&&(o[l("0xe5f")][l("0x169")]=this[l("0xe1d")].color),this.serverFirstStyle&&void 0!==t[l("0xfc")]?o[l("0xe5f")][l("0xfc")]=t.scale:void 0!==this[l("0xe1d")].scale&&(o[l("0xe5f")].scale=this[l("0xe1d")][l("0xfc")]),o.billboard[l("0xe5b")]=this[l("0xe1d")][l("0xe5b")],o.billboard[l("0xe5c")]=this[l("0xe1d")][l("0xe5c")]?this[l("0xe1d")][l("0xe5c")]:e[l("0xe63")].NONE);var s=new(e[l("0x18e")])(o);return s[l("0x176")]=r.t?this[l("0xe27")]:this[l("0xe25")],s[l("0xe2b")]=t.oid,s[l("0xe64")]=t[l("0xe64")]?t[l("0xe64")]:0,s[l("0xe65")]=r.x+"_"+r.y+"_"+(r.z-1),s}},r.prototype.getPropertyValue=function(e,t,r,i){return void 0!==t[e]?t[e]:void 0!==r[e]?r[e]:i},r.prototype[l("0xe3b")]=function(){if(this[l("0xe66")])for(var t=document[l("0xe67")](l("0xe66")),r=0;r<t[l("0x0")];r++)this[l("0x13b")][l("0x131")][l("0x41b")](t[r]),r--;var i=[],o=this.viewer[l("0x194")][l("0x190")];for(r=0;r<o[l("0x0")];r++)if(o[r][l("0x176")]&&(o[r][l("0x176")]===this[l("0xe25")]||o[r][l("0x176")]===this[l("0xe27")])){var s,a;s=e[l("0xe68")].wgs84ToDrawingBufferCoordinates(this[l("0x13b")][l("0x10f")],o[r].position[l("0x512")](0)),o[r][l("0x221")]=!0,a=this[l("0xe69")]({point:s,entity:o[r]}),o[r].collisionBox=a;for(var f=null,u=0;u<i.length;u++)if(i[u][l("0xe65")]===o[r].xyz){f=i[u];break}if(f||(f={xyz:o[r][l("0xe65")],entities:[]},i.push(f)),f[l("0x194")][l("0x17c")](o[r]),this[l("0xe66")]){var h=document[l("0x15c")](l("0x15d"));h[l("0x21b")](l("0xe6a"),l("0xe66")),h[l("0x150")][l("0xe6b")]=l("0xe6c")+a.y+l("0xe6d")+a.x+l("0xe6e")+a.width+l("0xe6f")+a[l("0x8b")]+"px; border: solid 1px red; color: rgba(0,0,0,0);",h[l("0x40a")]=o[r][l("0xe51")][l("0x31b")].getValue(0),this[l("0x13b")].container.appendChild(h)}}for(var c=0;c<i[l("0x0")];c++)for(i[c].entities[l("0x3d2")]((function(e,t){return e[l("0xe64")]-t[l("0xe64")]})),r=0;r<i[c][l("0x194")][l("0x0")];r++)if(i[c][l("0x194")][r].show)for(u=r+1;u<i[c].entities[l("0x0")];u++)i[c].entities[u][l("0x221")]&&n(i[c][l("0x194")][r][l("0xe70")],i[c][l("0x194")][u][l("0xe70")])&&(i[c][l("0x194")][u][l("0x221")]=!1);var d=[];for(c=0;c<i.length;c++)for(r=0;r<i[c][l("0x194")][l("0x0")];r++)i[c][l("0x194")][r][l("0x221")]&&d[l("0x17c")](i[c][l("0x194")][r]);for(r=0;r<d[l("0x0")];r++)if(d[r][l("0x221")])for(d[l("0x3d2")]((function(e,t){return e.priority-t[l("0xe64")]})),u=r+1;u<d[l("0x0")];u++)d[u].show&&n(d[r][l("0xe70")],d[u][l("0xe70")])&&(d[u].show=!1)},r.prototype[l("0xe71")]=function(){this[l("0x13b")].scene.camera[l("0xe72")][l("0x130")](this[l("0xaf6")],this),this.viewer.scene.camera[l("0xe73")][l("0x130")](this[l("0xaf7")],this)},r.prototype[l("0xe74")]=function(){this[l("0x13b")][l("0x10f")].camera[l("0xe72")][l("0x548")](this[l("0xaf6")],this),this[l("0x13b")][l("0x10f")].camera[l("0xe73")][l("0x548")](this[l("0xaf7")],this)},r.prototype[l("0x198")]=function(){this[l("0xe24")]=[],this[l("0xaf6")]()},r.prototype.destroy=function(){for(var e=0;e<this[l("0x13b")][l("0x194")][l("0x190")][l("0x0")];e++)!this.viewer[l("0x194")][l("0x190")][e].name||this.viewer.entities[l("0x190")][e][l("0x176")]!==this._UUID&&this.viewer.entities.values[e].name!==this[l("0xe27")]||(this[l("0x13b")][l("0x194")].remove(this[l("0x13b")].entities[l("0x190")][e]),e--);this[l("0x13b")].camera.percentageChanged=.5,this[l("0xe74")](),this[l("0x16f")]=this[l("0x16f")]&&this[l("0x16f")].destroy(),this[l("0xe18")]=void 0,this[l("0x13b")]=void 0,this[l("0x6af")]=void 0,this[l("0xe1c")]=void 0,this[l("0xe1d")]=void 0,this[l("0xe1e")]=void 0,this.collisionPadding=void 0,this.tileCache=void 0,this.labelCache=void 0,this[l("0xe24")]=void 0,this[l("0xe39")]=void 0,this[l("0xe75")]=void 0},r.prototype[l("0xe37")]=function(e,t){for(var r=!1,i=0;i<e[l("0x0")];i++){for(var n=!1,o=0;o<t.length;o++)if(e[i].x===t[o].x&&e[i].y===t[o].y&&e[i].level===t[o][l("0x968")]){n=!0;break}if(!n){r=!0;break}}return!r},r.prototype[l("0xe69")]=function(e){var t=o()(e[l("0xe76")].label[l("0xe4e")]);t=0<t?t:15;for(var r=e[l("0xe76")].label[l("0x31b")][l("0x512")](0)[l("0x7f2")]("\n"),i=0,n=r[l("0x0")],a=0;a<r[l("0x0")];a++){var f=s(r[a])/2;i<f&&(i=f)}var u=e[l("0xe76")][l("0xe5f")]?e[l("0xe76")][l("0xe5f")][l("0x19a")][l("0x512")](0)*e[l("0xe76")][l("0xe5f")].scale[l("0x512")](0):1,h=e.entity[l("0xe5f")]?e[l("0xe76")].billboard.height.getValue(0)*e.entity[l("0xe5f")][l("0xfc")][l("0x512")](0):1;return{x:(e[l("0x2ec")]?e[l("0x2ec")].x:-999)-u/2-this[l("0xe1f")][3],y:(e[l("0x2ec")]?e.point.y:-999)-h/2-this[l("0xe1f")][0],width:t*e[l("0xe76")][l("0xe51")][l("0xfc")][l("0x512")](0)*i+e[l("0xe76")][l("0xe51")][l("0x3dc")][l("0x512")](0).x+u+this[l("0xe1f")][1],height:t*e[l("0xe76")][l("0xe51")].scale.getValue(0)*(n+.5*(n-1))+e[l("0xe76")][l("0xe51")][l("0x3dc")][l("0x512")](0).y+h/2+this[l("0xe1f")][2]}},r.prototype.CutString=function(e){if(!e)return"";var t=e[l("0xaa9")];return t<=28?"":e[l("0x1d5")](19,t-9)},r.prototype.HexadecimalConversion=function(e){if(4278190080===e)return"#000000";var t=4278190080|o()(-Number(e));if(e="",(t=t[l("0x531")](16)[l("0x987")](1))[l("0x0")]<6)for(var r=6-t[l("0x0")],i=0;i<r;i++)e+="0";return"#"+e+t},r.prototype.getLabelVisibility=function(e){if(!e)return!1;var t=this[l("0x13b")][l("0x48")].getBoundingClientRect();return!(e.x<-10||e.x>t[l("0x9b")]+10||e.y<-10||e.y>t[l("0x9aa")]+10)};var s=function(e){for(var t=0,r=0;r<e[l("0x0")];r++)null!==e.charAt(r)[l("0x126")](/[^\x00-\xff]/gi)?t+=2:t+=1;return t},a=function(e){var t={stringTable:[],pois:[],enumGeometryType:[{ePoint:0},{eMultiLineString:1},{ePolygon:2}],enumZCoordType:[{eCloseGround:0},{eCloseSeaSurface:1},{eRelativelyGround:2},{eAbsolute:3}]},r=function(){var e=l("0xe7a")+l("0xe7b")+l("0xe7c")+l("0xe7d")+l("0xe7e")+l("0xe7f")+l("0xe80")+l("0xe81")+l("0xe82")+l("0xe83")+l("0xe84")+l("0xe85")+l("0xe86")+l("0xe87")+l("0xe88")+l("0xe89")+l("0xe8a")+l("0xe8b")+l("0xe8c")+"};message StringTable {"+l("0xe8d")+"}"+l("0xe8e")+l("0xe8f")+"required int64 TileKey = 2;"+l("0xe90")+l("0xe91")+"};";if("undefined"==typeof dcodeIO||!dcodeIO[l("0xe92")])throw new Error(l("0xe93"));return dcodeIO[l("0xe92")][l("0xe94")](e,"GEOPOI",l("0xe95"))[l("0xd22")](l("0xe96"))[l("0xe97")]}(),i=function(){var e=l("0xe7a")+l("0xe7b")+l("0xe7c")+l("0xe7d")+l("0xe7e")+l("0xe7f")+"};"+l("0xe98")+l("0xe81")+l("0xe82")+l("0xe83")+l("0xe84")+l("0xe85")+l("0xe86")+"optional int32 SymbolID = 10  [default = 0];"+l("0xe88")+l("0xe89")+"optional uint32\tFontNameIndex=13 [default =0];"+l("0xe8b")+l("0xe8c")+l("0xe99")+"};"+l("0xe9a")+"repeated string s = 1;}"+l("0xe8e")+"required int64 Version = 1;"+l("0xe9b")+l("0xe90")+"repeated PBPOI POIS = 4;};";if("undefined"==typeof dcodeIO||!dcodeIO[l("0xe92")])throw new Error("ProtoBuf.js is not present. Please see www/index.html for manual setup instructions.");return dcodeIO[l("0xe92")][l("0xe94")](e,l("0xe96"),l("0xe9c")).build(l("0xe96")).PBPOITile}(),n=function(){var e="option optimize_for = LITE_RUNTIME;"+l("0xe7b")+l("0xe7c")+"ePoint = 0;"+l("0xe7e")+l("0xe7f")+"};enum enumZCoordType {eCloseGround = 0;eCloseSeaSurface = 1;eRelativelyGround = 2;eAbsolute = 3;};message PBPOI{"+l("0xe82")+l("0xe83")+"repeated double Coordinates =3 [packed=true];"+l("0xe85")+l("0xe9d")+l("0xe9e")+l("0xe87")+l("0xe88")+l("0xe89")+l("0xe8a")+l("0xe8b")+l("0xe8c")+l("0xe99")+l("0xe9f")+l("0xea0")+"};"+l("0xe9a")+l("0xe8d")+"}message PBPOITile{"+l("0xe8f")+"required int64 TileKey = 2;"+l("0xe90")+l("0xe91")+"};";if(("undefined"==typeof dcodeIO?"undefined":u()(dcodeIO))===l("0x2b6")||!dcodeIO[l("0xe92")])throw new Error(l("0xe93"));return dcodeIO[l("0xe92")][l("0xe94")](e,l("0xe96"),l("0xea1"))[l("0xd22")](l("0xe96"))[l("0xe97")]}();t[l("0xe4a")][l("0x0")]=0;var s,a=e;try{s=n.decode(a)}catch(e){console.log(e)}if(!s)try{s=i[l("0xea2")](a)}catch(e){console.log(e[l("0xa63")]),s=r[l("0xea2")](a)}t[l("0xea3")]=o()(s[l("0xea4")][l("0x531")]()),t[l("0xea5")]=o()(s[l("0xea6")][l("0x531")]()),t.stringTable=[];for(var f=s.StringTable.s.length,h=0;h<f;h++)t[l("0xe50")][l("0x17c")](s[l("0xea7")].s[h].toString());return s[l("0xea8")][l("0x1e4")]((function(e){var r={};r[l("0xe2b")]=o()(e[l("0xea9")][l("0x531")]())+"_"+t[l("0xea5")],r.name=e[l("0xe48")][l("0x531")](),r[l("0xe5e")]=o()(e[l("0xeaa")].toString()),r[l("0xe61")]=e[l("0xeab")],r[l("0xe57")]=e[l("0xeac")],r[l("0xead")]=e[l("0xeae")],r[l("0xeaf")]=e[l("0xeb0")],r[l("0xe56")]=e.FontColor,e[l("0xeb1")]&&(r[l("0xeb2")]=e[l("0xeb1")]),r[l("0xeb3")]=e.GeometryType,r[l("0xe4d")]=e[l("0xeb4")],r[l("0xe64")]=u()(e.Priority)===l("0x2b6")?null:e.Priority,r[l("0xeb5")]=u()(e[l("0xeb6")])===l("0x2b6")?null:e[l("0xeb6")],r[l("0xe53")]=u()(e.FontStyle)===l("0x2b6")?null:e[l("0xeb7")],r.shiningSize=u()(e.ShiningSize)===l("0x2b6")?null:e[l("0xeb8")],t[l("0xe4a")][l("0x17c")](r)})),t};e[l("0xeb9")]=r}(n),function(){function e(e){throw e}var t=void 0,r=this;function i(e,i){var n,o=e[l("0x7f2")]("."),s=r;o[0]in s||!s[l("0xf4f")]||s[l("0xf4f")](l("0xf50")+o[0]);for(;o.length&&(n=o.shift());)o[l("0x0")]||i===t?s=s[n]?s[n]:s[n]={}:s[n]=i}var n="undefined"!=typeof Uint8Array&&l("0x2b6")!==("undefined"==typeof Uint16Array?"undefined":u()(Uint16Array))&&l("0x2b6")!==("undefined"==typeof Uint32Array?"undefined":u()(Uint32Array))&&l("0x2b6")!==("undefined"==typeof DataView?"undefined":u()(DataView));function o(t,r){this[l("0x286")]=l("0x49f")===u()(r)?r:0,this.i=0,this.buffer=t instanceof(n?Uint8Array:Array)?t:new(n?Uint8Array:Array)(32768),2*this.buffer[l("0x0")]<=this[l("0x286")]&&e(Error(l("0xf51"))),this.buffer.length<=this.index&&this.f()}o.prototype.f=function(){var e,t=this[l("0x4b3")],r=t[l("0x0")],i=new(n?Uint8Array:Array)(r<<1);if(n)i.set(t);else for(e=0;e<r;++e)i[e]=t[e];return this[l("0x4b3")]=i},o.prototype.d=function(e,t,r){var i,n=this[l("0x4b3")],o=this[l("0x286")],s=this.i,a=n[o];if(r&&1<t&&(e=8<t?(d[255&e]<<24|d[e>>>8&255]<<16|d[e>>>16&255]<<8|d[e>>>24&255])>>32-t:d[e]>>8-t),t+s<8)a=a<<t|e,s+=t;else for(i=0;i<t;++i)a=a<<1|e>>t-i-1&1,8==++s&&(s=0,n[o++]=d[a],a=0,o===n[l("0x0")]&&(n=this.f()));n[o]=a,this[l("0x4b3")]=n,this.i=s,this[l("0x286")]=o},o.prototype.finish=function(){var e=this[l("0x4b3")],t=this[l("0x286")];return 0<this.i&&(e[t]<<=8-this.i,e[t]=d[e[t]],t++),n?e.subarray(0,t):(e.length=t,e)};var s,a=new(n?Uint8Array:Array)(256);for(s=0;s<256;++s){for(var f=c=s,h=7,c=c>>>1;c;c>>>=1)f<<=1,f|=1&c,--h;a[s]=(f<<h&255)>>>0}var d=a;function p(e){this[l("0x4b3")]=new(n?Uint16Array:Array)(2*e),this.length=0}function x(e){var t,r,i,o,s,a,f,u,h,c,d=e[l("0x0")],p=0,x=Number[l("0x31")];for(u=0;u<d;++u)e[u]>p&&(p=e[u]),e[u]<x&&(x=e[u]);for(t=1<<p,r=new(n?Uint32Array:Array)(t),i=1,o=0,s=2;i<=p;){for(u=0;u<d;++u)if(e[u]===i){for(f=o,h=a=0;h<i;++h)a=a<<1|1&f,f>>=1;for(c=i<<16|u,h=a;h<t;h+=s)r[h]=c;++o}++i,o<<=1,s<<=1}return[r,p,x]}function g(e,t){this.h=y,this.w=0,this.input=n&&e instanceof Array?new Uint8Array(e):e,this.b=0,t&&(t[l("0xf54")]&&(this.w=t[l("0xf54")]),l("0x49f")===u()(t[l("0xf55")])&&(this.h=t.compressionType),t[l("0xf56")]&&(this.a=n&&t[l("0xf56")]instanceof Array?new Uint8Array(t[l("0xf56")]):t[l("0xf56")]),"number"==typeof t.outputIndex&&(this.b=t[l("0xf57")])),this.a||(this.a=new(n?Uint8Array:Array)(32768))}p.prototype[l("0xf52")]=function(e){return 2*((e-2)/4|0)},p.prototype[l("0x17c")]=function(e,t){var r,i,n,o=this[l("0x4b3")];for(r=this[l("0x0")],o[this[l("0x0")]++]=t,o[this[l("0x0")]++]=e;0<r&&(i=this[l("0xf52")](r),o[r]>o[i]);)n=o[r],o[r]=o[i],o[i]=n,n=o[r+1],o[r+1]=o[i+1],o[i+1]=n,r=i;return this[l("0x0")]},p.prototype[l("0x25c")]=function(){var e,t,r,i,n,o=this.buffer;for(t=o[0],e=o[1],this[l("0x0")]-=2,o[0]=o[this.length],o[1]=o[this[l("0x0")]+1],n=0;!((i=2*n+2)>=this[l("0x0")])&&(i+2<this[l("0x0")]&&o[i+2]>o[i]&&(i+=2),o[i]>o[n]);)r=o[n],o[n]=o[i],o[i]=r,r=o[n+1],o[n+1]=o[i+1],o[i+1]=r,n=i;return{index:e,value:t,length:this.length}};var v,y=2,b={NONE:0,r:1,k:y,O:3},m=[];for(v=0;v<288;v++)switch(!0){case v<=143:m[l("0x17c")]([v+48,8]);break;case v<=255:m.push([v-144+400,9]);break;case v<=279:m[l("0x17c")]([v-256+0,7]);break;case v<=287:m.push([v-280+192,8]);break;default:e("invalid literal: "+v)}function w(e,t){this[l("0x0")]=e,this.H=t}g.prototype.j=function(){var r,i,s,a,f=this.input;switch(this.h){case 0:for(s=0,a=f[l("0x0")];s<a;){var u,h,c,d=i=n?f[l("0xaaf")](s,s+65535):f.slice(s,s+65535),p=(s+=i.length)===a,x=t,g=t,v=this.a,b=this.b;if(n){for(v=new Uint8Array(this.a[l("0x4b3")]);v[l("0x0")]<=b+d[l("0x0")]+5;)v=new Uint8Array(v[l("0x0")]<<1);v.set(this.a)}if(u=p?1:0,v[b++]=0|u,c=65536+~(h=d[l("0x0")])&65535,v[b++]=255&h,v[b++]=h>>>8&255,v[b++]=255&c,v[b++]=c>>>8&255,n)v.set(d,b),b+=d[l("0x0")],v=v.subarray(0,b);else{for(x=0,g=d.length;x<g;++x)v[b++]=d[x];v[l("0x0")]=b}this.b=b,this.a=v}break;case 1:var w=new o(n?new Uint8Array(this.a[l("0x4b3")]):this.a,this.b);w.d(1,1,!0),w.d(1,2,!0);var E,T,I,P=S(this,f);for(E=0,T=P[l("0x0")];E<T;E++)if(I=P[E],o.prototype.d.apply(w,m[I]),256<I)w.d(P[++E],P[++E],!0),w.d(P[++E],5),w.d(P[++E],P[++E],!0);else if(256===I)break;this.a=w[l("0xf2f")](),this.b=this.a[l("0x0")];break;case y:var O,k,R,L,N,U,Y,B,C,M,F,V,j,z,D,G=new o(n?new Uint8Array(this.a[l("0x4b3")]):this.a,this.b),q=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],W=Array(19);for(O=y,G.d(1,1,!0),G.d(O,2,!0),k=S(this,f),Y=_(U=A(this.M,15)),C=_(B=A(this.L,7)),R=286;257<R&&0===U[R-1];R--);for(L=30;1<L&&0===B[L-1];L--);var Z,X,H,$,J,K,Q=R,ee=L,te=new(n?Uint32Array:Array)(Q+ee),re=new(n?Uint32Array:Array)(316),ie=new(n?Uint8Array:Array)(19);for(Z=X=0;Z<Q;Z++)te[X++]=U[Z];for(Z=0;Z<ee;Z++)te[X++]=B[Z];if(!n)for(Z=0,$=ie.length;Z<$;++Z)ie[Z]=0;for(Z=J=0,$=te.length;Z<$;Z+=X){for(X=1;Z+X<$&&te[Z+X]===te[Z];++X);if(H=X,0===te[Z])if(H<3)for(;0<H--;)ie[re[J++]=0]++;else for(;0<H;)H-3<(K=H<138?H:138)&&K<H&&(K=H-3),K<=10?(re[J++]=17,re[J++]=K-3,ie[17]++):(re[J++]=18,re[J++]=K-11,ie[18]++),H-=K;else if(re[J++]=te[Z],ie[te[Z]]++,--H<3)for(;0<H--;)re[J++]=te[Z],ie[te[Z]]++;else for(;0<H;)H-3<(K=H<6?H:6)&&K<H&&(K=H-3),re[J++]=16,re[J++]=K-3,ie[16]++,H-=K}for(r=n?re[l("0xaaf")](0,J):re[l("0x1d5")](0,J),M=A(ie,7),z=0;z<19;z++)W[z]=M[q[z]];for(N=19;4<N&&0===W[N-1];N--);for(F=_(M),G.d(R-257,5,!0),G.d(L-1,5,!0),G.d(N-4,4,!0),z=0;z<N;z++)G.d(W[z],3,!0);for(z=0,D=r[l("0x0")];z<D;z++)if(V=r[z],G.d(F[V],M[V],!0),16<=V){switch(z++,V){case 16:j=2;break;case 17:j=3;break;case 18:j=7;break;default:e(l("0xf58")+V)}G.d(r[z],j,!0)}var ne,oe,se,ae,fe,ue,he,le,ce=[Y,U],de=[C,B];for(fe=ce[0],ue=ce[1],he=de[0],le=de[1],ne=0,oe=k[l("0x0")];ne<oe;++ne)if(se=k[ne],G.d(fe[se],ue[se],!0),256<se)G.d(k[++ne],k[++ne],!0),ae=k[++ne],G.d(he[ae],le[ae],!0),G.d(k[++ne],k[++ne],!0);else if(256===se)break;this.a=G.finish(),this.b=this.a[l("0x0")];break;default:e("invalid compression type")}return this.a};var E=function(){function t(t){switch(!0){case 3===t:return[257,t-3,0];case 4===t:return[258,t-4,0];case 5===t:return[259,t-5,0];case 6===t:return[260,t-6,0];case 7===t:return[261,t-7,0];case 8===t:return[262,t-8,0];case 9===t:return[263,t-9,0];case 10===t:return[264,t-10,0];case t<=12:return[265,t-11,1];case t<=14:return[266,t-13,1];case t<=16:return[267,t-15,1];case t<=18:return[268,t-17,1];case t<=22:return[269,t-19,2];case t<=26:return[270,t-23,2];case t<=30:return[271,t-27,2];case t<=34:return[272,t-31,2];case t<=42:return[273,t-35,3];case t<=50:return[274,t-43,3];case t<=58:return[275,t-51,3];case t<=66:return[276,t-59,3];case t<=82:return[277,t-67,4];case t<=98:return[278,t-83,4];case t<=114:return[279,t-99,4];case t<=130:return[280,t-115,4];case t<=162:return[281,t-131,5];case t<=194:return[282,t-163,5];case t<=226:return[283,t-195,5];case t<=257:return[284,t-227,5];case 258===t:return[285,t-258,0];default:e("invalid length: "+t)}}var r,i,n=[];for(r=3;r<=258;r++)i=t(r),n[r]=i[2]<<24|i[1]<<16|i[0];return n}(),T=n?new Uint32Array(E):E;function S(r,i){function o(t,r){var i,n,o,s,a=t.H,f=[],u=0;switch(i=T[t[l("0x0")]],f[u++]=65535&i,f[u++]=i>>16&255,f[u++]=i>>24,!0){case 1===a:n=[0,a-1,0];break;case 2===a:n=[1,a-2,0];break;case 3===a:n=[2,a-3,0];break;case 4===a:n=[3,a-4,0];break;case a<=6:n=[4,a-5,1];break;case a<=8:n=[5,a-7,1];break;case a<=12:n=[6,a-9,2];break;case a<=16:n=[7,a-13,2];break;case a<=24:n=[8,a-17,3];break;case a<=32:n=[9,a-25,3];break;case a<=48:n=[10,a-33,4];break;case a<=64:n=[11,a-49,4];break;case a<=96:n=[12,a-65,5];break;case a<=128:n=[13,a-97,5];break;case a<=192:n=[14,a-129,6];break;case a<=256:n=[15,a-193,6];break;case a<=384:n=[16,a-257,7];break;case a<=512:n=[17,a-385,7];break;case a<=768:n=[18,a-513,8];break;case a<=1024:n=[19,a-769,8];break;case a<=1536:n=[20,a-1025,9];break;case a<=2048:n=[21,a-1537,9];break;case a<=3072:n=[22,a-2049,10];break;case a<=4096:n=[23,a-3073,10];break;case a<=6144:n=[24,a-4097,11];break;case a<=8192:n=[25,a-6145,11];break;case a<=12288:n=[26,a-8193,12];break;case a<=16384:n=[27,a-12289,12];break;case a<=24576:n=[28,a-16385,13];break;case a<=32768:n=[29,a-24577,13];break;default:e(l("0xf59"))}for(i=n,f[u++]=i[0],f[u++]=i[1],f[u++]=i[2],o=0,s=f[l("0x0")];o<s;++o)v[y++]=f[o];m[f[0]]++,w[f[3]]++,b=t[l("0x0")]+r-1,p=null}var s,a,f,u,h,c,d,p,x,g={},v=n?new Uint16Array(2*i[l("0x0")]):[],y=0,b=0,m=new(n?Uint32Array:Array)(286),w=new(n?Uint32Array:Array)(30),E=r.w;if(!n){for(f=0;f<=285;)m[f++]=0;for(f=0;f<=29;)w[f++]=0}for(m[256]=1,s=0,a=i[l("0x0")];s<a;++s){for(f=h=0,u=3;f<u&&s+f!==a;++f)h=h<<8|i[s+f];if(g[h]===t&&(g[h]=[]),c=g[h],!(0<b--)){for(;0<c[l("0x0")]&&32768<s-c[0];)c[l("0x513")]();if(a<=s+3){for(p&&o(p,-1),f=0,u=a-s;f<u;++f)x=i[s+f],v[y++]=x,++m[x];break}0<c[l("0x0")]?(d=I(i,s,c),p?p[l("0x0")]<d.length?(x=i[s-1],v[y++]=x,++m[x],o(d,0)):o(p,-1):d[l("0x0")]<E?p=d:o(d,0)):p?o(p,-1):(x=i[s],v[y++]=x,++m[x])}c.push(s)}return v[y++]=256,m[256]++,r.M=m,r.L=w,n?v[l("0xaaf")](0,y):v}function I(e,t,r){var i,n,o,s,a,f,u=0,h=e.length;s=0,f=r[l("0x0")];e:for(;s<f;s++){if(i=r[f-s-1],(o=3)<u){for(a=u;3<a;a--)if(e[i+a-1]!==e[t+a-1])continue e;o=u}for(;o<258&&t+o<h&&e[i+o]===e[t+o];)++o;if(u<o&&(n=i,u=o),258===o)break}return new w(u,t-n)}function A(e,t){var r,i,o,s,a,f=e[l("0x0")],u=new p(572),h=new(n?Uint8Array:Array)(f);if(!n)for(s=0;s<f;s++)h[s]=0;for(s=0;s<f;++s)0<e[s]&&u[l("0x17c")](s,e[s]);if(r=Array(u[l("0x0")]/2),i=new(n?Uint32Array:Array)(u[l("0x0")]/2),1===r[l("0x0")])return h[u[l("0x25c")]()[l("0x286")]]=1,h;for(s=0,a=u[l("0x0")]/2;s<a;++s)r[s]=u[l("0x25c")](),i[s]=r[s][l("0x3")];for(o=function(e,t,r){function i(e){var r=p[e][x[e]];r===t?(i(e+1),i(e+1)):--c[r],++x[e]}var o,s,a,f,u,h=new(n?Uint16Array:Array)(r),l=new(n?Uint8Array:Array)(r),c=new(n?Uint8Array:Array)(t),d=Array(r),p=Array(r),x=Array(r),g=(1<<r)-t,v=1<<r-1;for(h[r-1]=t,s=0;s<r;++s)g<v?l[s]=0:(l[s]=1,g-=v),g<<=1,h[r-2-s]=(h[r-1-s]/2|0)+t;for(h[0]=l[0],d[0]=Array(h[0]),p[0]=Array(h[0]),s=1;s<r;++s)h[s]>2*h[s-1]+l[s]&&(h[s]=2*h[s-1]+l[s]),d[s]=Array(h[s]),p[s]=Array(h[s]);for(o=0;o<t;++o)c[o]=r;for(a=0;a<h[r-1];++a)d[r-1][a]=e[a],p[r-1][a]=a;for(o=0;o<r;++o)x[o]=0;for(1===l[r-1]&&(--c[0],++x[r-1]),s=r-2;0<=s;--s){for(f=o=0,u=x[s+1],a=0;a<h[s];a++)(f=d[s+1][u]+d[s+1][u+1])>e[o]?(d[s][a]=f,p[s][a]=t,u+=2):(d[s][a]=e[o],p[s][a]=o,++o);x[s]=0,1===l[s]&&i(s)}return c}(i,i[l("0x0")],t),s=0,a=r[l("0x0")];s<a;++s)h[r[s][l("0x286")]]=o[s];return h}function _(e){var t,r,i,o,s=new(n?Uint16Array:Array)(e.length),a=[],f=[],u=0;for(t=0,r=e[l("0x0")];t<r;t++)a[e[t]]=1+(0|a[e[t]]);for(t=1,r=16;t<=r;t++)f[t]=u,u+=0|a[t],u<<=1;for(t=0,r=e.length;t<r;t++)for(u=f[e[t]],f[e[t]]+=1,i=s[t]=0,o=e[t];i<o;i++)s[t]=s[t]<<1|1&u,u>>>=1;return s}function P(t,r){switch(this.l=[],this.m=32768,this.e=this.g=this.c=this.q=0,this.input=n?new Uint8Array(t):t,this.s=!1,this.n=k,this.C=!1,(r||(r={},0))&&(r[l("0x286")]&&(this.c=r[l("0x286")]),r[l("0xf5a")]&&(this.m=r.bufferSize),r.bufferType&&(this.n=r[l("0xf5b")]),r[l("0xf5c")]&&(this.C=r[l("0xf5c")])),this.n){case O:this.b=32768,this.a=new(n?Uint8Array:Array)(32768+this.m+258);break;case k:this.b=0,this.a=new(n?Uint8Array:Array)(this.m),this.f=this.K,this.t=this.I,this.o=this.J;break;default:e(Error(l("0xf5d")))}}var O=0,k=1,R=O,L=k;P.prototype.p=function(){for(;!this.s;){var r=J(this,3);switch(1&r&&(this.s=!0),r>>>=1){case 0:var i=this.input,o=this.c,s=this.a,a=this.b,f=i.length,u=t,h=s[l("0x0")],c=t;switch(this.e=this.g=0,f<=o+1&&e(Error("invalid uncompressed block header: LEN")),u=i[o++]|i[o++]<<8,f<=o+1&&e(Error("invalid uncompressed block header: NLEN")),u===~(i[o++]|i[o++]<<8)&&e(Error(l("0xf5e"))),o+u>i[l("0x0")]&&e(Error(l("0xf5f"))),this.n){case O:for(;a+u>s[l("0x0")];){if(u-=c=h-a,n)s.set(i[l("0xaaf")](o,o+c),a),a+=c,o+=c;else for(;c--;)s[a++]=i[o++];this.b=a,s=this.f(),a=this.b}break;case k:for(;a+u>s[l("0x0")];)s=this.f({v:2});break;default:e(Error("invalid inflate mode"))}if(n)s[l("0xf60")](i.subarray(o,o+u),a),a+=u,o+=u;else for(;u--;)s[a++]=i[o++];this.c=o,this.b=a,this.a=s;break;case 1:this.o(X,$);break;case 2:Q(this);break;default:e(Error(l("0xf61")+r))}}return this.t()};var N,U,Y=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],B=n?new Uint16Array(Y):Y,C=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],M=n?new Uint16Array(C):C,F=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],V=n?new Uint8Array(F):F,j=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],z=n?new Uint16Array(j):j,D=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],G=n?new Uint8Array(D):D,q=new(n?Uint8Array:Array)(288);for(N=0,U=q[l("0x0")];N<U;++N)q[N]=N<=143?8:N<=255?9:N<=279?7:8;var W,Z,X=x(q),H=new(n?Uint8Array:Array)(30);for(W=0,Z=H[l("0x0")];W<Z;++W)H[W]=5;var $=x(H);function J(t,r){for(var i,n=t.g,o=t.e,s=t.input,a=t.c,f=s[l("0x0")];o<r;)f<=a&&e(Error(l("0xf5f"))),n|=s[a++]<<o,o+=8;return i=n&(1<<r)-1,t.g=n>>>r,t.e=o-r,t.c=a,i}function K(e,t){for(var r,i,n=e.g,o=e.e,s=e.input,a=e.c,f=s[l("0x0")],u=t[0],h=t[1];o<h&&!(f<=a);)n|=s[a++]<<o,o+=8;return i=(r=u[n&(1<<h)-1])>>>16,e.g=n>>i,e.e=o-i,e.c=a,65535&r}function Q(e){function t(e,t,r){var i,n,o,s=this.z;for(o=0;o<e;)switch(i=K(this,t)){case 16:for(n=3+J(this,2);n--;)r[o++]=s;break;case 17:for(n=3+J(this,3);n--;)r[o++]=0;s=0;break;case 18:for(n=11+J(this,7);n--;)r[o++]=0;s=0;break;default:s=r[o++]=i}return this.z=s,r}var r,i,o,s,a=J(e,5)+257,f=J(e,5)+1,u=J(e,4)+4,h=new(n?Uint8Array:Array)(B[l("0x0")]);for(s=0;s<u;++s)h[B[s]]=J(e,3);if(!n)for(s=u,u=h.length;s<u;++s)h[B[s]]=0;r=x(h),i=new(n?Uint8Array:Array)(a),o=new(n?Uint8Array:Array)(f),e.z=0,e.o(x(t.call(e,a,r,i)),x(t.call(e,f,r,o)))}function ee(e){if(l("0x9fe")===u()(e)){var t,r,i=e.split("");for(t=0,r=i[l("0x0")];t<r;t++)i[t]=(255&i[t].charCodeAt(0))>>>0;e=i}for(var n,o=1,s=0,a=e.length,f=0;0<a;){for(a-=n=1024<a?1024:a;s+=o+=e[f++],--n;);o%=65521,s%=65521}return(s<<16|o)>>>0}function te(t,r){var i,n;if(this.input=t,this.c=0,(r||(r={},0))&&(r[l("0x286")]&&(this.c=r.index),r.verify&&(this.N=r[l("0xf62")])),i=t[this.c++],n=t[this.c++],(15&i)===re)this.method=re;else e(Error(l("0xf63")));0!=((i<<8)+n)%31&&e(Error(l("0xf64")+((i<<8)+n)%31)),32&n&&e(Error(l("0xf65"))),this.B=new P(t,{index:this.c,bufferSize:r[l("0xf5a")],bufferType:r[l("0xf5b")],resize:r[l("0xf5c")]})}P.prototype.o=function(e,t){var r=this.a,i=this.b;this.u=e;for(var n,o,s,a,f=r[l("0x0")]-258;256!==(n=K(this,e));)if(n<256)f<=i&&(this.b=i,r=this.f(),i=this.b),r[i++]=n;else for(a=M[o=n-257],0<V[o]&&(a+=J(this,V[o])),n=K(this,t),s=z[n],0<G[n]&&(s+=J(this,G[n])),f<=i&&(this.b=i,r=this.f(),i=this.b);a--;)r[i]=r[i++-s];for(;8<=this.e;)this.e-=8,this.c--;this.b=i},P.prototype.J=function(e,t){var r=this.a,i=this.b;this.u=e;for(var n,o,s,a,f=r.length;256!==(n=K(this,e));)if(n<256)f<=i&&(f=(r=this.f()).length),r[i++]=n;else for(a=M[o=n-257],0<V[o]&&(a+=J(this,V[o])),n=K(this,t),s=z[n],0<G[n]&&(s+=J(this,G[n])),f<i+a&&(f=(r=this.f()).length);a--;)r[i]=r[i++-s];for(;8<=this.e;)this.e-=8,this.c--;this.b=i},P.prototype.f=function(){var e,t,r=new(n?Uint8Array:Array)(this.b-32768),i=this.b-32768,o=this.a;if(n)r.set(o[l("0xaaf")](32768,r.length));else for(e=0,t=r.length;e<t;++e)r[e]=o[e+32768];if(this.l[l("0x17c")](r),this.q+=r[l("0x0")],n)o[l("0xf60")](o[l("0xaaf")](i,32768+i));else for(e=0;e<32768;++e)o[e]=o[i+e];return this.b=32768,o},P.prototype.K=function(e){var t,r,i,o=this.input[l("0x0")]/this.c+1|0,s=this.input,a=this.a;return e&&("number"==typeof e.v&&(o=e.v),l("0x49f")===u()(e.G)&&(o+=e.G)),r=o<2?(i=(s.length-this.c)/this.u[2]/2*258|0)<a[l("0x0")]?a.length+i:a[l("0x0")]<<1:a.length*o,n?(t=new Uint8Array(r)).set(a):t=a,this.a=t},P.prototype.t=function(){var e,t,r,i,o,s=0,a=this.a,f=this.l,u=new(n?Uint8Array:Array)(this.q+(this.b-32768));if(0===f.length)return n?this.a[l("0xaaf")](32768,this.b):this.a.slice(32768,this.b);for(t=0,r=f[l("0x0")];t<r;++t)for(i=0,o=(e=f[t]).length;i<o;++i)u[s++]=e[i];for(t=32768,r=this.b;t<r;++t)u[s++]=a[t];return this.l=[],this[l("0x4b3")]=u},P.prototype.I=function(){var e,t=this.b;return n?this.C?(e=new Uint8Array(t))[l("0xf60")](this.a[l("0xaaf")](0,t)):e=this.a[l("0xaaf")](0,t):(this.a[l("0x0")]>t&&(this.a[l("0x0")]=t),e=this.a),this[l("0x4b3")]=e},te.prototype.p=function(){var t,r=this.input;return t=this.B.p(),this.c=this.B.c,this.N&&(r[this.c++]<<24|r[this.c++]<<16|r[this.c++]<<8|r[this.c++])>>>0!==ee(t)&&e(Error(l("0xf66"))),t};var re=8;function ie(e,t){this.input=e,this.a=new(n?Uint8Array:Array)(32768),this.h=ne.k;var r,i={};for(r in(t||(t={},0))&&l("0x49f")===u()(t.compressionType)&&(this.h=t[l("0xf55")]),t)i[r]=t[r];i[l("0xf56")]=this.a,this.A=new g(this.input,i)}var ne=b;function oe(e,t){var r,n,o,s;if(Object[l("0xdcb")])r=Object[l("0xdcb")](t);else for(n in r=[],o=0,t)r[o++]=n;for(o=0,s=r[l("0x0")];o<s;++o)i(e+"."+(n=r[o]),t[n])}ie.prototype.j=function(){var t,r,i,o,s,a,f,u=0;if(f=this.a,(t=re)===re)r=Math[l("0xf67")]*Math[l("0x133")](32768)-8;else e(Error(l("0xf68")));if(i=r<<4|t,f[u++]=i,t===re)switch(this.h){case ne.NONE:s=0;break;case ne.r:s=1;break;case ne.k:s=2;break;default:e(Error("unsupported compression type"))}else e(Error(l("0xf68")));return o=s<<6|0,f[u++]=o|31-(256*i+o)%31,a=ee(this.input),this.A.b=u,u=(f=this.A.j()).length,n&&((f=new Uint8Array(f[l("0x4b3")])).length<=u+4&&(this.a=new Uint8Array(f[l("0x0")]+4),this.a[l("0xf60")](f),f=this.a),f=f[l("0xaaf")](0,u+4)),f[u++]=a>>24&255,f[u++]=a>>16&255,f[u++]=a>>8&255,f[u++]=255&a,f},i(l("0xf69"),te),i(l("0xf6a"),te.prototype.p),oe(l("0xf6b"),{ADAPTIVE:L,BLOCK:R}),i(l("0xf6c"),ie),i(l("0xf6d"),(function(e,t){return new ie(e,t).j()})),i(l("0xf6e"),ie.prototype.j),oe("Zlib.Deflate.CompressionType",{NONE:ne[l("0xe5d")],FIXED:ne.r,DYNAMIC:ne.k})}.call(window),r(237)}]);const o=i.Cesium,s=i.layer.BaseLayer;class a extends s{get layer(){return this.wtfs}_showHook(e){e?this._addedHook():this._removedHook()}_addedHook(){if(!this.show)return;const e=this.options.url||"https://t{s}.tianditu.gov.cn/mapservice/GetTiles",t=this.options.key||i.Token.tianditu;let r;if(r=Array.isArray(this.options.subdomains)?this.options.subdomains.slice():o.defined(this.options.subdomains)&&this.options.subdomains.length>0?this.options.subdomains.split(""):["0","1","2","3","4","5","6","7"],!o.GeoWTFS)return void console.error("请确认正确引入天地图官方提供的cesiumTdt.js文件，并确认无冲突！");const n=new o.GeoWTFS({metadata:{boundBox:{minX:-180,minY:-90,maxX:180,maxY:90},minLevel:1,maxLevel:20},aotuCollide:!0,collisionPadding:[5,10,8,5],serverFirstStyle:!0,...this.options,viewer:this._map.viewer,subdomains:r,labelGraphics:i.LabelStyleConver.toCesiumVal(this.options.label,{font:"28px sans-serif",fontSize:28,fillColor:o.Color.WHITE,scale:.5,outlineColor:o.Color.BLACK,outlineWidth:5,style:o.LabelStyle.FILL_AND_OUTLINE,showBackground:!1,backgroundColor:o.Color.RED,backgroundPadding:new o.Cartesian2(10,10),horizontalOrigin:o.HorizontalOrigin.CENTER,verticalOrigin:o.VerticalOrigin.BOTTOM,eyeOffset:o.Cartesian3.ZERO,pixelOffset:new o.Cartesian2(0,-10)}),billboardGraphics:i.BillboardStyleConver.toCesiumVal(this.options.billboard,{horizontalOrigin:o.HorizontalOrigin.CENTER,verticalOrigin:o.VerticalOrigin.CENTER,eyeOffset:o.Cartesian3.ZERO,pixelOffset:o.Cartesian2.ZERO,alignedAxis:o.Cartesian3.ZERO,color:o.Color.WHITE,rotation:0,scale:1,width:18,height:18})});n.getTileUrl=function(){return e+"?lxys={z},{x},{y}&tk="+t},n.initTDT(this.options.boundBoxList||[{x:6,y:1,level:2,boundBox:{minX:90,minY:0,maxX:135,maxY:45}},{x:7,y:1,level:2,boundBox:{minX:135,minY:0,maxX:180,maxY:45}},{x:6,y:0,level:2,boundBox:{minX:90,minY:45,maxX:135,maxY:90}},{x:7,y:0,level:2,boundBox:{minX:135,minY:45,maxX:180,maxY:90}},{x:5,y:1,level:2,boundBox:{minX:45,minY:0,maxX:90,maxY:45}},{x:4,y:1,level:2,boundBox:{minX:0,minY:0,maxX:45,maxY:45}},{x:5,y:0,level:2,boundBox:{minX:45,minY:45,maxX:90,maxY:90}},{x:4,y:0,level:2,boundBox:{minX:0,minY:45,maxX:45,maxY:90}},{x:6,y:2,level:2,boundBox:{minX:90,minY:-45,maxX:135,maxY:0}},{x:6,y:3,level:2,boundBox:{minX:90,minY:-90,maxX:135,maxY:-45}},{x:7,y:2,level:2,boundBox:{minX:135,minY:-45,maxX:180,maxY:0}},{x:5,y:2,level:2,boundBox:{minX:45,minY:-45,maxX:90,maxY:0}},{x:4,y:2,level:2,boundBox:{minX:0,minY:-45,maxX:45,maxY:0}},{x:3,y:1,level:2,boundBox:{minX:-45,minY:0,maxX:0,maxY:45}},{x:3,y:0,level:2,boundBox:{minX:-45,minY:45,maxX:0,maxY:90}},{x:2,y:0,level:2,boundBox:{minX:-90,minY:45,maxX:-45,maxY:90}},{x:0,y:1,level:2,boundBox:{minX:-180,minY:0,maxX:-135,maxY:45}},{x:1,y:0,level:2,boundBox:{minX:-135,minY:45,maxX:-90,maxY:90}},{x:0,y:0,level:2,boundBox:{minX:-180,minY:45,maxX:-135,maxY:90}}]),this.wtfs=n}_removedHook(){this.wtfs&&(this.wtfs.destroy(),delete this.wtfs)}}i.layer.TdtDmLayer=a,i.layer.register("tdt_dm",a);const f=i.Cesium;class u extends f.GeoTerrainProvider{constructor(e={}){if(!e.urls){const t=e.url||"https://t{s}.tianditu.gov.cn/mapservice/swdx",r=e.key||i.Token.tianditu;let n;n=Array.isArray(e.subdomains)?e.subdomains.slice():f.defined(e.subdomains)&&e.subdomains.length>0?e.subdomains.split(""):["0","1","2","3","4","5","6","7"];const o=[];n.forEach((e=>{const n=i.Util.template(t,{s:e})+"?T=elv_c&tk="+r;o.push(n)})),e.urls=o}super(e),this._layers=[]}}i.LayerUtil.registerTerrainProvider("tdt",u),i.provider.TdtTerrainProvider=u,e.TdtDmLayer=a,e.TdtTerrainProvider=u,Object.defineProperty(e,"__esModule",{value:!0})}));
