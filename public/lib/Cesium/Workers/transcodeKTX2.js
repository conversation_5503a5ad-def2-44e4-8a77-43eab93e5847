/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.115
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as r_}from"./chunk-ESSWH6Y6.js";import{a as K}from"./chunk-BPPZBFNC.js";import{a as ie}from"./chunk-6DW2D3WX.js";import{a as n_}from"./chunk-A7LPWAU6.js";import{b as __}from"./chunk-UNVMUIJM.js";import{a as Gt,c as sr,d as or,e as ge}from"./chunk-WVB7XP3Q.js";var o_=sr((it,Pt)=>{var Nt=function(){var i=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename<"u"&&(i=i||__filename),function(f){f=f||{};var r=typeof f<"u"?f:{},B,l;r.ready=new Promise(function(e,t){B=e,l=t});var P={},C;for(C in r)r.hasOwnProperty(C)&&(P[C]=r[C]);var h=[],y="./this.program",m=function(e,t){throw t},V=!1,d=!1,p=!1,Q=!1;V=typeof window=="object",d=typeof importScripts=="function",p=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",Q=!V&&!p&&!d;var u="";function X(e){return r.locateFile?r.locateFile(e,u):u+e}var se,le,W,J,g,Fe;p?(d?u=Gt("path").dirname(u)+"/":u=__dirname+"/",se=function(t,_){return g||(g=Gt("fs")),Fe||(Fe=Gt("path")),t=Fe.normalize(t),g.readFileSync(t,_?null:"utf8")},W=function(t){var _=se(t,!0);return _.buffer||(_=new Uint8Array(_)),Se(_.buffer),_},process.argv.length>1&&(y=process.argv[1].replace(/\\/g,"/")),h=process.argv.slice(2),process.on("uncaughtException",function(e){if(!(e instanceof rr))throw e}),process.on("unhandledRejection",Ke),m=function(e){process.exit(e)},r.inspect=function(){return"[Emscripten Module object]"}):Q?(typeof read<"u"&&(se=function(t){return read(t)}),W=function(t){var _;return typeof readbuffer=="function"?new Uint8Array(readbuffer(t)):(_=read(t,"binary"),Se(typeof _=="object"),_)},typeof scriptArgs<"u"?h=scriptArgs:typeof arguments<"u"&&(h=arguments),typeof quit=="function"&&(m=function(e){quit(e)}),typeof print<"u"&&(typeof console>"u"&&(console={}),console.log=print,console.warn=console.error=typeof printErr<"u"?printErr:print)):(V||d)&&(d?u=self.location.href:typeof document<"u"&&document.currentScript&&(u=document.currentScript.src),i&&(u=i),u.indexOf("blob:")!==0?u=u.substr(0,u.lastIndexOf("/")+1):u="",se=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},d&&(W=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),le=function(e,t,_){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){if(n.status==200||n.status==0&&n.response){t(n.response);return}_()},n.onerror=_,n.send(null)},J=function(e){document.title=e});var st=r.print||console.log.bind(console),ee=r.printErr||console.warn.bind(console);for(C in P)P.hasOwnProperty(C)&&(r[C]=P[C]);P=null,r.arguments&&(h=r.arguments),r.thisProgram&&(y=r.thisProgram),r.quit&&(m=r.quit);var De=0,Ie=function(e){De=e},ae;r.wasmBinary&&(ae=r.wasmBinary);var ot=r.noExitRuntime||!0;typeof WebAssembly!="object"&&Ke("no native wasm support detected");var ce,de=!1,be;function Se(e,t){e||Ke("Assertion failed: "+t)}var we=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function He(e,t,_){for(var n=t+_,s=t;e[s]&&!(s>=n);)++s;if(s-t>16&&e.subarray&&we)return we.decode(e.subarray(t,s));for(var a="";t<s;){var c=e[t++];if(!(c&128)){a+=String.fromCharCode(c);continue}var o=e[t++]&63;if((c&224)==192){a+=String.fromCharCode((c&31)<<6|o);continue}var R=e[t++]&63;if((c&240)==224?c=(c&15)<<12|o<<6|R:c=(c&7)<<18|o<<12|R<<6|e[t++]&63,c<65536)a+=String.fromCharCode(c);else{var T=c-65536;a+=String.fromCharCode(55296|T>>10,56320|T&1023)}}return a}function Xe(e,t){return e?He(D,e,t):""}function Rt(e,t,_,n){if(!(n>0))return 0;for(var s=_,a=_+n-1,c=0;c<e.length;++c){var o=e.charCodeAt(c);if(o>=55296&&o<=57343){var R=e.charCodeAt(++c);o=65536+((o&1023)<<10)|R&1023}if(o<=127){if(_>=a)break;t[_++]=o}else if(o<=2047){if(_+1>=a)break;t[_++]=192|o>>6,t[_++]=128|o&63}else if(o<=65535){if(_+2>=a)break;t[_++]=224|o>>12,t[_++]=128|o>>6&63,t[_++]=128|o&63}else{if(_+3>=a)break;t[_++]=240|o>>18,t[_++]=128|o>>12&63,t[_++]=128|o>>6&63,t[_++]=128|o&63}}return t[_]=0,_-s}function L(e,t,_){return Rt(e,D,t,_)}function j(e){for(var t=0,_=0;_<e.length;++_){var n=e.charCodeAt(_);n>=55296&&n<=57343&&(n=65536+((n&1023)<<10)|e.charCodeAt(++_)&1023),n<=127?++t:n<=2047?t+=2:n<=65535?t+=3:t+=4}return t}var Ae=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0;function at(e,t){for(var _=e,n=_>>1,s=n+t/2;!(n>=s)&&Ge[n];)++n;if(_=n<<1,_-e>32&&Ae)return Ae.decode(D.subarray(e,_));for(var a="",c=0;!(c>=t/2);++c){var o=fe[e+c*2>>1];if(o==0)break;a+=String.fromCharCode(o)}return a}function xe(e,t,_){if(_===void 0&&(_=2147483647),_<2)return 0;_-=2;for(var n=t,s=_<e.length*2?_/2:e.length,a=0;a<s;++a){var c=e.charCodeAt(a);fe[t>>1]=c,t+=2}return fe[t>>1]=0,t-n}function f_(e){return e.length*2}function T_(e,t){for(var _=0,n="";!(_>=t/4);){var s=H[e+_*4>>2];if(s==0)break;if(++_,s>=65536){var a=s-65536;n+=String.fromCharCode(55296|a>>10,56320|a&1023)}else n+=String.fromCharCode(s)}return n}function u_(e,t,_){if(_===void 0&&(_=2147483647),_<4)return 0;for(var n=t,s=n+_-4,a=0;a<e.length;++a){var c=e.charCodeAt(a);if(c>=55296&&c<=57343){var o=e.charCodeAt(++a);c=65536+((c&1023)<<10)|o&1023}if(H[t>>2]=c,t+=4,t+4>s)break}return H[t>>2]=0,t-n}function O_(e){for(var t=0,_=0;_<e.length;++_){var n=e.charCodeAt(_);n>=55296&&n<=57343&&++_,t+=4}return t}function l_(e,t){return e%t>0&&(e+=t-e%t),e}var ct,$e,D,fe,Ge,H,z,pt,Et;function Ut(e){ct=e,r.HEAP8=$e=new Int8Array(e),r.HEAP16=fe=new Int16Array(e),r.HEAP32=H=new Int32Array(e),r.HEAPU8=D=new Uint8Array(e),r.HEAPU16=Ge=new Uint16Array(e),r.HEAPU32=z=new Uint32Array(e),r.HEAPF32=pt=new Float32Array(e),r.HEAPF64=Et=new Float64Array(e)}var ur=r.INITIAL_MEMORY||16777216,Ve,vt=[],ht=[],K_=[],yt=[],M_=!1;function B_(){if(r.preRun)for(typeof r.preRun=="function"&&(r.preRun=[r.preRun]);r.preRun.length;)S_(r.preRun.shift());ke(vt)}function C_(){M_=!0,ke(ht)}function F_(){ke(K_)}function d_(){if(r.postRun)for(typeof r.postRun=="function"&&(r.postRun=[r.postRun]);r.postRun.length;)V_(r.postRun.shift());ke(yt)}function S_(e){vt.unshift(e)}function G_(e){ht.unshift(e)}function V_(e){yt.unshift(e)}var Te=0,At=null,Ne=null;function N_(e){Te++,r.monitorRunDependencies&&r.monitorRunDependencies(Te)}function P_(e){if(Te--,r.monitorRunDependencies&&r.monitorRunDependencies(Te),Te==0&&(At!==null&&(clearInterval(At),At=null),Ne)){var t=Ne;Ne=null,t()}}r.preloadedImages={},r.preloadedAudios={};function Ke(e){r.onAbort&&r.onAbort(e),e+="",ee(e),de=!0,be=1,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(e);throw l(t),t}function gt(e,t){return String.prototype.startsWith?e.startsWith(t):e.indexOf(t)===0}var p_="data:application/octet-stream;base64,";function Lt(e){return gt(e,p_)}var E_="file://";function mt(e){return gt(e,E_)}var x="basis_transcoder.wasm";Lt(x)||(x=X(x));function Dt(e){try{if(e==x&&ae)return new Uint8Array(ae);if(W)return W(e);throw"both async and sync fetching of the wasm failed"}catch(t){Ke(t)}}function U_(){if(!ae&&(V||d)){if(typeof fetch=="function"&&!mt(x))return fetch(x,{credentials:"same-origin"}).then(function(e){if(!e.ok)throw"failed to load wasm binary file at '"+x+"'";return e.arrayBuffer()}).catch(function(){return Dt(x)});if(le)return new Promise(function(e,t){le(x,function(_){e(new Uint8Array(_))},t)})}return Promise.resolve().then(function(){return Dt(x)})}function v_(){var e={a:er};function t(c,o){var R=c.exports;r.asm=R,ce=r.asm.K,Ut(ce.buffer),Ve=r.asm.O,G_(r.asm.L),P_("wasm-instantiate")}N_("wasm-instantiate");function _(c){t(c.instance)}function n(c){return U_().then(function(o){var R=WebAssembly.instantiate(o,e);return R}).then(c,function(o){ee("failed to asynchronously prepare wasm: "+o),Ke(o)})}function s(){return!ae&&typeof WebAssembly.instantiateStreaming=="function"&&!Lt(x)&&!mt(x)&&typeof fetch=="function"?fetch(x,{credentials:"same-origin"}).then(function(c){var o=WebAssembly.instantiateStreaming(c,e);return o.then(_,function(R){return ee("wasm streaming compile failed: "+R),ee("falling back to ArrayBuffer instantiation"),n(_)})}):n(_)}if(r.instantiateWasm)try{var a=r.instantiateWasm(e,t);return a}catch(c){return ee("Module.instantiateWasm callback failed with error: "+c),!1}return s().catch(l),{}}function ke(e){for(;e.length>0;){var t=e.shift();if(typeof t=="function"){t(r);continue}var _=t.func;typeof _=="number"?t.arg===void 0?Ve.get(_)():Ve.get(_)(t.arg):_(t.arg===void 0?null:t.arg)}}var We={};function je(e){for(;e.length;){var t=e.pop(),_=e.pop();_(t)}}function Pe(e){return this.fromWireType(z[e>>2])}var Me={},ue={},Ye={},h_=48,y_=57;function ze(e){if(e===void 0)return"_unknown";e=e.replace(/[^a-zA-Z0-9_]/g,"$");var t=e.charCodeAt(0);return t>=h_&&t<=y_?"_"+e:e}function qe(e,t){return e=ze(e),new Function("body","return function "+e+`() {
    "use strict";    return body.apply(this, arguments);
};
`)(t)}function ft(e,t){var _=qe(t,function(n){this.name=t,this.message=n;var s=new Error(n).stack;s!==void 0&&(this.stack=this.toString()+`
`+s.replace(/^Error(:[^\n]*)?\n/,""))});return _.prototype=Object.create(e.prototype),_.prototype.constructor=_,_.prototype.toString=function(){return this.message===void 0?this.name:this.name+": "+this.message},_}var It=void 0;function Ze(e){throw new It(e)}function oe(e,t,_){e.forEach(function(o){Ye[o]=t});function n(o){var R=_(o);R.length!==e.length&&Ze("Mismatched type converter count");for(var T=0;T<e.length;++T)q(e[T],R[T])}var s=new Array(t.length),a=[],c=0;t.forEach(function(o,R){ue.hasOwnProperty(o)?s[R]=ue[o]:(a.push(o),Me.hasOwnProperty(o)||(Me[o]=[]),Me[o].push(function(){s[R]=ue[o],++c,c===a.length&&n(s)}))}),a.length===0&&n(s)}function g_(e){var t=We[e];delete We[e];var _=t.rawConstructor,n=t.rawDestructor,s=t.fields,a=s.map(function(c){return c.getterReturnType}).concat(s.map(function(c){return c.setterArgumentType}));oe([e],a,function(c){var o={};return s.forEach(function(R,T){var O=R.fieldName,M=c[T],G=R.getter,N=R.getterContext,v=c[T+s.length],I=R.setter,k=R.setterContext;o[O]={read:function(Z){return M.fromWireType(G(N,Z))},write:function(Z,ye){var re=[];I(k,Z,v.toWireType(re,ye)),je(re)}}}),[{name:t.name,fromWireType:function(R){var T={};for(var O in o)T[O]=o[O].read(R);return n(R),T},toWireType:function(R,T){for(var O in o)if(!(O in T))throw new TypeError('Missing field:  "'+O+'"');var M=_();for(O in o)o[O].write(M,T[O]);return R!==null&&R.push(n,M),M},argPackAdvance:8,readValueFromPointer:Pe,destructorFunction:n}]})}function Qe(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}function L_(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);bt=e}var bt=void 0;function U(e){for(var t="",_=e;D[_];)t+=bt[D[_++]];return t}var Be=void 0;function S(e){throw new Be(e)}function q(e,t,_){if(_=_||{},!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=t.name;if(e||S('type "'+n+'" must have a positive integer typeid pointer'),ue.hasOwnProperty(e)){if(_.ignoreDuplicateRegistrations)return;S("Cannot register type '"+n+"' twice")}if(ue[e]=t,delete Ye[e],Me.hasOwnProperty(e)){var s=Me[e];delete Me[e],s.forEach(function(a){a()})}}function m_(e,t,_,n,s){var a=Qe(_);t=U(t),q(e,{name:t,fromWireType:function(c){return!!c},toWireType:function(c,o){return o?n:s},argPackAdvance:8,readValueFromPointer:function(c){var o;if(_===1)o=$e;else if(_===2)o=fe;else if(_===4)o=H;else throw new TypeError("Unknown boolean type size: "+t);return this.fromWireType(o[c>>a])},destructorFunction:null})}function D_(e){if(!(this instanceof Re)||!(e instanceof Re))return!1;for(var t=this.$$.ptrType.registeredClass,_=this.$$.ptr,n=e.$$.ptrType.registeredClass,s=e.$$.ptr;t.baseClass;)_=t.upcast(_),t=t.baseClass;for(;n.baseClass;)s=n.upcast(s),n=n.baseClass;return t===n&&_===s}function I_(e){return{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}}function Tt(e){function t(_){return _.$$.ptrType.registeredClass.name}S(t(e)+" instance already deleted")}var ut=!1;function wt(e){}function b_(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}function Ht(e){e.count.value-=1;var t=e.count.value===0;t&&b_(e)}function pe(e){return typeof FinalizationGroup>"u"?(pe=function(t){return t},e):(ut=new FinalizationGroup(function(t){for(var _=t.next();!_.done;_=t.next()){var n=_.value;n.ptr?Ht(n):console.warn("object already deleted: "+n.ptr)}}),pe=function(t){return ut.register(t,t.$$,t.$$),t},wt=function(t){ut.unregister(t.$$)},pe(e))}function w_(){if(this.$$.ptr||Tt(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e=pe(Object.create(Object.getPrototypeOf(this),{$$:{value:I_(this.$$)}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e}function H_(){this.$$.ptr||Tt(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&S("Object already scheduled for deletion"),wt(this),Ht(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function X_(){return!this.$$.ptr}var Ee=void 0,Ue=[];function Ot(){for(;Ue.length;){var e=Ue.pop();e.$$.deleteScheduled=!1,e.delete()}}function x_(){return this.$$.ptr||Tt(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&S("Object already scheduled for deletion"),Ue.push(this),Ue.length===1&&Ee&&Ee(Ot),this.$$.deleteScheduled=!0,this}function $_(){Re.prototype.isAliasOf=D_,Re.prototype.clone=w_,Re.prototype.delete=H_,Re.prototype.isDeleted=X_,Re.prototype.deleteLater=x_}function Re(){}var Xt={};function xt(e,t,_){if(e[t].overloadTable===void 0){var n=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||S("Function '"+_+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n}}function lt(e,t,_){r.hasOwnProperty(e)?((_===void 0||r[e].overloadTable!==void 0&&r[e].overloadTable[_]!==void 0)&&S("Cannot register public name '"+e+"' twice"),xt(r,e,e),r.hasOwnProperty(_)&&S("Cannot register multiple overloads of a function with the same number of arguments ("+_+")!"),r[e].overloadTable[_]=t):(r[e]=t,_!==void 0&&(r[e].numArguments=_))}function k_(e,t,_,n,s,a,c,o){this.name=e,this.constructor=t,this.instancePrototype=_,this.rawDestructor=n,this.baseClass=s,this.getActualType=a,this.upcast=c,this.downcast=o,this.pureVirtualFunctions=[]}function Kt(e,t,_){for(;t!==_;)t.upcast||S("Expected null or instance of "+_.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function W_(e,t){if(t===null)return this.isReference&&S("null is not a valid "+this.name),0;t.$$||S('Cannot pass "'+Ce(t)+'" as a '+this.name),t.$$.ptr||S("Cannot pass deleted object as a pointer of type "+this.name);var _=t.$$.ptrType.registeredClass,n=Kt(t.$$.ptr,_,this.registeredClass);return n}function j_(e,t){var _;if(t===null)return this.isReference&&S("null is not a valid "+this.name),this.isSmartPointer?(_=this.rawConstructor(),e!==null&&e.push(this.rawDestructor,_),_):0;t.$$||S('Cannot pass "'+Ce(t)+'" as a '+this.name),t.$$.ptr||S("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&S("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var n=t.$$.ptrType.registeredClass;if(_=Kt(t.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(t.$$.smartPtr===void 0&&S("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?_=t.$$.smartPtr:S("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:_=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)_=t.$$.smartPtr;else{var s=t.clone();_=this.rawShare(_,_e(function(){s.delete()})),e!==null&&e.push(this.rawDestructor,_)}break;default:S("Unsupporting sharing policy")}return _}function Y_(e,t){if(t===null)return this.isReference&&S("null is not a valid "+this.name),0;t.$$||S('Cannot pass "'+Ce(t)+'" as a '+this.name),t.$$.ptr||S("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&S("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var _=t.$$.ptrType.registeredClass,n=Kt(t.$$.ptr,_,this.registeredClass);return n}function z_(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function q_(e){this.rawDestructor&&this.rawDestructor(e)}function Z_(e){e!==null&&e.delete()}function $t(e,t,_){if(t===_)return e;if(_.baseClass===void 0)return null;var n=$t(e,t,_.baseClass);return n===null?null:_.downcast(n)}function Q_(){return Object.keys(ve).length}function J_(){var e=[];for(var t in ve)ve.hasOwnProperty(t)&&e.push(ve[t]);return e}function en(e){Ee=e,Ue.length&&Ee&&Ee(Ot)}function tn(){r.getInheritedInstanceCount=Q_,r.getLiveInheritedInstances=J_,r.flushPendingDeletes=Ot,r.setDelayFunction=en}var ve={};function _n(e,t){for(t===void 0&&S("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}function nn(e,t){return t=_n(e,t),ve[t]}function Je(e,t){(!t.ptrType||!t.ptr)&&Ze("makeClassHandle requires ptr and ptrType");var _=!!t.smartPtrType,n=!!t.smartPtr;return _!==n&&Ze("Both smartPtrType and smartPtr must be specified"),t.count={value:1},pe(Object.create(e,{$$:{value:t}}))}function rn(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var _=nn(this.registeredClass,t);if(_!==void 0){if(_.$$.count.value===0)return _.$$.ptr=t,_.$$.smartPtr=e,_.clone();var n=_.clone();return this.destructor(e),n}function s(){return this.isSmartPointer?Je(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):Je(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var a=this.registeredClass.getActualType(t),c=Xt[a];if(!c)return s.call(this);var o;this.isConst?o=c.constPointerType:o=c.pointerType;var R=$t(t,this.registeredClass,o.registeredClass);return R===null?s.call(this):this.isSmartPointer?Je(o.registeredClass.instancePrototype,{ptrType:o,ptr:R,smartPtrType:this,smartPtr:e}):Je(o.registeredClass.instancePrototype,{ptrType:o,ptr:R})}function sn(){te.prototype.getPointee=z_,te.prototype.destructor=q_,te.prototype.argPackAdvance=8,te.prototype.readValueFromPointer=Pe,te.prototype.deleteObject=Z_,te.prototype.fromWireType=rn}function te(e,t,_,n,s,a,c,o,R,T,O){this.name=e,this.registeredClass=t,this.isReference=_,this.isConst=n,this.isSmartPointer=s,this.pointeeType=a,this.sharingPolicy=c,this.rawGetPointee=o,this.rawConstructor=R,this.rawShare=T,this.rawDestructor=O,!s&&t.baseClass===void 0?n?(this.toWireType=W_,this.destructorFunction=null):(this.toWireType=Y_,this.destructorFunction=null):this.toWireType=j_}function kt(e,t,_){r.hasOwnProperty(e)||Ze("Replacing nonexistant public symbol"),r[e].overloadTable!==void 0&&_!==void 0?r[e].overloadTable[_]=t:(r[e]=t,r[e].argCount=_)}function on(e,t,_){var n=r["dynCall_"+e];return _&&_.length?n.apply(null,[t].concat(_)):n.call(null,t)}function Rn(e,t,_){return e.indexOf("j")!=-1?on(e,t,_):Ve.get(t).apply(null,_)}function an(e,t){var _=[];return function(){_.length=arguments.length;for(var n=0;n<arguments.length;n++)_[n]=arguments[n];return Rn(e,t,_)}}function Y(e,t){e=U(e);function _(){return e.indexOf("j")!=-1?an(e,t):Ve.get(t)}var n=_();return typeof n!="function"&&S("unknown function pointer with signature "+e+": "+t),n}var Wt=void 0;function jt(e){var t=Qt(e),_=U(t);return ne(t),_}function et(e,t){var _=[],n={};function s(a){if(!n[a]&&!ue[a]){if(Ye[a]){Ye[a].forEach(s);return}_.push(a),n[a]=!0}}throw t.forEach(s),new Wt(e+": "+_.map(jt).join([", "]))}function cn(e,t,_,n,s,a,c,o,R,T,O,M,G){O=U(O),a=Y(s,a),o&&(o=Y(c,o)),T&&(T=Y(R,T)),G=Y(M,G);var N=ze(O);lt(N,function(){et("Cannot construct "+O+" due to unbound types",[n])}),oe([e,t,_],n?[n]:[],function(v){v=v[0];var I,k;n?(I=v.registeredClass,k=I.instancePrototype):k=Re.prototype;var Z=qe(N,function(){if(Object.getPrototypeOf(this)!==ye)throw new Be("Use 'new' to construct "+O);if(re.constructor_body===void 0)throw new Be(O+" has no accessible constructor");var t_=re.constructor_body[arguments.length];if(t_===void 0)throw new Be("Tried to invoke ctor of "+O+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(re.constructor_body).toString()+") parameters instead!");return t_.apply(this,arguments)}),ye=Object.create(k,{constructor:{value:Z}});Z.prototype=ye;var re=new k_(O,Z,ye,G,I,a,o,T),ir=new te(O,re,!0,!1,!1),Jt=new te(O+"*",re,!1,!1,!1),e_=new te(O+" const*",re,!1,!0,!1);return Xt[e]={pointerType:Jt,constPointerType:e_},kt(N,Z),[ir,Jt,e_]})}function Mt(e,t){for(var _=[],n=0;n<e;n++)_.push(H[(t>>2)+n]);return _}function An(e,t,_,n,s,a){Se(t>0);var c=Mt(t,_);s=Y(n,s);var o=[a],R=[];oe([],[e],function(T){T=T[0];var O="constructor "+T.name;if(T.registeredClass.constructor_body===void 0&&(T.registeredClass.constructor_body=[]),T.registeredClass.constructor_body[t-1]!==void 0)throw new Be("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+T.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return T.registeredClass.constructor_body[t-1]=function(){et("Cannot construct "+T.name+" due to unbound types",c)},oe([],c,function(M){return T.registeredClass.constructor_body[t-1]=function(){arguments.length!==t-1&&S(O+" called with "+arguments.length+" arguments, expected "+(t-1)),R.length=0,o.length=t;for(var N=1;N<t;++N)o[N]=M[N].toWireType(R,arguments[N-1]);var v=s.apply(null,o);return je(R),M[0].fromWireType(v)},[]}),[]})}function Yt(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var _=qe(e.name||"unknownFunctionName",function(){});_.prototype=e.prototype;var n=new _,s=e.apply(n,t);return s instanceof Object?s:n}function zt(e,t,_,n,s){var a=t.length;a<2&&S("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var c=t[1]!==null&&_!==null,o=!1,R=1;R<t.length;++R)if(t[R]!==null&&t[R].destructorFunction===void 0){o=!0;break}for(var T=t[0].name!=="void",O="",M="",R=0;R<a-2;++R)O+=(R!==0?", ":"")+"arg"+R,M+=(R!==0?", ":"")+"arg"+R+"Wired";var G="return function "+ze(e)+"("+O+`) {
if (arguments.length !== `+(a-2)+`) {
throwBindingError('function `+e+" called with ' + arguments.length + ' arguments, expected "+(a-2)+` args!');
}
`;o&&(G+=`var destructors = [];
`);var N=o?"destructors":"null",v=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],I=[S,n,s,je,t[0],t[1]];c&&(G+="var thisWired = classParam.toWireType("+N+`, this);
`);for(var R=0;R<a-2;++R)G+="var arg"+R+"Wired = argType"+R+".toWireType("+N+", arg"+R+"); // "+t[R+2].name+`
`,v.push("argType"+R),I.push(t[R+2]);if(c&&(M="thisWired"+(M.length>0?", ":"")+M),G+=(T?"var rv = ":"")+"invoker(fn"+(M.length>0?", ":"")+M+`);
`,o)G+=`runDestructors(destructors);
`;else for(var R=c?1:2;R<t.length;++R){var k=R===1?"thisWired":"arg"+(R-2)+"Wired";t[R].destructorFunction!==null&&(G+=k+"_dtor("+k+"); // "+t[R].name+`
`,v.push(k+"_dtor"),I.push(t[R].destructorFunction))}T&&(G+=`var ret = retType.fromWireType(rv);
return ret;
`),G+=`}
`,v.push(G);var Z=Yt(Function,v).apply(null,I);return Z}function fn(e,t,_,n,s,a,c,o){var R=Mt(_,n);t=U(t),a=Y(s,a),oe([],[e],function(T){T=T[0];var O=T.name+"."+t;o&&T.registeredClass.pureVirtualFunctions.push(t);function M(){et("Cannot call "+O+" due to unbound types",R)}var G=T.registeredClass.instancePrototype,N=G[t];return N===void 0||N.overloadTable===void 0&&N.className!==T.name&&N.argCount===_-2?(M.argCount=_-2,M.className=T.name,G[t]=M):(xt(G,t,O),G[t].overloadTable[_-2]=M),oe([],R,function(v){var I=zt(O,v,T,a,c);return G[t].overloadTable===void 0?(I.argCount=_-2,G[t]=I):G[t].overloadTable[_-2]=I,[]}),[]})}function Tn(e,t,_){e=U(e),oe([],[t],function(n){return n=n[0],r[e]=n.fromWireType(_),[]})}var Bt=[],$=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Ct(e){e>4&&--$[e].refcount===0&&($[e]=void 0,Bt.push(e))}function un(){for(var e=0,t=5;t<$.length;++t)$[t]!==void 0&&++e;return e}function On(){for(var e=5;e<$.length;++e)if($[e]!==void 0)return $[e];return null}function ln(){r.count_emval_handles=un,r.get_first_emval=On}function _e(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:{var t=Bt.length?Bt.pop():$.length;return $[t]={refcount:1,value:e},t}}}function Kn(e,t){t=U(t),q(e,{name:t,fromWireType:function(_){var n=$[_].value;return Ct(_),n},toWireType:function(_,n){return _e(n)},argPackAdvance:8,readValueFromPointer:Pe,destructorFunction:null})}function Mn(e,t,_){switch(t){case 0:return function(n){var s=_?$e:D;return this.fromWireType(s[n])};case 1:return function(n){var s=_?fe:Ge;return this.fromWireType(s[n>>1])};case 2:return function(n){var s=_?H:z;return this.fromWireType(s[n>>2])};default:throw new TypeError("Unknown integer type: "+e)}}function Bn(e,t,_,n){var s=Qe(_);t=U(t);function a(){}a.values={},q(e,{name:t,constructor:a,fromWireType:function(c){return this.constructor.values[c]},toWireType:function(c,o){return o.value},argPackAdvance:8,readValueFromPointer:Mn(t,s,n),destructorFunction:null}),lt(t,a)}function tt(e,t){var _=ue[e];return _===void 0&&S(t+" has unknown type "+jt(e)),_}function Cn(e,t,_){var n=tt(e,"enum");t=U(t);var s=n.constructor,a=Object.create(n.constructor.prototype,{value:{value:_},constructor:{value:qe(n.name+"_"+t,function(){})}});s.values[_]=a,s[t]=a}function Ce(e){if(e===null)return"null";var t=typeof e;return t==="object"||t==="array"||t==="function"?e.toString():""+e}function Fn(e,t){switch(t){case 2:return function(_){return this.fromWireType(pt[_>>2])};case 3:return function(_){return this.fromWireType(Et[_>>3])};default:throw new TypeError("Unknown float type: "+e)}}function dn(e,t,_){var n=Qe(_);t=U(t),q(e,{name:t,fromWireType:function(s){return s},toWireType:function(s,a){if(typeof a!="number"&&typeof a!="boolean")throw new TypeError('Cannot convert "'+Ce(a)+'" to '+this.name);return a},argPackAdvance:8,readValueFromPointer:Fn(t,n),destructorFunction:null})}function Sn(e,t,_,n,s,a){var c=Mt(t,_);e=U(e),s=Y(n,s),lt(e,function(){et("Cannot call "+e+" due to unbound types",c)},t-1),oe([],c,function(o){var R=[o[0],null].concat(o.slice(1));return kt(e,zt(e,R,null,s,a),t-1),[]})}function Gn(e,t,_){switch(t){case 0:return _?function(s){return $e[s]}:function(s){return D[s]};case 1:return _?function(s){return fe[s>>1]}:function(s){return Ge[s>>1]};case 2:return _?function(s){return H[s>>2]}:function(s){return z[s>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function Vn(e,t,_,n,s){t=U(t),s===-1&&(s=4294967295);var a=Qe(_),c=function(T){return T};if(n===0){var o=32-8*_;c=function(T){return T<<o>>>o}}var R=t.indexOf("unsigned")!=-1;q(e,{name:t,fromWireType:c,toWireType:function(T,O){if(typeof O!="number"&&typeof O!="boolean")throw new TypeError('Cannot convert "'+Ce(O)+'" to '+this.name);if(O<n||O>s)throw new TypeError('Passing a number "'+Ce(O)+'" from JS side to C/C++ side to an argument of type "'+t+'", which is outside the valid range ['+n+", "+s+"]!");return R?O>>>0:O|0},argPackAdvance:8,readValueFromPointer:Gn(t,a,n!==0),destructorFunction:null})}function Nn(e,t,_){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],s=n[t];function a(c){c=c>>2;var o=z,R=o[c],T=o[c+1];return new s(ct,T,R)}_=U(_),q(e,{name:_,fromWireType:a,argPackAdvance:8,readValueFromPointer:a},{ignoreDuplicateRegistrations:!0})}function Pn(e,t){t=U(t);var _=t==="std::string";q(e,{name:t,fromWireType:function(n){var s=z[n>>2],a;if(_)for(var c=n+4,o=0;o<=s;++o){var R=n+4+o;if(o==s||D[R]==0){var T=R-c,O=Xe(c,T);a===void 0?a=O:(a+="\0",a+=O),c=R+1}}else{for(var M=new Array(s),o=0;o<s;++o)M[o]=String.fromCharCode(D[n+4+o]);a=M.join("")}return ne(n),a},toWireType:function(n,s){s instanceof ArrayBuffer&&(s=new Uint8Array(s));var a,c=typeof s=="string";c||s instanceof Uint8Array||s instanceof Uint8ClampedArray||s instanceof Int8Array||S("Cannot pass non-string to std::string"),_&&c?a=function(){return j(s)}:a=function(){return s.length};var o=a(),R=dt(4+o+1);if(z[R>>2]=o,_&&c)L(s,R+4,o+1);else if(c)for(var T=0;T<o;++T){var O=s.charCodeAt(T);O>255&&(ne(R),S("String has UTF-16 code units that do not fit in 8 bits")),D[R+4+T]=O}else for(var T=0;T<o;++T)D[R+4+T]=s[T];return n!==null&&n.push(ne,R),R},argPackAdvance:8,readValueFromPointer:Pe,destructorFunction:function(n){ne(n)}})}function pn(e,t,_){_=U(_);var n,s,a,c,o;t===2?(n=at,s=xe,c=f_,a=function(){return Ge},o=1):t===4&&(n=T_,s=u_,c=O_,a=function(){return z},o=2),q(e,{name:_,fromWireType:function(R){for(var T=z[R>>2],O=a(),M,G=R+4,N=0;N<=T;++N){var v=R+4+N*t;if(N==T||O[v>>o]==0){var I=v-G,k=n(G,I);M===void 0?M=k:(M+="\0",M+=k),G=v+t}}return ne(R),M},toWireType:function(R,T){typeof T!="string"&&S("Cannot pass non-string to C++ string type "+_);var O=c(T),M=dt(4+O+t);return z[M>>2]=O>>o,s(T,M+4,O+t),R!==null&&R.push(ne,M),M},argPackAdvance:8,readValueFromPointer:Pe,destructorFunction:function(R){ne(R)}})}function En(e,t,_,n,s,a){We[e]={name:U(t),rawConstructor:Y(_,n),rawDestructor:Y(s,a),fields:[]}}function Un(e,t,_,n,s,a,c,o,R,T){We[e].fields.push({fieldName:U(t),getterReturnType:_,getter:Y(n,s),getterContext:a,setterArgumentType:c,setter:Y(o,R),setterContext:T})}function vn(e,t){t=U(t),q(e,{isVoid:!0,name:t,argPackAdvance:0,fromWireType:function(){},toWireType:function(_,n){}})}function he(e){return e||S("Cannot use deleted val. handle = "+e),$[e].value}function hn(e,t,_){e=he(e),t=tt(t,"emval::as");var n=[],s=_e(n);return H[_>>2]=s,t.toWireType(n,e)}var yn={};function _t(e){var t=yn[e];return t===void 0?U(e):t}var Ft=[];function gn(e,t,_,n){e=Ft[e],t=he(t),_=_t(_),e(t,_,null,n)}function qt(){return typeof globalThis=="object"?globalThis:function(){return Function}()("return this")()}function Ln(e){return e===0?_e(qt()):(e=_t(e),_e(qt()[e]))}function mn(e){var t=Ft.length;return Ft.push(e),t}function Dn(e,t){for(var _=new Array(e),n=0;n<e;++n)_[n]=tt(H[(t>>2)+n],"parameter "+n);return _}function In(e,t){for(var _=Dn(e,t),n=_[0],s=n.name+"_$"+_.slice(1).map(function(N){return N.name}).join("_")+"$",a=["retType"],c=[n],o="",R=0;R<e-1;++R)o+=(R!==0?", ":"")+"arg"+R,a.push("argType"+R),c.push(_[1+R]);for(var T=ze("methodCaller_"+s),O="return function "+T+`(handle, name, destructors, args) {
`,M=0,R=0;R<e-1;++R)O+="    var arg"+R+" = argType"+R+".readValueFromPointer(args"+(M?"+"+M:"")+`);
`,M+=_[R+1].argPackAdvance;O+="    var rv = handle[name]("+o+`);
`;for(var R=0;R<e-1;++R)_[R+1].deleteObject&&(O+="    argType"+R+".deleteObject(arg"+R+`);
`);n.isVoid||(O+=`    return retType.toWireType(destructors, rv);
`),O+=`};
`,a.push(O);var G=Yt(Function,a).apply(null,c);return mn(G)}function bn(e){return e=_t(e),_e(r[e])}function wn(e,t){return e=he(e),t=he(t),_e(e[t])}function Hn(e){e>4&&($[e].refcount+=1)}function Xn(e){for(var t="",_=0;_<e;++_)t+=(_!==0?", ":"")+"arg"+_;for(var n="return function emval_allocator_"+e+`(constructor, argTypes, args) {
`,_=0;_<e;++_)n+="var argType"+_+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+_+'], "parameter '+_+`");
var arg`+_+" = argType"+_+`.readValueFromPointer(args);
args += argType`+_+`['argPackAdvance'];
`;return n+="var obj = new constructor("+t+`);
return __emval_register(obj);
}
`,new Function("requireRegisteredType","Module","__emval_register",n)(tt,r,_e)}var Zt={};function xn(e,t,_,n){e=he(e);var s=Zt[t];return s||(s=Xn(t),Zt[t]=s),s(e,_,n)}function $n(e){return _e(_t(e))}function kn(e){var t=$[e].value;je(t),Ct(e)}function Wn(){Ke()}function jn(e,t,_){D.copyWithin(e,t,t+_)}function Yn(e){try{return ce.grow(e-ct.byteLength+65535>>>16),Ut(ce.buffer),1}catch{}}function zn(e){var t=D.length;e=e>>>0;var _=2147483648;if(e>_)return!1;for(var n=1;n<=4;n*=2){var s=t*(1+.2/n);s=Math.min(s,e+100663296);var a=Math.min(_,l_(Math.max(e,s),65536)),c=Yn(a);if(c)return!0}return!1}var nt={mappings:{},buffers:[null,[],[]],printChar:function(e,t){var _=nt.buffers[e];t===0||t===10?((e===1?st:ee)(He(_,0)),_.length=0):_.push(t)},varargs:void 0,get:function(){nt.varargs+=4;var e=H[nt.varargs-4>>2];return e},getStr:function(e){var t=Xe(e);return t},get64:function(e,t){return e}};function qn(e){return 0}function Zn(e,t,_,n,s){}function Qn(e,t,_,n){for(var s=0,a=0;a<_;a++){for(var c=H[t+a*8>>2],o=H[t+(a*8+4)>>2],R=0;R<o;R++)nt.printChar(e,D[c+R]);s+=o}return H[n>>2]=s,0}function Jn(e){Ie(e|0)}It=r.InternalError=ft(Error,"InternalError"),L_(),Be=r.BindingError=ft(Error,"BindingError"),$_(),sn(),tn(),Wt=r.UnboundTypeError=ft(Error,"UnboundTypeError"),ln();var er={t:g_,I:m_,x:cn,w:An,d:fn,k:Tn,H:Kn,n:Bn,a:Cn,A:dn,i:Sn,j:Vn,h:Nn,B:Pn,v:pn,u:En,c:Un,J:vn,m:hn,s:gn,b:Ct,y:Ln,p:In,r:bn,e:wn,g:Hn,q:xn,f:$n,l:kn,o:Wn,E:jn,F:zn,G:qn,C:Zn,z:Qn,D:Jn},Or=v_(),tr=r.___wasm_call_ctors=function(){return(tr=r.___wasm_call_ctors=r.asm.L).apply(null,arguments)},dt=r._malloc=function(){return(dt=r._malloc=r.asm.M).apply(null,arguments)},ne=r._free=function(){return(ne=r._free=r.asm.N).apply(null,arguments)},Qt=r.___getTypeName=function(){return(Qt=r.___getTypeName=r.asm.P).apply(null,arguments)},_r=r.___embind_register_native_and_builtin_types=function(){return(_r=r.___embind_register_native_and_builtin_types=r.asm.Q).apply(null,arguments)},nr=r.dynCall_jiji=function(){return(nr=r.dynCall_jiji=r.asm.R).apply(null,arguments)},rt;function rr(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}Ne=function e(){rt||St(),rt||(Ne=e)};function St(e){if(e=e||h,Te>0||(B_(),Te>0))return;function t(){rt||(rt=!0,r.calledRun=!0,!de&&(C_(),F_(),B(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),d_()))}r.setStatus?(r.setStatus("Running..."),setTimeout(function(){setTimeout(function(){r.setStatus("")},1),t()},1)):t()}if(r.run=St,r.preInit)for(typeof r.preInit=="function"&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return St(),f.ready}}();typeof it=="object"&&typeof Pt=="object"?Pt.exports=Nt:typeof define=="function"&&define.amd?define([],function(){return Nt}):typeof it=="object"&&(it.BASIS=Nt)});var F={UNSIGNED_BYTE:K.UNSIGNED_BYTE,UNSIGNED_SHORT:K.UNSIGNED_SHORT,UNSIGNED_INT:K.UNSIGNED_INT,FLOAT:K.FLOAT,HALF_FLOAT:K.HALF_FLOAT_OES,UNSIGNED_INT_24_8:K.UNSIGNED_INT_24_8,UNSIGNED_SHORT_4_4_4_4:K.UNSIGNED_SHORT_4_4_4_4,UNSIGNED_SHORT_5_5_5_1:K.UNSIGNED_SHORT_5_5_5_1,UNSIGNED_SHORT_5_6_5:K.UNSIGNED_SHORT_5_6_5};F.toWebGLConstant=function(i,f){switch(i){case F.UNSIGNED_BYTE:return K.UNSIGNED_BYTE;case F.UNSIGNED_SHORT:return K.UNSIGNED_SHORT;case F.UNSIGNED_INT:return K.UNSIGNED_INT;case F.FLOAT:return K.FLOAT;case F.HALF_FLOAT:return f.webgl2?K.HALF_FLOAT:K.HALF_FLOAT_OES;case F.UNSIGNED_INT_24_8:return K.UNSIGNED_INT_24_8;case F.UNSIGNED_SHORT_4_4_4_4:return K.UNSIGNED_SHORT_4_4_4_4;case F.UNSIGNED_SHORT_5_5_5_1:return K.UNSIGNED_SHORT_5_5_5_1;case F.UNSIGNED_SHORT_5_6_5:return F.UNSIGNED_SHORT_5_6_5}};F.isPacked=function(i){return i===F.UNSIGNED_INT_24_8||i===F.UNSIGNED_SHORT_4_4_4_4||i===F.UNSIGNED_SHORT_5_5_5_1||i===F.UNSIGNED_SHORT_5_6_5};F.sizeInBytes=function(i){switch(i){case F.UNSIGNED_BYTE:return 1;case F.UNSIGNED_SHORT:case F.UNSIGNED_SHORT_4_4_4_4:case F.UNSIGNED_SHORT_5_5_5_1:case F.UNSIGNED_SHORT_5_6_5:case F.HALF_FLOAT:return 2;case F.UNSIGNED_INT:case F.FLOAT:case F.UNSIGNED_INT_24_8:return 4}};F.validate=function(i){return i===F.UNSIGNED_BYTE||i===F.UNSIGNED_SHORT||i===F.UNSIGNED_INT||i===F.FLOAT||i===F.HALF_FLOAT||i===F.UNSIGNED_INT_24_8||i===F.UNSIGNED_SHORT_4_4_4_4||i===F.UNSIGNED_SHORT_5_5_5_1||i===F.UNSIGNED_SHORT_5_6_5};var b=Object.freeze(F);var A={DEPTH_COMPONENT:K.DEPTH_COMPONENT,DEPTH_STENCIL:K.DEPTH_STENCIL,ALPHA:K.ALPHA,RED:K.RED,RG:K.RG,RGB:K.RGB,RGBA:K.RGBA,LUMINANCE:K.LUMINANCE,LUMINANCE_ALPHA:K.LUMINANCE_ALPHA,RGB_DXT1:K.COMPRESSED_RGB_S3TC_DXT1_EXT,RGBA_DXT1:K.COMPRESSED_RGBA_S3TC_DXT1_EXT,RGBA_DXT3:K.COMPRESSED_RGBA_S3TC_DXT3_EXT,RGBA_DXT5:K.COMPRESSED_RGBA_S3TC_DXT5_EXT,RGB_PVRTC_4BPPV1:K.COMPRESSED_RGB_PVRTC_4BPPV1_IMG,RGB_PVRTC_2BPPV1:K.COMPRESSED_RGB_PVRTC_2BPPV1_IMG,RGBA_PVRTC_4BPPV1:K.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,RGBA_PVRTC_2BPPV1:K.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG,RGBA_ASTC:K.COMPRESSED_RGBA_ASTC_4x4_WEBGL,RGB_ETC1:K.COMPRESSED_RGB_ETC1_WEBGL,RGB8_ETC2:K.COMPRESSED_RGB8_ETC2,RGBA8_ETC2_EAC:K.COMPRESSED_RGBA8_ETC2_EAC,RGBA_BC7:K.COMPRESSED_RGBA_BPTC_UNORM};A.componentsLength=function(i){switch(i){case A.RGB:return 3;case A.RGBA:return 4;case A.LUMINANCE_ALPHA:case A.RG:return 2;case A.ALPHA:case A.RED:case A.LUMINANCE:return 1;default:return 1}};A.validate=function(i){return i===A.DEPTH_COMPONENT||i===A.DEPTH_STENCIL||i===A.ALPHA||i===A.RED||i===A.RG||i===A.RGB||i===A.RGBA||i===A.LUMINANCE||i===A.LUMINANCE_ALPHA||i===A.RGB_DXT1||i===A.RGBA_DXT1||i===A.RGBA_DXT3||i===A.RGBA_DXT5||i===A.RGB_PVRTC_4BPPV1||i===A.RGB_PVRTC_2BPPV1||i===A.RGBA_PVRTC_4BPPV1||i===A.RGBA_PVRTC_2BPPV1||i===A.RGBA_ASTC||i===A.RGB_ETC1||i===A.RGB8_ETC2||i===A.RGBA8_ETC2_EAC||i===A.RGBA_BC7};A.isColorFormat=function(i){return i===A.ALPHA||i===A.RGB||i===A.RGBA||i===A.LUMINANCE||i===A.LUMINANCE_ALPHA};A.isDepthFormat=function(i){return i===A.DEPTH_COMPONENT||i===A.DEPTH_STENCIL};A.isCompressedFormat=function(i){return i===A.RGB_DXT1||i===A.RGBA_DXT1||i===A.RGBA_DXT3||i===A.RGBA_DXT5||i===A.RGB_PVRTC_4BPPV1||i===A.RGB_PVRTC_2BPPV1||i===A.RGBA_PVRTC_4BPPV1||i===A.RGBA_PVRTC_2BPPV1||i===A.RGBA_ASTC||i===A.RGB_ETC1||i===A.RGB8_ETC2||i===A.RGBA8_ETC2_EAC||i===A.RGBA_BC7};A.isDXTFormat=function(i){return i===A.RGB_DXT1||i===A.RGBA_DXT1||i===A.RGBA_DXT3||i===A.RGBA_DXT5};A.isPVRTCFormat=function(i){return i===A.RGB_PVRTC_4BPPV1||i===A.RGB_PVRTC_2BPPV1||i===A.RGBA_PVRTC_4BPPV1||i===A.RGBA_PVRTC_2BPPV1};A.isASTCFormat=function(i){return i===A.RGBA_ASTC};A.isETC1Format=function(i){return i===A.RGB_ETC1};A.isETC2Format=function(i){return i===A.RGB8_ETC2||i===A.RGBA8_ETC2_EAC};A.isBC7Format=function(i){return i===A.RGBA_BC7};A.compressedTextureSizeInBytes=function(i,f,r){switch(i){case A.RGB_DXT1:case A.RGBA_DXT1:case A.RGB_ETC1:case A.RGB8_ETC2:return Math.floor((f+3)/4)*Math.floor((r+3)/4)*8;case A.RGBA_DXT3:case A.RGBA_DXT5:case A.RGBA_ASTC:case A.RGBA8_ETC2_EAC:return Math.floor((f+3)/4)*Math.floor((r+3)/4)*16;case A.RGB_PVRTC_4BPPV1:case A.RGBA_PVRTC_4BPPV1:return Math.floor((Math.max(f,8)*Math.max(r,8)*4+7)/8);case A.RGB_PVRTC_2BPPV1:case A.RGBA_PVRTC_2BPPV1:return Math.floor((Math.max(f,16)*Math.max(r,8)*2+7)/8);case A.RGBA_BC7:return Math.ceil(f/4)*Math.ceil(r/4)*16;default:return 0}};A.textureSizeInBytes=function(i,f,r,B){let l=A.componentsLength(i);return b.isPacked(f)&&(l=1),l*b.sizeInBytes(f)*r*B};A.alignmentInBytes=function(i,f,r){let B=A.textureSizeInBytes(i,f,r,1)%4;return B===0?4:B===2?2:1};A.createTypedArray=function(i,f,r,B){let l,P=b.sizeInBytes(f);P===Uint8Array.BYTES_PER_ELEMENT?l=Uint8Array:P===Uint16Array.BYTES_PER_ELEMENT?l=Uint16Array:P===Float32Array.BYTES_PER_ELEMENT&&f===b.FLOAT?l=Float32Array:l=Uint32Array;let C=A.componentsLength(i)*r*B;return new l(C)};A.flipY=function(i,f,r,B,l){if(l===1)return i;let P=A.createTypedArray(f,r,B,l),C=A.componentsLength(f),h=B*C;for(let y=0;y<l;++y){let m=y*B*C,V=(l-y-1)*B*C;for(let d=0;d<h;++d)P[V+d]=i[m+d]}return P};A.toInternalFormat=function(i,f,r){if(!r.webgl2)return i;if(i===A.DEPTH_STENCIL)return K.DEPTH24_STENCIL8;if(i===A.DEPTH_COMPONENT){if(f===b.UNSIGNED_SHORT)return K.DEPTH_COMPONENT16;if(f===b.UNSIGNED_INT)return K.DEPTH_COMPONENT24}if(f===b.FLOAT)switch(i){case A.RGBA:return K.RGBA32F;case A.RGB:return K.RGB32F;case A.RG:return K.RG32F;case A.RED:return K.R32F}if(f===b.HALF_FLOAT)switch(i){case A.RGBA:return K.RGBA16F;case A.RGB:return K.RGB16F;case A.RG:return K.RG16F;case A.RED:return K.R16F}return i};var E=Object.freeze(A);var Rr={VK_FORMAT_UNDEFINED:0,VK_FORMAT_R4G4_UNORM_PACK8:1,VK_FORMAT_R4G4B4A4_UNORM_PACK16:2,VK_FORMAT_B4G4R4A4_UNORM_PACK16:3,VK_FORMAT_R5G6B5_UNORM_PACK16:4,VK_FORMAT_B5G6R5_UNORM_PACK16:5,VK_FORMAT_R5G5B5A1_UNORM_PACK16:6,VK_FORMAT_B5G5R5A1_UNORM_PACK16:7,VK_FORMAT_A1R5G5B5_UNORM_PACK16:8,VK_FORMAT_R8_UNORM:9,VK_FORMAT_R8_SNORM:10,VK_FORMAT_R8_USCALED:11,VK_FORMAT_R8_SSCALED:12,VK_FORMAT_R8_UINT:13,VK_FORMAT_R8_SINT:14,VK_FORMAT_R8_SRGB:15,VK_FORMAT_R8G8_UNORM:16,VK_FORMAT_R8G8_SNORM:17,VK_FORMAT_R8G8_USCALED:18,VK_FORMAT_R8G8_SSCALED:19,VK_FORMAT_R8G8_UINT:20,VK_FORMAT_R8G8_SINT:21,VK_FORMAT_R8G8_SRGB:22,VK_FORMAT_R8G8B8_UNORM:23,VK_FORMAT_R8G8B8_SNORM:24,VK_FORMAT_R8G8B8_USCALED:25,VK_FORMAT_R8G8B8_SSCALED:26,VK_FORMAT_R8G8B8_UINT:27,VK_FORMAT_R8G8B8_SINT:28,VK_FORMAT_R8G8B8_SRGB:29,VK_FORMAT_B8G8R8_UNORM:30,VK_FORMAT_B8G8R8_SNORM:31,VK_FORMAT_B8G8R8_USCALED:32,VK_FORMAT_B8G8R8_SSCALED:33,VK_FORMAT_B8G8R8_UINT:34,VK_FORMAT_B8G8R8_SINT:35,VK_FORMAT_B8G8R8_SRGB:36,VK_FORMAT_R8G8B8A8_UNORM:37,VK_FORMAT_R8G8B8A8_SNORM:38,VK_FORMAT_R8G8B8A8_USCALED:39,VK_FORMAT_R8G8B8A8_SSCALED:40,VK_FORMAT_R8G8B8A8_UINT:41,VK_FORMAT_R8G8B8A8_SINT:42,VK_FORMAT_R8G8B8A8_SRGB:43,VK_FORMAT_B8G8R8A8_UNORM:44,VK_FORMAT_B8G8R8A8_SNORM:45,VK_FORMAT_B8G8R8A8_USCALED:46,VK_FORMAT_B8G8R8A8_SSCALED:47,VK_FORMAT_B8G8R8A8_UINT:48,VK_FORMAT_B8G8R8A8_SINT:49,VK_FORMAT_B8G8R8A8_SRGB:50,VK_FORMAT_A8B8G8R8_UNORM_PACK32:51,VK_FORMAT_A8B8G8R8_SNORM_PACK32:52,VK_FORMAT_A8B8G8R8_USCALED_PACK32:53,VK_FORMAT_A8B8G8R8_SSCALED_PACK32:54,VK_FORMAT_A8B8G8R8_UINT_PACK32:55,VK_FORMAT_A8B8G8R8_SINT_PACK32:56,VK_FORMAT_A8B8G8R8_SRGB_PACK32:57,VK_FORMAT_A2R10G10B10_UNORM_PACK32:58,VK_FORMAT_A2R10G10B10_SNORM_PACK32:59,VK_FORMAT_A2R10G10B10_USCALED_PACK32:60,VK_FORMAT_A2R10G10B10_SSCALED_PACK32:61,VK_FORMAT_A2R10G10B10_UINT_PACK32:62,VK_FORMAT_A2R10G10B10_SINT_PACK32:63,VK_FORMAT_A2B10G10R10_UNORM_PACK32:64,VK_FORMAT_A2B10G10R10_SNORM_PACK32:65,VK_FORMAT_A2B10G10R10_USCALED_PACK32:66,VK_FORMAT_A2B10G10R10_SSCALED_PACK32:67,VK_FORMAT_A2B10G10R10_UINT_PACK32:68,VK_FORMAT_A2B10G10R10_SINT_PACK32:69,VK_FORMAT_R16_UNORM:70,VK_FORMAT_R16_SNORM:71,VK_FORMAT_R16_USCALED:72,VK_FORMAT_R16_SSCALED:73,VK_FORMAT_R16_UINT:74,VK_FORMAT_R16_SINT:75,VK_FORMAT_R16_SFLOAT:76,VK_FORMAT_R16G16_UNORM:77,VK_FORMAT_R16G16_SNORM:78,VK_FORMAT_R16G16_USCALED:79,VK_FORMAT_R16G16_SSCALED:80,VK_FORMAT_R16G16_UINT:81,VK_FORMAT_R16G16_SINT:82,VK_FORMAT_R16G16_SFLOAT:83,VK_FORMAT_R16G16B16_UNORM:84,VK_FORMAT_R16G16B16_SNORM:85,VK_FORMAT_R16G16B16_USCALED:86,VK_FORMAT_R16G16B16_SSCALED:87,VK_FORMAT_R16G16B16_UINT:88,VK_FORMAT_R16G16B16_SINT:89,VK_FORMAT_R16G16B16_SFLOAT:90,VK_FORMAT_R16G16B16A16_UNORM:91,VK_FORMAT_R16G16B16A16_SNORM:92,VK_FORMAT_R16G16B16A16_USCALED:93,VK_FORMAT_R16G16B16A16_SSCALED:94,VK_FORMAT_R16G16B16A16_UINT:95,VK_FORMAT_R16G16B16A16_SINT:96,VK_FORMAT_R16G16B16A16_SFLOAT:97,VK_FORMAT_R32_UINT:98,VK_FORMAT_R32_SINT:99,VK_FORMAT_R32_SFLOAT:100,VK_FORMAT_R32G32_UINT:101,VK_FORMAT_R32G32_SINT:102,VK_FORMAT_R32G32_SFLOAT:103,VK_FORMAT_R32G32B32_UINT:104,VK_FORMAT_R32G32B32_SINT:105,VK_FORMAT_R32G32B32_SFLOAT:106,VK_FORMAT_R32G32B32A32_UINT:107,VK_FORMAT_R32G32B32A32_SINT:108,VK_FORMAT_R32G32B32A32_SFLOAT:109,VK_FORMAT_R64_UINT:110,VK_FORMAT_R64_SINT:111,VK_FORMAT_R64_SFLOAT:112,VK_FORMAT_R64G64_UINT:113,VK_FORMAT_R64G64_SINT:114,VK_FORMAT_R64G64_SFLOAT:115,VK_FORMAT_R64G64B64_UINT:116,VK_FORMAT_R64G64B64_SINT:117,VK_FORMAT_R64G64B64_SFLOAT:118,VK_FORMAT_R64G64B64A64_UINT:119,VK_FORMAT_R64G64B64A64_SINT:120,VK_FORMAT_R64G64B64A64_SFLOAT:121,VK_FORMAT_B10G11R11_UFLOAT_PACK32:122,VK_FORMAT_E5B9G9R9_UFLOAT_PACK32:123,VK_FORMAT_D16_UNORM:124,VK_FORMAT_X8_D24_UNORM_PACK32:125,VK_FORMAT_D32_SFLOAT:126,VK_FORMAT_S8_UINT:127,VK_FORMAT_D16_UNORM_S8_UINT:128,VK_FORMAT_D24_UNORM_S8_UINT:129,VK_FORMAT_D32_SFLOAT_S8_UINT:130,VK_FORMAT_BC1_RGB_UNORM_BLOCK:131,VK_FORMAT_BC1_RGB_SRGB_BLOCK:132,VK_FORMAT_BC1_RGBA_UNORM_BLOCK:133,VK_FORMAT_BC1_RGBA_SRGB_BLOCK:134,VK_FORMAT_BC2_UNORM_BLOCK:135,VK_FORMAT_BC2_SRGB_BLOCK:136,VK_FORMAT_BC3_UNORM_BLOCK:137,VK_FORMAT_BC3_SRGB_BLOCK:138,VK_FORMAT_BC4_UNORM_BLOCK:139,VK_FORMAT_BC4_SNORM_BLOCK:140,VK_FORMAT_BC5_UNORM_BLOCK:141,VK_FORMAT_BC5_SNORM_BLOCK:142,VK_FORMAT_BC6H_UFLOAT_BLOCK:143,VK_FORMAT_BC6H_SFLOAT_BLOCK:144,VK_FORMAT_BC7_UNORM_BLOCK:145,VK_FORMAT_BC7_SRGB_BLOCK:146,VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK:147,VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK:148,VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK:149,VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK:150,VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK:151,VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK:152,VK_FORMAT_EAC_R11_UNORM_BLOCK:153,VK_FORMAT_EAC_R11_SNORM_BLOCK:154,VK_FORMAT_EAC_R11G11_UNORM_BLOCK:155,VK_FORMAT_EAC_R11G11_SNORM_BLOCK:156,VK_FORMAT_ASTC_4x4_UNORM_BLOCK:157,VK_FORMAT_ASTC_4x4_SRGB_BLOCK:158,VK_FORMAT_ASTC_5x4_UNORM_BLOCK:159,VK_FORMAT_ASTC_5x4_SRGB_BLOCK:160,VK_FORMAT_ASTC_5x5_UNORM_BLOCK:161,VK_FORMAT_ASTC_5x5_SRGB_BLOCK:162,VK_FORMAT_ASTC_6x5_UNORM_BLOCK:163,VK_FORMAT_ASTC_6x5_SRGB_BLOCK:164,VK_FORMAT_ASTC_6x6_UNORM_BLOCK:165,VK_FORMAT_ASTC_6x6_SRGB_BLOCK:166,VK_FORMAT_ASTC_8x5_UNORM_BLOCK:167,VK_FORMAT_ASTC_8x5_SRGB_BLOCK:168,VK_FORMAT_ASTC_8x6_UNORM_BLOCK:169,VK_FORMAT_ASTC_8x6_SRGB_BLOCK:170,VK_FORMAT_ASTC_8x8_UNORM_BLOCK:171,VK_FORMAT_ASTC_8x8_SRGB_BLOCK:172,VK_FORMAT_ASTC_10x5_UNORM_BLOCK:173,VK_FORMAT_ASTC_10x5_SRGB_BLOCK:174,VK_FORMAT_ASTC_10x6_UNORM_BLOCK:175,VK_FORMAT_ASTC_10x6_SRGB_BLOCK:176,VK_FORMAT_ASTC_10x8_UNORM_BLOCK:177,VK_FORMAT_ASTC_10x8_SRGB_BLOCK:178,VK_FORMAT_ASTC_10x10_UNORM_BLOCK:179,VK_FORMAT_ASTC_10x10_SRGB_BLOCK:180,VK_FORMAT_ASTC_12x10_UNORM_BLOCK:181,VK_FORMAT_ASTC_12x10_SRGB_BLOCK:182,VK_FORMAT_ASTC_12x12_UNORM_BLOCK:183,VK_FORMAT_ASTC_12x12_SRGB_BLOCK:184,VK_FORMAT_G8B8G8R8_422_UNORM:1000156e3,VK_FORMAT_B8G8R8G8_422_UNORM:1000156001,VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM:1000156002,VK_FORMAT_G8_B8R8_2PLANE_420_UNORM:1000156003,VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM:1000156004,VK_FORMAT_G8_B8R8_2PLANE_422_UNORM:1000156005,VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM:1000156006,VK_FORMAT_R10X6_UNORM_PACK16:1000156007,VK_FORMAT_R10X6G10X6_UNORM_2PACK16:1000156008,VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16:1000156009,VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16:1000156010,VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16:1000156011,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16:1000156012,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16:1000156013,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16:1000156014,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16:1000156015,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16:1000156016,VK_FORMAT_R12X4_UNORM_PACK16:1000156017,VK_FORMAT_R12X4G12X4_UNORM_2PACK16:1000156018,VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16:1000156019,VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16:1000156020,VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16:1000156021,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16:1000156022,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16:1000156023,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16:1000156024,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16:1000156025,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16:1000156026,VK_FORMAT_G16B16G16R16_422_UNORM:1000156027,VK_FORMAT_B16G16R16G16_422_UNORM:1000156028,VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM:1000156029,VK_FORMAT_G16_B16R16_2PLANE_420_UNORM:1000156030,VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM:1000156031,VK_FORMAT_G16_B16R16_2PLANE_422_UNORM:1000156032,VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM:1000156033,VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG:1000054e3,VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG:1000054001,VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG:1000054002,VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG:1000054003,VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG:1000054004,VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG:1000054005,VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG:1000054006,VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG:1000054007,VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT:1000066e3,VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT:1000066001,VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT:1000066002,VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT:1000066003,VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT:1000066004,VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT:1000066005,VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT:1000066006,VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT:1000066007,VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT:1000066008,VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT:1000066009,VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT:1000066010,VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT:1000066011,VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT:1000066012,VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT:1000066013,VK_FORMAT_G8B8G8R8_422_UNORM_KHR:1000156e3,VK_FORMAT_B8G8R8G8_422_UNORM_KHR:1000156001,VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM_KHR:1000156002,VK_FORMAT_G8_B8R8_2PLANE_420_UNORM_KHR:1000156003,VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM_KHR:1000156004,VK_FORMAT_G8_B8R8_2PLANE_422_UNORM_KHR:1000156005,VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM_KHR:1000156006,VK_FORMAT_R10X6_UNORM_PACK16_KHR:1000156007,VK_FORMAT_R10X6G10X6_UNORM_2PACK16_KHR:1000156008,VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16_KHR:1000156009,VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16_KHR:1000156010,VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16_KHR:1000156011,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16_KHR:1000156012,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16_KHR:1000156013,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16_KHR:1000156014,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16_KHR:1000156015,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16_KHR:1000156016,VK_FORMAT_R12X4_UNORM_PACK16_KHR:1000156017,VK_FORMAT_R12X4G12X4_UNORM_2PACK16_KHR:1000156018,VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16_KHR:1000156019,VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16_KHR:1000156020,VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16_KHR:1000156021,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16_KHR:1000156022,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16_KHR:1000156023,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16_KHR:1000156024,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16_KHR:1000156025,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16_KHR:1000156026,VK_FORMAT_G16B16G16R16_422_UNORM_KHR:1000156027,VK_FORMAT_B16G16R16G16_422_UNORM_KHR:1000156028,VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM_KHR:1000156029,VK_FORMAT_G16_B16R16_2PLANE_420_UNORM_KHR:1000156030,VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM_KHR:1000156031,VK_FORMAT_G16_B16R16_2PLANE_422_UNORM_KHR:1000156032,VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM_KHR:1000156033},Le=Object.freeze(Rr);var Vt=class{constructor(){this.vkFormat=0,this.typeSize=1,this.pixelWidth=0,this.pixelHeight=0,this.pixelDepth=0,this.layerCount=0,this.faceCount=1,this.supercompressionScheme=0,this.levels=[],this.dataFormatDescriptor=[{vendorId:0,descriptorType:0,descriptorBlockSize:0,versionNumber:2,colorModel:0,colorPrimaries:1,transferFunction:2,flags:0,texelBlockDimension:[0,0,0,0],bytesPlane:[0,0,0,0,0,0,0,0],samples:[]}],this.keyValue={},this.globalData=null}},Oe=class{constructor(f,r,B,l){this._dataView=void 0,this._littleEndian=void 0,this._offset=void 0,this._dataView=new DataView(f.buffer,f.byteOffset+r,B),this._littleEndian=l,this._offset=0}_nextUint8(){let f=this._dataView.getUint8(this._offset);return this._offset+=1,f}_nextUint16(){let f=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,f}_nextUint32(){let f=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,f}_nextUint64(){let f=this._dataView.getUint32(this._offset,this._littleEndian),r=this._dataView.getUint32(this._offset+4,this._littleEndian),B=f+2**32*r;return this._offset+=8,B}_nextInt32(){let f=this._dataView.getInt32(this._offset,this._littleEndian);return this._offset+=4,f}_nextUint8Array(f){let r=new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+this._offset,f);return this._offset+=f,r}_skip(f){return this._offset+=f,this}_scan(f,r=0){let B=this._offset,l=0;for(;this._dataView.getUint8(this._offset)!==r&&l<f;)l++,this._offset++;return l<f&&this._offset++,new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+B,l)}};var dr=new Uint8Array([0]),w=[171,75,84,88,32,50,48,187,13,10,26,10];function i_(i){return typeof TextDecoder<"u"?new TextDecoder().decode(i):Buffer.from(i).toString("utf8")}function s_(i){let f=new Uint8Array(i.buffer,i.byteOffset,w.length);if(f[0]!==w[0]||f[1]!==w[1]||f[2]!==w[2]||f[3]!==w[3]||f[4]!==w[4]||f[5]!==w[5]||f[6]!==w[6]||f[7]!==w[7]||f[8]!==w[8]||f[9]!==w[9]||f[10]!==w[10]||f[11]!==w[11])throw new Error("Missing KTX 2.0 identifier.");let r=new Vt,B=17*Uint32Array.BYTES_PER_ELEMENT,l=new Oe(i,w.length,B,!0);r.vkFormat=l._nextUint32(),r.typeSize=l._nextUint32(),r.pixelWidth=l._nextUint32(),r.pixelHeight=l._nextUint32(),r.pixelDepth=l._nextUint32(),r.layerCount=l._nextUint32(),r.faceCount=l._nextUint32();let P=l._nextUint32();r.supercompressionScheme=l._nextUint32();let C=l._nextUint32(),h=l._nextUint32(),y=l._nextUint32(),m=l._nextUint32(),V=l._nextUint64(),d=l._nextUint64(),p=P*3*8,Q=new Oe(i,w.length+B,p,!0);for(let L=0;L<P;L++)r.levels.push({levelData:new Uint8Array(i.buffer,i.byteOffset+Q._nextUint64(),Q._nextUint64()),uncompressedByteLength:Q._nextUint64()});let u=new Oe(i,C,h,!0),X={vendorId:u._skip(4)._nextUint16(),descriptorType:u._nextUint16(),versionNumber:u._nextUint16(),descriptorBlockSize:u._nextUint16(),colorModel:u._nextUint8(),colorPrimaries:u._nextUint8(),transferFunction:u._nextUint8(),flags:u._nextUint8(),texelBlockDimension:[u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8()],bytesPlane:[u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8()],samples:[]},W=(X.descriptorBlockSize/4-6)/4;for(let L=0;L<W;L++){let j={bitOffset:u._nextUint16(),bitLength:u._nextUint8(),channelType:u._nextUint8(),samplePosition:[u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8()],sampleLower:-1/0,sampleUpper:1/0};j.channelType&64?(j.sampleLower=u._nextInt32(),j.sampleUpper=u._nextInt32()):(j.sampleLower=u._nextUint32(),j.sampleUpper=u._nextUint32()),X.samples[L]=j}r.dataFormatDescriptor.length=0,r.dataFormatDescriptor.push(X);let J=new Oe(i,y,m,!0);for(;J._offset<m;){let L=J._nextUint32(),j=J._scan(L),Ae=i_(j);if(r.keyValue[Ae]=J._nextUint8Array(L-j.byteLength-1),Ae.match(/^ktx/i)){let xe=i_(r.keyValue[Ae]);r.keyValue[Ae]=xe.substring(0,xe.lastIndexOf("\0"))}let at=L%4?4-L%4:0;J._skip(at)}if(d<=0)return r;let g=new Oe(i,V,d,!0),Fe=g._nextUint16(),st=g._nextUint16(),ee=g._nextUint32(),De=g._nextUint32(),Ie=g._nextUint32(),ae=g._nextUint32(),ot=[];for(let L=0;L<P;L++)ot.push({imageFlags:g._nextUint32(),rgbSliceByteOffset:g._nextUint32(),rgbSliceByteLength:g._nextUint32(),alphaSliceByteOffset:g._nextUint32(),alphaSliceByteLength:g._nextUint32()});let ce=V+g._offset,de=ce+ee,be=de+De,Se=be+Ie,we=new Uint8Array(i.buffer,i.byteOffset+ce,ee),He=new Uint8Array(i.buffer,i.byteOffset+de,De),Xe=new Uint8Array(i.buffer,i.byteOffset+be,Ie),Rt=new Uint8Array(i.buffer,i.byteOffset+Se,ae);return r.globalData={endpointCount:Fe,selectorCount:st,imageDescs:ot,endpointsData:we,selectorsData:He,tablesData:Xe,extendedData:Rt},r}var R_=or(o_(),1),a_=["positiveX","negativeX","positiveY","negativeY","positiveZ","negativeZ"],c_=163,A_=166,me;function ar(i,f){__.typeOf.object("transcoderModule",me);let r=i.ktx2Buffer,B=i.supportedTargetFormats,l;try{l=s_(r)}catch{throw new ie("Invalid KTX2 file.")}if(l.layerCount!==0)throw new ie("KTX2 texture arrays are not supported.");if(l.pixelDepth!==0)throw new ie("KTX2 3D textures are unsupported.");let P=l.dataFormatDescriptor[0],C=new Array(l.levelCount);return l.vkFormat===0&&(P.colorModel===c_||P.colorModel===A_)?Ar(r,l,B,me,f,C):(f.push(r.buffer),cr(l,C)),C}function cr(i,f){let r=i.vkFormat===Le.VK_FORMAT_R8G8B8_SRGB?E.RGB:E.RGBA,B;i.vkFormat===Le.VK_FORMAT_R8G8B8A8_UNORM?B=b.UNSIGNED_BYTE:i.vkFormat===Le.VK_FORMAT_R16G16B16A16_SFLOAT?B=b.HALF_FLOAT:i.vkFormat===Le.VK_FORMAT_R32G32B32A32_SFLOAT&&(B=b.FLOAT);for(let l=0;l<i.levels.length;++l){let P={};f[l]=P;let C=i.levels[l].levelData,h=i.pixelWidth>>l,y=i.pixelHeight>>l,m=h*y*E.componentsLength(r);for(let V=0;V<i.faceCount;++V){let d=C.byteOffset+m*i.typeSize*V,p;!ge(B)||b.sizeInBytes(B)===1?p=new Uint8Array(C.buffer,d,m):b.sizeInBytes(B)===2?p=new Uint16Array(C.buffer,d,m):p=new Float32Array(C.buffer,d,m),P[a_[V]]={internalFormat:r,datatype:B,width:h,height:y,levelBuffer:p}}}}function Ar(i,f,r,B,l,P){let C=new B.KTX2File(i),h=C.getWidth(),y=C.getHeight(),m=C.getLevels(),V=C.getHasAlpha();if(!(h>0)||!(y>0)||!(m>0))throw C.close(),C.delete(),new ie("Invalid KTX2 file");let d,p,Q=f.dataFormatDescriptor[0],u=B.transcoder_texture_format;if(Q.colorModel===c_)if(r.etc)d=V?E.RGBA8_ETC2_EAC:E.RGB8_ETC2,p=V?u.cTFETC2_RGBA:u.cTFETC1_RGB;else if(r.etc1&&!V)d=E.RGB_ETC1,p=u.cTFETC1_RGB;else if(r.s3tc)d=V?E.RGBA_DXT5:E.RGB_DXT1,p=V?u.cTFBC3_RGBA:u.cTFBC1_RGB;else if(r.pvrtc)d=V?E.RGBA_PVRTC_4BPPV1:E.RGB_PVRTC_4BPPV1,p=V?u.cTFPVRTC1_4_RGBA:u.cTFPVRTC1_4_RGB;else if(r.astc)d=E.RGBA_ASTC,p=u.cTFASTC_4x4_RGBA;else if(r.bc7)d=E.RGBA_BC7,p=u.cTFBC7_RGBA;else throw new ie("No transcoding format target available for ETC1S compressed ktx2.");else if(Q.colorModel===A_)if(r.astc)d=E.RGBA_ASTC,p=u.cTFASTC_4x4_RGBA;else if(r.bc7)d=E.RGBA_BC7,p=u.cTFBC7_RGBA;else if(r.s3tc)d=V?E.RGBA_DXT5:E.RGB_DXT1,p=V?u.cTFBC3_RGBA:u.cTFBC1_RGB;else if(r.etc)d=V?E.RGBA8_ETC2_EAC:E.RGB8_ETC2,p=V?u.cTFETC2_RGBA:u.cTFETC1_RGB;else if(r.etc1&&!V)d=E.RGB_ETC1,p=u.cTFETC1_RGB;else if(r.pvrtc)d=V?E.RGBA_PVRTC_4BPPV1:E.RGB_PVRTC_4BPPV1,p=V?u.cTFPVRTC1_4_RGBA:u.cTFPVRTC1_4_RGB;else throw new ie("No transcoding format target available for UASTC compressed ktx2.");if(!C.startTranscoding())throw C.close(),C.delete(),new ie("startTranscoding() failed");for(let X=0;X<f.levels.length;++X){let se={};P[X]=se,h=f.pixelWidth>>X,y=f.pixelHeight>>X;let le=C.getImageTranscodedSizeInBytes(X,0,0,p.value),W=new Uint8Array(le),J=C.transcodeImage(W,X,0,0,p.value,0,-1,-1);if(!ge(J))throw new ie("transcodeImage() failed.");l.push(W.buffer),se[a_[0]]={internalFormat:d,width:h,height:y,levelBuffer:W}}return C.close(),C.delete(),P}async function fr(i,f){let r=i.webAssemblyConfig,B=n_(R_.default,self.BASIS);return ge(r.wasmBinaryFile)?me=await B(r):me=await B(),me.initializeBasis(),!0}function Tr(i,f){let r=i.webAssemblyConfig;return ge(r)?fr(i,f):ar(i,f)}var yr=r_(Tr);export{yr as default};
