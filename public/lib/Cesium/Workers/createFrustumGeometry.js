/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.115
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{c as r}from"./chunk-TN3QK5HZ.js";import"./chunk-4L6WQV6O.js";import"./chunk-ASPQMVDG.js";import"./chunk-2OCTB5MG.js";import"./chunk-2NSXRWDL.js";import"./chunk-LCPJ46PJ.js";import"./chunk-IADNIOZ2.js";import"./chunk-QK5WKMSE.js";import"./chunk-DCQPNKAH.js";import"./chunk-HVT5SA4Z.js";import"./chunk-QHMHXQHC.js";import"./chunk-GGFNGKXD.js";import"./chunk-BPPZBFNC.js";import"./chunk-6DW2D3WX.js";import"./chunk-A7LPWAU6.js";import"./chunk-UNVMUIJM.js";import{e as o}from"./chunk-WVB7XP3Q.js";function i(e,t){return o(t)&&(e=r.unpack(e,t)),r.createGeometry(e)}var a=i;export{a as default};
