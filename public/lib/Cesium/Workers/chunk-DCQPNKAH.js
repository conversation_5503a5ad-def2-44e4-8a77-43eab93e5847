/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.115
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as L,b as u,c as H,d as jn,e as on}from"./chunk-QHMHXQHC.js";import{a as d}from"./chunk-GGFNGKXD.js";import{a as Rn}from"./chunk-6DW2D3WX.js";import{a as T}from"./chunk-A7LPWAU6.js";import{a as J,b as y}from"./chunk-UNVMUIJM.js";import{e as z}from"./chunk-WVB7XP3Q.js";function h(n,o,c,f){this.x=T(n,0),this.y=T(o,0),this.z=T(c,0),this.w=T(f,0)}h.fromElements=function(n,o,c,f,O){return z(O)?(O.x=n,O.y=o,O.z=c,O.w=f,O):new h(n,o,c,f)};h.fromColor=function(n,o){return y.typeOf.object("color",n),z(o)?(o.x=n.red,o.y=n.green,o.z=n.blue,o.w=n.alpha,o):new h(n.red,n.green,n.blue,n.alpha)};h.clone=function(n,o){if(z(n))return z(o)?(o.x=n.x,o.y=n.y,o.z=n.z,o.w=n.w,o):new h(n.x,n.y,n.z,n.w)};h.packedLength=4;h.pack=function(n,o,c){return y.typeOf.object("value",n),y.defined("array",o),c=T(c,0),o[c++]=n.x,o[c++]=n.y,o[c++]=n.z,o[c]=n.w,o};h.unpack=function(n,o,c){return y.defined("array",n),o=T(o,0),z(c)||(c=new h),c.x=n[o++],c.y=n[o++],c.z=n[o++],c.w=n[o],c};h.packArray=function(n,o){y.defined("array",n);let c=n.length,f=c*4;if(!z(o))o=new Array(f);else{if(!Array.isArray(o)&&o.length!==f)throw new J("If result is a typed array, it must have exactly array.length * 4 elements");o.length!==f&&(o.length=f)}for(let O=0;O<c;++O)h.pack(n[O],o,O*4);return o};h.unpackArray=function(n,o){if(y.defined("array",n),y.typeOf.number.greaterThanOrEquals("array.length",n.length,4),n.length%4!==0)throw new J("array length must be a multiple of 4.");let c=n.length;z(o)?o.length=c/4:o=new Array(c/4);for(let f=0;f<c;f+=4){let O=f/4;o[O]=h.unpack(n,f,o[O])}return o};h.fromArray=h.unpack;h.maximumComponent=function(n){return y.typeOf.object("cartesian",n),Math.max(n.x,n.y,n.z,n.w)};h.minimumComponent=function(n){return y.typeOf.object("cartesian",n),Math.min(n.x,n.y,n.z,n.w)};h.minimumByComponent=function(n,o,c){return y.typeOf.object("first",n),y.typeOf.object("second",o),y.typeOf.object("result",c),c.x=Math.min(n.x,o.x),c.y=Math.min(n.y,o.y),c.z=Math.min(n.z,o.z),c.w=Math.min(n.w,o.w),c};h.maximumByComponent=function(n,o,c){return y.typeOf.object("first",n),y.typeOf.object("second",o),y.typeOf.object("result",c),c.x=Math.max(n.x,o.x),c.y=Math.max(n.y,o.y),c.z=Math.max(n.z,o.z),c.w=Math.max(n.w,o.w),c};h.clamp=function(n,o,c,f){y.typeOf.object("value",n),y.typeOf.object("min",o),y.typeOf.object("max",c),y.typeOf.object("result",f);let O=d.clamp(n.x,o.x,c.x),p=d.clamp(n.y,o.y,c.y),b=d.clamp(n.z,o.z,c.z),e=d.clamp(n.w,o.w,c.w);return f.x=O,f.y=p,f.z=b,f.w=e,f};h.magnitudeSquared=function(n){return y.typeOf.object("cartesian",n),n.x*n.x+n.y*n.y+n.z*n.z+n.w*n.w};h.magnitude=function(n){return Math.sqrt(h.magnitudeSquared(n))};var fn=new h;h.distance=function(n,o){return y.typeOf.object("left",n),y.typeOf.object("right",o),h.subtract(n,o,fn),h.magnitude(fn)};h.distanceSquared=function(n,o){return y.typeOf.object("left",n),y.typeOf.object("right",o),h.subtract(n,o,fn),h.magnitudeSquared(fn)};h.normalize=function(n,o){y.typeOf.object("cartesian",n),y.typeOf.object("result",o);let c=h.magnitude(n);if(o.x=n.x/c,o.y=n.y/c,o.z=n.z/c,o.w=n.w/c,isNaN(o.x)||isNaN(o.y)||isNaN(o.z)||isNaN(o.w))throw new J("normalized result is not a number");return o};h.dot=function(n,o){return y.typeOf.object("left",n),y.typeOf.object("right",o),n.x*o.x+n.y*o.y+n.z*o.z+n.w*o.w};h.multiplyComponents=function(n,o,c){return y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c),c.x=n.x*o.x,c.y=n.y*o.y,c.z=n.z*o.z,c.w=n.w*o.w,c};h.divideComponents=function(n,o,c){return y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c),c.x=n.x/o.x,c.y=n.y/o.y,c.z=n.z/o.z,c.w=n.w/o.w,c};h.add=function(n,o,c){return y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c),c.x=n.x+o.x,c.y=n.y+o.y,c.z=n.z+o.z,c.w=n.w+o.w,c};h.subtract=function(n,o,c){return y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c),c.x=n.x-o.x,c.y=n.y-o.y,c.z=n.z-o.z,c.w=n.w-o.w,c};h.multiplyByScalar=function(n,o,c){return y.typeOf.object("cartesian",n),y.typeOf.number("scalar",o),y.typeOf.object("result",c),c.x=n.x*o,c.y=n.y*o,c.z=n.z*o,c.w=n.w*o,c};h.divideByScalar=function(n,o,c){return y.typeOf.object("cartesian",n),y.typeOf.number("scalar",o),y.typeOf.object("result",c),c.x=n.x/o,c.y=n.y/o,c.z=n.z/o,c.w=n.w/o,c};h.negate=function(n,o){return y.typeOf.object("cartesian",n),y.typeOf.object("result",o),o.x=-n.x,o.y=-n.y,o.z=-n.z,o.w=-n.w,o};h.abs=function(n,o){return y.typeOf.object("cartesian",n),y.typeOf.object("result",o),o.x=Math.abs(n.x),o.y=Math.abs(n.y),o.z=Math.abs(n.z),o.w=Math.abs(n.w),o};var Tn=new h;h.lerp=function(n,o,c,f){return y.typeOf.object("start",n),y.typeOf.object("end",o),y.typeOf.number("t",c),y.typeOf.object("result",f),h.multiplyByScalar(o,c,Tn),f=h.multiplyByScalar(n,1-c,f),h.add(Tn,f,f)};var Nn=new h;h.mostOrthogonalAxis=function(n,o){y.typeOf.object("cartesian",n),y.typeOf.object("result",o);let c=h.normalize(n,Nn);return h.abs(c,c),c.x<=c.y?c.x<=c.z?c.x<=c.w?o=h.clone(h.UNIT_X,o):o=h.clone(h.UNIT_W,o):c.z<=c.w?o=h.clone(h.UNIT_Z,o):o=h.clone(h.UNIT_W,o):c.y<=c.z?c.y<=c.w?o=h.clone(h.UNIT_Y,o):o=h.clone(h.UNIT_W,o):c.z<=c.w?o=h.clone(h.UNIT_Z,o):o=h.clone(h.UNIT_W,o),o};h.equals=function(n,o){return n===o||z(n)&&z(o)&&n.x===o.x&&n.y===o.y&&n.z===o.z&&n.w===o.w};h.equalsArray=function(n,o,c){return n.x===o[c]&&n.y===o[c+1]&&n.z===o[c+2]&&n.w===o[c+3]};h.equalsEpsilon=function(n,o,c,f){return n===o||z(n)&&z(o)&&d.equalsEpsilon(n.x,o.x,c,f)&&d.equalsEpsilon(n.y,o.y,c,f)&&d.equalsEpsilon(n.z,o.z,c,f)&&d.equalsEpsilon(n.w,o.w,c,f)};h.ZERO=Object.freeze(new h(0,0,0,0));h.ONE=Object.freeze(new h(1,1,1,1));h.UNIT_X=Object.freeze(new h(1,0,0,0));h.UNIT_Y=Object.freeze(new h(0,1,0,0));h.UNIT_Z=Object.freeze(new h(0,0,1,0));h.UNIT_W=Object.freeze(new h(0,0,0,1));h.prototype.clone=function(n){return h.clone(this,n)};h.prototype.equals=function(n){return h.equals(this,n)};h.prototype.equalsEpsilon=function(n,o,c){return h.equalsEpsilon(this,n,o,c)};h.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var Mn=new Float32Array(1),Q=new Uint8Array(Mn.buffer),_n=new Uint32Array([287454020]),Un=new Uint8Array(_n.buffer),dn=Un[0]===68;h.packFloat=function(n,o){return y.typeOf.number("value",n),z(o)||(o=new h),Mn[0]=n,dn?(o.x=Q[0],o.y=Q[1],o.z=Q[2],o.w=Q[3]):(o.x=Q[3],o.y=Q[2],o.z=Q[1],o.w=Q[0]),o};h.unpackFloat=function(n){return y.typeOf.object("packedFloat",n),dn?(Q[0]=n.x,Q[1]=n.y,Q[2]=n.z,Q[3]=n.w):(Q[0]=n.w,Q[1]=n.z,Q[2]=n.y,Q[3]=n.x),Mn[0]};var yn=h;function w(n,o,c,f,O,p,b,e,j,t,R,E,A,C,S,P){this[0]=T(n,0),this[1]=T(O,0),this[2]=T(j,0),this[3]=T(A,0),this[4]=T(o,0),this[5]=T(p,0),this[6]=T(t,0),this[7]=T(C,0),this[8]=T(c,0),this[9]=T(b,0),this[10]=T(R,0),this[11]=T(S,0),this[12]=T(f,0),this[13]=T(e,0),this[14]=T(E,0),this[15]=T(P,0)}w.packedLength=16;w.pack=function(n,o,c){return y.typeOf.object("value",n),y.defined("array",o),c=T(c,0),o[c++]=n[0],o[c++]=n[1],o[c++]=n[2],o[c++]=n[3],o[c++]=n[4],o[c++]=n[5],o[c++]=n[6],o[c++]=n[7],o[c++]=n[8],o[c++]=n[9],o[c++]=n[10],o[c++]=n[11],o[c++]=n[12],o[c++]=n[13],o[c++]=n[14],o[c]=n[15],o};w.unpack=function(n,o,c){return y.defined("array",n),o=T(o,0),z(c)||(c=new w),c[0]=n[o++],c[1]=n[o++],c[2]=n[o++],c[3]=n[o++],c[4]=n[o++],c[5]=n[o++],c[6]=n[o++],c[7]=n[o++],c[8]=n[o++],c[9]=n[o++],c[10]=n[o++],c[11]=n[o++],c[12]=n[o++],c[13]=n[o++],c[14]=n[o++],c[15]=n[o],c};w.packArray=function(n,o){y.defined("array",n);let c=n.length,f=c*16;if(!z(o))o=new Array(f);else{if(!Array.isArray(o)&&o.length!==f)throw new J("If result is a typed array, it must have exactly array.length * 16 elements");o.length!==f&&(o.length=f)}for(let O=0;O<c;++O)w.pack(n[O],o,O*16);return o};w.unpackArray=function(n,o){if(y.defined("array",n),y.typeOf.number.greaterThanOrEquals("array.length",n.length,16),n.length%16!==0)throw new J("array length must be a multiple of 16.");let c=n.length;z(o)?o.length=c/16:o=new Array(c/16);for(let f=0;f<c;f+=16){let O=f/16;o[O]=w.unpack(n,f,o[O])}return o};w.clone=function(n,o){if(z(n))return z(o)?(o[0]=n[0],o[1]=n[1],o[2]=n[2],o[3]=n[3],o[4]=n[4],o[5]=n[5],o[6]=n[6],o[7]=n[7],o[8]=n[8],o[9]=n[9],o[10]=n[10],o[11]=n[11],o[12]=n[12],o[13]=n[13],o[14]=n[14],o[15]=n[15],o):new w(n[0],n[4],n[8],n[12],n[1],n[5],n[9],n[13],n[2],n[6],n[10],n[14],n[3],n[7],n[11],n[15])};w.fromArray=w.unpack;w.fromColumnMajorArray=function(n,o){return y.defined("values",n),w.clone(n,o)};w.fromRowMajorArray=function(n,o){return y.defined("values",n),z(o)?(o[0]=n[0],o[1]=n[4],o[2]=n[8],o[3]=n[12],o[4]=n[1],o[5]=n[5],o[6]=n[9],o[7]=n[13],o[8]=n[2],o[9]=n[6],o[10]=n[10],o[11]=n[14],o[12]=n[3],o[13]=n[7],o[14]=n[11],o[15]=n[15],o):new w(n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8],n[9],n[10],n[11],n[12],n[13],n[14],n[15])};w.fromRotationTranslation=function(n,o,c){return y.typeOf.object("rotation",n),o=T(o,L.ZERO),z(c)?(c[0]=n[0],c[1]=n[1],c[2]=n[2],c[3]=0,c[4]=n[3],c[5]=n[4],c[6]=n[5],c[7]=0,c[8]=n[6],c[9]=n[7],c[10]=n[8],c[11]=0,c[12]=o.x,c[13]=o.y,c[14]=o.z,c[15]=1,c):new w(n[0],n[3],n[6],o.x,n[1],n[4],n[7],o.y,n[2],n[5],n[8],o.z,0,0,0,1)};w.fromTranslationQuaternionRotationScale=function(n,o,c,f){y.typeOf.object("translation",n),y.typeOf.object("rotation",o),y.typeOf.object("scale",c),z(f)||(f=new w);let O=c.x,p=c.y,b=c.z,e=o.x*o.x,j=o.x*o.y,t=o.x*o.z,R=o.x*o.w,E=o.y*o.y,A=o.y*o.z,C=o.y*o.w,S=o.z*o.z,P=o.z*o.w,W=o.w*o.w,N=e-E-S+W,_=2*(j-P),U=2*(t+C),m=2*(j+P),V=-e+E-S+W,X=2*(A-R),k=2*(t-C),$=2*(A+R),B=-e-E+S+W;return f[0]=N*O,f[1]=m*O,f[2]=k*O,f[3]=0,f[4]=_*p,f[5]=V*p,f[6]=$*p,f[7]=0,f[8]=U*b,f[9]=X*b,f[10]=B*b,f[11]=0,f[12]=n.x,f[13]=n.y,f[14]=n.z,f[15]=1,f};w.fromTranslationRotationScale=function(n,o){return y.typeOf.object("translationRotationScale",n),w.fromTranslationQuaternionRotationScale(n.translation,n.rotation,n.scale,o)};w.fromTranslation=function(n,o){return y.typeOf.object("translation",n),w.fromRotationTranslation(on.IDENTITY,n,o)};w.fromScale=function(n,o){return y.typeOf.object("scale",n),z(o)?(o[0]=n.x,o[1]=0,o[2]=0,o[3]=0,o[4]=0,o[5]=n.y,o[6]=0,o[7]=0,o[8]=0,o[9]=0,o[10]=n.z,o[11]=0,o[12]=0,o[13]=0,o[14]=0,o[15]=1,o):new w(n.x,0,0,0,0,n.y,0,0,0,0,n.z,0,0,0,0,1)};w.fromUniformScale=function(n,o){return y.typeOf.number("scale",n),z(o)?(o[0]=n,o[1]=0,o[2]=0,o[3]=0,o[4]=0,o[5]=n,o[6]=0,o[7]=0,o[8]=0,o[9]=0,o[10]=n,o[11]=0,o[12]=0,o[13]=0,o[14]=0,o[15]=1,o):new w(n,0,0,0,0,n,0,0,0,0,n,0,0,0,0,1)};w.fromRotation=function(n,o){return y.typeOf.object("rotation",n),z(o)||(o=new w),o[0]=n[0],o[1]=n[1],o[2]=n[2],o[3]=0,o[4]=n[3],o[5]=n[4],o[6]=n[5],o[7]=0,o[8]=n[6],o[9]=n[7],o[10]=n[8],o[11]=0,o[12]=0,o[13]=0,o[14]=0,o[15]=1,o};var s=new L,x=new L,cn=new L;w.fromCamera=function(n,o){y.typeOf.object("camera",n);let c=n.position,f=n.direction,O=n.up;y.typeOf.object("camera.position",c),y.typeOf.object("camera.direction",f),y.typeOf.object("camera.up",O),L.normalize(f,s),L.normalize(L.cross(s,O,x),x),L.normalize(L.cross(x,s,cn),cn);let p=x.x,b=x.y,e=x.z,j=s.x,t=s.y,R=s.z,E=cn.x,A=cn.y,C=cn.z,S=c.x,P=c.y,W=c.z,N=p*-S+b*-P+e*-W,_=E*-S+A*-P+C*-W,U=j*S+t*P+R*W;return z(o)?(o[0]=p,o[1]=E,o[2]=-j,o[3]=0,o[4]=b,o[5]=A,o[6]=-t,o[7]=0,o[8]=e,o[9]=C,o[10]=-R,o[11]=0,o[12]=N,o[13]=_,o[14]=U,o[15]=1,o):new w(p,b,e,N,E,A,C,_,-j,-t,-R,U,0,0,0,1)};w.computePerspectiveFieldOfView=function(n,o,c,f,O){y.typeOf.number.greaterThan("fovY",n,0),y.typeOf.number.lessThan("fovY",n,Math.PI),y.typeOf.number.greaterThan("near",c,0),y.typeOf.number.greaterThan("far",f,0),y.typeOf.object("result",O);let b=1/Math.tan(n*.5),e=b/o,j=(f+c)/(c-f),t=2*f*c/(c-f);return O[0]=e,O[1]=0,O[2]=0,O[3]=0,O[4]=0,O[5]=b,O[6]=0,O[7]=0,O[8]=0,O[9]=0,O[10]=j,O[11]=-1,O[12]=0,O[13]=0,O[14]=t,O[15]=0,O};w.computeOrthographicOffCenter=function(n,o,c,f,O,p,b){y.typeOf.number("left",n),y.typeOf.number("right",o),y.typeOf.number("bottom",c),y.typeOf.number("top",f),y.typeOf.number("near",O),y.typeOf.number("far",p),y.typeOf.object("result",b);let e=1/(o-n),j=1/(f-c),t=1/(p-O),R=-(o+n)*e,E=-(f+c)*j,A=-(p+O)*t;return e*=2,j*=2,t*=-2,b[0]=e,b[1]=0,b[2]=0,b[3]=0,b[4]=0,b[5]=j,b[6]=0,b[7]=0,b[8]=0,b[9]=0,b[10]=t,b[11]=0,b[12]=R,b[13]=E,b[14]=A,b[15]=1,b};w.computePerspectiveOffCenter=function(n,o,c,f,O,p,b){y.typeOf.number("left",n),y.typeOf.number("right",o),y.typeOf.number("bottom",c),y.typeOf.number("top",f),y.typeOf.number("near",O),y.typeOf.number("far",p),y.typeOf.object("result",b);let e=2*O/(o-n),j=2*O/(f-c),t=(o+n)/(o-n),R=(f+c)/(f-c),E=-(p+O)/(p-O),A=-1,C=-2*p*O/(p-O);return b[0]=e,b[1]=0,b[2]=0,b[3]=0,b[4]=0,b[5]=j,b[6]=0,b[7]=0,b[8]=t,b[9]=R,b[10]=E,b[11]=A,b[12]=0,b[13]=0,b[14]=C,b[15]=0,b};w.computeInfinitePerspectiveOffCenter=function(n,o,c,f,O,p){y.typeOf.number("left",n),y.typeOf.number("right",o),y.typeOf.number("bottom",c),y.typeOf.number("top",f),y.typeOf.number("near",O),y.typeOf.object("result",p);let b=2*O/(o-n),e=2*O/(f-c),j=(o+n)/(o-n),t=(f+c)/(f-c),R=-1,E=-1,A=-2*O;return p[0]=b,p[1]=0,p[2]=0,p[3]=0,p[4]=0,p[5]=e,p[6]=0,p[7]=0,p[8]=j,p[9]=t,p[10]=R,p[11]=E,p[12]=0,p[13]=0,p[14]=A,p[15]=0,p};w.computeViewportTransformation=function(n,o,c,f){z(f)||(f=new w),n=T(n,T.EMPTY_OBJECT);let O=T(n.x,0),p=T(n.y,0),b=T(n.width,0),e=T(n.height,0);o=T(o,0),c=T(c,1);let j=b*.5,t=e*.5,R=(c-o)*.5,E=j,A=t,C=R,S=O+j,P=p+t,W=o+R,N=1;return f[0]=E,f[1]=0,f[2]=0,f[3]=0,f[4]=0,f[5]=A,f[6]=0,f[7]=0,f[8]=0,f[9]=0,f[10]=C,f[11]=0,f[12]=S,f[13]=P,f[14]=W,f[15]=N,f};w.computeView=function(n,o,c,f,O){return y.typeOf.object("position",n),y.typeOf.object("direction",o),y.typeOf.object("up",c),y.typeOf.object("right",f),y.typeOf.object("result",O),O[0]=f.x,O[1]=c.x,O[2]=-o.x,O[3]=0,O[4]=f.y,O[5]=c.y,O[6]=-o.y,O[7]=0,O[8]=f.z,O[9]=c.z,O[10]=-o.z,O[11]=0,O[12]=-L.dot(f,n),O[13]=-L.dot(c,n),O[14]=L.dot(o,n),O[15]=1,O};w.toArray=function(n,o){return y.typeOf.object("matrix",n),z(o)?(o[0]=n[0],o[1]=n[1],o[2]=n[2],o[3]=n[3],o[4]=n[4],o[5]=n[5],o[6]=n[6],o[7]=n[7],o[8]=n[8],o[9]=n[9],o[10]=n[10],o[11]=n[11],o[12]=n[12],o[13]=n[13],o[14]=n[14],o[15]=n[15],o):[n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8],n[9],n[10],n[11],n[12],n[13],n[14],n[15]]};w.getElementIndex=function(n,o){return y.typeOf.number.greaterThanOrEquals("row",o,0),y.typeOf.number.lessThanOrEquals("row",o,3),y.typeOf.number.greaterThanOrEquals("column",n,0),y.typeOf.number.lessThanOrEquals("column",n,3),n*4+o};w.getColumn=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.number.greaterThanOrEquals("index",o,0),y.typeOf.number.lessThanOrEquals("index",o,3),y.typeOf.object("result",c);let f=o*4,O=n[f],p=n[f+1],b=n[f+2],e=n[f+3];return c.x=O,c.y=p,c.z=b,c.w=e,c};w.setColumn=function(n,o,c,f){y.typeOf.object("matrix",n),y.typeOf.number.greaterThanOrEquals("index",o,0),y.typeOf.number.lessThanOrEquals("index",o,3),y.typeOf.object("cartesian",c),y.typeOf.object("result",f),f=w.clone(n,f);let O=o*4;return f[O]=c.x,f[O+1]=c.y,f[O+2]=c.z,f[O+3]=c.w,f};w.getRow=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.number.greaterThanOrEquals("index",o,0),y.typeOf.number.lessThanOrEquals("index",o,3),y.typeOf.object("result",c);let f=n[o],O=n[o+4],p=n[o+8],b=n[o+12];return c.x=f,c.y=O,c.z=p,c.w=b,c};w.setRow=function(n,o,c,f){return y.typeOf.object("matrix",n),y.typeOf.number.greaterThanOrEquals("index",o,0),y.typeOf.number.lessThanOrEquals("index",o,3),y.typeOf.object("cartesian",c),y.typeOf.object("result",f),f=w.clone(n,f),f[o]=c.x,f[o+4]=c.y,f[o+8]=c.z,f[o+12]=c.w,f};w.setTranslation=function(n,o,c){return y.typeOf.object("matrix",n),y.typeOf.object("translation",o),y.typeOf.object("result",c),c[0]=n[0],c[1]=n[1],c[2]=n[2],c[3]=n[3],c[4]=n[4],c[5]=n[5],c[6]=n[6],c[7]=n[7],c[8]=n[8],c[9]=n[9],c[10]=n[10],c[11]=n[11],c[12]=o.x,c[13]=o.y,c[14]=o.z,c[15]=n[15],c};var Ln=new L;w.setScale=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("scale",o),y.typeOf.object("result",c);let f=w.getScale(n,Ln),O=o.x/f.x,p=o.y/f.y,b=o.z/f.z;return c[0]=n[0]*O,c[1]=n[1]*O,c[2]=n[2]*O,c[3]=n[3],c[4]=n[4]*p,c[5]=n[5]*p,c[6]=n[6]*p,c[7]=n[7],c[8]=n[8]*b,c[9]=n[9]*b,c[10]=n[10]*b,c[11]=n[11],c[12]=n[12],c[13]=n[13],c[14]=n[14],c[15]=n[15],c};var mn=new L;w.setUniformScale=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.number("scale",o),y.typeOf.object("result",c);let f=w.getScale(n,mn),O=o/f.x,p=o/f.y,b=o/f.z;return c[0]=n[0]*O,c[1]=n[1]*O,c[2]=n[2]*O,c[3]=n[3],c[4]=n[4]*p,c[5]=n[5]*p,c[6]=n[6]*p,c[7]=n[7],c[8]=n[8]*b,c[9]=n[9]*b,c[10]=n[10]*b,c[11]=n[11],c[12]=n[12],c[13]=n[13],c[14]=n[14],c[15]=n[15],c};var zn=new L;w.getScale=function(n,o){return y.typeOf.object("matrix",n),y.typeOf.object("result",o),o.x=L.magnitude(L.fromElements(n[0],n[1],n[2],zn)),o.y=L.magnitude(L.fromElements(n[4],n[5],n[6],zn)),o.z=L.magnitude(L.fromElements(n[8],n[9],n[10],zn)),o};var En=new L;w.getMaximumScale=function(n){return w.getScale(n,En),L.maximumComponent(En)};var Vn=new L;w.setRotation=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("result",c);let f=w.getScale(n,Vn);return c[0]=o[0]*f.x,c[1]=o[1]*f.x,c[2]=o[2]*f.x,c[3]=n[3],c[4]=o[3]*f.y,c[5]=o[4]*f.y,c[6]=o[5]*f.y,c[7]=n[7],c[8]=o[6]*f.z,c[9]=o[7]*f.z,c[10]=o[8]*f.z,c[11]=n[11],c[12]=n[12],c[13]=n[13],c[14]=n[14],c[15]=n[15],c};var Xn=new L;w.getRotation=function(n,o){y.typeOf.object("matrix",n),y.typeOf.object("result",o);let c=w.getScale(n,Xn);return o[0]=n[0]/c.x,o[1]=n[1]/c.x,o[2]=n[2]/c.x,o[3]=n[4]/c.y,o[4]=n[5]/c.y,o[5]=n[6]/c.y,o[6]=n[8]/c.z,o[7]=n[9]/c.z,o[8]=n[10]/c.z,o};w.multiply=function(n,o,c){y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c);let f=n[0],O=n[1],p=n[2],b=n[3],e=n[4],j=n[5],t=n[6],R=n[7],E=n[8],A=n[9],C=n[10],S=n[11],P=n[12],W=n[13],N=n[14],_=n[15],U=o[0],m=o[1],V=o[2],X=o[3],k=o[4],$=o[5],B=o[6],Z=o[7],I=o[8],D=o[9],G=o[10],K=o[11],F=o[12],v=o[13],i=o[14],g=o[15],a=f*U+e*m+E*V+P*X,l=O*U+j*m+A*V+W*X,r=p*U+t*m+C*V+N*X,nn=b*U+R*m+S*V+_*X,On=f*k+e*$+E*B+P*Z,pn=O*k+j*$+A*B+W*Z,bn=p*k+t*$+C*B+N*Z,wn=b*k+R*$+S*B+_*Z,hn=f*I+e*D+E*G+P*K,en=O*I+j*D+A*G+W*K,tn=p*I+t*D+C*G+N*K,Y=b*I+R*D+S*G+_*K,Cn=f*F+e*v+E*i+P*g,Pn=O*F+j*v+A*i+W*g,Sn=p*F+t*v+C*i+N*g,Wn=b*F+R*v+S*i+_*g;return c[0]=a,c[1]=l,c[2]=r,c[3]=nn,c[4]=On,c[5]=pn,c[6]=bn,c[7]=wn,c[8]=hn,c[9]=en,c[10]=tn,c[11]=Y,c[12]=Cn,c[13]=Pn,c[14]=Sn,c[15]=Wn,c};w.add=function(n,o,c){return y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c),c[0]=n[0]+o[0],c[1]=n[1]+o[1],c[2]=n[2]+o[2],c[3]=n[3]+o[3],c[4]=n[4]+o[4],c[5]=n[5]+o[5],c[6]=n[6]+o[6],c[7]=n[7]+o[7],c[8]=n[8]+o[8],c[9]=n[9]+o[9],c[10]=n[10]+o[10],c[11]=n[11]+o[11],c[12]=n[12]+o[12],c[13]=n[13]+o[13],c[14]=n[14]+o[14],c[15]=n[15]+o[15],c};w.subtract=function(n,o,c){return y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c),c[0]=n[0]-o[0],c[1]=n[1]-o[1],c[2]=n[2]-o[2],c[3]=n[3]-o[3],c[4]=n[4]-o[4],c[5]=n[5]-o[5],c[6]=n[6]-o[6],c[7]=n[7]-o[7],c[8]=n[8]-o[8],c[9]=n[9]-o[9],c[10]=n[10]-o[10],c[11]=n[11]-o[11],c[12]=n[12]-o[12],c[13]=n[13]-o[13],c[14]=n[14]-o[14],c[15]=n[15]-o[15],c};w.multiplyTransformation=function(n,o,c){y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c);let f=n[0],O=n[1],p=n[2],b=n[4],e=n[5],j=n[6],t=n[8],R=n[9],E=n[10],A=n[12],C=n[13],S=n[14],P=o[0],W=o[1],N=o[2],_=o[4],U=o[5],m=o[6],V=o[8],X=o[9],k=o[10],$=o[12],B=o[13],Z=o[14],I=f*P+b*W+t*N,D=O*P+e*W+R*N,G=p*P+j*W+E*N,K=f*_+b*U+t*m,F=O*_+e*U+R*m,v=p*_+j*U+E*m,i=f*V+b*X+t*k,g=O*V+e*X+R*k,a=p*V+j*X+E*k,l=f*$+b*B+t*Z+A,r=O*$+e*B+R*Z+C,nn=p*$+j*B+E*Z+S;return c[0]=I,c[1]=D,c[2]=G,c[3]=0,c[4]=K,c[5]=F,c[6]=v,c[7]=0,c[8]=i,c[9]=g,c[10]=a,c[11]=0,c[12]=l,c[13]=r,c[14]=nn,c[15]=1,c};w.multiplyByMatrix3=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("rotation",o),y.typeOf.object("result",c);let f=n[0],O=n[1],p=n[2],b=n[4],e=n[5],j=n[6],t=n[8],R=n[9],E=n[10],A=o[0],C=o[1],S=o[2],P=o[3],W=o[4],N=o[5],_=o[6],U=o[7],m=o[8],V=f*A+b*C+t*S,X=O*A+e*C+R*S,k=p*A+j*C+E*S,$=f*P+b*W+t*N,B=O*P+e*W+R*N,Z=p*P+j*W+E*N,I=f*_+b*U+t*m,D=O*_+e*U+R*m,G=p*_+j*U+E*m;return c[0]=V,c[1]=X,c[2]=k,c[3]=0,c[4]=$,c[5]=B,c[6]=Z,c[7]=0,c[8]=I,c[9]=D,c[10]=G,c[11]=0,c[12]=n[12],c[13]=n[13],c[14]=n[14],c[15]=n[15],c};w.multiplyByTranslation=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("translation",o),y.typeOf.object("result",c);let f=o.x,O=o.y,p=o.z,b=f*n[0]+O*n[4]+p*n[8]+n[12],e=f*n[1]+O*n[5]+p*n[9]+n[13],j=f*n[2]+O*n[6]+p*n[10]+n[14];return c[0]=n[0],c[1]=n[1],c[2]=n[2],c[3]=n[3],c[4]=n[4],c[5]=n[5],c[6]=n[6],c[7]=n[7],c[8]=n[8],c[9]=n[9],c[10]=n[10],c[11]=n[11],c[12]=b,c[13]=e,c[14]=j,c[15]=n[15],c};w.multiplyByScale=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("scale",o),y.typeOf.object("result",c);let f=o.x,O=o.y,p=o.z;return f===1&&O===1&&p===1?w.clone(n,c):(c[0]=f*n[0],c[1]=f*n[1],c[2]=f*n[2],c[3]=n[3],c[4]=O*n[4],c[5]=O*n[5],c[6]=O*n[6],c[7]=n[7],c[8]=p*n[8],c[9]=p*n[9],c[10]=p*n[10],c[11]=n[11],c[12]=n[12],c[13]=n[13],c[14]=n[14],c[15]=n[15],c)};w.multiplyByUniformScale=function(n,o,c){return y.typeOf.object("matrix",n),y.typeOf.number("scale",o),y.typeOf.object("result",c),c[0]=n[0]*o,c[1]=n[1]*o,c[2]=n[2]*o,c[3]=n[3],c[4]=n[4]*o,c[5]=n[5]*o,c[6]=n[6]*o,c[7]=n[7],c[8]=n[8]*o,c[9]=n[9]*o,c[10]=n[10]*o,c[11]=n[11],c[12]=n[12],c[13]=n[13],c[14]=n[14],c[15]=n[15],c};w.multiplyByVector=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("cartesian",o),y.typeOf.object("result",c);let f=o.x,O=o.y,p=o.z,b=o.w,e=n[0]*f+n[4]*O+n[8]*p+n[12]*b,j=n[1]*f+n[5]*O+n[9]*p+n[13]*b,t=n[2]*f+n[6]*O+n[10]*p+n[14]*b,R=n[3]*f+n[7]*O+n[11]*p+n[15]*b;return c.x=e,c.y=j,c.z=t,c.w=R,c};w.multiplyByPointAsVector=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("cartesian",o),y.typeOf.object("result",c);let f=o.x,O=o.y,p=o.z,b=n[0]*f+n[4]*O+n[8]*p,e=n[1]*f+n[5]*O+n[9]*p,j=n[2]*f+n[6]*O+n[10]*p;return c.x=b,c.y=e,c.z=j,c};w.multiplyByPoint=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("cartesian",o),y.typeOf.object("result",c);let f=o.x,O=o.y,p=o.z,b=n[0]*f+n[4]*O+n[8]*p+n[12],e=n[1]*f+n[5]*O+n[9]*p+n[13],j=n[2]*f+n[6]*O+n[10]*p+n[14];return c.x=b,c.y=e,c.z=j,c};w.multiplyByScalar=function(n,o,c){return y.typeOf.object("matrix",n),y.typeOf.number("scalar",o),y.typeOf.object("result",c),c[0]=n[0]*o,c[1]=n[1]*o,c[2]=n[2]*o,c[3]=n[3]*o,c[4]=n[4]*o,c[5]=n[5]*o,c[6]=n[6]*o,c[7]=n[7]*o,c[8]=n[8]*o,c[9]=n[9]*o,c[10]=n[10]*o,c[11]=n[11]*o,c[12]=n[12]*o,c[13]=n[13]*o,c[14]=n[14]*o,c[15]=n[15]*o,c};w.negate=function(n,o){return y.typeOf.object("matrix",n),y.typeOf.object("result",o),o[0]=-n[0],o[1]=-n[1],o[2]=-n[2],o[3]=-n[3],o[4]=-n[4],o[5]=-n[5],o[6]=-n[6],o[7]=-n[7],o[8]=-n[8],o[9]=-n[9],o[10]=-n[10],o[11]=-n[11],o[12]=-n[12],o[13]=-n[13],o[14]=-n[14],o[15]=-n[15],o};w.transpose=function(n,o){y.typeOf.object("matrix",n),y.typeOf.object("result",o);let c=n[1],f=n[2],O=n[3],p=n[6],b=n[7],e=n[11];return o[0]=n[0],o[1]=n[4],o[2]=n[8],o[3]=n[12],o[4]=c,o[5]=n[5],o[6]=n[9],o[7]=n[13],o[8]=f,o[9]=p,o[10]=n[10],o[11]=n[14],o[12]=O,o[13]=b,o[14]=e,o[15]=n[15],o};w.abs=function(n,o){return y.typeOf.object("matrix",n),y.typeOf.object("result",o),o[0]=Math.abs(n[0]),o[1]=Math.abs(n[1]),o[2]=Math.abs(n[2]),o[3]=Math.abs(n[3]),o[4]=Math.abs(n[4]),o[5]=Math.abs(n[5]),o[6]=Math.abs(n[6]),o[7]=Math.abs(n[7]),o[8]=Math.abs(n[8]),o[9]=Math.abs(n[9]),o[10]=Math.abs(n[10]),o[11]=Math.abs(n[11]),o[12]=Math.abs(n[12]),o[13]=Math.abs(n[13]),o[14]=Math.abs(n[14]),o[15]=Math.abs(n[15]),o};w.equals=function(n,o){return n===o||z(n)&&z(o)&&n[12]===o[12]&&n[13]===o[13]&&n[14]===o[14]&&n[0]===o[0]&&n[1]===o[1]&&n[2]===o[2]&&n[4]===o[4]&&n[5]===o[5]&&n[6]===o[6]&&n[8]===o[8]&&n[9]===o[9]&&n[10]===o[10]&&n[3]===o[3]&&n[7]===o[7]&&n[11]===o[11]&&n[15]===o[15]};w.equalsEpsilon=function(n,o,c){return c=T(c,0),n===o||z(n)&&z(o)&&Math.abs(n[0]-o[0])<=c&&Math.abs(n[1]-o[1])<=c&&Math.abs(n[2]-o[2])<=c&&Math.abs(n[3]-o[3])<=c&&Math.abs(n[4]-o[4])<=c&&Math.abs(n[5]-o[5])<=c&&Math.abs(n[6]-o[6])<=c&&Math.abs(n[7]-o[7])<=c&&Math.abs(n[8]-o[8])<=c&&Math.abs(n[9]-o[9])<=c&&Math.abs(n[10]-o[10])<=c&&Math.abs(n[11]-o[11])<=c&&Math.abs(n[12]-o[12])<=c&&Math.abs(n[13]-o[13])<=c&&Math.abs(n[14]-o[14])<=c&&Math.abs(n[15]-o[15])<=c};w.getTranslation=function(n,o){return y.typeOf.object("matrix",n),y.typeOf.object("result",o),o.x=n[12],o.y=n[13],o.z=n[14],o};w.getMatrix3=function(n,o){return y.typeOf.object("matrix",n),y.typeOf.object("result",o),o[0]=n[0],o[1]=n[1],o[2]=n[2],o[3]=n[4],o[4]=n[5],o[5]=n[6],o[6]=n[8],o[7]=n[9],o[8]=n[10],o};var kn=new on,$n=new on,Bn=new yn,Yn=new yn(0,0,0,1);w.inverse=function(n,o){y.typeOf.object("matrix",n),y.typeOf.object("result",o);let c=n[0],f=n[4],O=n[8],p=n[12],b=n[1],e=n[5],j=n[9],t=n[13],R=n[2],E=n[6],A=n[10],C=n[14],S=n[3],P=n[7],W=n[11],N=n[15],_=A*N,U=C*W,m=E*N,V=C*P,X=E*W,k=A*P,$=R*N,B=C*S,Z=R*W,I=A*S,D=R*P,G=E*S,K=_*e+V*j+X*t-(U*e+m*j+k*t),F=U*b+$*j+I*t-(_*b+B*j+Z*t),v=m*b+B*e+D*t-(V*b+$*e+G*t),i=k*b+Z*e+G*j-(X*b+I*e+D*j),g=U*f+m*O+k*p-(_*f+V*O+X*p),a=_*c+B*O+Z*p-(U*c+$*O+I*p),l=V*c+$*f+G*p-(m*c+B*f+D*p),r=X*c+I*f+D*O-(k*c+Z*f+G*O);_=O*t,U=p*j,m=f*t,V=p*e,X=f*j,k=O*e,$=c*t,B=p*b,Z=c*j,I=O*b,D=c*e,G=f*b;let nn=_*P+V*W+X*N-(U*P+m*W+k*N),On=U*S+$*W+I*N-(_*S+B*W+Z*N),pn=m*S+B*P+D*N-(V*S+$*P+G*N),bn=k*S+Z*P+G*W-(X*S+I*P+D*W),wn=m*A+k*C+U*E-(X*C+_*E+V*A),hn=Z*C+_*R+B*A-($*A+I*C+U*R),en=$*E+G*C+V*R-(D*C+m*R+B*E),tn=D*A+X*R+I*E-(Z*E+G*A+k*R),Y=c*K+f*F+O*v+p*i;if(Math.abs(Y)<d.EPSILON21){if(on.equalsEpsilon(w.getMatrix3(n,kn),$n,d.EPSILON7)&&yn.equals(w.getRow(n,3,Bn),Yn))return o[0]=0,o[1]=0,o[2]=0,o[3]=0,o[4]=0,o[5]=0,o[6]=0,o[7]=0,o[8]=0,o[9]=0,o[10]=0,o[11]=0,o[12]=-n[12],o[13]=-n[13],o[14]=-n[14],o[15]=1,o;throw new Rn("matrix is not invertible because its determinate is zero.")}return Y=1/Y,o[0]=K*Y,o[1]=F*Y,o[2]=v*Y,o[3]=i*Y,o[4]=g*Y,o[5]=a*Y,o[6]=l*Y,o[7]=r*Y,o[8]=nn*Y,o[9]=On*Y,o[10]=pn*Y,o[11]=bn*Y,o[12]=wn*Y,o[13]=hn*Y,o[14]=en*Y,o[15]=tn*Y,o};w.inverseTransformation=function(n,o){y.typeOf.object("matrix",n),y.typeOf.object("result",o);let c=n[0],f=n[1],O=n[2],p=n[4],b=n[5],e=n[6],j=n[8],t=n[9],R=n[10],E=n[12],A=n[13],C=n[14],S=-c*E-f*A-O*C,P=-p*E-b*A-e*C,W=-j*E-t*A-R*C;return o[0]=c,o[1]=p,o[2]=j,o[3]=0,o[4]=f,o[5]=b,o[6]=t,o[7]=0,o[8]=O,o[9]=e,o[10]=R,o[11]=0,o[12]=S,o[13]=P,o[14]=W,o[15]=1,o};var Zn=new w;w.inverseTranspose=function(n,o){return y.typeOf.object("matrix",n),y.typeOf.object("result",o),w.inverse(w.transpose(n,Zn),o)};w.IDENTITY=Object.freeze(new w(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1));w.ZERO=Object.freeze(new w(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0));w.COLUMN0ROW0=0;w.COLUMN0ROW1=1;w.COLUMN0ROW2=2;w.COLUMN0ROW3=3;w.COLUMN1ROW0=4;w.COLUMN1ROW1=5;w.COLUMN1ROW2=6;w.COLUMN1ROW3=7;w.COLUMN2ROW0=8;w.COLUMN2ROW1=9;w.COLUMN2ROW2=10;w.COLUMN2ROW3=11;w.COLUMN3ROW0=12;w.COLUMN3ROW1=13;w.COLUMN3ROW2=14;w.COLUMN3ROW3=15;Object.defineProperties(w.prototype,{length:{get:function(){return w.packedLength}}});w.prototype.clone=function(n){return w.clone(this,n)};w.prototype.equals=function(n){return w.equals(this,n)};w.equalsArray=function(n,o,c){return n[0]===o[c]&&n[1]===o[c+1]&&n[2]===o[c+2]&&n[3]===o[c+3]&&n[4]===o[c+4]&&n[5]===o[c+5]&&n[6]===o[c+6]&&n[7]===o[c+7]&&n[8]===o[c+8]&&n[9]===o[c+9]&&n[10]===o[c+10]&&n[11]===o[c+11]&&n[12]===o[c+12]&&n[13]===o[c+13]&&n[14]===o[c+14]&&n[15]===o[c+15]};w.prototype.equalsEpsilon=function(n,o){return w.equalsEpsilon(this,n,o)};w.prototype.toString=function(){return`(${this[0]}, ${this[4]}, ${this[8]}, ${this[12]})
(${this[1]}, ${this[5]}, ${this[9]}, ${this[13]})
(${this[2]}, ${this[6]}, ${this[10]}, ${this[14]})
(${this[3]}, ${this[7]}, ${this[11]}, ${this[15]})`};var yo=w;function q(n,o,c,f){this.west=T(n,0),this.south=T(o,0),this.east=T(c,0),this.north=T(f,0)}Object.defineProperties(q.prototype,{width:{get:function(){return q.computeWidth(this)}},height:{get:function(){return q.computeHeight(this)}}});q.packedLength=4;q.pack=function(n,o,c){return y.typeOf.object("value",n),y.defined("array",o),c=T(c,0),o[c++]=n.west,o[c++]=n.south,o[c++]=n.east,o[c]=n.north,o};q.unpack=function(n,o,c){return y.defined("array",n),o=T(o,0),z(c)||(c=new q),c.west=n[o++],c.south=n[o++],c.east=n[o++],c.north=n[o],c};q.computeWidth=function(n){y.typeOf.object("rectangle",n);let o=n.east,c=n.west;return o<c&&(o+=d.TWO_PI),o-c};q.computeHeight=function(n){return y.typeOf.object("rectangle",n),n.north-n.south};q.fromDegrees=function(n,o,c,f,O){return n=d.toRadians(T(n,0)),o=d.toRadians(T(o,0)),c=d.toRadians(T(c,0)),f=d.toRadians(T(f,0)),z(O)?(O.west=n,O.south=o,O.east=c,O.north=f,O):new q(n,o,c,f)};q.fromRadians=function(n,o,c,f,O){return z(O)?(O.west=T(n,0),O.south=T(o,0),O.east=T(c,0),O.north=T(f,0),O):new q(n,o,c,f)};q.fromCartographicArray=function(n,o){y.defined("cartographics",n);let c=Number.MAX_VALUE,f=-Number.MAX_VALUE,O=Number.MAX_VALUE,p=-Number.MAX_VALUE,b=Number.MAX_VALUE,e=-Number.MAX_VALUE;for(let j=0,t=n.length;j<t;j++){let R=n[j];c=Math.min(c,R.longitude),f=Math.max(f,R.longitude),b=Math.min(b,R.latitude),e=Math.max(e,R.latitude);let E=R.longitude>=0?R.longitude:R.longitude+d.TWO_PI;O=Math.min(O,E),p=Math.max(p,E)}return f-c>p-O&&(c=O,f=p,f>d.PI&&(f=f-d.TWO_PI),c>d.PI&&(c=c-d.TWO_PI)),z(o)?(o.west=c,o.south=b,o.east=f,o.north=e,o):new q(c,b,f,e)};q.fromCartesianArray=function(n,o,c){y.defined("cartesians",n),o=T(o,jn.WGS84);let f=Number.MAX_VALUE,O=-Number.MAX_VALUE,p=Number.MAX_VALUE,b=-Number.MAX_VALUE,e=Number.MAX_VALUE,j=-Number.MAX_VALUE;for(let t=0,R=n.length;t<R;t++){let E=o.cartesianToCartographic(n[t]);f=Math.min(f,E.longitude),O=Math.max(O,E.longitude),e=Math.min(e,E.latitude),j=Math.max(j,E.latitude);let A=E.longitude>=0?E.longitude:E.longitude+d.TWO_PI;p=Math.min(p,A),b=Math.max(b,A)}return O-f>b-p&&(f=p,O=b,O>d.PI&&(O=O-d.TWO_PI),f>d.PI&&(f=f-d.TWO_PI)),z(c)?(c.west=f,c.south=e,c.east=O,c.north=j,c):new q(f,e,O,j)};q.clone=function(n,o){if(z(n))return z(o)?(o.west=n.west,o.south=n.south,o.east=n.east,o.north=n.north,o):new q(n.west,n.south,n.east,n.north)};q.equalsEpsilon=function(n,o,c){return c=T(c,0),n===o||z(n)&&z(o)&&Math.abs(n.west-o.west)<=c&&Math.abs(n.south-o.south)<=c&&Math.abs(n.east-o.east)<=c&&Math.abs(n.north-o.north)<=c};q.prototype.clone=function(n){return q.clone(this,n)};q.prototype.equals=function(n){return q.equals(this,n)};q.equals=function(n,o){return n===o||z(n)&&z(o)&&n.west===o.west&&n.south===o.south&&n.east===o.east&&n.north===o.north};q.prototype.equalsEpsilon=function(n,o){return q.equalsEpsilon(this,n,o)};q.validate=function(n){y.typeOf.object("rectangle",n);let o=n.north;y.typeOf.number.greaterThanOrEquals("north",o,-d.PI_OVER_TWO),y.typeOf.number.lessThanOrEquals("north",o,d.PI_OVER_TWO);let c=n.south;y.typeOf.number.greaterThanOrEquals("south",c,-d.PI_OVER_TWO),y.typeOf.number.lessThanOrEquals("south",c,d.PI_OVER_TWO);let f=n.west;y.typeOf.number.greaterThanOrEquals("west",f,-Math.PI),y.typeOf.number.lessThanOrEquals("west",f,Math.PI);let O=n.east;y.typeOf.number.greaterThanOrEquals("east",O,-Math.PI),y.typeOf.number.lessThanOrEquals("east",O,Math.PI)};q.southwest=function(n,o){return y.typeOf.object("rectangle",n),z(o)?(o.longitude=n.west,o.latitude=n.south,o.height=0,o):new u(n.west,n.south)};q.northwest=function(n,o){return y.typeOf.object("rectangle",n),z(o)?(o.longitude=n.west,o.latitude=n.north,o.height=0,o):new u(n.west,n.north)};q.northeast=function(n,o){return y.typeOf.object("rectangle",n),z(o)?(o.longitude=n.east,o.latitude=n.north,o.height=0,o):new u(n.east,n.north)};q.southeast=function(n,o){return y.typeOf.object("rectangle",n),z(o)?(o.longitude=n.east,o.latitude=n.south,o.height=0,o):new u(n.east,n.south)};q.center=function(n,o){y.typeOf.object("rectangle",n);let c=n.east,f=n.west;c<f&&(c+=d.TWO_PI);let O=d.negativePiToPi((f+c)*.5),p=(n.south+n.north)*.5;return z(o)?(o.longitude=O,o.latitude=p,o.height=0,o):new u(O,p)};q.intersection=function(n,o,c){y.typeOf.object("rectangle",n),y.typeOf.object("otherRectangle",o);let f=n.east,O=n.west,p=o.east,b=o.west;f<O&&p>0?f+=d.TWO_PI:p<b&&f>0&&(p+=d.TWO_PI),f<O&&b<0?b+=d.TWO_PI:p<b&&O<0&&(O+=d.TWO_PI);let e=d.negativePiToPi(Math.max(O,b)),j=d.negativePiToPi(Math.min(f,p));if((n.west<n.east||o.west<o.east)&&j<=e)return;let t=Math.max(n.south,o.south),R=Math.min(n.north,o.north);if(!(t>=R))return z(c)?(c.west=e,c.south=t,c.east=j,c.north=R,c):new q(e,t,j,R)};q.simpleIntersection=function(n,o,c){y.typeOf.object("rectangle",n),y.typeOf.object("otherRectangle",o);let f=Math.max(n.west,o.west),O=Math.max(n.south,o.south),p=Math.min(n.east,o.east),b=Math.min(n.north,o.north);if(!(O>=b||f>=p))return z(c)?(c.west=f,c.south=O,c.east=p,c.north=b,c):new q(f,O,p,b)};q.union=function(n,o,c){y.typeOf.object("rectangle",n),y.typeOf.object("otherRectangle",o),z(c)||(c=new q);let f=n.east,O=n.west,p=o.east,b=o.west;f<O&&p>0?f+=d.TWO_PI:p<b&&f>0&&(p+=d.TWO_PI),f<O&&b<0?b+=d.TWO_PI:p<b&&O<0&&(O+=d.TWO_PI);let e=d.negativePiToPi(Math.min(O,b)),j=d.negativePiToPi(Math.max(f,p));return c.west=e,c.south=Math.min(n.south,o.south),c.east=j,c.north=Math.max(n.north,o.north),c};q.expand=function(n,o,c){return y.typeOf.object("rectangle",n),y.typeOf.object("cartographic",o),z(c)||(c=new q),c.west=Math.min(n.west,o.longitude),c.south=Math.min(n.south,o.latitude),c.east=Math.max(n.east,o.longitude),c.north=Math.max(n.north,o.latitude),c};q.contains=function(n,o){y.typeOf.object("rectangle",n),y.typeOf.object("cartographic",o);let c=o.longitude,f=o.latitude,O=n.west,p=n.east;return p<O&&(p+=d.TWO_PI,c<0&&(c+=d.TWO_PI)),(c>O||d.equalsEpsilon(c,O,d.EPSILON14))&&(c<p||d.equalsEpsilon(c,p,d.EPSILON14))&&f>=n.south&&f<=n.north};var In=new u;q.subsample=function(n,o,c,f){y.typeOf.object("rectangle",n),o=T(o,jn.WGS84),c=T(c,0),z(f)||(f=[]);let O=0,p=n.north,b=n.south,e=n.east,j=n.west,t=In;t.height=c,t.longitude=j,t.latitude=p,f[O]=o.cartographicToCartesian(t,f[O]),O++,t.longitude=e,f[O]=o.cartographicToCartesian(t,f[O]),O++,t.latitude=b,f[O]=o.cartographicToCartesian(t,f[O]),O++,t.longitude=j,f[O]=o.cartographicToCartesian(t,f[O]),O++,p<0?t.latitude=p:b>0?t.latitude=b:t.latitude=0;for(let R=1;R<8;++R)t.longitude=-Math.PI+R*d.PI_OVER_TWO,q.contains(n,t)&&(f[O]=o.cartographicToCartesian(t,f[O]),O++);return t.latitude===0&&(t.longitude=j,f[O]=o.cartographicToCartesian(t,f[O]),O++,t.longitude=e,f[O]=o.cartographicToCartesian(t,f[O]),O++),f.length=O,f};q.subsection=function(n,o,c,f,O,p){if(y.typeOf.object("rectangle",n),y.typeOf.number.greaterThanOrEquals("westLerp",o,0),y.typeOf.number.lessThanOrEquals("westLerp",o,1),y.typeOf.number.greaterThanOrEquals("southLerp",c,0),y.typeOf.number.lessThanOrEquals("southLerp",c,1),y.typeOf.number.greaterThanOrEquals("eastLerp",f,0),y.typeOf.number.lessThanOrEquals("eastLerp",f,1),y.typeOf.number.greaterThanOrEquals("northLerp",O,0),y.typeOf.number.lessThanOrEquals("northLerp",O,1),y.typeOf.number.lessThanOrEquals("westLerp",o,f),y.typeOf.number.lessThanOrEquals("southLerp",c,O),z(p)||(p=new q),n.west<=n.east){let e=n.east-n.west;p.west=n.west+o*e,p.east=n.west+f*e}else{let e=d.TWO_PI+n.east-n.west;p.west=d.negativePiToPi(n.west+o*e),p.east=d.negativePiToPi(n.west+f*e)}let b=n.north-n.south;return p.south=n.south+c*b,p.north=n.south+O*b,o===1&&(p.west=n.east),f===1&&(p.east=n.east),c===1&&(p.south=n.north),O===1&&(p.north=n.north),p};q.MAX_VALUE=Object.freeze(new q(-Math.PI,-d.PI_OVER_TWO,Math.PI,d.PI_OVER_TWO));var jo=q;function M(n,o,c,f){this[0]=T(n,0),this[1]=T(c,0),this[2]=T(o,0),this[3]=T(f,0)}M.packedLength=4;M.pack=function(n,o,c){return y.typeOf.object("value",n),y.defined("array",o),c=T(c,0),o[c++]=n[0],o[c++]=n[1],o[c++]=n[2],o[c++]=n[3],o};M.unpack=function(n,o,c){return y.defined("array",n),o=T(o,0),z(c)||(c=new M),c[0]=n[o++],c[1]=n[o++],c[2]=n[o++],c[3]=n[o++],c};M.packArray=function(n,o){y.defined("array",n);let c=n.length,f=c*4;if(!z(o))o=new Array(f);else{if(!Array.isArray(o)&&o.length!==f)throw new J("If result is a typed array, it must have exactly array.length * 4 elements");o.length!==f&&(o.length=f)}for(let O=0;O<c;++O)M.pack(n[O],o,O*4);return o};M.unpackArray=function(n,o){if(y.defined("array",n),y.typeOf.number.greaterThanOrEquals("array.length",n.length,4),n.length%4!==0)throw new J("array length must be a multiple of 4.");let c=n.length;z(o)?o.length=c/4:o=new Array(c/4);for(let f=0;f<c;f+=4){let O=f/4;o[O]=M.unpack(n,f,o[O])}return o};M.clone=function(n,o){if(z(n))return z(o)?(o[0]=n[0],o[1]=n[1],o[2]=n[2],o[3]=n[3],o):new M(n[0],n[2],n[1],n[3])};M.fromArray=M.unpack;M.fromColumnMajorArray=function(n,o){return y.defined("values",n),M.clone(n,o)};M.fromRowMajorArray=function(n,o){return y.defined("values",n),z(o)?(o[0]=n[0],o[1]=n[2],o[2]=n[1],o[3]=n[3],o):new M(n[0],n[1],n[2],n[3])};M.fromScale=function(n,o){return y.typeOf.object("scale",n),z(o)?(o[0]=n.x,o[1]=0,o[2]=0,o[3]=n.y,o):new M(n.x,0,0,n.y)};M.fromUniformScale=function(n,o){return y.typeOf.number("scale",n),z(o)?(o[0]=n,o[1]=0,o[2]=0,o[3]=n,o):new M(n,0,0,n)};M.fromRotation=function(n,o){y.typeOf.number("angle",n);let c=Math.cos(n),f=Math.sin(n);return z(o)?(o[0]=c,o[1]=f,o[2]=-f,o[3]=c,o):new M(c,-f,f,c)};M.toArray=function(n,o){return y.typeOf.object("matrix",n),z(o)?(o[0]=n[0],o[1]=n[1],o[2]=n[2],o[3]=n[3],o):[n[0],n[1],n[2],n[3]]};M.getElementIndex=function(n,o){return y.typeOf.number.greaterThanOrEquals("row",o,0),y.typeOf.number.lessThanOrEquals("row",o,1),y.typeOf.number.greaterThanOrEquals("column",n,0),y.typeOf.number.lessThanOrEquals("column",n,1),n*2+o};M.getColumn=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.number.greaterThanOrEquals("index",o,0),y.typeOf.number.lessThanOrEquals("index",o,1),y.typeOf.object("result",c);let f=o*2,O=n[f],p=n[f+1];return c.x=O,c.y=p,c};M.setColumn=function(n,o,c,f){y.typeOf.object("matrix",n),y.typeOf.number.greaterThanOrEquals("index",o,0),y.typeOf.number.lessThanOrEquals("index",o,1),y.typeOf.object("cartesian",c),y.typeOf.object("result",f),f=M.clone(n,f);let O=o*2;return f[O]=c.x,f[O+1]=c.y,f};M.getRow=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.number.greaterThanOrEquals("index",o,0),y.typeOf.number.lessThanOrEquals("index",o,1),y.typeOf.object("result",c);let f=n[o],O=n[o+2];return c.x=f,c.y=O,c};M.setRow=function(n,o,c,f){return y.typeOf.object("matrix",n),y.typeOf.number.greaterThanOrEquals("index",o,0),y.typeOf.number.lessThanOrEquals("index",o,1),y.typeOf.object("cartesian",c),y.typeOf.object("result",f),f=M.clone(n,f),f[o]=c.x,f[o+2]=c.y,f};var Dn=new H;M.setScale=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("scale",o),y.typeOf.object("result",c);let f=M.getScale(n,Dn),O=o.x/f.x,p=o.y/f.y;return c[0]=n[0]*O,c[1]=n[1]*O,c[2]=n[2]*p,c[3]=n[3]*p,c};var Gn=new H;M.setUniformScale=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.number("scale",o),y.typeOf.object("result",c);let f=M.getScale(n,Gn),O=o/f.x,p=o/f.y;return c[0]=n[0]*O,c[1]=n[1]*O,c[2]=n[2]*p,c[3]=n[3]*p,c};var qn=new H;M.getScale=function(n,o){return y.typeOf.object("matrix",n),y.typeOf.object("result",o),o.x=H.magnitude(H.fromElements(n[0],n[1],qn)),o.y=H.magnitude(H.fromElements(n[2],n[3],qn)),o};var An=new H;M.getMaximumScale=function(n){return M.getScale(n,An),H.maximumComponent(An)};var Qn=new H;M.setRotation=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("result",c);let f=M.getScale(n,Qn);return c[0]=o[0]*f.x,c[1]=o[1]*f.x,c[2]=o[2]*f.y,c[3]=o[3]*f.y,c};var Hn=new H;M.getRotation=function(n,o){y.typeOf.object("matrix",n),y.typeOf.object("result",o);let c=M.getScale(n,Hn);return o[0]=n[0]/c.x,o[1]=n[1]/c.x,o[2]=n[2]/c.y,o[3]=n[3]/c.y,o};M.multiply=function(n,o,c){y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c);let f=n[0]*o[0]+n[2]*o[1],O=n[0]*o[2]+n[2]*o[3],p=n[1]*o[0]+n[3]*o[1],b=n[1]*o[2]+n[3]*o[3];return c[0]=f,c[1]=p,c[2]=O,c[3]=b,c};M.add=function(n,o,c){return y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c),c[0]=n[0]+o[0],c[1]=n[1]+o[1],c[2]=n[2]+o[2],c[3]=n[3]+o[3],c};M.subtract=function(n,o,c){return y.typeOf.object("left",n),y.typeOf.object("right",o),y.typeOf.object("result",c),c[0]=n[0]-o[0],c[1]=n[1]-o[1],c[2]=n[2]-o[2],c[3]=n[3]-o[3],c};M.multiplyByVector=function(n,o,c){y.typeOf.object("matrix",n),y.typeOf.object("cartesian",o),y.typeOf.object("result",c);let f=n[0]*o.x+n[2]*o.y,O=n[1]*o.x+n[3]*o.y;return c.x=f,c.y=O,c};M.multiplyByScalar=function(n,o,c){return y.typeOf.object("matrix",n),y.typeOf.number("scalar",o),y.typeOf.object("result",c),c[0]=n[0]*o,c[1]=n[1]*o,c[2]=n[2]*o,c[3]=n[3]*o,c};M.multiplyByScale=function(n,o,c){return y.typeOf.object("matrix",n),y.typeOf.object("scale",o),y.typeOf.object("result",c),c[0]=n[0]*o.x,c[1]=n[1]*o.x,c[2]=n[2]*o.y,c[3]=n[3]*o.y,c};M.multiplyByUniformScale=function(n,o,c){return y.typeOf.object("matrix",n),y.typeOf.number("scale",o),y.typeOf.object("result",c),c[0]=n[0]*o,c[1]=n[1]*o,c[2]=n[2]*o,c[3]=n[3]*o,c};M.negate=function(n,o){return y.typeOf.object("matrix",n),y.typeOf.object("result",o),o[0]=-n[0],o[1]=-n[1],o[2]=-n[2],o[3]=-n[3],o};M.transpose=function(n,o){y.typeOf.object("matrix",n),y.typeOf.object("result",o);let c=n[0],f=n[2],O=n[1],p=n[3];return o[0]=c,o[1]=f,o[2]=O,o[3]=p,o};M.abs=function(n,o){return y.typeOf.object("matrix",n),y.typeOf.object("result",o),o[0]=Math.abs(n[0]),o[1]=Math.abs(n[1]),o[2]=Math.abs(n[2]),o[3]=Math.abs(n[3]),o};M.equals=function(n,o){return n===o||z(n)&&z(o)&&n[0]===o[0]&&n[1]===o[1]&&n[2]===o[2]&&n[3]===o[3]};M.equalsArray=function(n,o,c){return n[0]===o[c]&&n[1]===o[c+1]&&n[2]===o[c+2]&&n[3]===o[c+3]};M.equalsEpsilon=function(n,o,c){return c=T(c,0),n===o||z(n)&&z(o)&&Math.abs(n[0]-o[0])<=c&&Math.abs(n[1]-o[1])<=c&&Math.abs(n[2]-o[2])<=c&&Math.abs(n[3]-o[3])<=c};M.IDENTITY=Object.freeze(new M(1,0,0,1));M.ZERO=Object.freeze(new M(0,0,0,0));M.COLUMN0ROW0=0;M.COLUMN0ROW1=1;M.COLUMN1ROW0=2;M.COLUMN1ROW1=3;Object.defineProperties(M.prototype,{length:{get:function(){return M.packedLength}}});M.prototype.clone=function(n){return M.clone(this,n)};M.prototype.equals=function(n){return M.equals(this,n)};M.prototype.equalsEpsilon=function(n,o){return M.equalsEpsilon(this,n,o)};M.prototype.toString=function(){return`(${this[0]}, ${this[2]})
(${this[1]}, ${this[3]})`};var Ao=M;export{yn as a,yo as b,jo as c,Ao as d};
