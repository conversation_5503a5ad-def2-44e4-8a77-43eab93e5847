/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.115
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as _}from"./chunk-A7LPWAU6.js";import{a as B,b as S}from"./chunk-UNVMUIJM.js";import{e as i}from"./chunk-WVB7XP3Q.js";var c,r={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},l={};Object.defineProperties(l,{element:{get:function(){if(l.supportsFullscreen())return document[r.fullscreenElement]}},changeEventName:{get:function(){if(l.supportsFullscreen())return r.fullscreenchange}},errorEventName:{get:function(){if(l.supportsFullscreen())return r.fullscreenerror}},enabled:{get:function(){if(l.supportsFullscreen())return document[r.fullscreenEnabled]}},fullscreen:{get:function(){if(l.supportsFullscreen())return l.element!==null}}});l.supportsFullscreen=function(){if(i(c))return c;c=!1;let e=document.body;if(typeof e.requestFullscreen=="function")return r.requestFullscreen="requestFullscreen",r.exitFullscreen="exitFullscreen",r.fullscreenEnabled="fullscreenEnabled",r.fullscreenElement="fullscreenElement",r.fullscreenchange="fullscreenchange",r.fullscreenerror="fullscreenerror",c=!0,c;let t=["webkit","moz","o","ms","khtml"],n;for(let p=0,M=t.length;p<M;++p){let o=t[p];n=`${o}RequestFullscreen`,typeof e[n]=="function"?(r.requestFullscreen=n,c=!0):(n=`${o}RequestFullScreen`,typeof e[n]=="function"&&(r.requestFullscreen=n,c=!0)),n=`${o}ExitFullscreen`,typeof document[n]=="function"?r.exitFullscreen=n:(n=`${o}CancelFullScreen`,typeof document[n]=="function"&&(r.exitFullscreen=n)),n=`${o}FullscreenEnabled`,document[n]!==void 0?r.fullscreenEnabled=n:(n=`${o}FullScreenEnabled`,document[n]!==void 0&&(r.fullscreenEnabled=n)),n=`${o}FullscreenElement`,document[n]!==void 0?r.fullscreenElement=n:(n=`${o}FullScreenElement`,document[n]!==void 0&&(r.fullscreenElement=n)),n=`${o}fullscreenchange`,document[`on${n}`]!==void 0&&(o==="ms"&&(n="MSFullscreenChange"),r.fullscreenchange=n),n=`${o}fullscreenerror`,document[`on${n}`]!==void 0&&(o==="ms"&&(n="MSFullscreenError"),r.fullscreenerror=n)}return c};l.requestFullscreen=function(e,t){l.supportsFullscreen()&&e[r.requestFullscreen]({vrDisplay:t})};l.exitFullscreen=function(){l.supportsFullscreen()&&document[r.exitFullscreen]()};l._names=r;var C=l;var s;typeof navigator<"u"?s=navigator:s={};function a(e){let t=e.split(".");for(let n=0,p=t.length;n<p;++n)t[n]=parseInt(t[n],10);return t}var g,k;function w(){if(!i(g)&&(g=!1,!x())){let e=/ Chrome\/([\.0-9]+)/.exec(s.userAgent);e!==null&&(g=!0,k=a(e[1]))}return g}function T(){return w()&&k}var y,U;function $(){if(!i(y)&&(y=!1,!w()&&!x()&&/ Safari\/[\.0-9]+/.test(s.userAgent))){let e=/ Version\/([\.0-9]+)/.exec(s.userAgent);e!==null&&(y=!0,U=a(e[1]))}return y}function Q(){return $()&&U}var A,W;function q(){if(!i(A)){A=!1;let e=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(s.userAgent);e!==null&&(A=!0,W=a(e[1]),W.isNightly=!!e[2])}return A}function j(){return q()&&W}var m,P;function O(){if(!i(m)){m=!1;let e;s.appName==="Microsoft Internet Explorer"?(e=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(s.userAgent),e!==null&&(m=!0,P=a(e[1]))):s.appName==="Netscape"&&(e=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(s.userAgent),e!==null&&(m=!0,P=a(e[1])))}return m}function J(){return O()&&P}var F,D;function x(){if(!i(F)){F=!1;let e=/ Edg\/([\.0-9]+)/.exec(s.userAgent);e!==null&&(F=!0,D=a(e[1]))}return F}function Y(){return x()&&D}var E,v;function h(){if(!i(E)){E=!1;let e=/Firefox\/([\.0-9]+)/.exec(s.userAgent);e!==null&&(E=!0,v=a(e[1]))}return E}var I;function G(){return i(I)||(I=/Windows/i.test(s.appVersion)),I}var R;function K(){return i(R)||(R=navigator.platform==="iPhone"||navigator.platform==="iPod"||navigator.platform==="iPad"),R}function X(){return h()&&v}var V;function H(){return i(V)||(V=!h()&&typeof PointerEvent<"u"&&(!i(s.pointerEnabled)||s.pointerEnabled)),V}var N,b;function z(){if(!i(b)){let e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");let t=e.style.imageRendering;b=i(t)&&t!=="",b&&(N=t)}return b}function L(){return z()?N:void 0}function u(){if(!u.initialized)throw new B("You must call FeatureDetection.supportsWebP.initialize and wait for the promise to resolve before calling FeatureDetection.supportsWebP");return u._result}u._promise=void 0;u._result=void 0;u.initialize=function(){return i(u._promise)||(u._promise=new Promise(e=>{let t=new Image;t.onload=function(){u._result=t.width>0&&t.height>0,e(u._result)},t.onerror=function(){u._result=!1,e(u._result)},t.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA"})),u._promise};Object.defineProperties(u,{initialized:{get:function(){return i(u._result)}}});var d=[];typeof ArrayBuffer<"u"&&(d.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),typeof Uint8ClampedArray<"u"&&d.push(Uint8ClampedArray),typeof Uint8ClampedArray<"u"&&d.push(Uint8ClampedArray),typeof BigInt64Array<"u"&&d.push(BigInt64Array),typeof BigUint64Array<"u"&&d.push(BigUint64Array));var f={isChrome:w,chromeVersion:T,isSafari:$,safariVersion:Q,isWebkit:q,webkitVersion:j,isInternetExplorer:O,internetExplorerVersion:J,isEdge:x,edgeVersion:Y,isFirefox:h,firefoxVersion:X,isWindows:G,isIPadOrIOS:K,hardwareConcurrency:_(s.hardwareConcurrency,3),supportsPointerEvents:H,supportsImageRenderingPixelated:z,supportsWebP:u,imageRenderingValue:L,typedArrayTypes:d};f.supportsBasis=function(e){return f.supportsWebAssembly()&&e.context.supportsBasis};f.supportsFullscreen=function(){return C.supportsFullscreen()};f.supportsTypedArrays=function(){return typeof ArrayBuffer<"u"};f.supportsBigInt64Array=function(){return typeof BigInt64Array<"u"};f.supportsBigUint64Array=function(){return typeof BigUint64Array<"u"};f.supportsBigInt=function(){return typeof BigInt<"u"};f.supportsWebWorkers=function(){return typeof Worker<"u"};f.supportsWebAssembly=function(){return typeof WebAssembly<"u"};f.supportsWebgl2=function(e){return S.defined("scene",e),e.context.webgl2};f.supportsEsmWebWorkers=function(){return!h()||parseInt(v)>=114};var se=f;export{se as a};
