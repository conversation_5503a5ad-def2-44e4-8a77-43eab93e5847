# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 开发命令

### 启动开发服务器
```bash
npm run dev
```
开发服务器运行在端口 9528

### 构建命令
```bash
# 生产环境构建
npm run build:prod

# 发布环境构建
npm run build:release
```

### 代码检查
```bash
npm run lint
```

### 依赖安装
```bash
npm install
```

## 项目架构

这是一个基于 Vue 2 的智慧水务管理系统，包含多个业务模块：

### 核心技术栈
- Vue 2.6 + Vue Router + Vuex
- Element UI 作为 UI 组件库
- ECharts 用于数据可视化
- Cesium 和 Mars3D 用于 3D 地图展示
- Axios 用于 HTTP 请求

### 主要业务模块
1. **首页 (home)** - 水务综合概览，包含地图展示和数据面板
2. **水质监测 (wq)** - 水质数据监测、分析、预警和报告
3. **管网分析 (pipeNetwork)** - 管网数据分析和可视化
4. **供水调度 (dispatch)** - 实时监测、预警预报、风险预测、调蓄调度
5. **漏损中心 (leakageLoss)** - 分区管理、压力分析、漏损分析、智能诊断
6. **营收管理 (revenue)** - 营收数据分析和管理

### 项目结构
- `src/api/` - API 接口定义，按业务模块分文件夹
- `src/components/` - 公共组件库
- `src/views/` - 页面组件，按业务模块分文件夹
- `src/assets/` - 静态资源（CSS、图片、字体）
- `src/utils/` - 公共工具类和方法
- `src/store/` - Vuex 状态管理
- `src/router/` - 路由配置

### 环境配置
项目使用三个环境配置文件：
- `.env.development` - 开发环境，使用 `/dev-api` 代理
- `.env.production` - 生产环境，使用 `/api` 路径
- `.env.release` - 发布环境，使用 `/prod-api` 路径

### 开发规范
- 组件命名使用 PascalCase，文件名使用 kebab-case
- API 接口按业务模块分文件夹组织
- 样式文件使用 SCSS，包含公共样式和 Element UI 重写样式
- 使用 Webpack 别名简化导入路径（@、api、assets、components、utils、views）

### 地图相关
项目集成了多种地图技术：
- Cesium 用于 3D 地球展示
- Mars3D 作为 Cesium 的上层封装
- 支持多种底图切换和图层管理
- 包含丰富的地图交互功能

### 数据可视化
- 使用 ECharts 4.8 进行图表渲染
- 支持饼图、柱状图、折线图、仪表盘等多种图表类型
- 图表支持响应式布局和主题定制

### 视频监控
集成了多种视频播放器：
- 支持海康威视设备
- 支持大华设备
- 使用 HLS.js 进行流媒体播放